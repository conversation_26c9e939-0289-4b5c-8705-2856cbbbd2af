(()=>{var e={};e.id=2494,e.ids=[2494],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12662:(e,s,t)=>{"use strict";t.d(s,{AppBreadcrumb:()=>u});var r=t(60687),a=t(85814),i=t.n(a),l=t(16189),n=t(43210),d=t.n(n),c=t(70640),o=t(22482);function u({className:e,homeHref:s="/",homeLabel:t="Dashboard",showContainer:a=!0}){let n=(0,l.usePathname)(),u=n?n.split("/").filter(Boolean):[],m=e=>{if(/^\d+$/.test(e))return`ID: ${e}`;if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let s={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return s[e]?s[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},p=u.map((e,s)=>{let t="/"+u.slice(0,s+1).join("/"),a=s===u.length-1,l=m(e);return(0,r.jsxs)(d().Fragment,{children:[(0,r.jsx)(c.BreadcrumbItem,{children:a?(0,r.jsx)(c.BreadcrumbPage,{className:"font-medium text-foreground",children:l}):(0,r.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,r.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:l})})}),!a&&(0,r.jsx)(c.BreadcrumbSeparator,{})]},t)}),h=(0,r.jsx)(c.Breadcrumb,{className:(0,o.cn)("text-sm",e),children:(0,r.jsxs)(c.BreadcrumbList,{className:"flex-wrap",children:[(0,r.jsx)(c.BreadcrumbItem,{children:(0,r.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,r.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:t})})}),u.length>0&&(0,r.jsx)(c.BreadcrumbSeparator,{}),p]})});return a?(0,r.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,r.jsx)("div",{className:"flex items-center",children:h})}):h}},15795:(e,s,t)=>{"use strict";function r(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function a(e){if(e.fullName?.trim())return e.fullName.trim();if(e.name?.trim()){let s=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(s.toLowerCase())||s.includes("_")){let e=s.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${e} (Role)`}return s}if(e.role){let s=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${s} (Role)`}return"Unknown Employee"}function i(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function l(e){return e.replaceAll("_"," ")}t.d(s,{DV:()=>a,fZ:()=>r,s:()=>i,vq:()=>l})},18816:(e,s,t)=>{Promise.resolve().then(t.bind(t,58265))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26398:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29376:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\tasks\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\[id]\\page.tsx","default")},31441:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["[locale]",{children:["tasks",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,29376)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\[id]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/tasks/[id]/page",pathname:"/[locale]/tasks/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},31968:(e,s,t)=>{Promise.resolve().then(t.bind(t,29376))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35137:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},48041:(e,s,t)=>{"use strict";t.d(s,{z:()=>a});var r=t(60687);function a({children:e,description:s,icon:t,title:a}){return(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[t&&(0,r.jsx)(t,{className:"size-8 text-primary"}),(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:a})]}),s&&(0,r.jsx)("p",{className:"mt-1 text-muted-foreground",children:s})]}),e&&(0,r.jsx)("div",{className:"flex items-center gap-2",children:e})]})}t(43210)},52027:(e,s,t)=>{"use strict";t.d(s,{gO:()=>m,jt:()=>y,pp:()=>p});var r=t(60687),a=t(72963),i=t(11516);t(43210);var l=t(68752),n=t(91821),d=t(85726),c=t(22482);let o={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},u={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function m({children:e,className:s,data:t,emptyComponent:a,error:i,errorComponent:l,isLoading:n,loadingComponent:d,onRetry:o}){return n?d||(0,r.jsx)(x,{...s&&{className:s},text:"Loading..."}):i?l||(0,r.jsx)(h,{...s&&{className:s},message:i,...o&&{onRetry:o}}):!t||Array.isArray(t)&&0===t.length?a||(0,r.jsx)("div",{className:(0,c.cn)("text-center py-8",s),children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,r.jsx)("div",{className:s,children:e(t)})}function p({className:e,description:s,icon:t,primaryAction:a,secondaryAction:i,title:n}){return(0,r.jsxs)("div",{className:(0,c.cn)("space-y-6 text-center py-12",e),children:[t&&(0,r.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,r.jsx)(t,{className:"h-10 w-10 text-muted-foreground"})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:n}),s&&(0,r.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:s})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[a&&(0,r.jsx)(l.r,{actionType:"primary",asChild:!!a.href,icon:a.icon,onClick:a.onClick,children:a.href?(0,r.jsx)("a",{href:a.href,children:a.label}):a.label}),i&&(0,r.jsx)(l.r,{actionType:"tertiary",asChild:!!i.href,icon:i.icon,onClick:i.onClick,children:i.href?(0,r.jsx)("a",{href:i.href,children:i.label}):i.label})]})]})}function h({className:e,message:s,onRetry:t}){return(0,r.jsxs)(n.Fc,{className:(0,c.cn)("my-4",e),variant:"destructive",children:[(0,r.jsx)(a.A,{className:"size-4"}),(0,r.jsx)(n.XL,{children:"Error"}),(0,r.jsx)(n.TN,{children:(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:s}),t&&(0,r.jsx)(l.r,{actionType:"tertiary",icon:(0,r.jsx)(i.A,{className:"size-4"}),onClick:t,size:"sm",children:"Try Again"})]})})]})}function x({className:e,fullPage:s=!1,size:t="md",text:a}){return(0,r.jsx)("div",{className:(0,c.cn)("flex items-center justify-center",s&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",e),children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(i.A,{className:(0,c.cn)("animate-spin text-primary",o[t])}),a&&(0,r.jsx)("span",{className:(0,c.cn)("mt-2 text-muted-foreground",u[t]),children:a})]})})}function y({className:e,count:s=1,testId:t="loading-skeleton",variant:a="default"}){return"card"===a?(0,r.jsx)("div",{className:(0,c.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",e),"data-testid":t,children:Array(s).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,r.jsx)(d.E,{className:"aspect-[16/10] w-full"}),(0,r.jsxs)("div",{className:"p-5",children:[(0,r.jsx)(d.E,{className:"mb-1 h-7 w-3/4"}),(0,r.jsx)(d.E,{className:"mb-3 h-4 w-1/2"}),(0,r.jsx)(d.E,{className:"my-3 h-px w-full"}),(0,r.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.E,{className:"mr-2.5 size-5 rounded-full"}),(0,r.jsx)(d.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===a?(0,r.jsxs)("div",{className:(0,c.cn)("space-y-3",e),"data-testid":t,children:[(0,r.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,r.jsx)(d.E,{className:"h-8 flex-1"},s))}),Array(s).fill(0).map((e,s)=>(0,r.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,r.jsx)(d.E,{className:"h-6 flex-1"},s))},s))]}):"list"===a?(0,r.jsx)("div",{className:(0,c.cn)("space-y-3",e),"data-testid":t,children:Array(s).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(d.E,{className:"size-12 rounded-full"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)(d.E,{className:"h-4 w-1/3"}),(0,r.jsx)(d.E,{className:"h-4 w-full"})]})]},s))}):"stats"===a?(0,r.jsx)("div",{className:(0,c.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",e),"data-testid":t,children:Array(s).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(d.E,{className:"h-5 w-1/3"}),(0,r.jsx)(d.E,{className:"size-5 rounded-full"})]}),(0,r.jsx)(d.E,{className:"mt-3 h-8 w-1/2"}),(0,r.jsx)(d.E,{className:"mt-2 h-4 w-2/3"})]},s))}):(0,r.jsx)("div",{className:(0,c.cn)("space-y-2",e),"data-testid":t,children:Array(s).fill(0).map((e,s)=>(0,r.jsx)(d.E,{className:"h-5 w-full"},s))})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57207:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},58265:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>B});var r=t(60687),a=t(75699),i=t(58261),l=t(14975),n=t(80489),d=t(35137),c=t(57207),o=t(26398),u=t(92876),m=t(15036),p=t(48206),h=t(36644),x=t(24920),y=t(3662),f=t(58595),g=t(71032),v=t(97025);let j=(0,t(82614).A)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var b=t(85814),k=t.n(b),N=t(16189),E=t(43210),w=t(55925),A=t(12662),I=t(32584),S=t(96834),C=t(29523),q=t(44493),T=t(80013),D=t(52027),M=t(48041),P=t(15079),R=t(35950),z=t(3940),Q=t(19599),$=t(73227),L=t(22482),O=t(15795);let _=e=>{switch(e){case"Assigned":return"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30";case"Cancelled":return"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30";case"Completed":return"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30";case"In_Progress":return"bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800/30";case"Pending":return"bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30";default:return"bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30"}},K=e=>{switch(e){case"High":return"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30";case"Low":return"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30";case"Medium":return"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30";default:return"bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30"}},F=(e,s=!1)=>{if(!e)return"N/A";try{return(0,a.GP)((0,i.H)(e),s?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch{return"Invalid Date"}};function B(){let e=(0,N.useParams)(),s=(0,N.useRouter)(),{showEntityDeleted:t,showEntityDeletionError:a,showEntityUpdated:i,showEntityUpdateError:j,showFormError:b,showFormSuccess:I}=(0,z.O_)("task"),O=e?.id,{data:B,error:H,isLoading:V,refetch:Z}=(0,$.xo)(O),{data:J}=(0,Q.nR)(),W=J?.filter(e=>"Active"===e.status)||[],{mutateAsync:X}=(0,$.AK)(),{mutateAsync:Y}=(0,$.K)(),[ee,es]=(0,E.useState)(""),et=async()=>{if(B?.id)try{await X(B.id);let e={name:B.description.slice(0,30)+(B.description.length>30?"...":""),title:B.description.slice(0,30)+(B.description.length>30?"...":"")};t(e),s.push("/tasks")}catch(e){console.error("Error deleting task:",e),a(e.message||"Failed to delete task. Please try again.")}},er=async()=>{if(B?.id&&ee)try{await Y({data:{driverEmployeeId:Number.parseInt(ee)},id:B.id});let e={name:`Task assigned to employee ID ${ee}`,title:`Task assigned to employee ID ${ee}`};i(e),Z(),es("")}catch(e){console.error("Error assigning task:",e),j(e.message||"Failed to assign task. Please try again.")}},ea=async e=>{if(B?.id){let s={};if(B.driverEmployeeId===e)s.driverEmployeeId=void 0;else if(B.staffEmployeeId===e)return void b("Cannot unassign the primary staff member directly. Please reassign.",{errorTitle:"Action Not Allowed"});else return void b("Employee not found in task assignments.");if(0===Object.keys(s).length)return void I({successDescription:"No changes to apply for unassignment.",successTitle:"Info"});try{await Y({data:s,id:B.id}),i({name:"Employee unassigned from task",title:"Employee unassigned from task"}),Z()}catch(e){console.error("Error updating task assignment:",e),j(e.message||"Failed to unassign task. Please try again.")}}},ei=B?.status==="Completed"||B?.status==="Cancelled";return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(A.AppBreadcrumb,{}),(0,r.jsx)(D.gO,{data:B,emptyComponent:(0,r.jsxs)("div",{className:"py-10 text-center",children:[(0,r.jsx)(M.z,{icon:l.A,title:"Task Not Found"}),(0,r.jsx)("p",{className:"mb-4",children:"The requested task could not be found."})]}),error:H?H.message:null,isLoading:V??!1,loadingComponent:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(M.z,{icon:n.A,title:"Loading Task..."}),(0,r.jsx)(D.jt,{count:1,variant:"card"}),(0,r.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,r.jsx)(D.jt,{className:"lg:col-span-2",count:1,variant:"card"}),(0,r.jsx)(D.jt,{count:1,variant:"card"})]})]}),onRetry:Z,children:e=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(M.z,{description:"Manage details and assignment for this task.",icon:n.A,title:e.description||"Task Details",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,r.jsx)(C.$,{asChild:!0,className:"gap-2",variant:"default",children:(0,r.jsxs)(k(),{href:`/tasks/${e.id}/edit`,children:[(0,r.jsx)(d.A,{className:"size-4"}),"Edit"]})}),(0,r.jsxs)(w.Lt,{children:[(0,r.jsx)(w.tv,{asChild:!0,children:(0,r.jsxs)(C.$,{className:"gap-2",variant:"destructive",children:[(0,r.jsx)(c.A,{className:"size-4"}),"Delete Task"]})}),(0,r.jsxs)(w.EO,{children:[(0,r.jsxs)(w.wd,{children:[(0,r.jsx)(w.r7,{children:"Are you sure?"}),(0,r.jsx)(w.$v,{children:"This action cannot be undone. This will permanently delete the task."})]}),(0,r.jsxs)(w.ck,{children:[(0,r.jsx)(w.Zr,{children:"Cancel"}),(0,r.jsx)(w.Rx,{className:"bg-destructive hover:bg-destructive/90",onClick:et,children:"Delete"})]})]})]})]})}),(0,r.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,r.jsxs)(q.Zp,{className:"shadow-sm lg:col-span-2",children:[(0,r.jsx)(q.aR,{className:"border-b",children:(0,r.jsxs)("div",{className:"flex items-start justify-between gap-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)(q.ZB,{className:"text-2xl font-bold leading-tight",children:[e.description," "]})," "]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(S.E,{className:(0,L.cn)("justify-center",_(e.status)),children:e.status}),(0,r.jsxs)(S.E,{className:(0,L.cn)("justify-center",K(e.priority)),variant:"outline",children:[e.priority," Priority"]})]})]})}),(0,r.jsxs)(q.Wu,{className:"space-y-6 p-6",children:[(0,r.jsx)(G,{icon:o.A,label:"Location",value:e.location}),(0,r.jsx)(G,{icon:u.A,label:"Start Date & Time",value:F(e.dateTime,!0)}),(0,r.jsx)(G,{icon:m.A,label:"Estimated Duration",value:`${e.estimatedDuration} minutes`}),e.deadline&&(0,r.jsx)(G,{icon:m.A,label:"Deadline",value:F(e.deadline,!1)}),e.requiredSkills&&e.requiredSkills.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Required Skills"})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.requiredSkills.map((e,s)=>(0,r.jsx)(S.E,{className:"text-xs",variant:"secondary",children:e},s))})]}),e.notes&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Notes"})]}),(0,r.jsx)("p",{className:"rounded-lg bg-muted/30 p-3 text-sm text-muted-foreground",children:e.notes})]}),e.vehicleId&&(0,r.jsx)(G,{icon:x.A,label:"Assigned Vehicle",children:(0,r.jsxs)("div",{className:"rounded-lg border bg-muted/30 p-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:e.vehicle?`${e.vehicle.make} ${e.vehicle.model}`:`Vehicle ID: ${e.vehicleId}`}),e.vehicle?.licensePlate&&(0,r.jsxs)("p",{className:"mt-1 text-xs text-muted-foreground",children:["License Plate: ",e.vehicle.licensePlate]}),e.vehicle?.year&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Year: ",e.vehicle.year]})]})}),e.subtasks&&e.subtasks.length>0&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(R.w,{}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"mb-3 flex items-center gap-2 text-lg font-semibold",children:[(0,r.jsx)(n.A,{className:"size-4"}),"Sub-Tasks"]}),(0,r.jsx)("ul",{className:"space-y-2",children:e.subtasks.map(e=>(0,r.jsxs)("li",{className:(0,L.cn)("flex items-center gap-2 text-sm",e.completed&&"line-through text-muted-foreground"),children:[e.completed?(0,r.jsx)(y.A,{className:"size-4 text-green-500"}):(0,r.jsx)("div",{className:"size-4 rounded-sm border"}),e.title]},e.id))})]})]})]}),(0,r.jsx)(q.wL,{className:"flex items-center justify-center border-t bg-muted/20",children:(0,r.jsxs)("div",{className:"flex w-full flex-col gap-2 text-xs text-muted-foreground sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{className:"font-medium",children:"Created:"}),(0,r.jsx)("span",{children:F(e.createdAt,!0)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{className:"font-medium",children:"Updated:"}),(0,r.jsx)("span",{children:F(e.updatedAt,!0)})]})]})})]}),(0,r.jsxs)(q.Zp,{className:"h-fit shadow-sm",children:[(0,r.jsx)(q.aR,{children:(0,r.jsxs)(q.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"size-5 text-primary"}),"Task Assignment"]})}),(0,r.jsxs)(q.Wu,{className:"space-y-4",children:[e.staffEmployeeId&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(f.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Staff Assignment"})]}),(0,r.jsx)(U,{employee:e.staffEmployee,employeeId:e.staffEmployeeId,isTaskCompleted:ei,onUnassign:()=>{e.staffEmployeeId&&ea(e.staffEmployeeId)}})]}),e.driverEmployeeId&&(0,r.jsxs)(r.Fragment,{children:[e.staffEmployeeId&&(0,r.jsx)(R.w,{className:"my-4"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Driver Assignment"})]}),(0,r.jsx)(U,{employee:e.driverEmployee,employeeId:e.driverEmployeeId,isTaskCompleted:ei,onUnassign:()=>{e.driverEmployeeId&&ea(e.driverEmployeeId)}})]})]}),!e.staffEmployeeId&&!e.driverEmployeeId&&(0,r.jsx)("div",{className:"py-4 text-center",children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"This task is currently unassigned."})}),!ei&&!(e.staffEmployeeId||e.driverEmployeeId)&&(0,r.jsxs)("div",{className:"space-y-4 border-t pt-4",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Assign task to an employee:"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(T.J,{className:"text-sm font-medium",htmlFor:"assignee-select",children:"Select Employee"}),(0,r.jsxs)(P.l6,{onValueChange:es,value:ee,children:[(0,r.jsx)(P.bq,{children:(0,r.jsx)(P.yv,{placeholder:"Select an employee"})}),(0,r.jsx)(P.gC,{children:W.length>0?W.map(e=>(0,r.jsxs)(P.eb,{value:String(e.id),children:[e.fullName||e.name," (",e.position||e.role,")"]},e.id)):(0,r.jsx)("div",{className:"p-2 text-sm text-muted-foreground",children:"No available employees."})})]}),(0,r.jsxs)(C.$,{className:"w-full gap-2",disabled:!ee,onClick:er,children:[(0,r.jsx)(g.A,{className:"size-4"}),"Assign to Selected Employee"]})]})]}),"Completed"===e.status&&(0,r.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-3 dark:border-green-800 dark:bg-green-900/20",children:[(0,r.jsx)(y.A,{className:"size-4 text-green-600"}),(0,r.jsx)("p",{className:"text-sm text-green-700 dark:text-green-400",children:"Task completed."})]}),"Cancelled"===e.status&&(0,r.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20",children:[(0,r.jsx)(v.A,{className:"size-4 text-red-600"}),(0,r.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:"Task cancelled."})]})]})]})]})]})})]})}function U({employee:e,employeeId:s,isTaskCompleted:t,onUnassign:a}){if(!s&&0!==s)return(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"This task is currently unassigned."});if(!e)return(0,r.jsxs)("p",{className:"text-sm text-destructive",children:["Could not load assignee (ID: ",s,")."]});let i=(0,O.DV)(e);return(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border bg-muted/30 p-2",children:[(0,r.jsx)(I.eu,{className:"size-8",children:(0,r.jsx)(I.q5,{className:"text-xs font-medium",children:i.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(k(),{className:"text-sm font-medium hover:text-primary",href:`/employees/${e.employeeId}`,children:i}),(0,r.jsx)("span",{className:"text-xs capitalize text-muted-foreground",children:e.position||e.role})]}),!t&&a&&s&&(0,r.jsx)(C.$,{className:"size-6 p-0 text-muted-foreground hover:text-destructive",onClick:()=>a(s),size:"sm",variant:"ghost",children:(0,r.jsx)(j,{className:"size-3"})})]})})}function G({children:e,className:s,icon:t,label:a,value:i}){return void 0!==i||e?(0,r.jsxs)("div",{className:(0,L.cn)("flex items-start gap-3",s),children:[(0,r.jsx)(t,{className:"mt-1 size-4 shrink-0 text-muted-foreground"}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:a}),null!=i&&(0,r.jsx)("p",{className:"text-base font-medium text-foreground",children:i}),e]})]}):null}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69795:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},73227:(e,s,t)=>{"use strict";t.d(s,{ZY:()=>j,AK:()=>k,b7:()=>g,xo:()=>v,si:()=>f,K:()=>b});var r=t(93425),a=t(8693),i=t(54050),l=t(43210),n=t(46349),d=t(75176),c=t(83144),o=t(57930);let u={all:["tasks"],detail:e=>["tasks",e]},m=e=>({enabled:!!e,queryFn:()=>d.taskApiService.getById(e),queryKey:u.detail(e),staleTime:3e5}),p=()=>({queryFn:()=>d.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),h=()=>({queryFn:()=>d.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),x=e=>[m(e),p(),h()];var y=t(38118);let f=e=>(0,n.GK)([...u.all],async()=>(await d.taskApiService.getAll()).data,"task",{staleTime:0,...e}),g=e=>(0,n.GK)([...u.detail(e)],async()=>await d.taskApiService.getById(e),"task",{enabled:!!e,staleTime:3e5}),v=e=>{let[s,t,a]=(0,r.E)({queries:x(e)}),i=(0,l.useMemo)(()=>{if(s?.data&&t?.data&&a?.data)try{let e=o.J.fromApi(s.data),r=Array.isArray(t.data)?t.data:[],i=Array.isArray(a.data)?a.data:[];return(0,c.R)(e,r,i)}catch(e){throw console.error("Error enriching task data:",e),e}},[s?.data,t?.data,a?.data]),n=(0,l.useCallback)(()=>{s?.refetch(),t?.refetch(),a?.refetch()},[s?.refetch,t?.refetch,a?.refetch]);return{data:i,error:s?.error||t?.error||a?.error,isError:s?.isError||t?.isError||a?.isError,isLoading:s?.isLoading||t?.isLoading||a?.isLoading,isPending:s?.isPending||t?.isPending||a?.isPending,refetch:n}},j=()=>{let e=(0,a.jE)();return(0,i.n)({mutationFn:async e=>{let s=o.J.toCreateRequest(e);return await d.taskApiService.create(s)},onError:(s,t,r)=>{r?.previousTasks&&e.setQueryData(u.all,r.previousTasks),console.error("Failed to create task:",s)},onMutate:async s=>{await e.cancelQueries({queryKey:u.all});let t=e.getQueryData(u.all);return e.setQueryData(u.all,(e=[])=>{let t="optimistic-"+Date.now().toString(),r=new Date().toISOString();return[...e,{createdAt:r,dateTime:s.dateTime??null,deadline:s.deadline??null,description:s.description,driverEmployee:null,driverEmployeeId:s.driverEmployeeId??null,estimatedDuration:s.estimatedDuration??null,id:t,location:s.location??null,notes:s.notes??null,priority:s.priority,requiredSkills:s.requiredSkills??null,staffEmployee:null,staffEmployeeId:s.staffEmployeeId??null,status:s.status||"Pending",subtasks:s.subtasks?.map(e=>({completed:e.completed||!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:t,title:e.title}))||[],updatedAt:r,vehicle:null,vehicleId:s.vehicleId??null}]}),{previousTasks:t}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})}})},b=()=>{let e=(0,a.jE)();return(0,i.n)({mutationFn:async({data:e,id:s})=>{let t=o.J.toUpdateRequest(e);return await d.taskApiService.update(s,t)},onError:(s,t,r)=>{r?.previousTask&&e.setQueryData(u.detail(t.id),r.previousTask),r?.previousTasksList&&e.setQueryData(u.all,r.previousTasksList),console.error("Failed to update task:",s)},onMutate:async({data:s,id:t})=>{await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(t)});let r=e.getQueryData(u.detail(t)),a=e.getQueryData(u.all);return e.setQueryData(u.detail(t),e=>{if(!e)return e;let r=new Date().toISOString();return{...e,dateTime:void 0!==s.dateTime?s.dateTime:e.dateTime,deadline:(0,y.d$)(void 0!==s.deadline?s.deadline:e.deadline),description:s.description??e.description,driverEmployeeId:(0,y.d$)(void 0!==s.driverEmployeeId?s.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==s.estimatedDuration?s.estimatedDuration:e.estimatedDuration,location:void 0!==s.location?s.location:e.location,notes:(0,y.d$)(void 0!==s.notes?s.notes:e.notes),priority:s.priority??e.priority,requiredSkills:void 0!==s.requiredSkills?s.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==s.staffEmployeeId?s.staffEmployeeId:e.staffEmployeeId,status:s.status??e.status,subtasks:s.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:t,title:e.title}))||e.subtasks||[],updatedAt:r,vehicleId:(0,y.d$)(void 0!==s.vehicleId?s.vehicleId:e.vehicleId)}}),e.setQueryData(u.all,(e=[])=>e.map(e=>{if(e.id===t){let r=new Date().toISOString(),a=s.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:t,title:e.title}))||e.subtasks||[];return{...e,dateTime:void 0!==s.dateTime?s.dateTime:e.dateTime,deadline:(0,y.d$)(void 0!==s.deadline?s.deadline:e.deadline),description:s.description??e.description,driverEmployeeId:(0,y.d$)(void 0!==s.driverEmployeeId?s.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==s.estimatedDuration?s.estimatedDuration:e.estimatedDuration,location:void 0!==s.location?s.location:e.location,notes:(0,y.d$)(void 0!==s.notes?s.notes:e.notes),priority:s.priority??e.priority,requiredSkills:void 0!==s.requiredSkills?s.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==s.staffEmployeeId?s.staffEmployeeId:e.staffEmployeeId,status:s.status??e.status,subtasks:a,updatedAt:r,vehicleId:(0,y.d$)(void 0!==s.vehicleId?s.vehicleId:e.vehicleId)}}return e})),{previousTask:r,previousTasksList:a}},onSettled:(s,t,r)=>{e.invalidateQueries({queryKey:u.detail(r.id)}),e.invalidateQueries({queryKey:u.all})}})},k=()=>{let e=(0,a.jE)();return(0,i.n)({mutationFn:async e=>(await d.taskApiService.delete(e),e),onError:(s,t,r)=>{r?.previousTasksList&&e.setQueryData(u.all,r.previousTasksList),console.error("Failed to delete task:",s)},onMutate:async s=>{await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(s)});let t=e.getQueryData(u.all);return e.setQueryData(u.all,(e=[])=>e.filter(e=>e.id!==s)),e.removeQueries({queryKey:u.detail(s)}),{previousTasksList:t}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})}})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83144:(e,s,t)=>{"use strict";t.d(s,{R:()=>a});class r{static enrich(e,s,t){let{employeeMap:r,vehicleMap:a}=this.createLookupMaps(s,t),i=this.enrichStaffEmployee(e,r);return i=this.enrichDriverEmployee(i,r),i=this.enrichVehicle(i,a)}static createLookupMaps(e,s){let t=Array.isArray(e)?e:[],r=Array.isArray(s)?s:[];return{employeeMap:new Map(t.map(e=>[e.id,e])),vehicleMap:new Map(r.map(e=>[e.id,e]))}}static enrichDriverEmployee(e,s){if(!e.driverEmployeeId)return e;let t=e.driverEmployee??s.get(e.driverEmployeeId)??null;return{...e,driverEmployee:t}}static enrichStaffEmployee(e,s){if(!e.staffEmployeeId)return e;let t=e.staffEmployee??s.get(e.staffEmployeeId)??null;return{...e,staffEmployee:t}}static enrichVehicle(e,s){if(!e.vehicleId)return e;let t=e.vehicle??s.get(e.vehicleId)??null;return{...e,vehicle:t}}}let a=(e,s,t)=>r.enrich(e,s,t)},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},92876:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},93425:(e,s,t)=>{"use strict";t.d(s,{E:()=>x});var r=t(43210),a=t(33465),i=t(5563),l=t(35536),n=t(31212);function d(e,s){let t=new Set(s);return e.filter(e=>!t.has(e))}var c=class extends l.Q{#e;#s;#t;#r;#a;#i;#l;#n;#d=[];constructor(e,s,t){super(),this.#e=e,this.#r=t,this.#t=[],this.#a=[],this.#s=[],this.setQueries(s)}onSubscribe(){1===this.listeners.size&&this.#a.forEach(e=>{e.subscribe(s=>{this.#c(e,s)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#a.forEach(e=>{e.destroy()})}setQueries(e,s){this.#t=e,this.#r=s,a.jG.batch(()=>{let e=this.#a,s=this.#o(this.#t);this.#d=s,s.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let t=s.map(e=>e.observer),r=t.map(e=>e.getCurrentResult()),a=t.some((s,t)=>s!==e[t]);(e.length!==t.length||a)&&(this.#a=t,this.#s=r,this.hasListeners()&&(d(e,t).forEach(e=>{e.destroy()}),d(t,e).forEach(e=>{e.subscribe(s=>{this.#c(e,s)})}),this.#u()))})}getCurrentResult(){return this.#s}getQueries(){return this.#a.map(e=>e.getCurrentQuery())}getObservers(){return this.#a}getOptimisticResult(e,s){let t=this.#o(e),r=t.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[r,e=>this.#m(e??r,s),()=>this.#p(r,t)]}#p(e,s){return s.map((t,r)=>{let a=e[r];return t.defaultedQueryOptions.notifyOnChangeProps?a:t.observer.trackResult(a,e=>{s.forEach(s=>{s.observer.trackProp(e)})})})}#m(e,s){return s?(this.#i&&this.#s===this.#n&&s===this.#l||(this.#l=s,this.#n=this.#s,this.#i=(0,n.BH)(this.#i,s(e))),this.#i):e}#o(e){let s=new Map(this.#a.map(e=>[e.options.queryHash,e])),t=[];return e.forEach(e=>{let r=this.#e.defaultQueryOptions(e),a=s.get(r.queryHash);a?t.push({defaultedQueryOptions:r,observer:a}):t.push({defaultedQueryOptions:r,observer:new i.$(this.#e,r)})}),t}#c(e,s){let t=this.#a.indexOf(e);-1!==t&&(this.#s=function(e,s,t){let r=e.slice(0);return r[s]=t,r}(this.#s,t,s),this.#u())}#u(){if(this.hasListeners()){let e=this.#i,s=this.#p(this.#s,this.#d);e!==this.#m(s,this.#r?.combine)&&a.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#s)})})}}},o=t(8693),u=t(24903),m=t(18228),p=t(16142),h=t(76935);function x({queries:e,...s},t){let l=(0,o.jE)(t),d=(0,u.w)(),x=(0,m.h)(),y=r.useMemo(()=>e.map(e=>{let s=l.defaultQueryOptions(e);return s._optimisticResults=d?"isRestoring":"optimistic",s}),[e,l,d]);y.forEach(e=>{(0,h.jv)(e),(0,p.LJ)(e,x)}),(0,p.wZ)(x);let[f]=r.useState(()=>new c(l,y,s)),[g,v,j]=f.getOptimisticResult(y,s.combine),b=!d&&!1!==s.subscribed;r.useSyncExternalStore(r.useCallback(e=>b?f.subscribe(a.jG.batchCalls(e)):n.lQ,[f,b]),()=>f.getCurrentResult(),()=>f.getCurrentResult()),r.useEffect(()=>{f.setQueries(y,s)},[y,s,f]);let k=g.some((e,s)=>(0,h.EU)(y[s],e))?g.flatMap((e,s)=>{let t=y[s];if(t){let s=new i.$(l,t);if((0,h.EU)(t,e))return(0,h.iL)(t,s,x);(0,h.nE)(e,d)&&(0,h.iL)(t,s,x)}return[]}):[];if(k.length>0)throw Promise.all(k);let N=g.find((e,s)=>{let t=y[s];return t&&(0,p.$1)({result:e,errorResetBoundary:x,throwOnError:t.throwOnError,query:l.getQueryCache().get(t.queryHash),suspense:t.suspense})});if(N?.error)throw N.error;return v(j())}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,3871,7048,8390,2670,8739,3302,2936,9599,3211],()=>t(31441));module.exports=r})();