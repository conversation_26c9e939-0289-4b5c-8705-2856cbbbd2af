(()=>{var e={};e.id=5443,e.ids=[5443],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23473:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(60687);t(43210);var n=t(63213),a=t(321),i=t(60436);function o(){return(0,s.jsx)(n<PERSON>,{children:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"mb-8 text-center",children:[(0,s.jsx)("h1",{className:"mb-2 text-3xl font-bold text-gray-900",children:"\uD83D\uDEA8 Emergency Security Test"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Testing Supabase Authentication Implementation"})]}),(0,s.jsx)(a.OV,{children:(0,s.jsxs)("div",{className:"mx-auto max-w-2xl space-y-6",children:[(0,s.jsxs)("div",{className:"rounded-lg bg-white p-6 shadow-md",children:[(0,s.jsx)("h2",{className:"mb-4 text-xl font-semibold",children:"✅ Authentication Successful"}),(0,s.jsx)("p",{className:"mb-4 text-gray-600",children:"You are now authenticated and can access protected content."}),(0,s.jsx)(i.F,{variant:"card"})]}),(0,s.jsxs)("div",{className:"rounded-lg border border-green-200 bg-green-50 p-4",children:[(0,s.jsx)("h3",{className:"mb-2 font-medium text-green-800",children:"\uD83C\uDF89 Emergency Security Implementation Status"}),(0,s.jsxs)("ul",{className:"space-y-1 text-sm text-green-700",children:[(0,s.jsx)("li",{children:"✅ Supabase client configured"}),(0,s.jsx)("li",{children:"✅ Authentication hook implemented"}),(0,s.jsx)("li",{children:"✅ Login form functional"}),(0,s.jsx)("li",{children:"✅ Protected routes working"}),(0,s.jsx)("li",{children:"✅ User profile display active"}),(0,s.jsx)("li",{children:"✅ Session management operational"})]})]})]})})]})})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44180:(e,r,t)=>{Promise.resolve().then(t.bind(t,23473))},51043:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\auth-test\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\auth-test\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91417:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=t(65239),n=t(48088),a=t(88170),i=t.n(a),o=t(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);t.d(r,c);let l={children:["",{children:["[locale]",{children:["auth-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,51043)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\auth-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\auth-test\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/auth-test/page",pathname:"/[locale]/auth-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91444:(e,r,t)=>{Promise.resolve().then(t.bind(t,51043))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,3871,7048,8739],()=>t(91417));module.exports=s})();