(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7385],{14636:(e,s,t)=>{"use strict";t.d(s,{AM:()=>n,Wv:()=>d,hl:()=>c});var a=t(95155),r=t(20547),l=t(12115),i=t(54036);let n=r.bL,d=r.l9;r.bm;let c=l.forwardRef((e,s)=>{let{align:t="center",className:l,sideOffset:n=4,...d}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{align:t,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",l),ref:s,sideOffset:n,...d})})});c.displayName=r.UC.displayName},30356:(e,s,t)=>{"use strict";t.d(s,{C:()=>c,z:()=>d});var a=t(95155),r=t(54059),l=t(70154),i=t(12115),n=t(54036);let d=i.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.bL,{className:(0,n.cn)("grid gap-2",t),...l,ref:s})});d.displayName=r.bL.displayName;let c=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(r.q7,{className:(0,n.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i,children:(0,a.jsx)(r.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"size-2.5 fill-current text-current"})})})});c.displayName=r.q7.displayName},38342:(e,s,t)=>{"use strict";t.d(s,{s:()=>j});var a=t(95155),r=t(66655),l=t(71978),i=t(34214),n=t(51920),d=t(29471),c=t(18271),o=t(89829),m=t(67554);t(12115);var u=t(30285),x=t(85057),h=t(30356),g=t(76202),v=t(80333),p=t(17313);let f={cards:{icon:r.A,label:"Cards"},table:{icon:l.A,label:"Table"},list:{icon:i.A,label:"List"},calendar:{icon:n.A,label:"Calendar"},grid:{icon:d.A,label:"Grid"}},j=e=>{let{config:s,entityType:t,layout:l,monitoring:i,setViewMode:n,setGridColumns:d,toggleCompactMode:j,setMonitoringEnabled:y,setRefreshInterval:b,toggleAutoRefresh:N,resetSettings:w,className:k=""}=e,A=s.viewModes||["cards","table","list"];return(0,a.jsxs)("div",{className:"space-y-6 p-4 ".concat(k),children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"size-6"}),s.title," Settings"]}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["Customize your ",t," dashboard experience"]})]}),(0,a.jsxs)(p.tU,{className:"w-full",defaultValue:"layout",children:[(0,a.jsxs)(p.j7,{className:"grid w-full grid-cols-3",children:[(0,a.jsx)(p.Xi,{value:"layout",children:"Layout"}),(0,a.jsx)(p.Xi,{value:"display",children:"Display"}),(0,a.jsx)(p.Xi,{value:"refresh",children:"Refresh"})]}),(0,a.jsx)(p.av,{className:"mt-4 space-y-6",value:"layout",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{className:"text-lg font-semibold",children:"View Mode"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Choose how ",t,"s are displayed"]}),(0,a.jsx)(h.z,{className:"grid grid-cols-2 gap-4 pt-2",onValueChange:e=>n(e),value:l.viewMode,children:A.map(e=>{var s,t;let l=(null==(s=f[e])?void 0:s.icon)||r.A,i=(null==(t=f[e])?void 0:t.label)||e;return(0,a.jsxs)(x.J,{className:"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary cursor-pointer",htmlFor:"layout-".concat(e),children:[(0,a.jsx)(h.C,{className:"sr-only",id:"layout-".concat(e),value:e}),(0,a.jsx)(l,{className:"mb-3 size-6"}),i]},e)})})]}),("cards"===l.viewMode||"grid"===l.viewMode)&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(x.J,{className:"text-lg font-semibold",children:["Grid Columns: ",l.gridColumns]}),(0,a.jsx)(g.A,{defaultValue:[l.gridColumns],max:6,min:1,onValueChange:e=>{let[s]=e;return void 0!==s&&d(s)},step:1}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-muted-foreground",children:[(0,a.jsx)("span",{children:"1 column"}),(0,a.jsx)("span",{children:"6 columns"})]})]})]})}),(0,a.jsx)(p.av,{className:"mt-4 space-y-6",value:"display",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(x.J,{className:"text-lg font-semibold",children:"Compact Mode"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Show more ",t,"s in less space"]})]}),(0,a.jsx)(v.d,{checked:l.compactMode,onCheckedChange:j})]}),s.enableBulkActions&&(0,a.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(x.J,{className:"text-lg font-semibold",children:"Bulk Actions"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable selection and bulk operations"})]}),(0,a.jsx)(v.d,{checked:!0,disabled:!0})]})]})}),(0,a.jsx)(p.av,{className:"mt-4 space-y-6",value:"refresh",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsxs)(x.J,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"size-4"}),"Auto Refresh"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Automatically refresh ",t," data"]})]}),(0,a.jsx)(v.d,{checked:i.autoRefresh,onCheckedChange:N})]}),i.autoRefresh&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(x.J,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"size-4"}),"Refresh Interval"]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{label:"5 seconds",value:5e3},{label:"10 seconds",value:1e4},{label:"30 seconds",value:3e4},{label:"1 minute",value:6e4},{label:"5 minutes",value:3e5}].map(e=>(0,a.jsx)(u.$,{variant:i.refreshInterval===e.value?"default":"outline",size:"sm",onClick:()=>b(e.value),children:e.label},e.value))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(x.J,{className:"text-lg font-semibold",children:"Real-time Updates"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable live data updates"})]}),(0,a.jsx)(v.d,{checked:i.enabled,onCheckedChange:y})]})]})})]}),(0,a.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,a.jsx)(u.$,{onClick:w,variant:"outline",children:"Reset to Defaults"})})]})}},40879:(e,s,t)=>{"use strict";t.d(s,{dj:()=>u,oR:()=>m});var a=t(12115);let r=0,l=new Map,i=e=>{if(l.has(e))return;let s=setTimeout(()=>{l.delete(e),o({toastId:e,type:"REMOVE_TOAST"})},1e6);l.set(e,s)},n=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:t}=s;if(t)i(t);else for(let s of e.toasts)i(s.id);return{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)}}},d=[],c={toasts:[]};function o(e){for(let s of(c=n(c,e),d))s(c)}function m(e){let{...s}=e,t=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>o({toastId:t,type:"DISMISS_TOAST"});return o({toast:{...s,id:t,onOpenChange:e=>{e||a()},open:!0},type:"ADD_TOAST"}),{dismiss:a,id:t,update:e=>o({toast:{...e,id:t},type:"UPDATE_TOAST"})}}function u(){let[e,s]=a.useState(c);return a.useEffect(()=>(d.push(s),()=>{let e=d.indexOf(s);-1!==e&&d.splice(e,1)}),[e]),{...e,dismiss:e=>o({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:m}}},43840:(e,s,t)=>{"use strict";t.d(s,{cl:()=>a.cl,delegationApiService:()=>a.ac,employeeApiService:()=>a.aV,reliabilityApiService:()=>a.e_,taskApiService:()=>a.Hg,vehicleApiService:()=>a.oL});var a=t(36973);t(72248)},44598:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(92084);function r(e){return(0,a.w)(e,Date.now())}},50594:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},52639:(e,s,t)=>{"use strict";t.d(s,{GW:()=>n});var a=t(95155);t(12115);var r=t(88240),l=t(54036);let i=e=>{let{children:s,className:t="",config:i}=e;return(0,a.jsx)(r.A,{children:(0,a.jsx)("div",{className:(0,l.cn)("min-h-screen bg-background",t),children:(0,a.jsx)("main",{className:"flex-1",children:(0,a.jsx)("div",{className:"container mx-auto space-y-6 px-4 py-6 sm:px-6 lg:px-8",children:s})})})})},n=e=>{let{children:s,className:t="",config:r}=e;return(0,a.jsx)(i,{config:r,className:t,children:(0,a.jsx)("div",{className:"space-y-8",children:s})})}},57082:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58260:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},59154:(e,s,t)=>{Promise.resolve().then(t.bind(t,88049))},69738:(e,s,t)=>{"use strict";t.d(s,{y:()=>r});var a=t(12115);let r=e=>(0,a.useMemo)(()=>{var s,t,a;let r=e.escorts&&e.escorts.length>0&&(null==(s=e.escorts[0])?void 0:s.employee)?e.escorts[0].employee:null,l=e.drivers&&e.drivers.length>0&&(null==(t=e.drivers[0])?void 0:t.employee)?e.drivers[0].employee:null,i=e.vehicles&&e.vehicles.length>0&&(null==(a=e.vehicles[0])?void 0:a.vehicle)?e.vehicles[0].vehicle:null,n=!!(e.arrivalFlight||e.departureFlight),d=!r&&"Completed"!==e.status&&"Cancelled"!==e.status;return{escortInfo:r,driverInfo:l,vehicleInfo:i,hasFlightDetails:n,needsEscortAssignment:d,isActive:"In_Progress"===e.status}},[e])},76202:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var a=t(95155),r=t(54073),l=t(12115),i=t(54036);let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsxs)(r.bL,{className:(0,i.cn)("relative flex w-full touch-none select-none items-center",t),ref:s,...l,children:[(0,a.jsx)(r.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,a.jsx)(r.Q6,{className:"absolute h-full bg-primary"})}),(0,a.jsx)(r.zi,{className:"block size-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});n.displayName=r.bL.displayName},77931:(e,s,t)=>{"use strict";t.d(s,{BZ:()=>a.BZ,K:()=>a.K,Wy:()=>a.Wy,YB:()=>a.YB,ZI:()=>a.ZI,bQ:()=>r.b,nh:()=>a.nh,vk:()=>a.vk,yX:()=>a.yX});var a=t(83506),r=t(84411);t(85127)},80333:(e,s,t)=>{"use strict";t.d(s,{d:()=>n});var a=t(95155),r=t(4884),l=t(12115),i=t(54036);let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.bL,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...l,ref:s,children:(0,a.jsx)(r.zi,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});n.displayName=r.bL.displayName},80937:(e,s,t)=>{"use strict";t.d(s,{NS:()=>h,T$:()=>o,W_:()=>m,Y1:()=>u,lR:()=>x});var a=t(26715),r=t(5041),l=t(90111),i=t(42366),n=t(99605),d=t(43840);let c={all:["vehicles"],detail:e=>["vehicles",e]},o=e=>(0,l.GK)([...c.all],async()=>(await d.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),m=(e,s)=>{var t;return(0,l.GK)([...c.detail(e)],()=>d.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(null==(t=null==s?void 0:s.enabled)||t),staleTime:3e5,...s})},u=()=>{let e=(0,a.jE)(),{showError:s,showSuccess:t}=(0,i.useNotifications)();return(0,r.n)({mutationFn:e=>{let s=n.M.toCreateRequest(e);return d.vehicleApiService.create(s)},onError:e=>{s("Failed to create vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:s=>{e.invalidateQueries({queryKey:c.all}),t('Vehicle "'.concat(s.licensePlate,'" has been created successfully!'))}})},x=()=>{let e=(0,a.jE)(),{showError:s,showSuccess:t}=(0,i.useNotifications)();return(0,r.n)({mutationFn:e=>{let{data:s,id:t}=e,a=n.M.toUpdateRequest(s);return d.vehicleApiService.update(t,a)},onError:e=>{s("Failed to update vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:s=>{e.invalidateQueries({queryKey:c.all}),e.invalidateQueries({queryKey:c.detail(s.id)}),t('Vehicle "'.concat(s.licensePlate,'" has been updated successfully!'))}})},h=()=>{let e=(0,a.jE)(),{showError:s,showSuccess:t}=(0,i.useNotifications)();return(0,r.n)({mutationFn:e=>d.vehicleApiService.delete(e),onError:e=>{s("Failed to delete vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:(s,a)=>{e.invalidateQueries({queryKey:c.all}),e.removeQueries({queryKey:c.detail(a)}),t("Vehicle has been deleted successfully!")}})}},85511:(e,s,t)=>{"use strict";t.d(s,{V:()=>c});var a=t(95155),r=t(965),l=t(73158);t(12115);var i=t(33683),n=t(30285),d=t(54036);function c(e){let{className:s,classNames:t,showOutsideDays:c=!0,...o}=e;return(0,a.jsx)(i.hv,{className:(0,d.cn)("p-3",s),classNames:{caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,d.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_range_end:"day-range-end",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",head_row:"flex",month:"space-y-4",months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",nav:"space-x-1 flex items-center",nav_button:(0,d.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_next:"absolute right-1",nav_button_previous:"absolute left-1",row:"flex w-full mt-2",table:"w-full border-collapse space-y-1",...t},components:{IconLeft:e=>{let{className:s,...t}=e;return(0,a.jsx)(r.A,{className:(0,d.cn)("h-4 w-4",s),...t})},IconRight:e=>{let{className:s,...t}=e;return(0,a.jsx)(l.A,{className:(0,d.cn)("h-4 w-4",s),...t})}},showOutsideDays:c,...o})}c.displayName="Calendar"},88049:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eA});var a=t(95155),r=t(57082),l=t(34301),i=t(18271),n=t(6874),d=t.n(n),c=t(12115),o=t(88240),m=t(37648),u=t(50286),x=t(8376);t(52639);var h=t(97697),g=t(17841);r.A,m.A,u.A,x.A;var v=t(38342);let p={entityType:"delegation",title:"Delegation Dashboard",description:"Track and manage all your events, trips, and delegate information.",viewModes:["cards","table","list","calendar"],defaultViewMode:"cards",enableBulkActions:!0,enableExport:!0,refreshInterval:3e4},f=e=>{let{className:s=""}=e,{layout:t,monitoring:r,setViewMode:l,setGridColumns:i,toggleCompactMode:n,setMonitoringEnabled:d,setRefreshInterval:c,toggleAutoRefresh:o,resetSettings:m}=(0,h.fX)("delegation")();return(0,a.jsx)(v.s,{config:p,entityType:"delegation",layout:t,monitoring:r,setViewMode:l,setGridColumns:i,toggleCompactMode:n,setMonitoringEnabled:d,setRefreshInterval:c,toggleAutoRefresh:o,resetSettings:m,className:s})};var j=t(27300),y=t(25318),b=t(98328),N=t(83662),w=t(28328),k=t(75074),A=t(9572),C=t(30285),S=t(62523),D=t(85057),z=t(26126),M=t(47262),R=t(85511),T=t(22346),E=t(14636),F=t(38382),I=t(54036),_=t(41784);let L=[{value:"Planned",label:"Planned",icon:j.A,color:"border-blue-200 text-blue-700"},{value:"Confirmed",label:"Confirmed",icon:x.A,color:"border-green-200 text-green-700"},{value:"In_Progress",label:"In Progress",icon:j.A,color:"border-yellow-200 text-yellow-700"},{value:"Completed",label:"Completed",icon:x.A,color:"border-emerald-200 text-emerald-700"},{value:"Cancelled",label:"Cancelled",icon:y.A,color:"border-red-200 text-red-700"},{value:"No_details",label:"No Details",icon:j.A,color:"border-gray-200 text-gray-700"}],V=e=>{let{onFiltersChange:s,className:t,initialFilters:r={},employeesList:l=[],vehiclesList:i=[],locationsList:n=[]}=e,[d,o]=(0,c.useState)(!1),[m,h]=(0,c.useState)({search:"",status:[],dateRange:{},location:[],drivers:[],escorts:[],vehicles:[],...r}),g=e=>{let t={...m,...e};h(t),null==s||s(t)},v=()=>{let e={search:"",status:[],dateRange:{},location:[],drivers:[],escorts:[],vehicles:[]};h(e),null==s||s(e)},p=e=>{g({status:m.status.includes(e)?m.status.filter(s=>s!==e):[...m.status,e]})},f=e=>{g({location:m.location.includes(e)?m.location.filter(s=>s!==e):[...m.location,e]})},j=e=>{g({drivers:m.drivers.includes(e)?m.drivers.filter(s=>s!==e):[...m.drivers,e]})},V=e=>{g({vehicles:m.vehicles.includes(e)?m.vehicles.filter(s=>s!==e):[...m.vehicles,e]})},B=e=>{var s,t;g({dateRange:{from:null!=(s=null==e?void 0:e.from)?s:void 0,to:null!=(t=null==e?void 0:e.to)?t:void 0}})},P=+!!m.search+m.status.length+m.location.length+m.drivers.length+m.escorts.length+m.vehicles.length+(m.dateRange.from||m.dateRange.to?1:0);return(0,a.jsxs)("div",{className:(0,I.cn)("flex flex-col gap-4",t),children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(k.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(S.p,{placeholder:"Search delegations (Event, Location, Delegate, Status...)",value:m.search,onChange:e=>g({search:e.target.value}),className:"pl-10"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,a.jsxs)("div",{className:"hidden md:flex items-center gap-2",children:[(0,a.jsx)(()=>(0,a.jsxs)(E.AM,{children:[(0,a.jsx)(E.Wv,{asChild:!0,children:(0,a.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(x.A,{className:"size-4"}),"Status",m.status.length>0&&(0,a.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:m.status.length})]})}),(0,a.jsx)(E.hl,{className:"w-56 p-3",align:"start",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Delegation Status"}),(0,a.jsx)(C.$,{variant:"ghost",size:"sm",onClick:()=>g({status:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)(T.w,{}),(0,a.jsx)("div",{className:"space-y-2",children:L.map(e=>{let s=e.icon;return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(M.S,{id:"status-".concat(e.value),checked:m.status.includes(e.value),onCheckedChange:()=>p(e.value)}),(0,a.jsxs)(D.J,{htmlFor:"status-".concat(e.value),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,a.jsx)(s,{className:"size-3"}),(0,a.jsx)(z.E,{variant:"outline",className:(0,I.cn)("text-xs border",e.color),children:e.label})]})]},e.value)})})]})})]}),{}),(0,a.jsx)(()=>(0,a.jsxs)(E.AM,{children:[(0,a.jsx)(E.Wv,{asChild:!0,children:(0,a.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(b.A,{className:"size-4"}),"Date Range",(m.dateRange.from||m.dateRange.to)&&(0,a.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:"1"})]})}),(0,a.jsx)(E.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Delegation Date Range"}),(0,a.jsx)(C.$,{variant:"ghost",size:"sm",onClick:()=>g({dateRange:{}}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)(R.V,{mode:"range",selected:{from:m.dateRange.from,to:m.dateRange.to},onSelect:B,numberOfMonths:2,className:"rounded-md border-0"}),(0,a.jsx)("div",{className:"mt-3 text-xs text-muted-foreground text-center",children:m.dateRange.from&&!m.dateRange.to?"Select end date to complete range":"Click start date, then end date"})]})})]}),{}),n.length>0&&(0,a.jsx)(()=>(0,a.jsxs)(E.AM,{children:[(0,a.jsx)(E.Wv,{asChild:!0,children:(0,a.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(N.A,{className:"size-4"}),"Location",m.location.length>0&&(0,a.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:m.location.length})]})}),(0,a.jsx)(E.hl,{className:"w-64 p-3",align:"start",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Delegation Location"}),(0,a.jsx)(C.$,{variant:"ghost",size:"sm",onClick:()=>g({location:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)(T.w,{}),(0,a.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:n.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(M.S,{id:"location-".concat(e),checked:m.location.includes(e),onCheckedChange:()=>f(e)}),(0,a.jsxs)(D.J,{htmlFor:"location-".concat(e),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,a.jsx)(N.A,{className:"size-3"}),(0,a.jsx)("span",{children:e})]})]},e))})]})})]}),{}),l.some(e=>"driver"===e.role)&&(0,a.jsx)(()=>(0,a.jsxs)(E.AM,{children:[(0,a.jsx)(E.Wv,{asChild:!0,children:(0,a.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(u.A,{className:"size-4"}),"Drivers",m.drivers.length>0&&(0,a.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:m.drivers.length})]})}),(0,a.jsx)(E.hl,{className:"w-64 p-3",align:"start",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Assigned Drivers"}),(0,a.jsx)(C.$,{variant:"ghost",size:"sm",onClick:()=>g({drivers:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)(T.w,{}),(0,a.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:l.filter(e=>"driver"===e.role).map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(M.S,{id:"driver-".concat(e.id),checked:m.drivers.includes(e.id),onCheckedChange:()=>j(e.id)}),(0,a.jsxs)(D.J,{htmlFor:"driver-".concat(e.id),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,a.jsx)(u.A,{className:"size-3"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground capitalize",children:e.role})]})]})]},e.id))})]})})]}),{}),i.length>0&&(0,a.jsx)(()=>(0,a.jsxs)(E.AM,{children:[(0,a.jsx)(E.Wv,{asChild:!0,children:(0,a.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(w.A,{className:"size-4"}),"Vehicles",m.vehicles.length>0&&(0,a.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:m.vehicles.length})]})}),(0,a.jsx)(E.hl,{className:"w-64 p-3",align:"start",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Assigned Vehicles"}),(0,a.jsx)(C.$,{variant:"ghost",size:"sm",onClick:()=>g({vehicles:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)(T.w,{}),(0,a.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:i.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(M.S,{id:"vehicle-".concat(e.id),checked:m.vehicles.includes(e.id),onCheckedChange:()=>V(e.id)}),(0,a.jsxs)(D.J,{htmlFor:"vehicle-".concat(e.id),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,a.jsx)(w.A,{className:"size-3"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.type})]})]})]},e.id))})]})})]}),{})]}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsxs)(F.cj,{open:d,onOpenChange:o,children:[(0,a.jsx)(F.CG,{asChild:!0,children:(0,a.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(A.A,{className:"size-4"}),"Filters",P>0&&(0,a.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:P})]})}),(0,a.jsxs)(F.h,{side:"bottom",className:"max-h-[80vh] overflow-y-auto",children:[(0,a.jsxs)(F.Fm,{children:[(0,a.jsx)(F.qp,{children:"Filter Delegations"}),(0,a.jsx)(F.Qs,{children:"Apply filters to find specific delegations"})]}),(0,a.jsxs)("div",{className:"mt-6 space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(D.J,{className:"text-sm font-medium",children:"Status"}),(0,a.jsx)("div",{className:"grid gap-2",children:L.map(e=>{let s=e.icon;return(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,a.jsx)(M.S,{id:"mobile-status-".concat(e.value),checked:m.status.includes(e.value),onCheckedChange:()=>p(e.value)}),(0,a.jsxs)(D.J,{htmlFor:"mobile-status-".concat(e.value),className:"cursor-pointer text-sm flex-1 flex items-center gap-2",children:[(0,a.jsx)(s,{className:"size-3"}),e.label]})]},e.value)})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(D.J,{className:"text-sm font-medium",children:"Date Range"}),(0,a.jsx)("div",{className:"border rounded-md p-3",children:(0,a.jsx)(R.V,{mode:"range",selected:{from:m.dateRange.from,to:m.dateRange.to},onSelect:B,numberOfMonths:1,className:"rounded-md border-0"})})]}),P>0&&(0,a.jsxs)(C.$,{variant:"outline",onClick:v,className:"w-full gap-2",children:[(0,a.jsx)(y.A,{className:"size-4"}),"Clear All Filters (",P,")"]})]})]})]})}),P>0&&(0,a.jsxs)(C.$,{variant:"ghost",size:"sm",onClick:v,className:"gap-1 text-muted-foreground hover:text-foreground hidden md:flex",children:[(0,a.jsx)(y.A,{className:"size-3"}),"Clear (",P,")"]})]}),P>0&&(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Active filters:"}),m.status.map(e=>{let s=L.find(s=>s.value===e);return s?(0,a.jsxs)(z.E,{variant:"secondary",className:"gap-1",children:[s.label,(0,a.jsx)(C.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>p(e),children:(0,a.jsx)(y.A,{className:"size-3"})})]},e):null}),m.location.map(e=>(0,a.jsxs)(z.E,{variant:"secondary",className:"gap-1",children:["Location: ",e,(0,a.jsx)(C.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>f(e),children:(0,a.jsx)(y.A,{className:"size-3"})})]},e)),m.drivers.map(e=>{let s=l.find(s=>s.id===e);return(0,a.jsxs)(z.E,{variant:"secondary",className:"gap-1",children:["Driver: ",(null==s?void 0:s.name)||"Unknown",(0,a.jsx)(C.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>j(e),children:(0,a.jsx)(y.A,{className:"size-3"})})]},e)}),m.vehicles.map(e=>{let s=i.find(s=>s.id===e);return(0,a.jsxs)(z.E,{variant:"secondary",className:"gap-1",children:["Vehicle: ",(null==s?void 0:s.name)||"Unknown",(0,a.jsx)(C.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>V(e),children:(0,a.jsx)(y.A,{className:"size-3"})})]},e)}),(m.dateRange.from||m.dateRange.to)&&(0,a.jsxs)(z.E,{variant:"secondary",className:"gap-1",children:["Date:"," ",m.dateRange.from?(0,_.GP)(m.dateRange.from,"MMM d"):"?"," ","-"," ",m.dateRange.to?(0,_.GP)(m.dateRange.to,"MMM d, yyyy"):"?",(0,a.jsx)(C.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>g({dateRange:{}}),children:(0,a.jsx)(y.A,{className:"size-3"})})]})]})]})};var B=t(51920),P=t(44956),$=t(65064),O=t(17652),G=t(77931),J=t(83506),Z=t(40879);let U=e=>{let{className:s="",delegations:t,onBulkArchive:r,onBulkDelete:l,onDelete:i}=e,{toast:n}=(0,Z.dj)(),d=(0,O.c3)("status"),c=(0,O.c3)("tables.headers"),o=(0,O.c3)("tables.actions"),m=(0,O.c3)("common"),x=(0,J.e7)(d),h=e=>{try{return(0,_.GP)(new Date(e),"MMM dd, yyyy")}catch(e){return"Invalid Date"}},g=[(0,G.BZ)(),{accessorKey:"eventName",cell:e=>{let{row:s}=e,t=s.getValue("eventName"),r=s.original.notes;return(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"font-semibold text-foreground",children:t||"-"}),r&&(0,a.jsx)("div",{className:"line-clamp-1 text-xs text-muted-foreground",children:r})]})},header:(0,G.YB)("Event Name")},(0,G.ZI)("status",c("status"),x.delegation),(0,G.nh)("location",c("location"),e=>{let{className:s}=e;return(0,a.jsx)(N.A,{className:s})}),{accessorKey:"durationFrom",cell:e=>{let{row:s}=e,t=s.getValue("durationFrom");return(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[(0,a.jsx)(B.A,{className:"size-3 text-muted-foreground"}),h(t)]})},header:(0,G.YB)("Start Date")},{accessorKey:"durationTo",cell:e=>{let{row:s}=e,t=s.getValue("durationTo");return(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[(0,a.jsx)(B.A,{className:"size-3 text-muted-foreground"}),h(t)]})},header:(0,G.YB)("End Date")},(0,G.nh)("delegates","Delegates",e=>{let{className:s}=e;return(0,a.jsx)(u.A,{className:s})},{formatter:e=>{var s;return null!=(s=null==e?void 0:e.length)?s:0}}),(0,G.Wy)({editHref:e=>"/delegations/".concat(e.id,"/edit"),viewHref:e=>"/delegations/".concat(e.id),...i&&{onDelete:e=>{i(e)}},customActions:[{label:"Duplicate",onClick:e=>{n({description:"Duplicate functionality for ".concat(e.eventName),title:"Feature Coming Soon"})}}],showCopyId:!0},{actions:m("actions"),delete:o("delete"),edit:o("edit"),viewDetails:o("viewDetails")})],v=[{icon:e=>{let{className:s}=e;return(0,a.jsx)(P.A,{className:s})},label:"Delete Selected",onClick:async e=>{l&&(await l(e),n({description:"".concat(e.length," delegations have been deleted"),title:"Delegations Deleted"}))},variant:"destructive"},{icon:e=>{let{className:s}=e;return(0,a.jsx)($.A,{className:s})},label:"Archive Selected",onClick:async e=>{r&&(await r(e),n({description:"".concat(e.length," delegations have been archived"),title:"Delegations Archived"}))}}];return(0,a.jsx)(G.bQ,{bulkActions:v,className:s,columns:g,data:t,emptyMessage:m("noData"),enableBulkActions:!0,enableColumnVisibility:!0,enableRowSelection:!0,headerClassName:"bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900",pageSize:15,rowClassName:"hover:bg-gray-50/50 dark:hover:bg-gray-800/50",searchColumn:"eventName",searchPlaceholder:"Search delegations by event name or location...",tableClassName:"shadow-lg"})};function W(e){let{children:s,searchTerm:t="",filters:r}=e;console.log("DelegationListContainer rendered");let{data:l=[],error:i,isLoading:n,refetch:d}=(0,g.BD)(),o=i?i.message:null,m=(0,c.useMemo)(()=>{console.log("Filtering delegations",{allDelegationsCount:l.length,searchTerm:t,filters:r});let e=[...l],s=(null==r?void 0:r.search)||t;if(s){let t=s.toLowerCase();e=e.filter(e=>{var s;return e.eventName.toLowerCase().includes(t)||e.location.toLowerCase().includes(t)||e.status.toLowerCase().includes(t)||(null==(s=e.delegates)?void 0:s.some(e=>e.name.toLowerCase().includes(t)))})}return(null==r?void 0:r.status)&&r.status.length>0&&(e=e.filter(e=>r.status.includes(e.status))),(null==r?void 0:r.location)&&r.location.length>0&&(e=e.filter(e=>r.location.includes(e.location))),(null==r?void 0:r.dateRange)&&(r.dateRange.from||r.dateRange.to)&&(e=e.filter(e=>{let s=new Date(e.durationFrom),t=new Date(e.durationTo);return r.dateRange.from&&r.dateRange.to?s<=r.dateRange.to&&t>=r.dateRange.from:r.dateRange.from?t>=r.dateRange.from:!r.dateRange.to||s<=r.dateRange.to})),(null==r?void 0:r.drivers)&&r.drivers.length>0&&(e=e.filter(e=>{var s;return null==(s=e.drivers)?void 0:s.some(e=>{var s;return r.drivers.includes((null==(s=e.employee)?void 0:s.id.toString())||"")})})),(null==r?void 0:r.escorts)&&r.escorts.length>0&&(e=e.filter(e=>{var s;return null==(s=e.escorts)?void 0:s.some(e=>{var s;return r.escorts.includes((null==(s=e.employee)?void 0:s.id.toString())||"")})})),(null==r?void 0:r.vehicles)&&r.vehicles.length>0&&(e=e.filter(e=>{var s;return null==(s=e.vehicles)?void 0:s.some(e=>{var s;return r.vehicles.includes((null==(s=e.vehicle)?void 0:s.id.toString())||"")})})),e},[t,l,r]),u=(0,c.useCallback)(async()=>{console.log("Manual fetch triggered via fetchDelegations prop (refetch)"),await d()},[d]);return(0,a.jsx)(a.Fragment,{children:s({delegations:m,error:o,fetchDelegations:u,loading:n})})}var q=t(19164),K=t(74641),Y=t(35476),X=t(44598),H=t(76959),Q=t(965),ee=t(73158),es=t(66695);let et=e=>{switch(e){case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Completed":return"bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20";case"Confirmed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Planned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},ea=e=>{let s=new Date(e);return Number.isNaN(s.getTime())?"N/A":(0,_.GP)(s,"MMM d, yyyy")},er=e=>{let s=new Date(e);return Number.isNaN(s.getTime())?"N/A":(0,_.GP)(s,"HH:mm")};var el=t(99673);let ei=(e,s)=>e.filter(e=>{let t=new Date(e.durationFrom),a=new Date(e.durationTo);return s>=t&&s<=a}),en=e=>{let{className:s="",delegations:t}=e,[r,l]=c.useState(new Date),i=(0,q.w)(r),n=function(e,s){let t=(0,Y.a)(e.start),a=(0,Y.a)(e.end),r=+t>+a,l=r?+t:+a,i=r?a:t;i.setHours(0,0,0,0);let n=1;if(!n)return[];n<0&&(n=-n,r=!r);let d=[];for(;+i<=l;)d.push((0,Y.a)(i)),i.setDate(i.getDate()+n),i.setHours(0,0,0,0);return r?d.reverse():d}({end:(0,K.p)(r),start:i});return(0,a.jsxs)("div",{className:(0,I.cn)("space-y-6",s),children:[(0,a.jsx)(es.Zp,{className:"border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-900",children:(0,a.jsx)(es.aR,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0",children:[(0,a.jsxs)(es.ZB,{className:"flex items-center gap-3 text-2xl font-semibold text-gray-900 dark:text-white",children:[(0,a.jsx)("div",{className:"rounded-full bg-blue-600 p-2",children:(0,a.jsx)(B.A,{className:"size-6 text-white"})}),(0,_.GP)(r,"MMMM yyyy")]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(C.$,{className:"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800",onClick:()=>{l(new Date)},size:"sm",variant:"outline",children:"Today"}),(0,a.jsx)(C.$,{className:"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800",onClick:()=>{l(new Date(r.getFullYear(),r.getMonth()-1,1))},size:"sm",variant:"outline",children:(0,a.jsx)(Q.A,{className:"size-4"})}),(0,a.jsx)(C.$,{className:"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800",onClick:()=>{l(new Date(r.getFullYear(),r.getMonth()+1,1))},size:"sm",variant:"outline",children:(0,a.jsx)(ee.A,{className:"size-4"})})]})]})})}),(0,a.jsx)(es.Zp,{className:"border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-900",children:(0,a.jsxs)(es.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-7 gap-1",children:[["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>(0,a.jsx)("div",{className:"border border-gray-200 bg-gray-50 p-3 text-center text-sm font-semibold text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:e},e)),n.map(e=>{let s=ei(t,e),r=function(e){return(0,H.r)(e,(0,X.A)(e))}(e);return(0,a.jsxs)("div",{className:(0,I.cn)("min-h-[120px] border border-gray-200 dark:border-gray-700 p-2 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200",r&&"bg-blue-50 dark:bg-blue-950/30 border-blue-300 dark:border-blue-700"),children:[(0,a.jsx)("div",{className:(0,I.cn)("text-sm font-medium mb-2 flex items-center justify-center w-6 h-6 rounded-full",r?"bg-blue-600 text-white shadow-sm":"text-gray-700 dark:text-gray-300"),children:(0,_.GP)(e,"d")}),(0,a.jsxs)("div",{className:"space-y-1",children:[s.slice(0,2).map(e=>(0,a.jsx)(d(),{className:"block",href:"/delegations/".concat(e.id),children:(0,a.jsxs)("div",{className:(0,I.cn)("text-xs p-2 rounded border cursor-pointer hover:shadow-sm transition-all duration-200",et(e.status)),title:"".concat(e.eventName," - ").concat((0,el.fZ)(e.status)),children:[(0,a.jsx)("div",{className:"truncate font-medium",children:e.eventName}),(0,a.jsx)("div",{className:"mt-0.5 text-xs opacity-75",children:er(e.durationFrom)})]})},e.id)),s.length>2&&(0,a.jsxs)("div",{className:"rounded border border-gray-200 bg-gray-100 p-1 text-center text-xs text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400",children:["+",s.length-2," more"]})]})]},e.toISOString())})]}),(0,a.jsxs)("div",{className:"mt-8 border-t border-gray-200 pt-6 dark:border-gray-700",children:[(0,a.jsx)("div",{className:"mb-4 text-sm font-semibold text-gray-900 dark:text-white",children:"Status Legend"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-3",children:[{label:"Planned",status:"Planned"},{label:"Confirmed",status:"Confirmed"},{label:"In Progress",status:"In_Progress"},{label:"Completed",status:"Completed"},{label:"Cancelled",status:"Cancelled"}].map(e=>{let{label:s,status:t}=e;return(0,a.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 dark:border-gray-700 dark:bg-gray-800",children:[(0,a.jsx)("div",{className:(0,I.cn)("w-3 h-3 rounded-full",et(t))}),(0,a.jsx)("span",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:s})]},t)})})]}),(0,a.jsx)("div",{className:"mt-6 border-t border-gray-200 pt-4 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-sm text-gray-600 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:t.length})," ","delegation",1===t.length?"":"s"," this month"]})})]})})]})};var ed=t(58260),ec=t(91721),eo=t(31949),em=t(50594),eu=t(17607),ex=t(6560);let eh=e=>{let{status:s,className:t,size:r="md",floating:l=!1}=e,i=(0,I.cn)("font-medium border shadow-sm transition-all duration-200",{sm:"text-xs py-1 px-2",md:"text-xs py-1.5 px-3",lg:"text-sm py-2 px-4"}[r],l&&"absolute top-4 right-4",et(s));return(0,a.jsx)(z.E,{className:(0,I.cn)(i,t),children:(0,el.fZ)(s)})};var eg=t(69738);function ev(e){var s,t;let{className:r,delegation:l}=e,{driverInfo:i,escortInfo:n,hasFlightDetails:c,isActive:o,needsEscortAssignment:m,vehicleInfo:x}=(0,eg.y)(l);return(0,a.jsxs)(es.Zp,{className:(0,I.cn)("flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md","transition-all duration-200 ease-in-out","hover:shadow-lg hover:border-primary/30","group",r),children:[(0,a.jsx)("div",{className:(0,I.cn)("h-1 w-full transition-all duration-200",o?"bg-gradient-to-r from-primary to-accent":"Completed"===l.status?"bg-green-500":"Cancelled"===l.status?"bg-destructive":"bg-muted")}),(0,a.jsxs)(es.aR,{className:"p-5 pb-3",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsx)(es.ZB,{className:"mb-1 line-clamp-2 text-xl font-semibold text-primary",children:l.eventName}),(0,a.jsxs)(es.BT,{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)(N.A,{className:"size-4 shrink-0 text-muted-foreground"}),(0,a.jsx)("span",{className:"truncate",children:l.location})]})]}),(0,a.jsx)(eh,{className:"shrink-0",size:"sm",status:l.status})]}),o&&(0,a.jsxs)("div",{className:"mt-3 flex w-fit items-center gap-2 rounded-full border border-primary/20 bg-primary/10 px-3 py-1.5",children:[(0,a.jsx)("div",{className:"size-2 animate-pulse rounded-full bg-primary"}),(0,a.jsx)("span",{className:"text-xs font-medium text-primary",children:"Currently Active"})]})]}),(0,a.jsxs)(es.Wu,{className:"flex-1 p-5 pt-0",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"size-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Duration"})]}),(0,a.jsxs)("p",{className:"text-sm font-medium",children:[ea(l.durationFrom)," -"," ",ea(l.durationTo)]})]}),(0,a.jsx)(T.w,{className:"my-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"size-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Delegates"})]}),(0,a.jsx)(z.E,{className:"text-xs",variant:"secondary",children:null!=(t=null==(s=l.delegates)?void 0:s.length)?t:0})]}),c&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ed.A,{className:"size-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Flight Details"})]}),(0,a.jsx)(z.E,{className:"border-blue-200 text-xs text-blue-600",variant:"outline",children:"Available"})]}),(n||i||x)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T.w,{className:"my-3"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("span",{className:"text-xs font-medium uppercase tracking-wide text-muted-foreground",children:"Assignments"}),(0,a.jsxs)("div",{className:"space-y-2",children:[n&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ec.A,{className:"size-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm",children:"Escort"})]}),(0,a.jsx)("span",{className:"max-w-32 truncate text-sm font-medium",children:(0,el.DV)(n)})]}),i&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ec.A,{className:"size-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm",children:"Driver"})]}),(0,a.jsx)("span",{className:"max-w-32 truncate text-sm font-medium",children:(0,el.DV)(i)})]}),x&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"size-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm",children:"Vehicle"})]}),(0,a.jsxs)("span",{className:"max-w-32 truncate text-sm font-medium",children:[x.make," ",x.model]})]})]})]})]}),m&&(0,a.jsxs)("div",{className:"mt-3 flex items-center gap-2 rounded-lg border border-destructive/20 bg-destructive/10 p-3",children:[(0,a.jsx)(eo.A,{className:"size-4 shrink-0 text-destructive"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-destructive",children:"Escort Required"}),(0,a.jsx)("p",{className:"text-xs text-destructive/80",children:"No escort assigned"})]})]})]}),l.notes&&(0,a.jsx)("div",{className:"mt-4 rounded-lg bg-muted/30 p-3",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(em.A,{className:"mt-0.5 size-4 shrink-0 text-muted-foreground"}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsx)("p",{className:"mb-1 text-xs font-medium text-muted-foreground",children:"Notes"}),(0,a.jsx)("p",{className:"line-clamp-2 text-sm text-muted-foreground",children:l.notes})]})]})})]}),(0,a.jsx)(es.wL,{className:"border-t bg-muted/20 p-4",children:(0,a.jsx)(ex.r,{actionType:"primary",asChild:!0,className:"w-full",icon:(0,a.jsx)(eu.A,{className:"size-4"}),children:(0,a.jsx)(d(),{href:"./".concat(l.id),children:"View Details"})})})]})}let ep=e=>{let{className:s="",compactMode:t,delegations:r,gridColumns:l,viewMode:i}=e;switch(i){case"calendar":return(0,a.jsx)(en,{className:s,delegations:r});case"list":return(0,a.jsx)("div",{className:(0,I.cn)("flex flex-col",t?"gap-2":"gap-4",s),children:r.map(e=>(0,a.jsx)(ev,{delegation:e},e.id))});case"table":return(0,a.jsx)(U,{className:s,delegations:r});default:return(0,a.jsx)("div",{className:(0,I.cn)("grid grid-cols-1 gap-6","md:grid-cols-2 lg:grid-cols-".concat(l),t&&"gap-3",s),children:r.map(e=>(0,a.jsx)(ev,{delegation:e},e.id))})}};var ef=t(24865),ej=t(89440),ey=t(54165),eb=t(77023),eN=t(95647),ew=t(83761),ek=t(80937);function eA(){let[e,s]=(0,c.useState)(""),[t,n]=(0,c.useState)({dateRange:{},drivers:[],escorts:[],location:[],search:"",status:[],vehicles:[]}),{layout:m}=(0,h.fX)("delegation")(),{data:u=[]}=(0,g.BD)(),{data:x=[]}=(0,ew.nR)(),{data:v=[]}=(0,ek.T$)(),p=[...new Set(u.map(e=>e.location).filter(Boolean))],j=x.map(e=>({id:e.id.toString(),name:e.name,role:e.role})),y=v.map(e=>({id:e.id.toString(),name:"".concat(e.make," ").concat(e.model),type:"".concat(e.year," ").concat(e.licensePlate)}));return(0,a.jsx)(o.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(ej.AppBreadcrumb,{homeHref:"/",homeLabel:"Dashboard"}),(0,a.jsx)(eN.z,{description:"Track and manage all your events, trips, and delegate information.",icon:r.A,title:"Manage Delegations",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ex.r,{actionType:"primary",asChild:!0,icon:(0,a.jsx)(l.A,{className:"mr-2 size-4"}),children:(0,a.jsx)(d(),{href:"/delegations/add",children:"Add New Delegation"})}),(0,a.jsx)(ef.M,{getReportUrl:()=>{let s=new URLSearchParams({searchTerm:t.search||e}).toString();return"/delegations/report/list?".concat(s)},isList:!0}),(0,a.jsxs)(ey.lG,{children:[(0,a.jsx)(ey.zM,{asChild:!0,children:(0,a.jsx)(ex.r,{actionType:"secondary",icon:(0,a.jsx)(i.A,{className:"size-4"}),children:"Settings"})}),(0,a.jsxs)(ey.Cf,{className:"sm:max-w-[600px]",children:[(0,a.jsx)(ey.L3,{children:"Dashboard Settings"}),(0,a.jsx)(ey.rr,{children:"Customize the display and behavior of your delegation dashboard."}),(0,a.jsx)(f,{})]})]})]})}),(0,a.jsx)("div",{className:"mb-6 rounded-lg bg-card p-4 shadow-md",children:(0,a.jsx)(V,{employeesList:j,initialFilters:{dateRange:{},drivers:[],escorts:[],location:[],search:e,status:[],vehicles:[]},locationsList:p,onFiltersChange:e=>{n(e),s(e.search)},vehiclesList:y})}),(0,a.jsx)(W,{filters:t,searchTerm:e,children:s=>{let{delegations:t,error:i,fetchDelegations:n,loading:c}=s;return(0,a.jsx)(eb.gO,{data:t,emptyComponent:(0,a.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,a.jsx)(r.A,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,a.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:e?"No Delegations Match Your Search":"No Delegations Yet!"}),(0,a.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:e?"Try adjusting your search terms or add a new delegation.":"It looks like you haven't added any delegations yet. Get started by adding one."}),!e&&(0,a.jsx)(ex.r,{actionType:"primary",asChild:!0,icon:(0,a.jsx)(l.A,{className:"size-4"}),size:"lg",children:(0,a.jsx)(d(),{href:"./add",children:"Add Your First Delegation"})})]}),error:i,isLoading:c,loadingComponent:(0,a.jsx)(eb.jt,{count:3,variant:"card"}),onRetry:n,children:e=>(0,a.jsx)(ep,{compactMode:m.compactMode,delegations:e,gridColumns:m.gridColumns,viewMode:m.viewMode})})}})]})})}},88240:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var a=t(95155),r=t(31949),l=t(67554),i=t(12115),n=t(55365),d=t(30285);class c extends i.Component{static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,s){this.setState({errorInfo:s}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",s.componentStack),this.props.onError&&this.props.onError(e,s)}render(){let{description:e="An unexpected error occurred.",resetLabel:s="Try Again",title:t="Something went wrong"}=this.props;if(this.state.hasError){var i;return this.props.fallback?this.props.fallback:(0,a.jsxs)(n.Fc,{className:"my-4",variant:"destructive",children:[(0,a.jsx)(r.A,{className:"mr-2 size-4"}),(0,a.jsx)(n.XL,{className:"text-lg font-semibold",children:t}),(0,a.jsxs)(n.TN,{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-2",children:(null==(i=this.state.error)?void 0:i.message)||e}),!1,(0,a.jsxs)(d.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,a.jsx)(l.A,{className:"mr-2 size-4"}),s]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}}let o=c},97697:(e,s,t)=>{"use strict";t.d(s,{fX:()=>n});var a=t(12115),r=t(65453),l=t(46786);let i=new Map;function n(e){return(0,a.useMemo)(()=>(i.has(e)||i.set(e,(0,r.v)()((0,l.lt)((0,l.Zr)((e,s)=>({activeTab:"all",layout:{viewMode:"cards",gridColumns:3,compactMode:!1,showFilters:!0,showSettings:!1},monitoring:{enabled:!1,refreshInterval:3e4,autoRefresh:!0,pausedDataTypes:new Set},filters:{},sortBy:"createdAt",sortDirection:"desc",selectedItems:new Set,searchTerm:"",setActiveTab:s=>e({activeTab:s},!1,"setActiveTab"),setViewMode:s=>e(e=>({layout:{...e.layout,viewMode:s}}),!1,"setViewMode"),setGridColumns:s=>e(e=>({layout:{...e.layout,gridColumns:s}}),!1,"setGridColumns"),toggleCompactMode:()=>e(e=>({layout:{...e.layout,compactMode:!e.layout.compactMode}}),!1,"toggleCompactMode"),toggleFilters:()=>e(e=>({layout:{...e.layout,showFilters:!e.layout.showFilters}}),!1,"toggleFilters"),toggleSettings:()=>e(e=>({layout:{...e.layout,showSettings:!e.layout.showSettings}}),!1,"toggleSettings"),updateFilter:(s,t)=>e(e=>({filters:{...e.filters,[s]:t}}),!1,"updateFilter"),clearFilters:()=>e({filters:{}},!1,"clearFilters"),setSorting:(s,t)=>e({sortBy:s,sortDirection:t},!1,"setSorting"),setSearchTerm:s=>e({searchTerm:s},!1,"setSearchTerm"),toggleItemSelection:s=>e(e=>{let t=new Set(e.selectedItems);return t.has(s)?t.delete(s):t.add(s),{selectedItems:t}},!1,"toggleItemSelection"),clearSelection:()=>e({selectedItems:new Set},!1,"clearSelection"),selectAll:s=>e({selectedItems:new Set(s)},!1,"selectAll"),setMonitoringEnabled:s=>e(e=>({monitoring:{...e.monitoring,enabled:s}}),!1,"setMonitoringEnabled"),setRefreshInterval:s=>e(e=>({monitoring:{...e.monitoring,refreshInterval:s}}),!1,"setRefreshInterval"),toggleAutoRefresh:()=>e(e=>({monitoring:{...e.monitoring,autoRefresh:!e.monitoring.autoRefresh}}),!1,"toggleAutoRefresh"),pauseDataType:s=>e(e=>({monitoring:{...e.monitoring,pausedDataTypes:new Set([...e.monitoring.pausedDataTypes,s])}}),!1,"pauseDataType"),resumeDataType:s=>e(e=>{let t=new Set(e.monitoring.pausedDataTypes);return t.delete(s),{monitoring:{...e.monitoring,pausedDataTypes:t}}},!1,"resumeDataType"),resetSettings:()=>e({layout:{viewMode:"cards",gridColumns:3,compactMode:!1,showFilters:!0,showSettings:!1},monitoring:{enabled:!1,refreshInterval:3e4,autoRefresh:!0,pausedDataTypes:new Set},filters:{},sortBy:"createdAt",sortDirection:"desc",selectedItems:new Set,searchTerm:""},!1,"resetSettings"),getFilteredData:(e,t)=>{let a=s(),r=[...e];if(a.searchTerm){let e=a.searchTerm.toLowerCase();r=r.filter(s=>Object.values(s).some(s=>String(s).toLowerCase().includes(e)))}return Object.entries(a.filters).forEach(e=>{let[s,a]=e;null!=a&&""!==a&&(r=r.filter(e=>{var r;let l=null==(r=t.filters)?void 0:r.find(e=>e.id===s);if(!l)return!0;switch(l.type){case"select":return e[s]===a;case"multiselect":return!Array.isArray(a)||a.includes(e[s]);case"toggle":return!a||e[s];default:return!0}}))}),r.sort((e,s)=>{let t=e[a.sortBy],r=s[a.sortBy],l="asc"===a.sortDirection?1:-1;return t<r?-1*l:t>r?+l:0}),r},getSelectedCount:()=>s().selectedItems.size,hasActiveFilters:()=>{let e=s();return e.searchTerm.length>0||Object.values(e.filters).some(e=>null!=e&&""!==e)}}),{name:"workhub-dashboard-".concat(e),partialize:e=>({layout:e.layout,monitoring:e.monitoring,filters:e.filters,sortBy:e.sortBy,sortDirection:e.sortDirection})}),{name:"dashboard-".concat(e)}))),i.get(e)),[e])}}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,7047,6897,3860,9664,375,7876,6874,1859,5247,6453,6463,7454,3030,6233,3122,4036,4767,303,7515,8128,7841,4411,9258,8441,1684,7358],()=>s(59154)),_N_E=e.O()}]);