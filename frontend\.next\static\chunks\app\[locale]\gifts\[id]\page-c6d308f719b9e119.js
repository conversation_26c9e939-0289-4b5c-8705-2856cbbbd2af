(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7989],{8715:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(95155),a=s(2160),i=s(18763),n=s(77223),l=s(17652),d=s(6874),c=s.n(d),o=s(35695);s(12115);var m=s(6560),u=s(89440),x=s(26126),f=s(66695),p=s(54165),h=s(77023),g=s(95647),j=s(22346),b=s(45119),N=s(47980);function y(){let e=(0,o.useParams)(),t=(0,o.useRouter)(),s=(0,l.c3)("gifts"),d=(0,l.c3)("common"),y=(0,l.c3)("navigation"),v=(0,l.c3)("forms"),B=e.id,{data:w,error:k,isLoading:A}=(0,b.oe)(B),{data:C}=(0,N.$Q)((null==w?void 0:w.recipientId)||null),{isPending:E,mutateAsync:R}=(0,b.HJ)(),D=async()=>{try{await R(B),t.push("/gifts")}catch(e){console.error("Error deleting gift:",e)}},F=e=>new Date(e).toLocaleDateString();return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(u.AppBreadcrumb,{homeHref:"/",homeLabel:y("dashboard")}),(0,r.jsx)(h.gO,{data:w,emptyComponent:(0,r.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,r.jsx)(a.A,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,r.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:s("giftNotFound")}),(0,r.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:s("giftNotFoundDescription")}),(0,r.jsx)(m.r,{asChild:!0,children:(0,r.jsx)(c(),{href:"../",children:s("backToGifts")})})]}),error:(null==k?void 0:k.message)||null,isLoading:A,loadingComponent:(0,r.jsx)(h.jt,{count:1,variant:"card"}),children:e=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.z,{description:s("giftDetailsDescription"),icon:a.A,title:e.itemDescription,children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.r,{actionType:"secondary",asChild:!0,icon:(0,r.jsx)(i.A,{className:"size-4"}),children:(0,r.jsx)(c(),{href:"/gifts/".concat(B,"/edit"),children:v("buttons.edit")})}),(0,r.jsxs)(p.lG,{children:[(0,r.jsx)(p.zM,{asChild:!0,children:(0,r.jsx)(m.r,{actionType:"danger",icon:(0,r.jsx)(n.A,{className:"size-4"}),children:v("buttons.delete")})}),(0,r.jsxs)(p.Cf,{children:[(0,r.jsxs)(p.c7,{children:[(0,r.jsx)(p.L3,{children:s("deleteGiftTitle")}),(0,r.jsx)(p.rr,{children:s("deleteGiftConfirmation",{item:e.itemDescription})})]}),(0,r.jsxs)(p.Es,{children:[(0,r.jsx)(m.r,{actionType:"secondary",children:d("cancel")}),(0,r.jsx)(m.r,{actionType:"danger",isLoading:E,onClick:D,children:v("buttons.delete")})]})]})]})]})}),(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:s("giftInformation")})}),(0,r.jsxs)(f.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:v("labels.itemDescription")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:e.itemDescription})]}),(0,r.jsx)(j.w,{}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:v("labels.dateSent")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:F(e.dateSent)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:v("labels.senderName")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:e.senderName})]}),e.occasion&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:v("labels.occasion")}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(x.E,{variant:"secondary",children:e.occasion})})]}),e.notes&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:v("labels.notes")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:e.notes})]})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:s("recipientInformation")})}),(0,r.jsx)(f.Wu,{className:"space-y-4",children:C?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:v("labels.name")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:(0,r.jsx)(c(),{className:"text-primary hover:underline",href:"/recipients/".concat(C.id),children:C.name})})]}),C.email&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:v("labels.email")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:C.email})]}),C.phone&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:v("labels.phone")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:C.phone})]}),C.address&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:v("labels.address")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:C.address})]})]}):(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:s("recipientNotFound")})})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:d("metadata")})}),(0,r.jsxs)(f.Wu,{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:d("createdAt")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:F(e.createdAt)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:d("updatedAt")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:F(e.updatedAt)})]})]})]})]})})]})}},19018:(e,t,s)=>{"use strict";s.d(t,{Breadcrumb:()=>d,BreadcrumbItem:()=>o,BreadcrumbLink:()=>m,BreadcrumbList:()=>c,BreadcrumbPage:()=>u,BreadcrumbSeparator:()=>x});var r=s(95155),a=s(99708),i=s(73158),n=(s(3561),s(12115)),l=s(54036);let d=n.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("nav",{"aria-label":"breadcrumb",className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",s),ref:t,...a})});d.displayName="Breadcrumb";let c=n.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("ol",{className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",s),ref:t,...a})});c.displayName="BreadcrumbList";let o=n.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("li",{className:(0,l.cn)("inline-flex items-center gap-1.5",s),ref:t,...a})});o.displayName="BreadcrumbItem";let m=n.forwardRef((e,t)=>{let{asChild:s,className:i,...n}=e,d=s?a.DX:"a";return(0,r.jsx)(d,{className:(0,l.cn)("transition-colors hover:text-foreground",i),ref:t,...n})});m.displayName="BreadcrumbLink";let u=n.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,l.cn)("font-normal text-foreground",s),ref:t,role:"link",...a})});u.displayName="BreadcrumbPage";let x=e=>{let{children:t,className:s,...a}=e;return(0,r.jsx)("span",{"aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",s),role:"presentation",...a,children:null!=t?t:(0,r.jsx)(i.A,{className:"size-4"})})};x.displayName="BreadcrumbSeparator"},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(95155),a=s(74466);s(12115);var i=s(54036);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function l(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,i.cn)(n({variant:s}),t),...a})}},45119:(e,t,s)=>{"use strict";s.d(t,{HJ:()=>p,Kv:()=>x,N:()=>u,Zj:()=>o,oe:()=>m,xj:()=>f});var r=s(28755),a=s(26715),i=s(5041),n=s(90111),l=s(42366),d=s(36973);let c={all:["gifts"],lists:()=>[...c.all,"list"],list:e=>[...c.lists(),{filters:e}],details:()=>[...c.all,"detail"],detail:e=>[...c.details(),e],byRecipient:e=>[...c.all,"recipient",e],byOccasion:e=>[...c.all,"occasion",e],bySender:e=>[...c.all,"sender",e],recent:()=>[...c.all,"recent"]},o=(e,t)=>(0,n.GK)(c.list(e||{}),()=>d.Oo.getAll(e).then(e=>e.data),"gift",{staleTime:3e5,...t}),m=(e,t)=>{var s;return(0,n.GK)(c.detail(e),()=>d.Oo.getById(e),"gift",{enabled:!!e&&(null==(s=null==t?void 0:t.enabled)||s),staleTime:3e5,...t})},u=(e,t)=>(0,r.I)({queryKey:c.byRecipient(e),queryFn:()=>d.Oo.getByRecipient(e),enabled:!!e,staleTime:3e5,...t}),x=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:s}=(0,l.useNotifications)();return(0,i.n)({mutationFn:e=>d.Oo.create(e),onSuccess:t=>{e.invalidateQueries({queryKey:c.all}),t.recipientId&&e.invalidateQueries({queryKey:["recipients","detail",t.recipientId]}),s("Gift created successfully")},onError:e=>{t("Failed to create gift: ".concat(e.message))}})},f=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:s}=(0,l.useNotifications)();return(0,i.n)({mutationFn:e=>{let{id:t,data:s}=e;return d.Oo.update(t,s)},onSuccess:t=>{e.setQueryData(c.detail(t.id),t),e.invalidateQueries({queryKey:c.lists()}),t.recipientId&&e.invalidateQueries({queryKey:["recipients","detail",t.recipientId]}),s("Gift updated successfully")},onError:e=>{t("Failed to update gift: ".concat(e.message))}})},p=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:s}=(0,l.useNotifications)();return(0,i.n)({mutationFn:e=>d.Oo.delete(e),onSuccess:(t,r)=>{e.removeQueries({queryKey:c.detail(r)}),e.invalidateQueries({queryKey:c.lists()}),e.invalidateQueries({queryKey:["recipients"]}),s("Gift deleted successfully")},onError:e=>{t("Failed to delete gift: ".concat(e.message))}})}},54165:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>f,L3:()=>p,c7:()=>x,lG:()=>d,rr:()=>h,zM:()=>c});var r=s(95155),a=s(15452),i=s(25318),n=s(12115),l=s(54036);let d=a.bL,c=a.l9,o=a.ZL;a.bm;let m=n.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)(a.hJ,{className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),ref:t,...i})});m.displayName=a.hJ.displayName;let u=n.forwardRef((e,t)=>{let{children:s,className:n,...d}=e;return(0,r.jsxs)(o,{children:[(0,r.jsx)(m,{}),(0,r.jsxs)(a.UC,{className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",n),ref:t,...d,children:[s,(0,r.jsxs)(a.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(i.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=a.UC.displayName;let x=e=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};x.displayName="DialogHeader";let f=e=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};f.displayName="DialogFooter";let p=n.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)(a.hE,{className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",s),ref:t,...i})});p.displayName=a.hE.displayName;let h=n.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)(a.VY,{className:(0,l.cn)("text-sm text-muted-foreground",s),ref:t,...i})});h.displayName=a.VY.displayName},89440:(e,t,s)=>{"use strict";s.d(t,{AppBreadcrumb:()=>o});var r=s(95155),a=s(6874),i=s.n(a),n=s(35695),l=s(12115),d=s(19018),c=s(54036);function o(e){let{className:t,homeHref:s="/",homeLabel:a="Dashboard",showContainer:o=!0}=e,m=(0,n.usePathname)(),u=m?m.split("/").filter(Boolean):[],x=e=>{if(/^\d+$/.test(e))return"ID: ".concat(e);if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let t={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return t[e]?t[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},f=u.map((e,t)=>{let s="/"+u.slice(0,t+1).join("/"),a=t===u.length-1,n=x(e);return(0,r.jsxs)(l.Fragment,{children:[(0,r.jsx)(d.BreadcrumbItem,{children:a?(0,r.jsx)(d.BreadcrumbPage,{className:"font-medium text-foreground",children:n}):(0,r.jsx)(d.BreadcrumbLink,{asChild:!0,children:(0,r.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:n})})}),!a&&(0,r.jsx)(d.BreadcrumbSeparator,{})]},s)}),p=(0,r.jsx)(d.Breadcrumb,{className:(0,c.cn)("text-sm",t),children:(0,r.jsxs)(d.BreadcrumbList,{className:"flex-wrap",children:[(0,r.jsx)(d.BreadcrumbItem,{children:(0,r.jsx)(d.BreadcrumbLink,{asChild:!0,children:(0,r.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:a})})}),u.length>0&&(0,r.jsx)(d.BreadcrumbSeparator,{}),f]})});return o?(0,r.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,r.jsx)("div",{className:"flex items-center",children:p})}):p}},99637:(e,t,s)=>{Promise.resolve().then(s.bind(s,8715))}},e=>{var t=t=>e(e.s=t);e.O(0,[6476,7047,6897,3860,9664,375,6874,6453,3326,4036,4767,303,4863,8441,1684,7358],()=>t(99637)),_N_E=e.O()}]);