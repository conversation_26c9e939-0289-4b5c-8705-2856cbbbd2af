#!/usr/bin/env node

/**
 * Fix Script: Resolve 401 Unauthorized Issues for /api/vehicles
 * This script diagnoses and fixes common authentication problems
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 WorkHub Authentication Fix Tool');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
	console.error(
		'❌ Error: Please run this script from the project root directory'
	);
	process.exit(1);
}

console.log('1️⃣ CHECKING FRONTEND ENVIRONMENT CONFIGURATION');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

// Check frontend environment files
const frontendEnvFiles = [
	'frontend/.env.local',
	'frontend/.env.network',
	'frontend/.env',
];

let envFileFound = false;
frontendEnvFiles.forEach((file) => {
	const fullPath = path.join(process.cwd(), file);
	if (fs.existsSync(fullPath)) {
		console.log(`✅ Found: ${file}`);
		envFileFound = true;

		const content = fs.readFileSync(fullPath, 'utf8');
		if (content.includes('**************:3001')) {
			console.log(`   📍 Contains correct API URL: **************:3001`);
		} else {
			console.log(`   ⚠️  API URL might be incorrect in ${file}`);
		}
	} else {
		console.log(`❌ Missing: ${file}`);
	}
});

if (!envFileFound) {
	console.log(
		'\n🔧 FIXING: Creating frontend/.env.local with correct configuration'
	);

	const envContent = `# WorkHub Development Environment - Network Access
# This file is used for network access (**************)

# Backend API URLs
# Replace ************** with your actual network IP
NEXT_PUBLIC_API_URL=http://**************:3001
NEXT_PUBLIC_API_BASE_URL=http://**************:3001/api
NEXT_PUBLIC_WS_URL=ws://**************:3001

# Backend URL for server-side requests
BACKEND_URL=http://**************:3001

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url-here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key-here

# CSP Configuration
NEXT_PUBLIC_ALLOWED_CSP_CONNECT_SRC="http://**************:3001"

# Development Mode
NODE_ENV=development
NEXT_PUBLIC_ENABLE_DEBUG_LOGGING=true
`;

	fs.writeFileSync(path.join(process.cwd(), 'frontend/.env.local'), envContent);
	console.log('✅ Created frontend/.env.local');
}

console.log('\n2️⃣ CHECKING BACKEND ENVIRONMENT CONFIGURATION');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

// Check backend environment
const backendEnvPath = path.join(process.cwd(), 'backend/.env');
if (fs.existsSync(backendEnvPath)) {
	console.log('✅ Found: backend/.env');

	const backendEnvContent = fs.readFileSync(backendEnvPath, 'utf8');

	// Check critical environment variables
	const requiredVars = [
		'SUPABASE_URL',
		'SUPABASE_SERVICE_ROLE_KEY',
		'COOKIE_SECRET',
	];

	requiredVars.forEach((varName) => {
		if (backendEnvContent.includes(`${varName}=`)) {
			console.log(`   ✅ ${varName} is configured`);
		} else {
			console.log(`   ❌ Missing ${varName}`);
		}
	});
} else {
	console.log('❌ Missing: backend/.env');
	console.log('💡 Create backend/.env with required Supabase credentials');
}

console.log('\n3️⃣ CHECKING FRONTEND AUTHENTICATION SETUP');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

// Check if AuthContext is properly configured
const authContextPath = path.join(
	process.cwd(),
	'frontend/src/contexts/AuthContext.tsx'
);
if (fs.existsSync(authContextPath)) {
	console.log('✅ AuthContext.tsx exists');

	const authContent = fs.readFileSync(authContextPath, 'utf8');

	if (authContent.includes('setSecureAuthTokenProvider')) {
		console.log('   ✅ Secure auth token provider is configured');
	} else {
		console.log('   ⚠️  Auth token provider might need updating');
	}
} else {
	console.log('❌ AuthContext.tsx not found');
}

console.log('\n4️⃣ FRONTEND API CLIENT CONFIGURATION');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

// Check API client configuration
const apiIndexPath = path.join(process.cwd(), 'frontend/src/lib/api/index.ts');
if (fs.existsSync(apiIndexPath)) {
	console.log('✅ API client index.ts exists');

	const apiContent = fs.readFileSync(apiIndexPath, 'utf8');

	if (apiContent.includes("credentials: 'include'")) {
		console.log('   ✅ API client configured for cookie authentication');
	} else {
		console.log('   ⚠️  API client might not be sending cookies');
	}
} else {
	console.log('❌ API client configuration not found');
}

console.log('\n5️⃣ COMMON AUTHENTICATION ISSUES & FIXES');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

console.log('\n🔍 MOST COMMON CAUSES OF 401 ERRORS:');
console.log('');
console.log('1. ❌ USER NOT LOGGED IN');
console.log("   Solution: Make sure you're logged into the application");
console.log('   Test: Try logging in at http://**************:3000/login');
console.log('');

console.log('2. ❌ EXPIRED SESSION TOKEN');
console.log('   Solution: Log out and log back in to refresh tokens');
console.log('   Test: Clear browser cookies and login again');
console.log('');

console.log('3. ❌ MISSING HTTPONLY COOKIES');
console.log('   Solution: Ensure API requests use credentials: "include"');
console.log(
	'   Check: Browser dev tools → Network → Request headers should show cookies'
);
console.log('');

console.log('4. ❌ CORS CONFIGURATION ISSUES');
console.log('   Solution: Backend must allow credentials from frontend origin');
console.log(
	'   Check: Backend CORS_ORIGIN should include http://**************:3000'
);
console.log('');

console.log('5. ❌ RATE LIMITING');
console.log('   Solution: Wait a few minutes or whitelist your IP');
console.log('   Check: Look for rate limiting messages in API response');
console.log('');

console.log('6. ❌ USER PERMISSIONS');
console.log(
	'   Solution: Ensure user has appropriate role (USER, MANAGER, ADMIN)'
);
console.log('   Check: Verify user role in database user_profiles table');

console.log('\n🔧 IMMEDIATE FIXES TO TRY:');
console.log('');
console.log('1. 🔄 RESTART BOTH SERVICES:');
console.log('   cd backend && npm run dev');
console.log('   cd frontend && npm run dev');
console.log('');

console.log('2. 🧹 CLEAR BROWSER DATA:');
console.log('   - Clear all cookies for **************');
console.log('   - Clear localStorage and sessionStorage');
console.log('   - Hard refresh (Ctrl+F5)');
console.log('');

console.log('3. 🔐 TEST AUTHENTICATION MANUALLY:');
console.log('   - Go to http://**************:3000/login');
console.log('   - Login with valid credentials');
console.log('   - Check Network tab for Set-Cookie headers');
console.log('   - Navigate to a protected page');
console.log('');

console.log('4. 🐛 DEBUG WITH BROWSER DEV TOOLS:');
console.log('   - Open Network tab');
console.log('   - Try to access /api/vehicles');
console.log('   - Check request headers for cookies');
console.log('   - Check response for error details');

console.log('\n📋 TESTING COMMANDS:');
console.log('');
console.log('# Test the authentication flow with the debug script:');
console.log('node debug-auth-test.js');
console.log('');
console.log('# Test backend directly with curl:');
console.log('curl -v http://**************:3001/api/health');
console.log('curl -v http://**************:3001/api/vehicles');
console.log('');
console.log('# Test login endpoint:');
console.log('curl -X POST http://**************:3001/api/auth/login \\');
console.log('  -H "Content-Type: application/json" \\');
console.log('  -d \'{"email":"your-email","password":"your-password"}\'');

console.log('\n🎯 NEXT STEPS:');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('1. Update the TEST_CREDENTIALS in debug-auth-test.js');
console.log('2. Run: node debug-auth-test.js');
console.log('3. Check the browser dev tools Network tab');
console.log('4. Verify you have a valid user account in the database');
console.log(
	'5. If issues persist, check backend logs for detailed error messages'
);

console.log(
	'\n✅ Fix script completed. Run the debug script to test authentication flow.'
);
