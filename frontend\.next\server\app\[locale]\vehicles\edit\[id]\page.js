(()=>{var e={};e.id=2130,e.ids=[2130],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3940:(e,t,r)=>{"use strict";r.d(t,{O_:()=>c,t6:()=>a});var s=r(43210),i=r(49278);function a(){let e=(0,s.useCallback)((e,t)=>i.JP.success(e,t),[]),t=(0,s.useCallback)((e,t)=>i.JP.error(e,t),[]),r=(0,s.useCallback)((e,t)=>i.JP.info(e,t),[]),a=(0,s.useCallback)(t=>e(t?.successTitle||"Success",t?.successDescription||"Operation completed successfully"),[e]),c=(0,s.useCallback)((e,r)=>{let s=e instanceof Error?e.message:e;return t(r?.errorTitle||"Error",r?.errorDescription||s||"An unexpected error occurred")},[t]);return{showSuccess:e,showError:t,showInfo:r,showFormSuccess:a,showFormError:c}}function c(e){let t;switch(e){case"employee":t=r(49278).Ok;break;case"vehicle":t=r(49278).G7;break;case"task":t=r(49278).z0;break;case"delegation":t=r(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,t){let{showFormSuccess:r,showFormError:c}=a(),n=t||(e?(0,i.iw)(e):null),o=(0,s.useCallback)(e=>n?n.entityCreated(e):r({successTitle:"Created",successDescription:"Item has been created successfully"}),[n,r]),d=(0,s.useCallback)(e=>n?n.entityUpdated(e):r({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[n,r]),l=(0,s.useCallback)(e=>n?n.entityDeleted(e):r({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[n,r]),u=(0,s.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityCreationError(t)}return c(e,{errorTitle:"Creation Failed"})},[n,c]);return{showEntityCreated:o,showEntityUpdated:d,showEntityDeleted:l,showEntityCreationError:u,showEntityUpdateError:(0,s.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityUpdateError(t)}return c(e,{errorTitle:"Update Failed"})},[n,c]),showEntityDeletionError:(0,s.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityDeletionError(t)}return c(e,{errorTitle:"Deletion Failed"})},[n,c]),showFormSuccess:r,showFormError:c}}(void 0,t)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12815:(e,t,r)=>{Promise.resolve().then(r.bind(r,92598))},17705:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>c.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>d});var s=r(65239),i=r(48088),a=r(88170),c=r.n(a),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["[locale]",{children:["vehicles",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,31056)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\edit\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/vehicles/edit/[id]/page",pathname:"/[locale]/vehicles/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31056:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\vehicles\\\\edit\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\edit\\[id]\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35137:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},48041:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(60687);function i({children:e,description:t,icon:r,title:i}){return(0,s.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,s.jsx)(r,{className:"size-8 text-primary"}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:i})]}),t&&(0,s.jsx)("p",{className:"mt-1 text-muted-foreground",children:t})]}),e&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:e})]})}r(43210)},49278:(e,t,r)=>{"use strict";r.d(t,{G7:()=>p,Gb:()=>o,JP:()=>d,Ok:()=>l,Qu:()=>u,iw:()=>n,oz:()=>m,z0:()=>h});var s=r(3389);class i{show(e){return(0,s.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class a extends i{constructor(e){super(),this.config=e}entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class c extends i{serviceRecordCreated(e,t){return this.success("Service Record Added",`${t} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,t){return this.success("Service Record Updated",`${t} service for "${e}" has been updated.`)}serviceRecordDeleted(e,t){return this.success("Service Record Deleted",`${t} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function n(e){return new a(e)}function o(e,t){return new a({entityName:e,getDisplayName:t,messages:{created:{title:`${e} Created`,description:t=>`The ${e.toLowerCase()} "${t}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:t=>t||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:t=>t||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:t=>t||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let d=new i,l=new a({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new a({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),p=new a({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),h=new a({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),m=new c},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84791:(e,t,r)=>{Promise.resolve().then(r.bind(r,31056))},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var s=r(60687),i=r(22482);function a({className:e,...t}){return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},92598:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687),i=r(24920),a=r(35137),c=r(16189);r(43210);var n=r(29523),o=r(48041),d=r(85726),l=r(70258),u=r(3940),p=r(72273);let h=()=>{let e=(0,c.useRouter)(),t=(0,c.useParams)(),{showEntityUpdated:r,showEntityUpdateError:h}=(0,u.O_)("vehicle"),m=t?.id,g=m?Number(m):null,{data:x,error:y,isLoading:v}=(0,p.W_)(g),{error:f,isPending:b,mutateAsync:k}=(0,p.lR)(),E=async t=>{if(!g)return void h("Vehicle ID is missing. Cannot update.");try{let s={...t,initialOdometer:void 0===t.initialOdometer?x?.initialOdometer||0:t.initialOdometer};await k({data:s,id:g});let i={make:t.make,model:t.model};r(i),e.push("/vehicles")}catch(e){console.error("Failed to update vehicle:",e),h(e.message||f?.message||"Could not update the vehicle. Please check the details and try again.")}};return g?v?(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,s.jsx)(o.z,{description:"Loading vehicle details...",icon:a.A,title:"Edit Vehicle"}),(0,s.jsxs)("div",{className:"mx-auto max-w-2xl space-y-6",children:[(0,s.jsx)(d.E,{className:"h-10 w-1/3"}),(0,s.jsx)(d.E,{className:"h-12 w-full"}),(0,s.jsx)(d.E,{className:"h-12 w-full"}),(0,s.jsx)(d.E,{className:"h-12 w-full"}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-6",children:[(0,s.jsx)(d.E,{className:"h-10 w-24"}),(0,s.jsx)(d.E,{className:"h-10 w-24"})]})]})]}):y?(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8 text-center",children:[(0,s.jsx)(o.z,{description:y.message||"Could not load vehicle data.",icon:i.A,title:"Error Loading Vehicle"}),(0,s.jsx)(n.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]}):x?(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,s.jsx)(o.z,{description:`Update details for ${x?.make||"vehicle"} ${x?.model||""}`,icon:a.A,title:"Edit Vehicle"}),f&&(0,s.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error updating: ",f.message]}),x&&(0,s.jsx)(l.x,{initialData:x?{...x,id:String(x.id)}:void 0,isEditing:!0,isLoading:b,onSubmit:E})]}):(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8 text-center",children:[(0,s.jsx)(o.z,{description:"The requested vehicle could not be found.",icon:i.A,title:"Vehicle Not Found"}),(0,s.jsx)(n.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]}):(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8 text-center",children:[(0,s.jsx)(o.z,{description:"Invalid Vehicle ID.",icon:i.A,title:"Error"}),(0,s.jsx)(n.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,3871,7048,8390,2670,9275,6013,8739,3302,2936,5348],()=>r(17705));module.exports=s})();