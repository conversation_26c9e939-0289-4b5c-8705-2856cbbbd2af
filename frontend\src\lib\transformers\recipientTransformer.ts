/**
 * @file Data transformer for Recipient domain models.
 * @module transformers/recipientTransformer
 */

import type {
  CreateRecipientRequest,
  RecipientApiResponse,
  UpdateRecipientRequest,
} from '../types/apiContracts';
import type {
  CreateRecipientData,
  Recipient,
  UpdateRecipientData,
} from '../types/domain';

/**
 * Transforms recipient data between API response formats and frontend domain models.
 */
export const RecipientTransformer = {
  /**
   * Converts an API Recipient response into a frontend Recipient domain model.
   * @param apiData - The data received from the API.
   * @returns The Recipient domain model.
   */
  fromApi(apiData: RecipientApiResponse): Recipient {
    return {
      id: apiData.id,
      name: apiData.name,
      role: apiData.role || null,
      worksite: apiData.worksite || null,
      email: apiData.email || null,
      phone: apiData.phone || null,
      address: apiData.address || null,
      notes: apiData.notes || null,
      createdAt: apiData.createdAt,
      updatedAt: apiData.updatedAt,
      ...(apiData.gifts && {
        gifts: apiData.gifts.map(gift => ({
          id: gift.id,
          itemDescription: gift.itemDescription,
          recipientId: gift.recipientId,
          dateSent: gift.dateSent,
          senderName: gift.senderName,
          occasion: gift.occasion || null,
          notes: gift.notes || null,
          createdAt: gift.createdAt,
          updatedAt: gift.updatedAt,
        })),
      }),
    };
  },

  /**
   * Converts frontend CreateRecipientData into an API request payload.
   * @param domainData - The domain data to transform.
   * @returns The API request payload.
   */
  toCreateRequest(domainData: CreateRecipientData): CreateRecipientRequest {
    return {
      name: domainData.name,
      role: domainData.role ?? null,
      worksite: domainData.worksite ?? null,
      email: domainData.email ?? null,
      phone: domainData.phone ?? null,
      address: domainData.address ?? null,
      notes: domainData.notes ?? null,
    };
  },

  /**
   * Converts frontend UpdateRecipientData into an API request payload.
   * @param domainData - The domain data to transform.
   * @returns The API request payload.
   */
  toUpdateRequest(domainData: UpdateRecipientData): UpdateRecipientRequest {
    const request: UpdateRecipientRequest = {};

    if (domainData.name !== undefined) {
      request.name = domainData.name;
    }
    if (domainData.role !== undefined) {
      request.role = domainData.role;
    }
    if (domainData.worksite !== undefined) {
      request.worksite = domainData.worksite;
    }
    if (domainData.email !== undefined) {
      request.email = domainData.email;
    }
    if (domainData.phone !== undefined) {
      request.phone = domainData.phone;
    }
    if (domainData.address !== undefined) {
      request.address = domainData.address;
    }
    if (domainData.notes !== undefined) {
      request.notes = domainData.notes;
    }

    return request;
  },

  /**
   * Transforms an array of API Recipient responses into domain models.
   * @param apiDataArray - Array of API responses.
   * @returns Array of Recipient domain models.
   */
  fromApiArray(apiDataArray: RecipientApiResponse[]): Recipient[] {
    return apiDataArray.map(this.fromApi);
  },
};
