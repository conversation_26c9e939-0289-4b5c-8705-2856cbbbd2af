(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4032],{12626:(e,s,i)=>{"use strict";i.r(s),i.d(s,{default:()=>n});var r=i(95155),c=i(28328),t=i(35695),a=i(30836),l=i(80937);function n(){let e=(0,t.useRouter)(),s=(0,l.Y1)(),i=async i=>{try{await s.mutateAsync(i),e.push("/vehicles")}catch(e){console.error("Error adding vehicle:",e)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center space-x-2",children:[(0,r.jsx)(c.A,{className:"size-8 text-primary"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-primary",children:"Add New Vehicle"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Enter the details of your new vehicle."})]})]}),(0,r.jsx)(a.x,{onSubmit:i})]})}},43840:(e,s,i)=>{"use strict";i.d(s,{cl:()=>r.cl,delegationApiService:()=>r.ac,employeeApiService:()=>r.aV,reliabilityApiService:()=>r.e_,taskApiService:()=>r.Hg,vehicleApiService:()=>r.oL});var r=i(36973);i(72248)},82111:(e,s,i)=>{Promise.resolve().then(i.bind(i,12626))}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,7047,6897,3860,9664,375,7876,1859,5669,4629,4036,4767,303,8122,8441,1684,7358],()=>s(82111)),_N_E=e.O()}]);