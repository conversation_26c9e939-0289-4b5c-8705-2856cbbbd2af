'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON>Left, Save } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useTranslations } from 'next-intl';

import type {
  CreateRecipientData,
  Recipient,
  UpdateRecipientData,
} from '@/lib/types/domain';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

// Create schema with translations
const createRecipientSchema = () => {
  const t = useTranslations('forms.validation');

  return z.object({
    name: z
      .string()
      .min(1, t('required'))
      .max(255, t('maxLength', { max: 255 })),
    email: z
      .string()
      .email(t('invalidEmail'))
      .max(255, t('maxLength', { max: 255 }))
      .optional()
      .or(z.literal('')),
    phone: z
      .string()
      .max(20, t('maxLength', { max: 20 }))
      .optional()
      .or(z.literal('')),
    address: z
      .string()
      .max(500, t('maxLength', { max: 500 }))
      .optional()
      .or(z.literal('')),
    notes: z
      .string()
      .max(1000, t('maxLength', { max: 1000 }))
      .optional()
      .or(z.literal('')),
  });
};

// Recipient form validation schema
const recipientFormSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .max(255, 'Name must be less than 255 characters'),
  role: z
    .string()
    .max(100, 'Role must be less than 100 characters')
    .optional()
    .or(z.literal('')),
  worksite: z
    .string()
    .max(100, 'Worksite must be less than 100 characters')
    .optional()
    .or(z.literal('')),
  email: z
    .string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters')
    .optional()
    .or(z.literal('')),
  phone: z
    .string()
    .max(20, 'Phone number must be less than 20 characters')
    .optional()
    .or(z.literal('')),
  address: z
    .string()
    .max(500, 'Address must be less than 500 characters')
    .optional()
    .or(z.literal('')),
  notes: z
    .string()
    .max(1000, 'Notes must be less than 1000 characters')
    .optional()
    .or(z.literal('')),
});

type RecipientFormData = z.infer<typeof recipientFormSchema>;

interface RecipientFormProps {
  initialData?: Partial<Recipient>;
  isEditing?: boolean;
  isLoading?: boolean;
  onSubmit: (data: CreateRecipientData | UpdateRecipientData) => Promise<void>;
}

export const RecipientForm: React.FC<RecipientFormProps> = ({
  initialData,
  isEditing = false,
  isLoading = false,
  onSubmit,
}) => {
  const router = useRouter();
  const t = useTranslations('forms');
  const tLabels = useTranslations('forms.labels');
  const tButtons = useTranslations('forms.buttons');
  const tPlaceholders = useTranslations('forms.placeholders');
  const tTitles = useTranslations('forms.titles');

  const form = useForm<RecipientFormData>({
    resolver: zodResolver(recipientFormSchema),
    defaultValues: {
      name: initialData?.name || '',
      role: initialData?.role || '',
      worksite: initialData?.worksite || '',
      email: initialData?.email || '',
      phone: initialData?.phone || '',
      address: initialData?.address || '',
      notes: initialData?.notes || '',
    },
  });

  const handleSubmit = async (data: RecipientFormData) => {
    try {
      // Convert empty strings to null for optional fields
      const formattedData: CreateRecipientData | UpdateRecipientData = {
        name: data.name,
        role: data.role || null,
        worksite: data.worksite || null,
        email: data.email || null,
        phone: data.phone || null,
        address: data.address || null,
        notes: data.notes || null,
      };

      await onSubmit(formattedData);
    } catch (error) {
      console.error('Error submitting recipient form:', error);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCancel}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <CardTitle>
            {isEditing ? tTitles('editRecipient') : tTitles('addRecipient')}
          </CardTitle>
        </div>
      </CardHeader>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardContent className="space-y-6">
            {/* Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('name')} *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={tPlaceholders('enterName')}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Role */}
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('role')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={tPlaceholders('enterRole')}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Worksite */}
            <FormField
              control={form.control}
              name="worksite"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('worksite')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={tPlaceholders('enterWorksite')}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Email */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('email')}</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder={tPlaceholders('enterEmail')}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Phone */}
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('phone')}</FormLabel>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder={tPlaceholders('enterPhone')}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Address */}
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('address')}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={tPlaceholders('enterAddress')}
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('notes')}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={tPlaceholders('enterNotes')}
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>

          <CardFooter className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              {tButtons('cancel')}
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? (
                tButtons('save') + '...'
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ? tButtons('update') : tButtons('save')}
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
};
