'use client';

import { Filter, Search, X } from 'lucide-react';
import { useMemo } from 'react';

import type { Recipient } from '@/lib/types/domain';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface FilterState {
  search: string;
  hasEmail: string; // 'all', 'yes', 'no'
  hasPhone: string; // 'all', 'yes', 'no'
  hasAddress: string; // 'all', 'yes', 'no'
  hasRole: string; // 'all', 'yes', 'no'
  hasWorksite: string; // 'all', 'yes', 'no'
  role: string; // 'all' or specific role value
  worksite: string; // 'all' or specific worksite value
  giftCount: string; // 'all', 'none', 'some', 'many'
}

interface RecipientFiltersProps {
  filters: FilterState;
  onFilterChange: (filters: Partial<FilterState>) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  recipients: Recipient[]; // For calculating statistics
}

export const RecipientFilters: React.FC<RecipientFiltersProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
  hasActiveFilters,
  recipients,
}) => {
  // Calculate statistics for filter options
  const statistics = useMemo(() => {
    const stats = {
      total: recipients.length,
      withEmail: 0,
      withPhone: 0,
      withAddress: 0,
      withRole: 0,
      withWorksite: 0,
      withNoGifts: 0,
      withSomeGifts: 0,
      withManyGifts: 0,
    };

    const uniqueRoles = new Set<string>();
    const uniqueWorksites = new Set<string>();

    recipients.forEach(recipient => {
      if (recipient.email) stats.withEmail++;
      if (recipient.phone) stats.withPhone++;
      if (recipient.address) stats.withAddress++;
      if (recipient.role) {
        stats.withRole++;
        uniqueRoles.add(recipient.role);
      }
      if (recipient.worksite) {
        stats.withWorksite++;
        uniqueWorksites.add(recipient.worksite);
      }

      const giftCount = recipient.gifts?.length || 0;
      if (giftCount === 0) stats.withNoGifts++;
      else if (giftCount < 5) stats.withSomeGifts++;
      else stats.withManyGifts++;
    });

    return {
      ...stats,
      uniqueRoles: Array.from(uniqueRoles).sort(),
      uniqueWorksites: Array.from(uniqueWorksites).sort(),
    };
  }, [recipients]);

  const handleSearchChange = (value: string) => {
    onFilterChange({ search: value });
  };

  const handleEmailFilterChange = (value: string) => {
    onFilterChange({ hasEmail: value });
  };

  const handlePhoneFilterChange = (value: string) => {
    onFilterChange({ hasPhone: value });
  };

  const handleAddressFilterChange = (value: string) => {
    onFilterChange({ hasAddress: value });
  };

  const handleRoleFilterChange = (value: string) => {
    onFilterChange({ hasRole: value });
  };

  const handleWorksiteFilterChange = (value: string) => {
    onFilterChange({ hasWorksite: value });
  };

  const handleSpecificRoleChange = (value: string) => {
    onFilterChange({ role: value });
  };

  const handleSpecificWorksiteChange = (value: string) => {
    onFilterChange({ worksite: value });
  };

  const handleGiftCountFilterChange = (value: string) => {
    onFilterChange({ giftCount: value });
  };

  const activeFilterCount = [
    filters.search,
    filters.hasEmail !== 'all' ? filters.hasEmail : '',
    filters.hasPhone !== 'all' ? filters.hasPhone : '',
    filters.hasAddress !== 'all' ? filters.hasAddress : '',
    filters.hasRole !== 'all' ? filters.hasRole : '',
    filters.hasWorksite !== 'all' ? filters.hasWorksite : '',
    filters.role !== 'all' ? filters.role : '',
    filters.worksite !== 'all' ? filters.worksite : '',
    filters.giftCount !== 'all' ? filters.giftCount : '',
  ].filter(Boolean).length;

  return (
    <Card>
      <Collapsible>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CollapsibleTrigger asChild>
                <ActionButton actionType="tertiary" size="sm" className="p-0">
                  <Filter className="h-4 w-4" />
                </ActionButton>
              </CollapsibleTrigger>
              <CardTitle className="text-base">Filters</CardTitle>
              {activeFilterCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {activeFilterCount}
                </Badge>
              )}
            </div>
            {hasActiveFilters && (
              <ActionButton
                actionType="tertiary"
                size="sm"
                onClick={onClearFilters}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4 mr-1" />
                Clear All
              </ActionButton>
            )}
          </div>
          <CardDescription>
            Filter recipients by contact information and gift history
          </CardDescription>
        </CardHeader>

        <CollapsibleContent>
          <CardContent className="space-y-4">
            {/* Search */}
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search recipients by name, role, worksite, email, phone, or notes..."
                  value={filters.search}
                  onChange={e => handleSearchChange(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {/* Email Filter */}
              <div className="space-y-2">
                <Label htmlFor="email-filter">Email Address</Label>
                <Select
                  value={filters.hasEmail}
                  onValueChange={handleEmailFilterChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      All recipients ({statistics.total})
                    </SelectItem>
                    <SelectItem value="yes">
                      Has email ({statistics.withEmail})
                    </SelectItem>
                    <SelectItem value="no">
                      No email ({statistics.total - statistics.withEmail})
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Phone Filter */}
              <div className="space-y-2">
                <Label htmlFor="phone-filter">Phone Number</Label>
                <Select
                  value={filters.hasPhone}
                  onValueChange={handlePhoneFilterChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      All recipients ({statistics.total})
                    </SelectItem>
                    <SelectItem value="yes">
                      Has phone ({statistics.withPhone})
                    </SelectItem>
                    <SelectItem value="no">
                      No phone ({statistics.total - statistics.withPhone})
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Address Filter */}
              <div className="space-y-2">
                <Label htmlFor="address-filter">Address</Label>
                <Select
                  value={filters.hasAddress}
                  onValueChange={handleAddressFilterChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      All recipients ({statistics.total})
                    </SelectItem>
                    <SelectItem value="yes">
                      Has address ({statistics.withAddress})
                    </SelectItem>
                    <SelectItem value="no">
                      No address ({statistics.total - statistics.withAddress})
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Role Filter */}
              <div className="space-y-2">
                <Label htmlFor="role-filter">Role</Label>
                <Select
                  value={filters.hasRole}
                  onValueChange={handleRoleFilterChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      All recipients ({statistics.total})
                    </SelectItem>
                    <SelectItem value="yes">
                      Has role ({statistics.withRole})
                    </SelectItem>
                    <SelectItem value="no">
                      No role ({statistics.total - statistics.withRole})
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Worksite Filter */}
              <div className="space-y-2">
                <Label htmlFor="worksite-filter">Worksite</Label>
                <Select
                  value={filters.hasWorksite}
                  onValueChange={handleWorksiteFilterChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      All recipients ({statistics.total})
                    </SelectItem>
                    <SelectItem value="yes">
                      Has worksite ({statistics.withWorksite})
                    </SelectItem>
                    <SelectItem value="no">
                      No worksite ({statistics.total - statistics.withWorksite})
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Specific Role Filter */}
              {statistics.uniqueRoles.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="specific-role-filter">Specific Role</Label>
                  <Select
                    value={filters.role}
                    onValueChange={handleSpecificRoleChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All roles" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All roles</SelectItem>
                      {statistics.uniqueRoles.map(role => (
                        <SelectItem key={role} value={role}>
                          {role}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Specific Worksite Filter */}
              {statistics.uniqueWorksites.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="specific-worksite-filter">
                    Specific Worksite
                  </Label>
                  <Select
                    value={filters.worksite}
                    onValueChange={handleSpecificWorksiteChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All worksites" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All worksites</SelectItem>
                      {statistics.uniqueWorksites.map(worksite => (
                        <SelectItem key={worksite} value={worksite}>
                          {worksite}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Gift Count Filter */}
              <div className="space-y-2">
                <Label htmlFor="gift-count-filter">Gift History</Label>
                <Select
                  value={filters.giftCount}
                  onValueChange={handleGiftCountFilterChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All recipients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      All recipients ({statistics.total})
                    </SelectItem>
                    <SelectItem value="none">
                      No gifts ({statistics.withNoGifts})
                    </SelectItem>
                    <SelectItem value="some">
                      1-4 gifts ({statistics.withSomeGifts})
                    </SelectItem>
                    <SelectItem value="many">
                      5+ gifts ({statistics.withManyGifts})
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Statistics Summary */}
            <div className="pt-4 border-t">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-lg">
                    {statistics.total}
                  </div>
                  <div className="text-muted-foreground">Total Recipients</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg">
                    {statistics.withRole}
                  </div>
                  <div className="text-muted-foreground">With Role</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg">
                    {statistics.withWorksite}
                  </div>
                  <div className="text-muted-foreground">With Worksite</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg">
                    {statistics.withEmail}
                  </div>
                  <div className="text-muted-foreground">With Email</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg">
                    {statistics.withPhone}
                  </div>
                  <div className="text-muted-foreground">With Phone</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg">
                    {statistics.withAddress}
                  </div>
                  <div className="text-muted-foreground">With Address</div>
                </div>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};
