(()=>{var e={};e.id=3303,e.ids=[3303],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14535:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\tasks\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\[id]\\edit\\page.tsx","default")},16005:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(60687),i=r(75699),a=r(58261);let o=(0,r(82614).A)("FilePen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]]);var n=r(16189),d=r(43210),l=r(78207),c=r(48041),p=r(85726),u=r(3940),m=r(73227);let y=e=>({dateTime:e.dateTime?(0,i.GP)((0,a.H)(e.dateTime),"yyyy-MM-dd'T'HH:mm"):"",deadline:e.deadline?(0,i.GP)((0,a.H)(e.deadline),"yyyy-MM-dd'T'HH:mm"):"",description:e.description||"",driverEmployeeId:e.driverEmployeeId||null,estimatedDuration:e.estimatedDuration||0,location:e.location||"",notes:e.notes||"",priority:e.priority,requiredSkills:e.requiredSkills||[],staffEmployeeId:e.staffEmployeeId,status:e.status.replace("_"," "),subtasks:e.subtasks?.map(e=>({completed:e.completed,id:e.id,taskId:e.taskId,title:e.title}))||[],vehicleId:e.vehicleId||null});function f(){let e=(0,n.useRouter)(),t=(0,n.useParams)(),{showEntityUpdated:r,showEntityUpdateError:i}=(0,u.O_)("task"),a=t?.id,{data:f,error:x,isLoading:k}=(0,m.b7)(a),{error:h,isPending:v,mutateAsync:g}=(0,m.K)(),b=async t=>{if(a&&f){if(!t.staffEmployeeId)return void i("Staff employee is required for task updates.");try{let s={dateTime:t.dateTime,deadline:t.deadline,description:t.description,driverEmployeeId:t.driverEmployeeId||void 0,estimatedDuration:t.estimatedDuration,location:t.location,notes:t.notes,priority:t.priority,requiredSkills:t.requiredSkills,staffEmployeeId:t.staffEmployeeId,status:t.status.replace(" ","_"),subtasks:t.subtasks?.map(e=>({completed:e.completed??!1,taskId:a,title:e.title})),vehicleId:t.vehicleId||void 0},o=Object.fromEntries(Object.entries(s).filter(([,e])=>void 0!==e)),n=await g({data:o,id:a});if(n){let t={title:n.description.slice(0,30)+(n.description.length>30?"...":""),name:n.description.slice(0,30)+(n.description.length>30?"...":"")};r(t),e.push(`/tasks/${a}`)}else i("Failed to update task (no data returned).")}catch(e){console.error("Error updating task:",e),i(e.message||h?.message||"Failed to update task. Please try again.")}}},q=(0,d.useMemo)(()=>{if(f)return y(f)},[f]);return k?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(c.z,{icon:o,title:"Loading Task..."}),(0,s.jsx)(p.E,{className:"h-[600px] w-full rounded-lg bg-card"})]}):f&&q?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(c.z,{description:"Modify the details for this task.",icon:o,title:`Edit Task: ${f.description.slice(0,50)}${f.description.length>50?"...":""}`}),h&&(0,s.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error updating: ",h.message]}),(0,s.jsx)(l.A,{initialData:q,isEditing:!0,isLoading:v,onSubmit:b})]}):(0,s.jsxs)("div",{className:"space-y-6 text-center",children:[(0,s.jsx)(c.z,{description:"The requested task could not be found.",icon:o,title:"Task Not Found"}),(0,s.jsx)("button",{className:"text-blue-500",onClick:()=>e.push("/tasks"),children:"Back to Tasks"})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40030:(e,t,r)=>{Promise.resolve().then(r.bind(r,16005))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76822:(e,t,r)=>{Promise.resolve().then(r.bind(r,14535))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},82857:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let l={children:["",{children:["[locale]",{children:["tasks",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,14535)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\[id]\\edit\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/tasks/[id]/edit/page",pathname:"/[locale]/tasks/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var s=r(60687),i=r(22482);function a({className:e,...t}){return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,3871,7048,8390,2670,9275,6013,8739,3302,2936,9599,2153],()=>r(82857));module.exports=s})();