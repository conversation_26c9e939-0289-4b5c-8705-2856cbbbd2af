(()=>{var e={};e.id=9226,e.ids=[5176,9226],e.modules={854:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>h});var t=s(60687),a=s(44610),i=s(30474),l=s(85814),c=s.n(l),n=s(16189);s(43210);var o=s(997),d=s(68752),m=s(52027),p=s(72273),x=s(48184);function h(){let e=(0,n.useParams)(),r=e?.id,{data:s,error:l,isLoading:h,refetch:u}=(0,p.W_)(Number(r),{enabled:!!r});return(0,t.jsx)("div",{className:"mx-auto max-w-4xl bg-white p-2 text-gray-800 sm:p-4",children:(0,t.jsx)(m.gO,{data:s,emptyComponent:(0,t.jsx)("div",{className:"py-10 text-center",children:"Vehicle not found or could not be loaded."}),error:(0,x.u1)(l),isLoading:h,loadingComponent:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h1",{className:"text-center text-3xl font-bold text-gray-800",children:"Loading Report..."}),(0,t.jsx)(m.jt,{count:1,variant:"card"}),(0,t.jsx)(m.jt,{className:"mt-6",count:3,variant:"table"})]}),onRetry:()=>{u()},children:e=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"no-print mb-4 flex items-center justify-between",children:[(0,t.jsx)(d.r,{actionType:"tertiary",asChild:!0,icon:(0,t.jsx)(a.A,{className:"size-4"}),children:(0,t.jsx)(c(),{href:`/vehicles/${r}/report/service-history`,children:"Detailed Service History"})}),(0,t.jsx)(o.k,{enableCsv:(e.serviceHistory?.length||0)>0,entityId:r,fileName:`vehicle-report-${e.make}-${e.model}`,reportContentId:"#vehicle-report-content",reportType:"vehicle",tableId:"#service-history-table"})]}),(0,t.jsxs)("div",{className:"report-content",id:"vehicle-report-content",children:[(0,t.jsxs)("header",{className:"report-header mb-8 border-b-2 border-gray-300 pb-4 text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"Maintenance Report"}),(0,t.jsxs)("p",{className:"text-xl text-gray-600",children:[e.make," ",e.model," (",e.year,")"]}),e.licensePlate&&(0,t.jsxs)("p",{className:"text-md text-gray-500",children:["Plate: ",e.licensePlate]})]}),(0,t.jsxs)("section",{className:"card-print mb-8 rounded border border-gray-200 p-4",children:[(0,t.jsx)("h2",{className:"mb-4 border-b border-gray-200 pb-2 text-2xl font-semibold text-gray-700",children:"Vehicle Details"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4 text-sm md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Make:"})," ",e.make]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Model:"})," ",e.model]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Year:"})," ",e.year]}),e.licensePlate&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Plate Number:"})," ",e.licensePlate]}),e.color&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Color:"})," ",e.color]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Initial Odometer:"})," ",null!==e.initialOdometer&&void 0!==e.initialOdometer?`${e.initialOdometer.toLocaleString()} miles`:"Not recorded"]})]}),e.imageUrl&&(0,t.jsx)("div",{className:"no-print relative mx-auto mt-4 aspect-[16/9] w-full max-w-md overflow-hidden rounded",children:(0,t.jsx)(i.default,{alt:`${e.make} ${e.model}`,"data-ai-hint":"car side",layout:"fill",objectFit:"contain",src:e.imageUrl})})]}),(0,t.jsxs)("section",{className:"card-print rounded border border-gray-200 p-4",children:[(0,t.jsxs)("div",{className:"mb-4 flex items-center justify-between border-b border-gray-200 pb-2",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-700",children:"Service History"}),(0,t.jsx)("p",{className:"no-print text-sm text-gray-500",children:(0,t.jsx)(c(),{className:"text-blue-600 hover:underline",href:`/vehicles/${r}/report/service-history`,children:"View detailed service history"})})]}),e.serviceHistory?.length===0?(0,t.jsx)("p",{className:"text-gray-500",children:"No service records available for this vehicle."}):(0,t.jsxs)("table",{className:"w-full text-left text-sm text-gray-600",id:"service-history-table",children:[(0,t.jsx)("thead",{className:"bg-gray-50 text-xs uppercase text-gray-700",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Date"}),(0,t.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Odometer"}),(0,t.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Service Performed"}),(0,t.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Notes"}),(0,t.jsx)("th",{className:"px-3 py-2 text-right",scope:"col",children:"Cost"})]})}),(0,t.jsx)("tbody",{children:e.serviceHistory?.map(e=>(0,t.jsxs)("tr",{className:"border-b bg-white hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"px-3 py-2",children:new Date(e.date).toLocaleDateString()}),(0,t.jsx)("td",{className:"px-3 py-2",children:e.odometer.toLocaleString()}),(0,t.jsx)("td",{className:"px-3 py-2",children:e.servicePerformed.join(", ")}),(0,t.jsx)("td",{className:"px-3 py-2",children:e.notes||"-"}),(0,t.jsx)("td",{className:"px-3 py-2 text-right",children:e.cost?`$${Number(e.cost).toFixed(2)}`:"-"})]},e.id))})]})]}),(0,t.jsxs)("footer",{className:"report-footer mt-12 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,t.jsxs)("p",{children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,t.jsx)("p",{children:"WorkHub"})]})]})]})})})}},997:(e,r,s)=>{"use strict";s.d(r,{k:()=>u});var t=s(60687),a=s(28946),i=s(11516),l=s(20620),c=s(36644);let n=(0,s(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var o=s(43210),d=s(68752),m=s(21342),p=s(3940),x=s(22482),h=s(22364);function u({className:e,csvData:r,enableCsv:s=!1,entityId:u,fileName:y,reportContentId:v,reportType:j,tableId:f}){let[g,b]=(0,o.useState)(!1),[N,k]=(0,o.useState)(!1),{showFormSuccess:C,showFormError:w}=(0,p.t6)(),S=async()=>{b(!0);try{let e=`/api/reports/${j}${u?`/${u}`:""}`,r=document.createElement("a");r.href=e,r.download=`${y}.pdf`,r.target="_blank",document.body.append(r),r.click(),r.remove(),C({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),w(`PDF download failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{b(!1)}},A=async()=>{if(s){k(!0);try{if(r?.data&&r.headers)(0,h.og)(r.data,r.headers,`${y}.csv`);else if(f){let e=(0,h.tL)(f);(0,h.og)(e.data,e.headers,`${y}.csv`)}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");C({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),w(`CSV generation failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{k(!1)}}},P=g||N;return(0,t.jsxs)("div",{className:(0,x.cn)("flex items-center gap-2 no-print",e),children:[(0,t.jsx)(d.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,t.jsx)(a.A,{className:"size-4"})}),(0,t.jsxs)(m.rI,{children:[(0,t.jsx)(m.ty,{asChild:!0,children:(0,t.jsx)(d.r,{actionType:"secondary","aria-label":"Download report",disabled:P,size:"icon",title:"Download Report",children:P?(0,t.jsx)(i.A,{className:"size-4 animate-spin"}):(0,t.jsx)(l.A,{className:"size-4"})})}),(0,t.jsxs)(m.SQ,{align:"end",children:[(0,t.jsxs)(m._2,{disabled:g,onClick:S,children:[g?(0,t.jsx)(i.A,{className:"mr-2 size-4 animate-spin"}):(0,t.jsx)(c.A,{className:"mr-2 size-4"}),(0,t.jsx)("span",{children:"Download PDF"})]}),s&&(0,t.jsxs)(m._2,{disabled:N,onClick:A,children:[N?(0,t.jsx)(i.A,{className:"mr-2 size-4 animate-spin"}):(0,t.jsx)(n,{className:"mr-2 size-4"}),(0,t.jsx)("span",{children:"Download CSV"})]})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6655:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i,metadata:()=>a});var t=s(37413);let a={title:"Vehicle Report"};function i({children:e}){return(0,t.jsx)(t.Fragment,{children:e})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18621:(e,r,s)=>{Promise.resolve().then(s.bind(s,90164))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20620:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},52027:(e,r,s)=>{"use strict";s.d(r,{gO:()=>p,jt:()=>y,pp:()=>x});var t=s(60687),a=s(72963),i=s(11516);s(43210);var l=s(68752),c=s(91821),n=s(85726),o=s(22482);let d={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function p({children:e,className:r,data:s,emptyComponent:a,error:i,errorComponent:l,isLoading:c,loadingComponent:n,onRetry:d}){return c?n||(0,t.jsx)(u,{...r&&{className:r},text:"Loading..."}):i?l||(0,t.jsx)(h,{...r&&{className:r},message:i,...d&&{onRetry:d}}):!s||Array.isArray(s)&&0===s.length?a||(0,t.jsx)("div",{className:(0,o.cn)("text-center py-8",r),children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,t.jsx)("div",{className:r,children:e(s)})}function x({className:e,description:r,icon:s,primaryAction:a,secondaryAction:i,title:c}){return(0,t.jsxs)("div",{className:(0,o.cn)("space-y-6 text-center py-12",e),children:[s&&(0,t.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,t.jsx)(s,{className:"h-10 w-10 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:c}),r&&(0,t.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:r})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[a&&(0,t.jsx)(l.r,{actionType:"primary",asChild:!!a.href,icon:a.icon,onClick:a.onClick,children:a.href?(0,t.jsx)("a",{href:a.href,children:a.label}):a.label}),i&&(0,t.jsx)(l.r,{actionType:"tertiary",asChild:!!i.href,icon:i.icon,onClick:i.onClick,children:i.href?(0,t.jsx)("a",{href:i.href,children:i.label}):i.label})]})]})}function h({className:e,message:r,onRetry:s}){return(0,t.jsxs)(c.Fc,{className:(0,o.cn)("my-4",e),variant:"destructive",children:[(0,t.jsx)(a.A,{className:"size-4"}),(0,t.jsx)(c.XL,{children:"Error"}),(0,t.jsx)(c.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:r}),s&&(0,t.jsx)(l.r,{actionType:"tertiary",icon:(0,t.jsx)(i.A,{className:"size-4"}),onClick:s,size:"sm",children:"Try Again"})]})})]})}function u({className:e,fullPage:r=!1,size:s="md",text:a}){return(0,t.jsx)("div",{className:(0,o.cn)("flex items-center justify-center",r&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",e),children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(i.A,{className:(0,o.cn)("animate-spin text-primary",d[s])}),a&&(0,t.jsx)("span",{className:(0,o.cn)("mt-2 text-muted-foreground",m[s]),children:a})]})})}function y({className:e,count:r=1,testId:s="loading-skeleton",variant:a="default"}){return"card"===a?(0,t.jsx)("div",{className:(0,o.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",e),"data-testid":s,children:Array(r).fill(0).map((e,r)=>(0,t.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,t.jsx)(n.E,{className:"aspect-[16/10] w-full"}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)(n.E,{className:"mb-1 h-7 w-3/4"}),(0,t.jsx)(n.E,{className:"mb-3 h-4 w-1/2"}),(0,t.jsx)(n.E,{className:"my-3 h-px w-full"}),(0,t.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,r)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.E,{className:"mr-2.5 size-5 rounded-full"}),(0,t.jsx)(n.E,{className:"h-5 w-2/3"})]},r))})]})]},r))}):"table"===a?(0,t.jsxs)("div",{className:(0,o.cn)("space-y-3",e),"data-testid":s,children:[(0,t.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,r)=>(0,t.jsx)(n.E,{className:"h-8 flex-1"},r))}),Array(r).fill(0).map((e,r)=>(0,t.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,r)=>(0,t.jsx)(n.E,{className:"h-6 flex-1"},r))},r))]}):"list"===a?(0,t.jsx)("div",{className:(0,o.cn)("space-y-3",e),"data-testid":s,children:Array(r).fill(0).map((e,r)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(n.E,{className:"size-12 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)(n.E,{className:"h-4 w-1/3"}),(0,t.jsx)(n.E,{className:"h-4 w-full"})]})]},r))}):"stats"===a?(0,t.jsx)("div",{className:(0,o.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",e),"data-testid":s,children:Array(r).fill(0).map((e,r)=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(n.E,{className:"h-5 w-1/3"}),(0,t.jsx)(n.E,{className:"size-5 rounded-full"})]}),(0,t.jsx)(n.E,{className:"mt-3 h-8 w-1/2"}),(0,t.jsx)(n.E,{className:"mt-2 h-4 w-2/3"})]},r))}):(0,t.jsx)("div",{className:(0,o.cn)("space-y-2",e),"data-testid":s,children:Array(r).fill(0).map((e,r)=>(0,t.jsx)(n.E,{className:"h-5 w-full"},r))})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62589:(e,r,s)=>{Promise.resolve().then(s.bind(s,854))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63805:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>o});var t=s(65239),a=s(48088),i=s(88170),l=s.n(i),c=s(30893),n={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);s.d(r,n);let o={children:["",{children:["[locale]",{children:["vehicles",{children:["[id]",{children:["report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90164)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\[id]\\report\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,6655)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\[id]\\report\\layout.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\[id]\\report\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/vehicles/[id]/report/page",pathname:"/[locale]/vehicles/[id]/report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},68752:(e,r,s)=>{"use strict";s.d(r,{r:()=>o});var t=s(60687),a=s(11516),i=s(43210),l=s.n(i),c=s(29523),n=s(22482);let o=l().forwardRef(({actionType:e="primary",asChild:r=!1,children:s,className:i,disabled:l,icon:o,isLoading:d=!1,loadingText:m,...p},x)=>{let{className:h,variant:u}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[e];return(0,t.jsx)(c.$,{asChild:r,className:(0,n.cn)(h,i),disabled:d||l,ref:x,variant:u,...p,children:d?(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,t.jsx)(a.A,{className:"mr-2 size-4 animate-spin"}),m||s]}):(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",o&&(0,t.jsx)("span",{className:"mr-2",children:o}),s]})})});o.displayName="ActionButton"},72273:(e,r,s)=>{"use strict";s.d(r,{NS:()=>h,T$:()=>d,W_:()=>m,Y1:()=>p,lR:()=>x});var t=s(8693),a=s(54050),i=s(46349),l=s(87676),c=s(48839),n=s(75176);let o={all:["vehicles"],detail:e=>["vehicles",e]},d=e=>(0,i.GK)([...o.all],async()=>(await n.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),m=(e,r)=>(0,i.GK)([...o.detail(e)],()=>n.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(r?.enabled??!0),staleTime:3e5,...r}),p=()=>{let e=(0,t.jE)(),{showError:r,showSuccess:s}=(0,l.useNotifications)();return(0,a.n)({mutationFn:e=>{let r=c.M.toCreateRequest(e);return n.vehicleApiService.create(r)},onError:e=>{r(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:r=>{e.invalidateQueries({queryKey:o.all}),s(`Vehicle "${r.licensePlate}" has been created successfully!`)}})},x=()=>{let e=(0,t.jE)(),{showError:r,showSuccess:s}=(0,l.useNotifications)();return(0,a.n)({mutationFn:({data:e,id:r})=>{let s=c.M.toUpdateRequest(e);return n.vehicleApiService.update(r,s)},onError:e=>{r(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:r=>{e.invalidateQueries({queryKey:o.all}),e.invalidateQueries({queryKey:o.detail(r.id)}),s(`Vehicle "${r.licensePlate}" has been updated successfully!`)}})},h=()=>{let e=(0,t.jE)(),{showError:r,showSuccess:s}=(0,l.useNotifications)();return(0,a.n)({mutationFn:e=>n.vehicleApiService.delete(e),onError:e=>{r(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(r,t)=>{e.invalidateQueries({queryKey:o.all}),e.removeQueries({queryKey:o.detail(t)}),s("Vehicle has been deleted successfully!")}})}},74075:e=>{"use strict";e.exports=require("zlib")},75176:(e,r,s)=>{"use strict";s.d(r,{cl:()=>t.cl,delegationApiService:()=>t.ac,employeeApiService:()=>t.aV,reliabilityApiService:()=>t.e_,taskApiService:()=>t.Hg,vehicleApiService:()=>t.oL});var t=s(3302);s(8342)},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,r,s)=>{"use strict";s.d(r,{E:()=>i});var t=s(60687),a=s(22482);function i({className:e,...r}){return(0,t.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...r})}},87676:(e,r,s)=>{"use strict";s.r(r),s.d(r,{useNotifications:()=>i,useWorkHubNotifications:()=>l});var t=s(43210),a=s(94538);let i=()=>{let e=(0,a.C)(e=>e.addNotification),r=(0,a.C)(e=>e.removeNotification),s=(0,a.C)(e=>e.clearAllNotifications),i=(0,a.C)(e=>e.unreadNotificationCount),l=(0,t.useCallback)(r=>{e({message:r,type:"success"})},[e]),c=(0,t.useCallback)(r=>{e({message:r,type:"error"})},[e]),n=(0,t.useCallback)(r=>{e({message:r,type:"warning"})},[e]),o=(0,t.useCallback)(r=>{e({message:r,type:"info"})},[e]),d=(0,t.useCallback)((e,r,s)=>{e?l(r):c(s)},[l,c]),m=(0,t.useCallback)((s,t,i=5e3)=>{e({message:t,type:s}),setTimeout(()=>{let e=a.C.getState().notifications.at(-1);e&&e.message===t&&r(e.id)},i)},[e,r]),p=(0,t.useCallback)((r="Loading...")=>{e({message:r,type:"info"});let s=a.C.getState().notifications;return s.at(-1)?.id},[e]),x=(0,t.useCallback)((e,s,t)=>{r(e),s?l(t):c(t)},[r,l,c]);return{clearAllNotifications:s,removeNotification:r,showApiResult:d,showError:c,showInfo:o,showLoading:p,showSuccess:l,showTemporary:m,showWarning:n,unreadCount:i,updateLoadingNotification:x}},l=()=>{let{clearAllNotifications:e,removeNotification:r,showError:s,showInfo:l,showSuccess:c,showWarning:n,unreadCount:o}=i(),d=(0,t.useCallback)((e,r)=>{(0,a.C.getState().addNotification)({...r&&{actionUrl:r},category:"delegation",message:e,type:"delegation-update"})},[]),m=(0,t.useCallback)((e,r)=>{(0,a.C.getState().addNotification)({...r&&{actionUrl:r},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),p=(0,t.useCallback)((e,r)=>{(0,a.C.getState().addNotification)({...r&&{actionUrl:r},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:r,showDelegationUpdate:d,showEmployeeUpdate:(0,t.useCallback)((e,r)=>{(0,a.C.getState().addNotification)({...r&&{actionUrl:r},category:"employee",message:e,type:"employee-update"})},[]),showError:s,showInfo:l,showSuccess:c,showTaskAssigned:p,showVehicleMaintenance:m,showWarning:n,unreadCount:o}}},90164:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\vehicles\\\\[id]\\\\report\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\[id]\\report\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,3871,7048,8390,474,8739,3302,2936,2452],()=>s(63805));module.exports=t})();