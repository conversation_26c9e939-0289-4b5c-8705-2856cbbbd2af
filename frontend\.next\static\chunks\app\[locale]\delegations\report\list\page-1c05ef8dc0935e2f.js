(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1569],{6560:(e,a,s)=>{"use strict";s.d(a,{r:()=>d});var t=s(95155),r=s(50172),l=s(12115),n=s(30285),i=s(54036);let d=l.forwardRef((e,a)=>{let{actionType:s="primary",asChild:l=!1,children:d,className:o,disabled:c,icon:m,isLoading:x=!1,loadingText:u,...p}=e,{className:f,variant:g}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[s];return(0,t.jsx)(n.$,{asChild:l,className:(0,i.cn)(f,o),disabled:x||c,ref:a,variant:g,...p,children:x?(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,t.jsx)(r.A,{className:"mr-2 size-4 animate-spin"}),u||d]}):(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",m&&(0,t.jsx)("span",{className:"mr-2",children:m}),d]})})});d.displayName="ActionButton"},26126:(e,a,s)=>{"use strict";s.d(a,{E:()=>i});var t=s(95155),r=s(74466);s(12115);var l=s(54036);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function i(e){let{className:a,variant:s,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)(n({variant:s}),a),...r})}},29797:(e,a,s)=>{"use strict";s.d(a,{$o:()=>h,Eb:()=>p,Iu:()=>m,WA:()=>f,cU:()=>x,dK:()=>c,n$:()=>u});var t=s(95155),r=s(965),l=s(73158),n=s(3561),i=s(12115),d=s(30285),o=s(54036);let c=i.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:(0,o.cn)("flex justify-center",s),ref:a,...r})});c.displayName="Pagination";let m=i.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("ul",{className:(0,o.cn)("flex flex-row items-center gap-1",s),ref:a,...r})});m.displayName="PaginationContent";let x=i.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("li",{className:(0,o.cn)("",s),ref:a,...r})});x.displayName="PaginationItem";let u=i.forwardRef((e,a)=>{let{className:s,isActive:r,...l}=e;return(0,t.jsx)(d.$,{"aria-current":r?"page":void 0,className:(0,o.cn)("h-9 w-9",s),ref:a,size:"icon",variant:r?"outline":"ghost",...l})});u.displayName="PaginationLink";let p=i.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsxs)(d.$,{className:(0,o.cn)("h-9 w-9 gap-1",s),ref:a,size:"icon",variant:"ghost",...l,children:[(0,t.jsx)(r.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Previous page"})]})});p.displayName="PaginationPrevious";let f=i.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsxs)(d.$,{className:(0,o.cn)("h-9 w-9 gap-1",s),ref:a,size:"icon",variant:"ghost",...r,children:[(0,t.jsx)(l.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Next page"})]})});f.displayName="PaginationNext";let g=i.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",s),ref:a,...r,children:[(0,t.jsx)(n.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"More pages"})]})});function h(e){let{className:a,currentPage:s,onPageChange:r,totalPages:l}=e,n=(()=>{let e=[];e.push(1);let a=Math.max(2,s-1),t=Math.min(l-1,s+1);a>2&&e.push("ellipsis1");for(let s=a;s<=t;s++)e.push(s);return t<l-1&&e.push("ellipsis2"),l>1&&e.push(l),e})();return l<=1?null:(0,t.jsx)(c,{className:a,children:(0,t.jsxs)(m,{children:[(0,t.jsx)(x,{children:(0,t.jsx)(p,{"aria-disabled":1===s?"true":void 0,"aria-label":"Go to previous page",disabled:1===s,onClick:()=>r(s-1)})}),n.map((e,a)=>"ellipsis1"===e||"ellipsis2"===e?(0,t.jsx)(x,{children:(0,t.jsx)(g,{})},"ellipsis-".concat(a)):(0,t.jsx)(x,{children:(0,t.jsx)(u,{"aria-label":"Go to page ".concat(e),isActive:s===e,onClick:()=>r(e),children:e})},"page-".concat(e))),(0,t.jsx)(x,{children:(0,t.jsx)(f,{"aria-disabled":s===l?"true":void 0,"aria-label":"Go to next page",disabled:s===l,onClick:()=>r(s+1)})})]})})}g.displayName="PaginationEllipsis"},30940:()=>{},33271:(e,a,s)=>{"use strict";s.d(a,{k:()=>f});var t=s(95155),r=s(18018),l=s(50172),n=s(68718),i=s(15300),d=s(60679),o=s(12115),c=s(6560),m=s(44838),x=s(53712),u=s(54036),p=s(16146);function f(e){let{className:a,csvData:s,enableCsv:f=!1,entityId:g,fileName:h,reportContentId:b,reportType:N,tableId:j}=e,[v,y]=(0,o.useState)(!1),[w,C]=(0,o.useState)(!1),{showFormSuccess:k,showFormError:A}=(0,x.t6)(),D=async()=>{y(!0);try{let e="/api/reports/".concat(N).concat(g?"/".concat(g):""),a=document.createElement("a");a.href=e,a.download="".concat(h,".pdf"),a.target="_blank",document.body.append(a),a.click(),a.remove(),k({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),A("PDF download failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{y(!1)}},R=async()=>{if(f){C(!0);try{if((null==s?void 0:s.data)&&s.headers)(0,p.og)(s.data,s.headers,"".concat(h,".csv"));else if(j){let e=(0,p.tL)(j);(0,p.og)(e.data,e.headers,"".concat(h,".csv"))}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");k({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),A("CSV generation failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{C(!1)}}},z=v||w;return(0,t.jsxs)("div",{className:(0,u.cn)("flex items-center gap-2 no-print",a),children:[(0,t.jsx)(c.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,t.jsx)(r.A,{className:"size-4"})}),(0,t.jsxs)(m.rI,{children:[(0,t.jsx)(m.ty,{asChild:!0,children:(0,t.jsx)(c.r,{actionType:"secondary","aria-label":"Download report",disabled:z,size:"icon",title:"Download Report",children:z?(0,t.jsx)(l.A,{className:"size-4 animate-spin"}):(0,t.jsx)(n.A,{className:"size-4"})})}),(0,t.jsxs)(m.SQ,{align:"end",children:[(0,t.jsxs)(m._2,{disabled:v,onClick:D,children:[v?(0,t.jsx)(l.A,{className:"mr-2 size-4 animate-spin"}):(0,t.jsx)(i.A,{className:"mr-2 size-4"}),(0,t.jsx)("span",{children:"Download PDF"})]}),f&&(0,t.jsxs)(m._2,{disabled:w,onClick:R,children:[w?(0,t.jsx)(l.A,{className:"mr-2 size-4 animate-spin"}):(0,t.jsx)(d.A,{className:"mr-2 size-4"}),(0,t.jsx)("span",{children:"Download CSV"})]})]})]})]})}},44838:(e,a,s)=>{"use strict";s.d(a,{SQ:()=>x,_2:()=>u,hO:()=>p,lp:()=>f,mB:()=>g,rI:()=>c,ty:()=>m});var t=s(95155),r=s(12115),l=s(48698),n=s(73158),i=s(10518),d=s(70154),o=s(54036);let c=l.bL,m=l.l9;l.YJ,l.ZL,l.Pb,l.z6,r.forwardRef((e,a)=>{let{className:s,inset:r,children:i,...d}=e;return(0,t.jsxs)(l.ZP,{ref:a,className:(0,o.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",s),...d,children:[i,(0,t.jsx)(n.A,{className:"ml-auto"})]})}).displayName=l.ZP.displayName,r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.G5,{ref:a,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",s),...r})}).displayName=l.G5.displayName;let x=r.forwardRef((e,a)=>{let{className:s,sideOffset:r=4,...n}=e;return(0,t.jsx)(l.ZL,{children:(0,t.jsx)(l.UC,{ref:a,sideOffset:r,className:(0,o.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",s),...n})})});x.displayName=l.UC.displayName;let u=r.forwardRef((e,a)=>{let{className:s,inset:r,...n}=e;return(0,t.jsx)(l.q7,{ref:a,className:(0,o.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",s),...n})});u.displayName=l.q7.displayName;let p=r.forwardRef((e,a)=>{let{className:s,children:r,checked:n,...d}=e;return(0,t.jsxs)(l.H_,{ref:a,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...void 0!==n&&{checked:n},...d,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})}),r]})});p.displayName=l.H_.displayName,r.forwardRef((e,a)=>{let{className:s,children:r,...n}=e;return(0,t.jsxs)(l.hN,{ref:a,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(d.A,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=l.hN.displayName;let f=r.forwardRef((e,a)=>{let{className:s,inset:r,...n}=e;return(0,t.jsx)(l.JU,{ref:a,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",s),...n})});f.displayName=l.JU.displayName;let g=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.wv,{ref:a,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",s),...r})});g.displayName=l.wv.displayName},55365:(e,a,s)=>{"use strict";s.d(a,{Fc:()=>d,TN:()=>c,XL:()=>o});var t=s(95155),r=s(74466),l=s(12115),n=s(54036);let i=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),d=l.forwardRef((e,a)=>{let{className:s,variant:r,...l}=e;return(0,t.jsx)("div",{className:(0,n.cn)(i({variant:r}),s),ref:a,role:"alert",...l})});d.displayName="Alert";let o=l.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("h5",{className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",s),ref:a,...r})});o.displayName="AlertTitle";let c=l.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:(0,n.cn)("text-sm [&_p]:leading-relaxed",s),ref:a,...r})});c.displayName="AlertDescription"},59409:(e,a,s)=>{"use strict";s.d(a,{bq:()=>x,eb:()=>g,gC:()=>f,l6:()=>c,yv:()=>m});var t=s(95155),r=s(31992),l=s(79556),n=s(77381),i=s(10518),d=s(12115),o=s(54036);let c=r.bL;r.YJ;let m=r.WT,x=d.forwardRef((e,a)=>{let{children:s,className:n,...i}=e;return(0,t.jsxs)(r.l9,{className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",n),ref:a,...i,children:[s,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(l.A,{className:"size-4 opacity-50"})})]})});x.displayName=r.l9.displayName;let u=d.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.PP,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),ref:a,...l,children:(0,t.jsx)(n.A,{className:"size-4"})})});u.displayName=r.PP.displayName;let p=d.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)(r.wn,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),ref:a,...n,children:(0,t.jsx)(l.A,{className:"size-4"})})});p.displayName=r.wn.displayName;let f=d.forwardRef((e,a)=>{let{children:s,className:l,position:n="popper",...i}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",l),position:n,ref:a,...i,children:[(0,t.jsx)(u,{}),(0,t.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,t.jsx)(p,{})]})})});f.displayName=r.UC.displayName,d.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.JU,{className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),ref:a,...l})}).displayName=r.JU.displayName;let g=d.memo(d.forwardRef((e,a)=>{let{children:s,className:l,...n}=e,c=d.useCallback(e=>{"function"==typeof a?a(e):a&&(a.current=e)},[a]);return(0,t.jsxs)(r.q7,{className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l),ref:c,...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(i.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:s})]})}));g.displayName=r.q7.displayName,d.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.wv,{className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",s),ref:a,...l})}).displayName=r.wv.displayName},62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>n});var t=s(95155),r=s(12115),l=s(54036);let n=r.forwardRef((e,a)=>{let{className:s,type:r,...n}=e;return(0,t.jsx)("input",{className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:a,type:r,...n})});n.displayName="Input"},66695:(e,a,s)=>{"use strict";s.d(a,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>m});var t=s(95155),r=s(12115),l=s(54036);let n=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),ref:a,...r})});n.displayName="Card";let i=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),ref:a,...r})});i.displayName="CardHeader";let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),ref:a,...r})});d.displayName="CardTitle";let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)("text-sm text-muted-foreground",s),ref:a,...r})});o.displayName="CardDescription";let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)("p-6 pt-0",s),ref:a,...r})});c.displayName="CardContent";let m=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)("flex items-center p-6 pt-0",s),ref:a,...r})});m.displayName="CardFooter"},68856:(e,a,s)=>{"use strict";s.d(a,{E:()=>l});var t=s(95155),r=s(54036);function l(e){let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",a),...s})}},77023:(e,a,s)=>{"use strict";s.d(a,{gO:()=>x,jt:()=>g,pp:()=>u});var t=s(95155),r=s(11133),l=s(50172);s(12115);var n=s(6560),i=s(55365),d=s(68856),o=s(54036);let c={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function x(e){let{children:a,className:s,data:r,emptyComponent:l,error:n,errorComponent:i,isLoading:d,loadingComponent:c,onRetry:m}=e;return d?c||(0,t.jsx)(f,{...s&&{className:s},text:"Loading..."}):n?i||(0,t.jsx)(p,{...s&&{className:s},message:n,...m&&{onRetry:m}}):!r||Array.isArray(r)&&0===r.length?l||(0,t.jsx)("div",{className:(0,o.cn)("text-center py-8",s),children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,t.jsx)("div",{className:s,children:a(r)})}function u(e){let{className:a,description:s,icon:r,primaryAction:l,secondaryAction:i,title:d}=e;return(0,t.jsxs)("div",{className:(0,o.cn)("space-y-6 text-center py-12",a),children:[r&&(0,t.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,t.jsx)(r,{className:"h-10 w-10 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:d}),s&&(0,t.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:s})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[l&&(0,t.jsx)(n.r,{actionType:"primary",asChild:!!l.href,icon:l.icon,onClick:l.onClick,children:l.href?(0,t.jsx)("a",{href:l.href,children:l.label}):l.label}),i&&(0,t.jsx)(n.r,{actionType:"tertiary",asChild:!!i.href,icon:i.icon,onClick:i.onClick,children:i.href?(0,t.jsx)("a",{href:i.href,children:i.label}):i.label})]})]})}function p(e){let{className:a,message:s,onRetry:d}=e;return(0,t.jsxs)(i.Fc,{className:(0,o.cn)("my-4",a),variant:"destructive",children:[(0,t.jsx)(r.A,{className:"size-4"}),(0,t.jsx)(i.XL,{children:"Error"}),(0,t.jsx)(i.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:s}),d&&(0,t.jsx)(n.r,{actionType:"tertiary",icon:(0,t.jsx)(l.A,{className:"size-4"}),onClick:d,size:"sm",children:"Try Again"})]})})]})}function f(e){let{className:a,fullPage:s=!1,size:r="md",text:n}=e;return(0,t.jsx)("div",{className:(0,o.cn)("flex items-center justify-center",s&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",a),children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(l.A,{className:(0,o.cn)("animate-spin text-primary",c[r])}),n&&(0,t.jsx)("span",{className:(0,o.cn)("mt-2 text-muted-foreground",m[r]),children:n})]})})}function g(e){let{className:a,count:s=1,testId:r="loading-skeleton",variant:l="default"}=e;return"card"===l?(0,t.jsx)("div",{className:(0,o.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",a),"data-testid":r,children:Array(s).fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,t.jsx)(d.E,{className:"aspect-[16/10] w-full"}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)(d.E,{className:"mb-1 h-7 w-3/4"}),(0,t.jsx)(d.E,{className:"mb-3 h-4 w-1/2"}),(0,t.jsx)(d.E,{className:"my-3 h-px w-full"}),(0,t.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.E,{className:"mr-2.5 size-5 rounded-full"}),(0,t.jsx)(d.E,{className:"h-5 w-2/3"})]},a))})]})]},a))}):"table"===l?(0,t.jsxs)("div",{className:(0,o.cn)("space-y-3",a),"data-testid":r,children:[(0,t.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,a)=>(0,t.jsx)(d.E,{className:"h-8 flex-1"},a))}),Array(s).fill(0).map((e,a)=>(0,t.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,a)=>(0,t.jsx)(d.E,{className:"h-6 flex-1"},a))},a))]}):"list"===l?(0,t.jsx)("div",{className:(0,o.cn)("space-y-3",a),"data-testid":r,children:Array(s).fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(d.E,{className:"size-12 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)(d.E,{className:"h-4 w-1/3"}),(0,t.jsx)(d.E,{className:"h-4 w-full"})]})]},a))}):"stats"===l?(0,t.jsx)("div",{className:(0,o.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",a),"data-testid":r,children:Array(s).fill(0).map((e,a)=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(d.E,{className:"h-5 w-1/3"}),(0,t.jsx)(d.E,{className:"size-5 rounded-full"})]}),(0,t.jsx)(d.E,{className:"mt-3 h-8 w-1/2"}),(0,t.jsx)(d.E,{className:"mt-2 h-4 w-2/3"})]},a))}):(0,t.jsx)("div",{className:(0,o.cn)("space-y-2",a),"data-testid":r,children:Array(s).fill(0).map((e,a)=>(0,t.jsx)(d.E,{className:"h-5 w-full"},a))})}},80760:(e,a,s)=>{Promise.resolve().then(s.bind(s,99178))},83103:()=>{},85127:(e,a,s)=>{"use strict";s.d(a,{A0:()=>i,BF:()=>d,Hj:()=>o,XI:()=>n,nA:()=>m,nd:()=>c});var t=s(95155),r=s(12115),l=s(54036);let n=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{className:(0,l.cn)("w-full caption-bottom text-sm",s),ref:a,...r})})});n.displayName="Table";let i=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("thead",{className:(0,l.cn)("[&_tr]:border-b",s),ref:a,...r})});i.displayName="TableHeader";let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tbody",{className:(0,l.cn)("[&_tr:last-child]:border-0",s),ref:a,...r})});d.displayName="TableBody",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tfoot",{className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),ref:a,...r})}).displayName="TableFooter";let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tr",{className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),ref:a,...r})});o.displayName="TableRow";let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("th",{className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),ref:a,...r})});c.displayName="TableHead";let m=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("td",{className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),ref:a,...r})});m.displayName="TableCell",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("caption",{className:(0,l.cn)("mt-4 text-sm text-muted-foreground",s),ref:a,...r})}).displayName="TableCaption"},99178:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>P});var t=s(95155),r=s(41784),l=s(83343);s(30940),s(83103);var n=s(77381),i=s(79556),d=s(75074),o=s(25318),c=s(98328),m=s(83662),x=s(50286),u=s(35695),p=s(12115),f=s(66695),g=s(54036);let h={Cancelled:{bg:"bg-gradient-to-br from-red-50 to-red-100",border:"border-red-200",text:"text-red-700"},Completed:{bg:"bg-gradient-to-br from-purple-50 to-purple-100",border:"border-purple-200",text:"text-purple-700"},Confirmed:{bg:"bg-gradient-to-br from-green-50 to-green-100",border:"border-green-200",text:"text-green-700"},In_Progress:{bg:"bg-gradient-to-br from-yellow-50 to-yellow-100",border:"border-yellow-200",text:"text-yellow-700"},No_details:{bg:"bg-gradient-to-br from-gray-50 to-gray-100",border:"border-gray-200",text:"text-gray-700"},Planned:{bg:"bg-gradient-to-br from-blue-50 to-blue-100",border:"border-blue-200",text:"text-blue-700"}},b=e=>e.replace("_"," ");function N(e){let{className:a,delegations:s}=e,r=s.length,l=s.reduce((e,a)=>{let s=a.status;return e[s]=(e[s]||0)+1,e},{}),n=s.reduce((e,a)=>{var s;return e+((null==(s=a.delegates)?void 0:s.length)||0)},0),i=Object.entries(l).sort((e,a)=>{let[,s]=e,[,t]=a;return t-s}).map(e=>{let[a]=e;return a});return(0,t.jsxs)("div",{className:(0,g.cn)("mt-6 mb-8",a),children:[(0,t.jsxs)("div",{className:"mb-6 grid grid-cols-2 gap-4 lg:grid-cols-4",children:[(0,t.jsx)(j,{className:"border-slate-200 bg-gradient-to-br from-slate-50 to-slate-100 shadow-sm transition-shadow hover:shadow-md",label:"Total Delegations",textColor:"text-slate-700",value:r,valueColor:"text-slate-800"}),(0,t.jsx)(j,{className:"border-indigo-200 bg-gradient-to-br from-indigo-50 to-indigo-100 shadow-sm transition-shadow hover:shadow-md",label:"Total Delegates",textColor:"text-indigo-700",value:n,valueColor:"text-indigo-800"}),i.slice(0,2).map(e=>{let a=h[e];return(0,t.jsx)(j,{className:(0,g.cn)(a.bg,a.border,"shadow-sm hover:shadow-md transition-shadow"),label:b(e),textColor:a.text,value:l[e],valueColor:a.text},e)})]}),i.length>2&&(0,t.jsx)("div",{className:"grid grid-cols-2 gap-3 sm:grid-cols-3 lg:grid-cols-6",children:i.slice(2).map(e=>{let a=h[e];return(0,t.jsx)(j,{className:(0,g.cn)(a.bg,a.border,"shadow-sm hover:shadow-md transition-shadow"),compact:!0,label:b(e),textColor:a.text,value:l[e],valueColor:a.text},e)})})]})}function j(e){let{className:a,compact:s=!1,label:r,textColor:l="text-gray-600",value:n,valueColor:i="text-gray-800"}=e;return(0,t.jsx)(f.Zp,{className:(0,g.cn)("overflow-hidden border transition-all duration-200",a),children:(0,t.jsxs)(f.Wu,{className:(0,g.cn)("text-center",s?"p-3":"p-4"),children:[(0,t.jsx)("div",{className:(0,g.cn)("font-bold",s?"text-xl mb-1":"text-3xl mb-2",i),children:n.toLocaleString()}),(0,t.jsx)("div",{className:(0,g.cn)("font-medium",s?"text-xs":"text-sm",l),children:r})]})})}var v=s(33271),y=s(26126),w=s(30285),C=s(62523),k=s(77023),A=s(29797),D=s(59409),R=s(85127),z=s(17841),S=s(99673);let F=["Planned","Confirmed","In_Progress","Completed","Cancelled","No_details"],E=e=>{switch(e){case"Cancelled":return"bg-red-100 text-red-800 border-red-300";case"Completed":return"bg-purple-100 text-purple-800 border-purple-300";case"Confirmed":return"bg-green-100 text-green-800 border-green-300";case"In_Progress":return"bg-yellow-100 text-yellow-800 border-yellow-300";case"Planned":return"bg-blue-100 text-blue-800 border-blue-300";default:return"bg-gray-100 text-gray-800 border-gray-300"}},T=e=>{if(!e)return"N/A";try{return(0,r.GP)((0,l.H)(e),"MMM d, yyyy")}catch(e){return"Invalid Date"}};function P(){return(0,t.jsx)(p.Suspense,{fallback:(0,t.jsx)("div",{className:"py-10 text-center",children:"Loading report..."}),children:(0,t.jsx)(_,{})})}function _(){let e=(0,u.useSearchParams)(),{data:a,error:s,isLoading:r,refetch:l}=(0,z.BD)(),h=(0,p.useMemo)(()=>a||[],[a]),[b,j]=(0,p.useState)(""),[P,_]=(0,p.useState)(""),[L,I]=(0,p.useState)("all"),[Z,M]=(0,p.useState)({}),[U,V]=(0,p.useState)(1),[H]=(0,p.useState)(10),[$,B]=(0,p.useState)("durationFrom"),[q,G]=(0,p.useState)("asc");(0,p.useEffect)(()=>{let a=(null==e?void 0:e.get("searchTerm"))||"",s=(null==e?void 0:e.get("status"))||"all";j(a),_(a),I(s)},[e]),(0,p.useEffect)(()=>{let e=setTimeout(()=>{_(b)},300);return()=>clearTimeout(e)},[b]),(0,p.useEffect)(()=>{document.title="Delegation List Report"},[]);let O=(0,p.useCallback)((e,a,s)=>[...e].sort((e,t)=>{let r,l;switch(a){case"delegates":var n,i;r=(null==(n=e.delegates)?void 0:n.length)||0,l=(null==(i=t.delegates)?void 0:i.length)||0;break;case"durationFrom":r=new Date(e.durationFrom).getTime(),l=new Date(t.durationFrom).getTime();break;case"eventName":r=e.eventName.toLowerCase(),l=t.eventName.toLowerCase();break;case"location":r=e.location.toLowerCase(),l=t.location.toLowerCase();break;case"status":r=e.status,l=t.status;break;default:r=e[a],l=t[a]}return null==r||null==l?0:r<l?"asc"===s?-1:1:r>l?"asc"===s?1:-1:0}),[]),J=(0,p.useMemo)(()=>{let e=[...h];if(P){let a=P.toLowerCase();e=e.filter(e=>{var s,t;return e.eventName.toLowerCase().includes(a)||e.location.toLowerCase().includes(a)||(null==(s=e.delegates)?void 0:s.some(e=>e.name.toLowerCase().includes(a)))||(null==(t=e.notes)?void 0:t.toLowerCase().includes(a))||e.status.toLowerCase().includes(a)})}return"all"!==L&&(e=e.filter(e=>e.status===L)),Z.from&&(e=e.filter(e=>new Date(e.durationFrom)>=Z.from)),Z.to&&(e=e.filter(e=>new Date(e.durationFrom)<=Z.to)),O(e,$,q)},[h,P,L,Z,$,q,O]),K=(0,p.useCallback)(e=>{$===e?G("asc"===q?"desc":"asc"):(B(e),G("asc"))},[$,q]),W=(0,p.useCallback)(e=>$!==e?"none":"asc"===q?"ascending":"descending",[$,q]),X=(0,p.useCallback)(()=>{j(""),_(""),I("all"),M({}),V(1)},[]),Y=U*H,Q=Y-H,ee=(0,p.useMemo)(()=>J.slice(Q,Y),[J,Q,Y]),ea=(0,p.useMemo)(()=>Math.ceil(J.length/H),[J.length,H]),es=(0,p.useCallback)(e=>{V(e)},[]),et=(0,p.useCallback)(e=>$!==e?null:"asc"===q?(0,t.jsx)(n.A,{className:"ml-1 inline-block size-4"}):(0,t.jsx)(i.A,{className:"ml-1 inline-block size-4"}),[$,q]);return r?(0,t.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,t.jsx)(k.jt,{count:5,variant:"table"})}):s?(0,t.jsxs)("div",{className:"mx-auto max-w-5xl p-4 text-red-500",children:["Error loading delegations: ",s.message,(0,t.jsx)(w.$,{className:"ml-2",onClick:()=>l(),children:"Retry"})]}):(0,t.jsxs)("div",{className:"delegation-report-container",children:[(0,t.jsx)("div",{className:"no-print mb-6 text-right",children:(0,t.jsx)(v.k,{enableCsv:J.length>0,fileName:"delegations-list-report-".concat(new Date().toISOString().split("T")[0]),reportContentId:"#delegations-list-report-content",reportType:"delegations",tableId:"#delegations-table"})}),(0,t.jsxs)("div",{className:"report-content",id:"delegations-list-report-content",children:[(0,t.jsxs)("header",{className:"delegation-report-header",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"delegation-report-title",children:"Delegation List Report"}),(0,t.jsx)("div",{className:"no-print mx-auto h-1 w-24 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600"})]}),(0,t.jsx)("p",{className:"delegation-report-subtitle",children:b||"all"!==L?"Filtered by: ".concat("all"===L?"":"Status - ".concat((0,S.fZ)(L))).concat(b?("all"===L?"":" | ")+'Search - "'.concat(b,'"'):""):"All Delegations"}),(0,t.jsxs)("p",{className:"delegation-report-date",children:["Generated: ",new Date().toLocaleDateString()," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]}),(0,t.jsx)("div",{className:"no-print delegation-summary-grid",children:(0,t.jsx)(N,{delegations:J.map(e=>{var a;return{...e,delegates:e.delegates||[],escortEmployeeIds:(null==(a=e.escorts)?void 0:a.map(e=>e.employeeId.toString()))||[]}})})}),(0,t.jsxs)("div",{className:"print-only delegation-print-summary",children:[(0,t.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,t.jsx)("span",{className:"delegation-print-summary-label",children:"Total Delegations:"})," ",(0,t.jsx)("span",{className:"delegation-print-summary-value",children:J.length})]}),(0,t.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,t.jsx)("span",{className:"delegation-print-summary-label",children:"Total Delegates:"})," ",(0,t.jsx)("span",{className:"delegation-print-summary-value",children:J.reduce((e,a)=>{var s;return e+((null==(s=a.delegates)?void 0:s.length)||0)},0)})]}),F.map(e=>({count:J.filter(a=>a.status===e).length,status:e})).filter(e=>e.count>0).sort((e,a)=>a.count-e.count).slice(0,3).map(e=>{let{count:a,status:s}=e;return(0,t.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,t.jsxs)("span",{className:"delegation-print-summary-label",children:[(0,S.fZ)(s),":"]})," ",(0,t.jsx)("span",{className:"delegation-print-summary-value",children:a})]},s)}),"all"!==L&&(0,t.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,t.jsx)("span",{className:"delegation-print-summary-label",children:"Filtered by Status:"})," ",(0,t.jsx)("span",{className:"delegation-print-summary-value",children:(0,S.fZ)(L)})]}),b&&(0,t.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,t.jsx)("span",{className:"delegation-print-summary-label",children:"Search Term:"})," ",(0,t.jsxs)("span",{className:"delegation-print-summary-value",children:['"',b,'"']})]})]})]}),(0,t.jsx)("div",{className:"no-print mb-8",children:(0,t.jsx)(f.Zp,{className:"border-0 bg-gradient-to-r from-slate-50 to-gray-50 shadow-lg",children:(0,t.jsxs)(f.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"delegation-filters",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"status-filter",children:"Filter by Status"}),(0,t.jsxs)(D.l6,{"aria-label":"Filter by status",onValueChange:I,value:L,children:[(0,t.jsx)(D.bq,{className:"w-full border-gray-300 bg-white focus:border-blue-500 focus:ring-blue-500",children:(0,t.jsx)(D.yv,{placeholder:"All Statuses"})}),(0,t.jsxs)(D.gC,{children:[(0,t.jsx)(D.eb,{value:"all",children:"All Statuses"}),F.map(e=>(0,t.jsx)(D.eb,{value:e,children:(0,S.fZ)(e)},e))]})]})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"search-input",children:"Search Delegations"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(C.p,{"aria-label":"Search delegations",className:"border-gray-300 bg-white px-10 focus:border-blue-500 focus:ring-blue-500",id:"search-input",onChange:e=>j(e.target.value),placeholder:"Search by event, location, or delegate...",type:"text",value:b}),(0,t.jsx)(d.A,{"aria-hidden":"true",className:"absolute left-3 top-1/2 size-5 -translate-y-1/2 text-gray-400"}),b&&(0,t.jsxs)(w.$,{"aria-label":"Clear search",className:"absolute right-1 top-1/2 size-7 -translate-y-1/2",onClick:()=>j(""),size:"icon",variant:"ghost",children:[(0,t.jsx)(o.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Clear search"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"date-range",children:"Date Range"}),(0,t.jsx)(C.p,{"aria-label":"Date range filter (coming soon)",className:"bg-gray-100 opacity-50",disabled:!0,id:"date-range",placeholder:"Date range filter coming soon",type:"text"})]})]}),(b||"all"!==L)&&(0,t.jsxs)("div",{className:"mt-6 flex items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-4",children:[(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"font-semibold text-blue-800",children:"Active Filters:"}),b&&(0,t.jsxs)("span",{className:"ml-2 rounded bg-blue-100 px-2 py-1 text-xs text-blue-800",children:['Search: "',b,'"']}),"all"!==L&&(0,t.jsxs)("span",{className:"ml-2 rounded bg-green-100 px-2 py-1 text-xs text-green-800",children:["Status:"," ",(0,S.fZ)(L)]})]}),(0,t.jsx)(w.$,{"aria-label":"Reset all filters",className:"border-blue-300 text-blue-700 hover:bg-blue-100",onClick:X,size:"sm",variant:"outline",children:"Reset Filters"})]})]})})}),0===J.length?(0,t.jsx)("div",{className:"rounded-xl border border-gray-200 bg-gradient-to-br from-gray-50 to-slate-100 py-16 text-center",children:(0,t.jsxs)("div",{className:"mx-auto max-w-md",children:[(0,t.jsx)("div",{className:"mb-4 text-gray-400",children:(0,t.jsx)(c.A,{className:"mx-auto mb-4 size-16"})}),(0,t.jsx)("h3",{className:"mb-2 text-lg font-semibold text-gray-700",children:"No delegations found"}),(0,t.jsx)("p",{className:"mb-4 text-gray-500",children:"No delegations match the current filter criteria."}),(0,t.jsx)(w.$,{"aria-label":"Reset filters to show all delegations",className:"mt-2",onClick:X,size:"lg",variant:"outline",children:"Reset Filters"})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"delegation-table-container",children:(0,t.jsxs)(R.XI,{className:"delegation-table",id:"delegations-table",children:[(0,t.jsx)(R.A0,{children:(0,t.jsxs)(R.Hj,{className:"border-b border-gray-200 bg-gradient-to-r from-slate-100 to-gray-100",children:[(0,t.jsxs)(R.nd,{"aria-label":"Sort by event name","aria-sort":W("eventName"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>K("eventName"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),K("eventName"))},role:"columnheader",style:{width:"25%"},tabIndex:0,children:["Event Name ",et("eventName")]}),(0,t.jsxs)(R.nd,{"aria-label":"Sort by location","aria-sort":W("location"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>K("location"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),K("location"))},role:"columnheader",style:{width:"20%"},tabIndex:0,children:["Location ",et("location")]}),(0,t.jsxs)(R.nd,{"aria-label":"Sort by duration","aria-sort":W("durationFrom"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>K("durationFrom"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),K("durationFrom"))},role:"columnheader",style:{width:"20%"},tabIndex:0,children:["Duration ",et("durationFrom")]}),(0,t.jsxs)(R.nd,{"aria-label":"Sort by status","aria-sort":W("status"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>K("status"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),K("status"))},role:"columnheader",style:{width:"10%"},tabIndex:0,children:["Status ",et("status")]}),(0,t.jsxs)(R.nd,{"aria-label":"Sort by number of delegates","aria-sort":W("delegates"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>K("delegates"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),K("delegates"))},role:"columnheader",style:{width:"25%"},tabIndex:0,children:["Delegates ",et("delegates")]})]})}),(0,t.jsx)(R.BF,{className:"no-print",children:ee.map((e,a)=>{var s,r,l,n,i,d,o;return(0,t.jsxs)(R.Hj,{className:(0,g.cn)("page-break-inside-avoid hover:bg-slate-50 transition-colors border-b border-gray-100",a%2==0?"bg-white":"bg-slate-50/30"),children:[(0,t.jsx)(R.nA,{className:"print-text-wrap p-4 font-medium",title:e.eventName,children:(0,t.jsx)("div",{className:"font-semibold text-gray-800",children:e.eventName})}),(0,t.jsx)(R.nA,{className:"print-text-wrap print-location-col p-4",title:e.location,children:(0,t.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,t.jsx)(m.A,{className:"mr-2 size-4 text-gray-400"}),e.location]})}),(0,t.jsx)(R.nA,{className:"whitespace-nowrap p-4",children:(0,t.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,t.jsx)(c.A,{className:"mr-2 size-4 text-gray-400"}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("div",{children:T(e.durationFrom)}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["to ",T(e.durationTo)]})]})]})}),(0,t.jsx)(R.nA,{className:"p-4",children:(0,t.jsx)(y.E,{className:(0,g.cn)("text-xs py-1 px-2 font-medium",E(e.status)),children:(0,S.fZ)(e.status)})}),(0,t.jsx)(R.nA,{className:"print-text-wrap max-w-xs p-4",title:null==(s=e.delegates)?void 0:s.map(e=>e.name).join(", "),children:((null==(r=e.delegates)?void 0:r.length)||0)>0?(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(x.A,{className:"mr-2 mt-0.5 size-4 shrink-0 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"no-print",children:3>=((null==(l=e.delegates)?void 0:l.length)||0)?(0,t.jsx)("div",{className:"space-y-1",children:null==(n=e.delegates)?void 0:n.map((e,a)=>(0,t.jsx)("div",{className:"text-sm text-gray-700",children:e.name},e.id||a))}):(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"mb-1 space-y-1",children:null==(i=e.delegates)?void 0:i.slice(0,2).map((e,a)=>(0,t.jsx)("div",{className:"text-sm text-gray-700",children:e.name},e.id||a))}),(0,t.jsxs)("span",{className:"rounded bg-gray-100 px-2 py-1 text-xs text-gray-500",children:["+",((null==(d=e.delegates)?void 0:d.length)||0)-2," ","more"]})]})}),(0,t.jsx)("span",{className:"print-only",children:(0,t.jsx)("div",{className:"space-y-1",children:null==(o=e.delegates)?void 0:o.map((e,a)=>(0,t.jsx)("div",{className:"text-sm",children:e.name},e.id||a))})})]})]}):(0,t.jsx)("span",{className:"text-sm text-gray-400",children:"No delegates"})})]},e.id)})}),(0,t.jsx)(R.BF,{className:"print-only",children:J.map((e,a)=>{var s,r,l;return(0,t.jsxs)(R.Hj,{className:(0,g.cn)("page-break-inside-avoid",a%2==0?"bg-white":"bg-slate-50/30"),children:[(0,t.jsx)(R.nA,{className:"print-text-wrap font-medium",title:e.eventName,children:(0,t.jsx)("div",{className:"font-semibold text-gray-800",children:e.eventName})}),(0,t.jsx)(R.nA,{className:"print-text-wrap print-location-col",title:e.location,children:(0,t.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,t.jsx)(m.A,{className:"mr-2 size-4 text-gray-400"}),e.location]})}),(0,t.jsx)(R.nA,{className:"",children:(0,t.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,t.jsx)(c.A,{className:"mr-2 size-4 text-gray-400"}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("div",{children:T(e.durationFrom)}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["to ",T(e.durationTo)]})]})]})}),(0,t.jsx)(R.nA,{className:"",children:(0,t.jsx)(y.E,{className:(0,g.cn)("text-xs py-1 px-2 font-medium",E(e.status)),children:(0,S.fZ)(e.status)})}),(0,t.jsx)(R.nA,{className:"print-text-wrap",title:null==(s=e.delegates)?void 0:s.map(e=>e.name).join(", "),children:((null==(r=e.delegates)?void 0:r.length)||0)>0?(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(x.A,{className:"mr-2 mt-0.5 size-4 shrink-0 text-gray-400"}),(0,t.jsx)("div",{children:(0,t.jsx)("div",{className:"space-y-1",children:null==(l=e.delegates)?void 0:l.map((e,a)=>(0,t.jsx)("div",{className:"text-sm",children:e.name},e.id||a))})})]}):(0,t.jsx)("span",{className:"text-sm text-gray-400",children:"No delegates"})})]},e.id)})})]})}),J.length>H&&(0,t.jsx)("div",{className:"no-print mt-8 flex justify-center",children:(0,t.jsx)(A.$o,{currentPage:U,onPageChange:es,totalPages:ea})})]}),(0,t.jsx)("footer",{className:"delegation-report-footer",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("p",{className:"font-medium",children:["Report generated on: ",new Date().toLocaleDateString()," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]}),(0,t.jsx)("p",{className:"text-gray-400",children:"WorkHub - Delegation Management"}),(0,t.jsx)("p",{className:"print-only text-xs",children:"Confidential - For internal use only"})]})})]})]})}},99673:(e,a,s)=>{"use strict";function t(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function r(e){var a,s;if(null==(a=e.fullName)?void 0:a.trim())return e.fullName.trim();if(null==(s=e.name)?void 0:s.trim()){let a=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(a.toLowerCase())||a.includes("_")){let e=a.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(e," (Role)")}return a}if(e.role){let a=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(a," (Role)")}return"Unknown Employee"}function l(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function n(e){return e.replaceAll("_"," ")}s.d(a,{DV:()=>r,fZ:()=>t,s:()=>l,vq:()=>n})}},e=>{var a=a=>e(e.s=a);e.O(0,[5866,6476,7047,6897,3860,9664,375,7876,1859,5247,7998,4036,4767,303,5067,7841,8441,1684,7358],()=>a(80760)),_N_E=e.O()}]);