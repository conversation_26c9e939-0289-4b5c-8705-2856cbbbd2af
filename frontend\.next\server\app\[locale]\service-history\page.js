(()=>{var e={};e.id=2796,e.ids=[2796],e.modules={997:(e,r,s)=>{"use strict";s.d(r,{k:()=>f});var t=s(60687),i=s(28946),n=s(11516),a=s(20620),l=s(36644);let o=(0,s(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var c=s(43210),d=s(68752),u=s(21342),h=s(3940),p=s(22482),m=s(22364);function f({className:e,csvData:r,enableCsv:s=!1,entityId:f,fileName:x,reportContentId:v,reportType:y,tableId:j}){let[g,b]=(0,c.useState)(!1),[S,_]=(0,c.useState)(!1),{showFormSuccess:N,showFormError:C}=(0,h.t6)(),w=async()=>{b(!0);try{let e=`/api/reports/${y}${f?`/${f}`:""}`,r=document.createElement("a");r.href=e,r.download=`${x}.pdf`,r.target="_blank",document.body.append(r),r.click(),r.remove(),N({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),C(`PDF download failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{b(!1)}},k=async()=>{if(s){_(!0);try{if(r?.data&&r.headers)(0,m.og)(r.data,r.headers,`${x}.csv`);else if(j){let e=(0,m.tL)(j);(0,m.og)(e.data,e.headers,`${x}.csv`)}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");N({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),C(`CSV generation failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{_(!1)}}},A=g||S;return(0,t.jsxs)("div",{className:(0,p.cn)("flex items-center gap-2 no-print",e),children:[(0,t.jsx)(d.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,t.jsx)(i.A,{className:"size-4"})}),(0,t.jsxs)(u.rI,{children:[(0,t.jsx)(u.ty,{asChild:!0,children:(0,t.jsx)(d.r,{actionType:"secondary","aria-label":"Download report",disabled:A,size:"icon",title:"Download Report",children:A?(0,t.jsx)(n.A,{className:"size-4 animate-spin"}):(0,t.jsx)(a.A,{className:"size-4"})})}),(0,t.jsxs)(u.SQ,{align:"end",children:[(0,t.jsxs)(u._2,{disabled:g,onClick:w,children:[g?(0,t.jsx)(n.A,{className:"mr-2 size-4 animate-spin"}):(0,t.jsx)(l.A,{className:"mr-2 size-4"}),(0,t.jsx)("span",{children:"Download PDF"})]}),s&&(0,t.jsxs)(u._2,{disabled:S,onClick:k,children:[S?(0,t.jsx)(n.A,{className:"mr-2 size-4 animate-spin"}):(0,t.jsx)(o,{className:"mr-2 size-4"}),(0,t.jsx)("span",{children:"Download CSV"})]})]})]})]})}},2664:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\service-history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\service-history\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12193:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var t=s(65239),i=s(48088),n=s(88170),a=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(r,o);let c={children:["",{children:["[locale]",{children:["service-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2664)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\service-history\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\service-history\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/service-history/page",pathname:"/[locale]/service-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},12662:(e,r,s)=>{"use strict";s.d(r,{AppBreadcrumb:()=>u});var t=s(60687),i=s(85814),n=s.n(i),a=s(16189),l=s(43210),o=s.n(l),c=s(70640),d=s(22482);function u({className:e,homeHref:r="/",homeLabel:s="Dashboard",showContainer:i=!0}){let l=(0,a.usePathname)(),u=l?l.split("/").filter(Boolean):[],h=e=>{if(/^\d+$/.test(e))return`ID: ${e}`;if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let r={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return r[e]?r[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},p=u.map((e,r)=>{let s="/"+u.slice(0,r+1).join("/"),i=r===u.length-1,a=h(e);return(0,t.jsxs)(o().Fragment,{children:[(0,t.jsx)(c.BreadcrumbItem,{children:i?(0,t.jsx)(c.BreadcrumbPage,{className:"font-medium text-foreground",children:a}):(0,t.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,t.jsx)(n(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:a})})}),!i&&(0,t.jsx)(c.BreadcrumbSeparator,{})]},s)}),m=(0,t.jsx)(c.Breadcrumb,{className:(0,d.cn)("text-sm",e),children:(0,t.jsxs)(c.BreadcrumbList,{className:"flex-wrap",children:[(0,t.jsx)(c.BreadcrumbItem,{children:(0,t.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,t.jsx)(n(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:r,children:s})})}),u.length>0&&(0,t.jsx)(c.BreadcrumbSeparator,{}),p]})});return i?(0,t.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,t.jsx)("div",{className:"flex items-center",children:m})}):m}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20620:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38237:(e,r,s)=>{Promise.resolve().then(s.bind(s,2664))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70474:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>E});var t=s(60687),i=s(76180),n=s.n(i),a=s(44610),l=s(35265),o=s(78726),c=s(41936),d=s(24920),u=s(29333),h=s(85814),p=s.n(h),m=s(43210),f=s(95668);let x=({children:e,fallback:r})=>(0,t.jsx)(f.A,{description:"An unexpected error occurred while loading service records.",fallback:r,onError:(e,r)=>{console.error("ServiceRecords component error:",e),console.error("Component stack:",r.componentStack)},resetLabel:"Try Again",title:"Error Loading Service Records",children:e});var v=s(997),y=s(24847),j=s(68752),g=s(12662),b=s(96834),S=s(29523),_=s(44493),N=s(89667),C=s(80013),w=s(52027),k=s(48041),A=s(15079),R=s(85726),z=s(63213),F=s(2775),T=s(72273);let P=["Oil Change","Tire Rotation","Brake Service","Battery Replacement","Air Filter Replacement","Cabin Air Filter Replacement","Wiper Blade Replacement","Fluid Check/Top-up","Spark Plug Replacement","Coolant Flush","Transmission Service","Wheel Alignment","State Inspection","Other"];function E(){let{loading:e,session:r,user:s}=(0,z.useAuthContext)(),i=!!s&&!!r?.access_token,{data:h,error:E,isLoading:q,refetch:B}=(0,T.T$)({enabled:i&&!e}),D=(0,m.useMemo)(()=>h||[],[h]),{data:I,error:L,isLoading:M,refetch:$}=(0,F.fs)({enabled:i&&!e}),V=(0,m.useMemo)(()=>I||[],[I]),[O,H]=(0,m.useState)("all"),[G,W]=(0,m.useState)("all"),[Z,J]=(0,m.useState)(""),[Y,X]=(0,m.useState)(""),K=(0,m.useMemo)(()=>{let e=new Set;return V.forEach(r=>{r.servicePerformed.forEach(r=>e.add(r))}),P.forEach(r=>e.add(r)),[...e].sort()},[V]),Q=(0,m.useMemo)(()=>{let e=[...V];if("all"!==O&&(e=e.filter(e=>String(e.vehicleId)===O)),"all"!==G&&(e=e.filter(e=>e.servicePerformed.includes(G))),Y){let r=Y.toLowerCase();e=e.filter(e=>`${e.vehicleMake} ${e.vehicleModel}`.toLowerCase().includes(r)||e.servicePerformed.join(" ").toLowerCase().includes(r)||e.notes?.toLowerCase().includes(r)||e.licensePlate?.toLowerCase().includes(r)||e.odometer.toString().includes(r))}return e},[V,O,G,Y]),U=(0,m.useCallback)(()=>{"function"==typeof B&&B(),"function"==typeof $&&$()},[B,$]),ee=e||q||M,er=(0,m.useMemo)(()=>{let e=[];return E&&e.push(`Vehicles: ${E.message}`),L&&e.push(`Service Records: ${L.message}`),e.length>0?e.join("; "):null},[E,L]);if(ee)return(0,t.jsx)(f.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(k.z,{icon:a.A,title:"Service History",children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(R.E,{className:"h-10 w-32"}),(0,t.jsx)(R.E,{className:"h-10 w-32"})]})}),(0,t.jsx)(_.Zp,{children:(0,t.jsx)(_.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"mb-6 grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,t.jsx)(R.E,{className:"h-10 w-full"}),(0,t.jsx)(R.E,{className:"h-10 w-full"}),(0,t.jsx)(R.E,{className:"h-10 w-full"})]})})}),(0,t.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,r)=>(0,t.jsxs)("div",{className:"flex items-center space-x-4 border-b p-4",children:[(0,t.jsx)(R.E,{className:"h-6 w-1/6"}),(0,t.jsx)(R.E,{className:"h-6 w-1/6"}),(0,t.jsx)(R.E,{className:"h-6 w-2/6"}),(0,t.jsx)(R.E,{className:"h-6 w-1/6"}),(0,t.jsx)(R.E,{className:"h-6 w-1/6"})]},r))})]})});if(!e&&!i)return(0,t.jsx)(f.A,{children:(0,t.jsxs)("div",{className:"space-y-6 text-center",children:[(0,t.jsx)(k.z,{icon:a.A,title:"Access Denied"}),(0,t.jsx)("p",{children:"Please sign in to view service history."})]})});if(!ee&&i&&0===Q.length&&!er){let e="all"!==O||"all"!==G||Z;return(0,t.jsx)(f.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(g.AppBreadcrumb,{}),(0,t.jsx)(k.z,{description:"View and manage all service records for your vehicles.",icon:a.A,title:"Service History Report",children:(0,t.jsx)("div",{className:"no-print flex gap-2",children:(0,t.jsx)(j.r,{actionType:"tertiary",asChild:!0,icon:(0,t.jsx)(l.A,{className:"size-4"}),children:(0,t.jsx)(p(),{href:"/vehicles",children:"Log New Service"})})})}),(0,t.jsx)(w.pp,{description:e?"There are no service records matching your current filters. You can log a new service record or adjust your filters.":"No service records have been logged yet. Get started by logging your first service record.",icon:a.A,primaryAction:{href:"/vehicles",icon:(0,t.jsx)(l.A,{className:"size-4"}),label:"Log New Service"},title:"No Service Records Found",...e&&{secondaryAction:{icon:(0,t.jsx)(o.A,{className:"size-4"}),label:"Reset Filters",onClick:()=>{H("all"),W("all"),J("")}}}})]})})}return(0,t.jsx)(f.A,{children:(0,t.jsxs)("div",{className:"jsx-fb311328cf4436d9 print-container space-y-6",children:[(0,t.jsx)(g.AppBreadcrumb,{}),(0,t.jsx)(k.z,{description:"View and manage all service records for your vehicles.",icon:a.A,title:"Service History Report",children:(0,t.jsxs)("div",{className:"jsx-fb311328cf4436d9 no-print flex gap-2",children:[(0,t.jsx)(j.r,{actionType:"tertiary",asChild:!0,icon:(0,t.jsx)(l.A,{className:"size-4"}),children:(0,t.jsx)(p(),{href:"/vehicles",children:"Log New Service"})}),(0,t.jsx)(v.k,{enableCsv:Q.length>0,fileName:`service-history-report-${new Date().toISOString().split("T")[0]}`,reportContentId:"#service-history-report-content",reportType:"service-history",tableId:"#service-history-table"})]})}),(0,t.jsxs)(_.Zp,{className:"no-print shadow-sm",children:[(0,t.jsxs)(_.aR,{className:"pb-2",children:[(0,t.jsx)(_.ZB,{className:"text-lg",children:"Filter Options"}),(0,t.jsx)(_.BT,{children:"Filter service records by vehicle, service type, or search terms"})]}),(0,t.jsxs)(_.Wu,{children:[(0,t.jsxs)("div",{className:"jsx-fb311328cf4436d9 filter-grid grid grid-cols-1 items-end gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsxs)("div",{className:"jsx-fb311328cf4436d9",children:[(0,t.jsx)(C.J,{htmlFor:"vehicle-filter",children:"Filter by Vehicle"}),(0,t.jsxs)(A.l6,{"aria-label":"Filter by vehicle",onValueChange:H,value:O,children:[(0,t.jsx)(A.bq,{className:"mt-1.5 w-full",id:"vehicle-filter",children:(0,t.jsx)(A.yv,{placeholder:"All Vehicles"})}),(0,t.jsxs)(A.gC,{children:[(0,t.jsx)(A.eb,{value:"all",children:"All Vehicles"}),D.map(e=>(0,t.jsxs)(A.eb,{value:String(e.id),children:[e.make," ",e.model," (",e.year,")"]},e.id))]})]})]}),(0,t.jsxs)("div",{className:"jsx-fb311328cf4436d9",children:[(0,t.jsx)(C.J,{htmlFor:"service-filter",children:"Filter by Service Type"}),(0,t.jsxs)(A.l6,{"aria-label":"Filter by service type",onValueChange:W,value:G,children:[(0,t.jsx)(A.bq,{className:"mt-1.5 w-full",id:"service-filter",children:(0,t.jsx)(A.yv,{placeholder:"All Services"})}),(0,t.jsxs)(A.gC,{children:[(0,t.jsx)(A.eb,{value:"all",children:"All Services"}),K.map(e=>(0,t.jsx)(A.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{className:"jsx-fb311328cf4436d9",children:[(0,t.jsx)(C.J,{htmlFor:"search-records",children:"Search Records"}),(0,t.jsxs)("div",{className:"jsx-fb311328cf4436d9 relative mt-1.5",children:[(0,t.jsx)(N.p,{"aria-label":"Search service records",className:"px-10",id:"search-records",onChange:e=>J(e.target.value),placeholder:"Search by keyword, notes, plate...",type:"text",value:Z}),(0,t.jsx)(c.A,{"aria-hidden":"true",className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),Z&&(0,t.jsxs)(S.$,{"aria-label":"Clear search",className:"absolute right-1 top-1/2 size-7 -translate-y-1/2",onClick:()=>J(""),size:"icon",variant:"ghost",children:[(0,t.jsx)(o.A,{className:"size-4"}),(0,t.jsx)("span",{className:"jsx-fb311328cf4436d9 sr-only",children:"Clear search"})]})]})]})]}),("all"!==O||"all"!==G||Z)&&(0,t.jsxs)("div",{className:"jsx-fb311328cf4436d9 mt-6 flex flex-wrap items-center justify-between rounded-md border border-border p-3",children:[(0,t.jsxs)("div",{className:"jsx-fb311328cf4436d9 flex flex-wrap items-center gap-2",children:[(0,t.jsx)("span",{className:"jsx-fb311328cf4436d9 text-sm font-medium",children:"Active Filters:"}),"all"!==O&&(0,t.jsxs)(b.E,{className:"flex items-center gap-1",variant:"outline",children:[(0,t.jsx)(d.A,{className:"size-3"}),D.find(e=>String(e.id)===O)?.make," ",D.find(e=>String(e.id)===O)?.model]}),"all"!==G&&(0,t.jsxs)(b.E,{className:"flex items-center gap-1",variant:"outline",children:[(0,t.jsx)(u.A,{className:"size-3"}),G]}),Z&&(0,t.jsxs)(b.E,{className:"flex items-center gap-1",variant:"outline",children:[(0,t.jsx)(c.A,{className:"size-3"}),'"',Z,'"']})]}),(0,t.jsxs)(S.$,{"aria-label":"Reset all filters",className:"mt-2 sm:mt-0",onClick:()=>{H("all"),W("all"),J("")},size:"sm",variant:"outline",children:[(0,t.jsx)(o.A,{className:"mr-1 size-3"}),"Reset Filters"]})]})]})]}),(0,t.jsxs)("div",{id:"service-history-report-content",className:"jsx-fb311328cf4436d9 report-content",children:[(0,t.jsx)(x,{children:(0,t.jsx)(y.R,{error:er,isLoading:M&&!I,onRetry:U,records:Q,showVehicleInfo:!0,vehicleSpecific:!1})}),(0,t.jsxs)("footer",{className:"jsx-fb311328cf4436d9 mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,t.jsxs)("p",{className:"jsx-fb311328cf4436d9",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,t.jsx)("p",{className:"jsx-fb311328cf4436d9",children:"WorkHub - Vehicle Service Management"})]})]}),(0,t.jsx)(n(),{id:"fb311328cf4436d9",children:""})]})})}},70640:(e,r,s)=>{"use strict";s.d(r,{Breadcrumb:()=>o,BreadcrumbItem:()=>d,BreadcrumbLink:()=>u,BreadcrumbList:()=>c,BreadcrumbPage:()=>h,BreadcrumbSeparator:()=>p});var t=s(60687),i=s(8730),n=s(74158),a=(s(69795),s(43210)),l=s(22482);let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("nav",{"aria-label":"breadcrumb",className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),ref:s,...r}));o.displayName="Breadcrumb";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("ol",{className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),ref:s,...r}));c.displayName="BreadcrumbList";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("li",{className:(0,l.cn)("inline-flex items-center gap-1.5",e),ref:s,...r}));d.displayName="BreadcrumbItem";let u=a.forwardRef(({asChild:e,className:r,...s},n)=>{let a=e?i.DX:"a";return(0,t.jsx)(a,{className:(0,l.cn)("transition-colors hover:text-foreground",r),ref:n,...s})});u.displayName="BreadcrumbLink";let h=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,l.cn)("font-normal text-foreground",e),ref:s,role:"link",...r}));h.displayName="BreadcrumbPage";let p=({children:e,className:r,...s})=>(0,t.jsx)("span",{"aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",r),role:"presentation",...s,children:e??(0,t.jsx)(n.A,{className:"size-4"})});p.displayName="BreadcrumbSeparator"},74075:e=>{"use strict";e.exports=require("zlib")},75913:(e,r,s)=>{"use strict";s(56397);var t=s(43210),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(t),n="undefined"!=typeof process&&process.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var r=void 0===e?{}:e,s=r.name,t=void 0===s?"stylesheet":s,i=r.optimizeForSpeed,l=void 0===i?n:i;o(a(t),"`name` must be a string"),this._name=t,this._deletedRulePlaceholder="#"+t+"-deleted-rule____{}",o("boolean"==typeof l,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=l,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var r,s=e.prototype;return s.setOptimizeForSpeed=function(e){o("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),o(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;o(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(r,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:r}:e._serverSheet.cssRules.push({cssText:r}),s},deleteRule:function(r){e._serverSheet.cssRules[r]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===e)return document.styleSheets[r]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,r){return o(a(e),"`insertRule` accepts only strings"),"number"!=typeof r&&(r=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,r),this._rulesCount++},s.replaceRule=function(e,r){this._optimizeForSpeed;var s=this._serverSheet;if(r.trim()||(r=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(r,e)}catch(t){n||console.warn("StyleSheet: illegal rule: \n\n"+r+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}return e},s.deleteRule=function(e){this._serverSheet.deleteRule(e)},s.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},s.cssRules=function(){return this._serverSheet.cssRules},s.makeStyleTag=function(e,r,s){r&&o(a(r),"makeStyleTag accepts only strings as second parameter");var t=document.createElement("style");this._nonce&&t.setAttribute("nonce",this._nonce),t.type="text/css",t.setAttribute("data-"+e,""),r&&t.appendChild(document.createTextNode(r));var i=document.head||document.getElementsByTagName("head")[0];return s?i.insertBefore(t,s):i.appendChild(t),t},r=[{key:"length",get:function(){return this._rulesCount}}],function(e,r){for(var s=0;s<r.length;s++){var t=r[s];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}(e.prototype,r),e}();function o(e,r){if(!e)throw Error("StyleSheet: "+r+".")}var c=function(e){for(var r=5381,s=e.length;s;)r=33*r^e.charCodeAt(--s);return r>>>0},d={};function u(e,r){if(!r)return"jsx-"+e;var s=String(r),t=e+s;return d[t]||(d[t]="jsx-"+c(e+"-"+s)),d[t]}function h(e,r){var s=e+(r=r.replace(/\/style/gi,"\\/style"));return d[s]||(d[s]=r.replace(/__jsx-style-dynamic-selector/g,e)),d[s]}var p=function(){function e(e){var r=void 0===e?{}:e,s=r.styleSheet,t=void 0===s?null:s,i=r.optimizeForSpeed,n=void 0!==i&&i;this._sheet=t||new l({name:"styled-jsx",optimizeForSpeed:n}),this._sheet.inject(),t&&"boolean"==typeof n&&(this._sheet.setOptimizeForSpeed(n),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var r=e.prototype;return r.add=function(e){var r=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var s=this.getIdAndRules(e),t=s.styleId,i=s.rules;if(t in this._instancesCounts){this._instancesCounts[t]+=1;return}var n=i.map(function(e){return r._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[t]=n,this._instancesCounts[t]=1},r.remove=function(e){var r=this,s=this.getIdAndRules(e).styleId;if(function(e,r){if(!e)throw Error("StyleSheetRegistry: "+r+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var t=this._fromServer&&this._fromServer[s];t?(t.parentNode.removeChild(t),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return r._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},r.update=function(e,r){this.add(r),this.remove(e)},r.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},r.cssRules=function(){var e=this,r=this._fromServer?Object.keys(this._fromServer).map(function(r){return[r,e._fromServer[r]]}):[],s=this._sheet.cssRules();return r.concat(Object.keys(this._indices).map(function(r){return[r,e._indices[r].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},r.styles=function(e){var r,s;return r=this.cssRules(),void 0===(s=e)&&(s={}),r.map(function(e){var r=e[0],t=e[1];return i.default.createElement("style",{id:"__"+r,key:"__"+r,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:t}})})},r.getIdAndRules=function(e){var r=e.children,s=e.dynamic,t=e.id;if(s){var i=u(t,s);return{styleId:i,rules:Array.isArray(r)?r.map(function(e){return h(i,e)}):[h(i,r)]}}return{styleId:u(t),rules:Array.isArray(r)?r:[r]}},r.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,r){return e[r.id.slice(2)]=r,e},{})},e}(),m=t.createContext(null);m.displayName="StyleSheetContext";i.default.useInsertionEffect||i.default.useLayoutEffect;var f=void 0;function x(e){var r=f||t.useContext(m);return r&&r.add(e),null}x.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},r.style=x},76180:(e,r,s)=>{"use strict";e.exports=s(75913).style},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},82205:(e,r,s)=>{Promise.resolve().then(s.bind(s,70474))},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,3871,7048,8390,2670,4897,8739,3302,2936,742,2452,5009,5335],()=>s(12193));module.exports=t})();