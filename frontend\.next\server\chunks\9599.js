"use strict";exports.id=9599,exports.ids=[5176,9599],exports.modules={19599:(e,t,a)=>{a.d(t,{Ar:()=>y,Db:()=>g,FT:()=>f,nR:()=>u,sZ:()=>m,uC:()=>p});var i=a(8693),o=a(54050),l=a(46349),n=a(87676),r=a(52765),s=a(38118),c=a(75176);let d={all:["employees"],detail:e=>["employees",e]},u=e=>(0,l.GK)([...d.all],async()=>(await c.employeeApiService.getAll()).data,"employee",{staleTime:3e5,...e}),m=(e,t)=>(0,l.GK)(e?["employees","role",e]:[...d.all],async()=>e?await c.employeeApiService.getByRole(e):(await c.employeeApiService.getAll()).data,"employee",{staleTime:3e5,...t}),p=e=>(0,l.GK)([...d.detail(e)],async()=>await c.employeeApiService.getById(e),"employee",{enabled:!!e,staleTime:3e5}),y=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:a}=(0,n.useNotifications)();return(0,o.n)({mutationFn:async e=>{let t=r.A.toCreateRequest(e);return await c.employeeApiService.create(t)},onError:(a,i,o)=>{o?.previousEmployees&&e.setQueryData(d.all,o.previousEmployees),t(`Failed to create employee: ${a.message||"Unknown error occurred"}`)},onMutate:async t=>{await e.cancelQueries({queryKey:d.all});let a=e.getQueryData(d.all);return e.setQueryData(d.all,(e=[])=>{let a="optimistic-"+Date.now().toString(),i=new Date().toISOString();return[...e,{availability:(0,s.d$)(t.availability),contactEmail:(0,s.d$)(t.contactEmail),contactInfo:t.contactInfo,contactMobile:(0,s.d$)(t.contactMobile),contactPhone:(0,s.d$)(t.contactPhone),createdAt:i,currentLocation:(0,s.d$)(t.currentLocation),department:(0,s.d$)(t.department),employeeId:t.employeeId,fullName:t.fullName||t.name,generalAssignments:t.generalAssignments||[],hireDate:(0,s.d$)(t.hireDate),id:Number(a.replace("optimistic-","")),name:t.name,notes:(0,s.d$)(t.notes),position:(0,s.d$)(t.position),profileImageUrl:(0,s.d$)(t.profileImageUrl),role:t.role,shiftSchedule:(0,s.d$)(t.shiftSchedule),skills:t.skills||[],status:(0,s.d$)(t.status),updatedAt:i,...void 0!==t.workingHours&&{workingHours:(0,s.d$)(t.workingHours)}}]}),{previousEmployees:a}},onSettled:()=>{e.invalidateQueries({queryKey:d.all})},onSuccess:e=>{a(`Employee "${e.name}" has been created successfully!`)}})},g=()=>{let e=(0,i.jE)();return(0,o.n)({mutationFn:async({data:e,id:t})=>{let a=r.A.toUpdateRequest(e);return await c.employeeApiService.update(t,a)},onError:(t,a,i)=>{i?.previousEmployee&&e.setQueryData(d.detail(a.id),i.previousEmployee),i?.previousEmployeesList&&e.setQueryData(d.all,i.previousEmployeesList),console.error("Failed to update employee:",t)},onMutate:async({data:t,id:a})=>{await e.cancelQueries({queryKey:d.all}),await e.cancelQueries({queryKey:d.detail(a)});let i=e.getQueryData(d.detail(a)),o=e.getQueryData(d.all);return e.setQueryData(d.detail(a),e=>{if(!e)return e;let a=new Date().toISOString();return{...e,availability:(0,s.d$)(void 0===t.availability?e.availability:t.availability),contactEmail:(0,s.d$)(void 0===t.contactEmail?e.contactEmail:t.contactEmail),contactInfo:t.contactInfo??e.contactInfo,contactMobile:(0,s.d$)(void 0===t.contactMobile?e.contactMobile:t.contactMobile),contactPhone:(0,s.d$)(void 0===t.contactPhone?e.contactPhone:t.contactPhone),currentLocation:(0,s.d$)(void 0===t.currentLocation?e.currentLocation:t.currentLocation),department:(0,s.d$)(t.department??e.department),employeeId:t.employeeId??e.employeeId,fullName:(0,s.d$)(t.fullName??e.fullName),generalAssignments:t.generalAssignments??e.generalAssignments,hireDate:(0,s.d$)(t.hireDate??e.hireDate),name:t.name??e.name,notes:(0,s.d$)(void 0===t.notes?e.notes:t.notes),position:(0,s.d$)(t.position??e.position),profileImageUrl:(0,s.d$)(void 0===t.profileImageUrl?e.profileImageUrl:t.profileImageUrl),role:t.role??e.role,shiftSchedule:(0,s.d$)(void 0===t.shiftSchedule?e.shiftSchedule:t.shiftSchedule),skills:t.skills??e.skills,status:(0,s.d$)(t.status??e.status),updatedAt:a,...void 0!==t.workingHours&&{workingHours:(0,s.d$)(t.workingHours)}}}),e.setQueryData(d.all,(e=[])=>e.map(e=>{if(e.id===Number(a)){let a=new Date().toISOString();return{...e,availability:(0,s.d$)(t.availability??e.availability),contactEmail:(0,s.d$)(t.contactEmail??e.contactEmail),contactInfo:t.contactInfo??e.contactInfo,contactMobile:(0,s.d$)(t.contactMobile??e.contactMobile),contactPhone:(0,s.d$)(t.contactPhone??e.contactPhone),currentLocation:(0,s.d$)(t.currentLocation??e.currentLocation),department:(0,s.d$)(t.department??e.department),employeeId:t.employeeId??e.employeeId,fullName:(0,s.d$)(t.fullName??e.fullName),generalAssignments:t.generalAssignments??e.generalAssignments,hireDate:(0,s.d$)(t.hireDate??e.hireDate),name:t.name??e.name,notes:(0,s.d$)(t.notes??e.notes),position:(0,s.d$)(t.position??e.position),profileImageUrl:(0,s.d$)(t.profileImageUrl??e.profileImageUrl),role:t.role??e.role,shiftSchedule:(0,s.d$)(t.shiftSchedule??e.shiftSchedule),skills:t.skills??e.skills,status:(0,s.d$)(t.status??e.status),updatedAt:a,...void 0!==t.workingHours&&{workingHours:(0,s.d$)(t.workingHours)}}}return e})),{previousEmployee:i,previousEmployeesList:o}},onSettled:(t,a,i)=>{e.invalidateQueries({queryKey:d.detail(i.id)}),e.invalidateQueries({queryKey:d.all})}})},f=()=>{let e=(0,i.jE)();return(0,o.n)({mutationFn:async e=>(await c.employeeApiService.delete(e),e),onError:(t,a,i)=>{i?.previousEmployeesList&&e.setQueryData(d.all,i.previousEmployeesList),console.error("Failed to delete employee:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:d.all}),await e.cancelQueries({queryKey:d.detail(t)});let a=e.getQueryData(d.all);return e.setQueryData(d.all,(e=[])=>e.filter(e=>e.id!==Number(t))),e.removeQueries({queryKey:d.detail(t)}),{previousEmployeesList:a}},onSettled:()=>{e.invalidateQueries({queryKey:d.all})}})}},75176:(e,t,a)=>{a.d(t,{cl:()=>i.cl,delegationApiService:()=>i.ac,employeeApiService:()=>i.aV,reliabilityApiService:()=>i.e_,taskApiService:()=>i.Hg,vehicleApiService:()=>i.oL});var i=a(3302);a(8342)},87676:(e,t,a)=>{a.r(t),a.d(t,{useNotifications:()=>l,useWorkHubNotifications:()=>n});var i=a(43210),o=a(94538);let l=()=>{let e=(0,o.C)(e=>e.addNotification),t=(0,o.C)(e=>e.removeNotification),a=(0,o.C)(e=>e.clearAllNotifications),l=(0,o.C)(e=>e.unreadNotificationCount),n=(0,i.useCallback)(t=>{e({message:t,type:"success"})},[e]),r=(0,i.useCallback)(t=>{e({message:t,type:"error"})},[e]),s=(0,i.useCallback)(t=>{e({message:t,type:"warning"})},[e]),c=(0,i.useCallback)(t=>{e({message:t,type:"info"})},[e]),d=(0,i.useCallback)((e,t,a)=>{e?n(t):r(a)},[n,r]),u=(0,i.useCallback)((a,i,l=5e3)=>{e({message:i,type:a}),setTimeout(()=>{let e=o.C.getState().notifications.at(-1);e&&e.message===i&&t(e.id)},l)},[e,t]),m=(0,i.useCallback)((t="Loading...")=>{e({message:t,type:"info"});let a=o.C.getState().notifications;return a.at(-1)?.id},[e]),p=(0,i.useCallback)((e,a,i)=>{t(e),a?n(i):r(i)},[t,n,r]);return{clearAllNotifications:a,removeNotification:t,showApiResult:d,showError:r,showInfo:c,showLoading:m,showSuccess:n,showTemporary:u,showWarning:s,unreadCount:l,updateLoadingNotification:p}},n=()=>{let{clearAllNotifications:e,removeNotification:t,showError:a,showInfo:n,showSuccess:r,showWarning:s,unreadCount:c}=l(),d=(0,i.useCallback)((e,t)=>{(0,o.C.getState().addNotification)({...t&&{actionUrl:t},category:"delegation",message:e,type:"delegation-update"})},[]),u=(0,i.useCallback)((e,t)=>{(0,o.C.getState().addNotification)({...t&&{actionUrl:t},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),m=(0,i.useCallback)((e,t)=>{(0,o.C.getState().addNotification)({...t&&{actionUrl:t},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:t,showDelegationUpdate:d,showEmployeeUpdate:(0,i.useCallback)((e,t)=>{(0,o.C.getState().addNotification)({...t&&{actionUrl:t},category:"employee",message:e,type:"employee-update"})},[]),showError:a,showInfo:n,showSuccess:r,showTaskAssigned:m,showVehicleMaintenance:u,showWarning:s,unreadCount:c}}}};