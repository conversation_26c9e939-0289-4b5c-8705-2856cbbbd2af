(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[925],{26126:(e,a,t)=>{"use strict";t.d(a,{E:()=>d});var s=t(95155),r=t(74466);t(12115);var l=t(54036);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function d(e){let{className:a,variant:t,...r}=e;return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:t}),a),...r})}},44838:(e,a,t)=>{"use strict";t.d(a,{SQ:()=>x,_2:()=>u,hO:()=>g,lp:()=>h,mB:()=>p,rI:()=>c,ty:()=>m});var s=t(95155),r=t(12115),l=t(48698),n=t(73158),d=t(10518),i=t(70154),o=t(54036);let c=l.bL,m=l.l9;l.YJ,l.ZL,l.Pb,l.z6,r.forwardRef((e,a)=>{let{className:t,inset:r,children:d,...i}=e;return(0,s.jsxs)(l.ZP,{ref:a,className:(0,o.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",t),...i,children:[d,(0,s.jsx)(n.A,{className:"ml-auto"})]})}).displayName=l.ZP.displayName,r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.G5,{ref:a,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...r})}).displayName=l.G5.displayName;let x=r.forwardRef((e,a)=>{let{className:t,sideOffset:r=4,...n}=e;return(0,s.jsx)(l.ZL,{children:(0,s.jsx)(l.UC,{ref:a,sideOffset:r,className:(0,o.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...n})})});x.displayName=l.UC.displayName;let u=r.forwardRef((e,a)=>{let{className:t,inset:r,...n}=e;return(0,s.jsx)(l.q7,{ref:a,className:(0,o.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",t),...n})});u.displayName=l.q7.displayName;let g=r.forwardRef((e,a)=>{let{className:t,children:r,checked:n,...i}=e;return(0,s.jsxs)(l.H_,{ref:a,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...void 0!==n&&{checked:n},...i,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),r]})});g.displayName=l.H_.displayName,r.forwardRef((e,a)=>{let{className:t,children:r,...n}=e;return(0,s.jsxs)(l.hN,{ref:a,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(i.A,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=l.hN.displayName;let h=r.forwardRef((e,a)=>{let{className:t,inset:r,...n}=e;return(0,s.jsx)(l.JU,{ref:a,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",t),...n})});h.displayName=l.JU.displayName;let p=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.wv,{ref:a,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...r})});p.displayName=l.wv.displayName},46102:(e,a,t)=>{"use strict";t.d(a,{Bc:()=>d,ZI:()=>c,k$:()=>o,m_:()=>i});var s=t(95155),r=t(89613),l=t(12115),n=t(54036);let d=r.Kq,i=r.bL,o=r.l9,c=l.forwardRef((e,a)=>{let{className:t,sideOffset:l=4,...d}=e;return(0,s.jsx)(r.UC,{className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),ref:a,sideOffset:l,...d})});c.displayName=r.UC.displayName},46741:(e,a,t)=>{Promise.resolve().then(t.bind(t,96055))},59409:(e,a,t)=>{"use strict";t.d(a,{bq:()=>x,eb:()=>p,gC:()=>h,l6:()=>c,yv:()=>m});var s=t(95155),r=t(31992),l=t(79556),n=t(77381),d=t(10518),i=t(12115),o=t(54036);let c=r.bL;r.YJ;let m=r.WT,x=i.forwardRef((e,a)=>{let{children:t,className:n,...d}=e;return(0,s.jsxs)(r.l9,{className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",n),ref:a,...d,children:[t,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"size-4 opacity-50"})})]})});x.displayName=r.l9.displayName;let u=i.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.PP,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),ref:a,...l,children:(0,s.jsx)(n.A,{className:"size-4"})})});u.displayName=r.PP.displayName;let g=i.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,s.jsx)(r.wn,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),ref:a,...n,children:(0,s.jsx)(l.A,{className:"size-4"})})});g.displayName=r.wn.displayName;let h=i.forwardRef((e,a)=>{let{children:t,className:l,position:n="popper",...d}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",l),position:n,ref:a,...d,children:[(0,s.jsx)(u,{}),(0,s.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(g,{})]})})});h.displayName=r.UC.displayName,i.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.JU,{className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),ref:a,...l})}).displayName=r.JU.displayName;let p=i.memo(i.forwardRef((e,a)=>{let{children:t,className:l,...n}=e,c=i.useCallback(e=>{"function"==typeof a?a(e):a&&(a.current=e)},[a]);return(0,s.jsxs)(r.q7,{className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l),ref:c,...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(d.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}));p.displayName=r.q7.displayName,i.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.wv,{className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),ref:a,...l})}).displayName=r.wv.displayName},90010:(e,a,t)=>{"use strict";t.d(a,{$v:()=>p,EO:()=>x,Lt:()=>i,Rx:()=>b,Zr:()=>f,ck:()=>g,r7:()=>h,tv:()=>o,wd:()=>u});var s=t(95155),r=t(17649),l=t(12115),n=t(30285),d=t(54036);let i=r.bL,o=r.l9,c=r.ZL,m=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.hJ,{className:(0,d.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...l,ref:a})});m.displayName=r.hJ.displayName;let x=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)(m,{}),(0,s.jsx)(r.UC,{className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),ref:a,...l})]})});x.displayName=r.UC.displayName;let u=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...t})};u.displayName="AlertDialogHeader";let g=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};g.displayName="AlertDialogFooter";let h=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.hE,{className:(0,d.cn)("text-lg font-semibold",t),ref:a,...l})});h.displayName=r.hE.displayName;let p=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.VY,{className:(0,d.cn)("text-sm text-muted-foreground",t),ref:a,...l})});p.displayName=r.VY.displayName;let b=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.rc,{className:(0,d.cn)((0,n.r)(),t),ref:a,...l})});b.displayName=r.rc.displayName;let f=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.ZD,{className:(0,d.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",t),ref:a,...l})});f.displayName=r.ZD.displayName},96055:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>eZ});var s=t(95155),r=t(31949),l=t(57082),n=t(35695),d=t(41784),i=t(83343),o=t(24371),c=t(85268),m=t(58127),x=t(14347),u=t(35079),g=t(11133),h=t(3638),p=t(67554),b=t(18046),f=t(51920),j=t(91721),y=t(37648),v=t(12115),N=t(99673),w=t(30285),k=t(54165),A=t(62523),C=t(85057),F=t(59409);let D=e=>{let{currentStatus:a,delegationId:t,isOpen:r,onClose:l,onConfirm:n}=e,[d,i]=(0,v.useState)(a),[o,c]=(0,v.useState)(""),[m,x]=(0,v.useState)(null);return(0,s.jsx)(k.lG,{onOpenChange:l,open:r,children:(0,s.jsxs)(k.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(k.c7,{children:(0,s.jsx)(k.L3,{children:"Update Delegation Status"})}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(C.J,{className:"text-right",htmlFor:"current-status",children:"Current Status"}),(0,s.jsx)(A.p,{className:"col-span-3",id:"current-status",readOnly:!0,value:(0,N.fZ)(a)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(C.J,{className:"text-right",htmlFor:"new-status",children:"New Status"}),(0,s.jsxs)(F.l6,{onValueChange:e=>i(e),value:d,children:[(0,s.jsx)(F.bq,{className:"col-span-3",children:(0,s.jsx)(F.yv,{placeholder:"Select new status"})}),(0,s.jsx)(F.gC,{children:["Planned","Confirmed","In_Progress","Completed","Cancelled","No_details"].map(e=>(0,s.jsx)(F.eb,{disabled:e===a,value:e,children:(0,N.fZ)(e)},e))})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(C.J,{className:"text-right",htmlFor:"reason",children:"Reason"}),(0,s.jsx)(A.p,{className:"col-span-3",id:"reason",onChange:e=>c(e.target.value),placeholder:"Enter reason for status change",value:o})]}),m&&(0,s.jsx)("p",{className:"col-span-4 text-center text-sm text-red-500",children:m})]}),(0,s.jsxs)(k.Es,{children:[(0,s.jsx)(w.$,{onClick:l,variant:"outline",children:"Cancel"}),(0,s.jsx)(w.$,{onClick:()=>o.trim()?d===a?void x("Please select a different status."):void(x(null),n(d,o)):void x("Reason for status change is required."),children:"Confirm"})]})]})})};var z=t(26126),R=t(66695),E=t(22346),I=t(46102),Z=t(54036);let P=e=>{switch(e){case"Cancelled":return"bg-gradient-to-r from-red-500/20 to-rose-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:from-red-500/10 dark:to-rose-500/10 dark:border-red-500/20";case"Completed":return"bg-gradient-to-r from-purple-500/20 to-violet-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:from-purple-500/10 dark:to-violet-500/10 dark:border-purple-500/20";case"Confirmed":return"bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:from-green-500/10 dark:to-emerald-500/10 dark:border-green-500/20";case"In_Progress":return"bg-gradient-to-r from-yellow-500/20 to-amber-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:from-yellow-500/10 dark:to-amber-500/10 dark:border-yellow-500/20";case"Planned":return"bg-gradient-to-r from-blue-500/20 to-blue-600/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:from-blue-500/10 dark:to-blue-600/10 dark:border-blue-500/20";default:return"bg-gradient-to-r from-gray-500/20 to-slate-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:from-gray-500/10 dark:to-slate-500/10 dark:border-gray-500/20"}},_=e=>{switch(e){case"Cancelled":return o.A;case"Completed":return c.A;case"Confirmed":return m.A;case"In_Progress":return x.A;case"Planned":return u.A;default:return g.A}},M=e=>{switch(e){case"Cancelled":return"from-red-500/20 to-rose-500/20 border-red-500/30";case"Completed":return"from-purple-500/20 to-violet-500/20 border-purple-500/30";case"Confirmed":return"from-green-500/20 to-emerald-500/20 border-green-500/30";case"In_Progress":return"from-yellow-500/20 to-amber-500/20 border-yellow-500/30";case"Planned":return"from-blue-500/20 to-blue-600/20 border-blue-500/30";default:return"from-gray-500/20 to-slate-500/20 border-gray-500/30"}},S=function(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"N/A";try{return(0,d.GP)((0,i.H)(e),a?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch(e){return"Invalid Date"}};function T(e){let{currentStatus:a,delegationId:t,isUpdating:r=!1,onStatusUpdate:l,statusHistory:n}=e,[d,i]=(0,v.useState)(!1),o=async(e,a)=>{try{await l(e,a),i(!1)}catch(e){console.error("Status update failed:",e)}},c=(null==n?void 0:n.slice().sort((e,a)=>new Date(a.changedAt).getTime()-new Date(e.changedAt).getTime()))||[];return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(R.Zp,{className:"border-border/60 bg-gradient-to-br from-card to-card/95 shadow-lg backdrop-blur-sm",children:[(0,s.jsx)(R.aR,{className:"pb-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex min-w-0 flex-1 items-center gap-3",children:[(0,s.jsx)("div",{className:"shrink-0 rounded-lg bg-primary/10 p-2 text-primary",children:(0,s.jsx)(h.A,{className:"size-5"})}),(0,s.jsxs)("div",{className:"flex min-w-0 items-center gap-2",children:[(0,s.jsx)(R.ZB,{className:"whitespace-nowrap text-xl font-semibold text-primary",children:"Status History"}),(0,s.jsxs)(z.E,{className:"shrink-0 text-xs",variant:"secondary",children:[c.length," ",1===c.length?"entry":"entries"]})]})]}),(0,s.jsx)(I.Bc,{children:(0,s.jsxs)(I.m_,{children:[(0,s.jsx)(I.k$,{asChild:!0,children:(0,s.jsx)(w.$,{className:"shrink-0 gap-2 whitespace-nowrap bg-gradient-to-r from-primary to-primary/90 text-primary-foreground shadow-md transition-all duration-200 hover:from-primary/90 hover:to-primary hover:shadow-lg",disabled:r,onClick:()=>{i(!0)},size:"sm",children:r?(0,s.jsx)(p.A,{className:"size-4 animate-spin"}):(0,s.jsx)(b.A,{className:"size-4"})})}),(0,s.jsx)(I.ZI,{children:(0,s.jsx)("p",{children:"Change delegation status"})})]})})]})}),(0,s.jsx)(R.Wu,{className:"pt-0",children:c.length>0?(0,s.jsx)("div",{className:"scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent max-h-96 space-y-4 overflow-y-auto pr-2",children:c.map((e,a)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"group relative rounded-xl border border-border/50 bg-gradient-to-r from-background/50 to-background/30 p-4 transition-all duration-200 hover:border-border",children:[a<c.length-1&&(0,s.jsx)("div",{className:"absolute left-7 top-16 h-8 w-0.5 bg-gradient-to-b from-border via-border/50 to-transparent"}),(0,s.jsxs)("div",{className:"flex items-start gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(()=>{let a=_(e.status),t=M(e.status);return(0,s.jsx)("div",{className:(0,Z.cn)("p-2.5 rounded-full bg-gradient-to-br border-2 transition-all duration-200 group-hover:scale-105",t),children:(0,s.jsx)(a,{className:"size-4"})})})(),0===a&&(0,s.jsx)("div",{className:"absolute -right-1 -top-1 size-3 animate-pulse rounded-full border-2 border-background bg-green-500 shadow-lg"})]}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsxs)("div",{className:"mb-2 flex items-center justify-between gap-3",children:[(0,s.jsxs)(z.E,{className:(0,Z.cn)("text-sm py-1.5 px-3 font-medium border shadow-sm flex items-center gap-1.5 flex-shrink-0 whitespace-nowrap",P(e.status)),children:[(()=>{let a=_(e.status);return(0,s.jsx)(a,{className:"size-3.5 shrink-0"})})(),(0,s.jsx)("span",{className:"whitespace-nowrap",children:(0,N.fZ)(e.status)})]}),(0,s.jsxs)("div",{className:"flex shrink-0 items-center gap-2 text-xs text-muted-foreground",children:[(0,s.jsx)(f.A,{className:"size-3"}),(0,s.jsx)("span",{className:"whitespace-nowrap",children:S(e.changedAt,!0)})]})]}),e.reason&&(0,s.jsx)("div",{className:"mt-2 rounded-lg border border-border/30 bg-muted/30 p-3",children:(0,s.jsxs)("p",{className:"flex items-start gap-2 text-sm italic text-muted-foreground",children:[(0,s.jsx)(j.A,{className:"mt-0.5 size-3 shrink-0"}),(0,s.jsx)("span",{className:"shrink-0 font-medium",children:"Reason:"}),(0,s.jsx)("span",{className:"break-words",children:e.reason})]})})]})]})]}),a<c.length-1&&(0,s.jsx)(E.w,{className:"my-2 bg-gradient-to-r from-transparent via-border to-transparent"})]},e.id||a))}):(0,s.jsxs)("div",{className:"py-12 text-center",children:[(0,s.jsx)("div",{className:"mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-muted/30 p-4",children:(0,s.jsx)(y.A,{className:"size-8 text-muted-foreground"})}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"No status history available."}),(0,s.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:"Status changes will appear here."})]})})]}),(0,s.jsx)(D,{currentStatus:a,delegationId:t,isOpen:d,onClose:()=>i(!1),onConfirm:o})]})}var H=t(66766),B=t(83662),L=t(98328),U=t(50286),O=t(50594);let V=t(55414).b;function G(e){let{children:a,icon:t,label:r,value:l,valueClassName:n,className:d}=e;return(0,s.jsxs)("div",{className:(0,Z.cn)("flex items-start space-x-3",d),children:[(0,s.jsx)("div",{className:"mt-0.5 rounded-full bg-blue-50 dark:bg-blue-900/30 p-2",children:(0,s.jsx)(t,{className:"size-4 text-blue-600 dark:text-blue-400"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1",children:r}),(0,s.jsx)("div",{className:(0,Z.cn)("font-medium text-gray-900 dark:text-white",n),children:a||l||"N/A"})]})]})}var J=t(44689);let $=e=>{if(!e)return"N/A";try{return(0,d.GP)((0,i.H)(e),"MMM d, yyyy")}catch(e){return"Invalid Date"}};function q(e){let{delegation:a,className:t}=e;return(0,s.jsxs)(R.Zp,{className:t,children:[(0,s.jsx)(R.aR,{children:(0,s.jsxs)(R.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(B.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("span",{children:"Event Overview"})]})}),(0,s.jsxs)(R.Wu,{className:"space-y-6",children:[(0,s.jsx)(V,{ratio:16/9,className:"overflow-hidden rounded-lg",children:(0,s.jsx)(H.default,{src:(0,J._x)(a.imageUrl,a.id,"detail"),alt:a.eventName,className:"object-cover transition-transform hover:scale-105",fill:!0,priority:!0})}),(0,s.jsx)(E.w,{}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,s.jsx)(G,{icon:L.A,label:"Duration",value:"".concat($(a.durationFrom)," - ").concat($(a.durationTo))}),(0,s.jsx)(G,{icon:B.A,label:"Location",value:a.location}),a.invitationFrom&&(0,s.jsx)(G,{icon:U.A,label:"Invitation From",value:a.invitationFrom}),a.invitationTo&&(0,s.jsx)(G,{icon:U.A,label:"Invitation To",value:a.invitationTo})]}),a.notes&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{}),(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)(G,{icon:O.A,label:"General Notes",valueClassName:"whitespace-pre-wrap",value:a.notes})})]})]})]})}var W=t(17313),X=t(58260),Y=t(83082),Q=t(74465);let K=e=>{if(!e)return"N/A";try{return(0,d.GP)((0,i.H)(e),"MMM d, yyyy, HH:mm")}catch(e){return"Invalid Date"}};function ee(e){let{delegation:a,className:t}=e;return a.arrivalFlight||a.departureFlight?(0,s.jsxs)(R.Zp,{className:t,children:[(0,s.jsx)(R.aR,{children:(0,s.jsxs)(R.ZB,{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(X.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("span",{children:"Flight Information"})]}),(0,s.jsxs)(z.E,{variant:"secondary",className:"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800",children:[[a.arrivalFlight,a.departureFlight].filter(Boolean).length," ","flight",1!==[a.arrivalFlight,a.departureFlight].filter(Boolean).length?"s":""]})]})}),(0,s.jsxs)(R.Wu,{className:"space-y-6",children:[a.arrivalFlight&&(0,s.jsxs)("div",{className:"rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-900/20",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)(Y.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-green-900 dark:text-green-100",children:"Arrival Flight"})]}),(0,s.jsxs)("div",{className:"grid gap-3 md:grid-cols-2",children:[(0,s.jsx)(G,{icon:X.A,label:"Flight Number",value:a.arrivalFlight.flightNumber}),(0,s.jsx)(G,{icon:y.A,label:"Date & Time",value:K(a.arrivalFlight.dateTime)}),(0,s.jsx)(G,{icon:B.A,label:"Airport",value:a.arrivalFlight.airport}),a.arrivalFlight.terminal&&(0,s.jsx)(G,{icon:B.A,label:"Terminal",value:a.arrivalFlight.terminal})]}),a.arrivalFlight.notes&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{className:"my-3"}),(0,s.jsx)(G,{icon:O.A,label:"Notes",value:a.arrivalFlight.notes,valueClassName:"whitespace-pre-wrap"})]})]}),a.departureFlight&&(0,s.jsxs)("div",{className:"rounded-lg border border-orange-200 bg-orange-50 p-4 dark:border-orange-800 dark:bg-orange-900/20",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)(Q.A,{className:"h-5 w-5 text-orange-600 dark:text-orange-400"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-orange-900 dark:text-orange-100",children:"Departure Flight"})]}),(0,s.jsxs)("div",{className:"grid gap-3 md:grid-cols-2",children:[(0,s.jsx)(G,{icon:X.A,label:"Flight Number",value:a.departureFlight.flightNumber}),(0,s.jsx)(G,{icon:y.A,label:"Date & Time",value:K(a.departureFlight.dateTime)}),(0,s.jsx)(G,{icon:B.A,label:"Airport",value:a.departureFlight.airport}),a.departureFlight.terminal&&(0,s.jsx)(G,{icon:B.A,label:"Terminal",value:a.departureFlight.terminal})]}),a.departureFlight.notes&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{className:"my-3"}),(0,s.jsx)(G,{icon:O.A,label:"Notes",value:a.departureFlight.notes,valueClassName:"whitespace-pre-wrap"})]})]})]})]}):(0,s.jsxs)(R.Zp,{className:t,children:[(0,s.jsx)(R.aR,{children:(0,s.jsxs)(R.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(X.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("span",{children:"Flight Information"})]})}),(0,s.jsx)(R.Wu,{children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,s.jsx)("div",{className:"mx-auto mb-4 h-12 w-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center",children:(0,s.jsx)(X.A,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"No Flight Details"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"No flight information has been provided for this delegation."})]})})]})}function ea(e){let{title:a,icon:t,items:r,renderItem:l,emptyMessage:n,className:d}=e;return(0,s.jsxs)(R.Zp,{className:d,children:[(0,s.jsx)(R.aR,{children:(0,s.jsxs)(R.ZB,{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(t,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("span",{children:a})]}),(0,s.jsx)(z.E,{variant:"secondary",className:"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800",children:r.length})]})}),(0,s.jsx)(R.Wu,{children:r.length>0?(0,s.jsx)("div",{className:"space-y-3",children:r.map(l)}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,s.jsx)("div",{className:"rounded-full bg-gray-100 dark:bg-gray-800 p-3 mb-3",children:(0,s.jsx)(t,{className:"h-6 w-6 text-gray-400"})}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 font-medium",children:n})]})})]})}function et(e){var a;let{className:t,delegation:r}=e;return(0,s.jsx)(ea,{className:null!=t?t:"",emptyMessage:"No delegates assigned to this delegation.",icon:U.A,items:null!=(a=r.delegates)?a:[],renderItem:(e,a)=>{var t;return(0,s.jsxs)("div",{className:"space-y-2 rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-sm dark:border-gray-700 dark:bg-gray-800",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white",children:e.name}),e.title&&(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.title}),e.notes&&(0,s.jsx)("p",{className:"rounded bg-gray-50 p-2 text-xs italic text-gray-500 dark:bg-gray-700 dark:text-gray-500",children:e.notes})]},null!=(t=e.id)?t:a)},title:"Delegates"})}function es(e){var a;let{className:t,delegation:r}=e;return(0,s.jsx)(ea,{className:null!=t?t:"",emptyMessage:"No drivers assigned to this delegation.",icon:j.A,items:null!=(a=r.drivers)?a:[],renderItem:(e,a)=>{var t,r;return(0,s.jsxs)("div",{className:"space-y-2 rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-sm dark:border-gray-700 dark:bg-gray-800",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white",children:e.employee?(0,N.DV)(e.employee):"Employee ID: ".concat(e.employeeId)}),(null==(t=e.employee)?void 0:t.role)&&(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:(0,N.s)(e.employee.role)}),(null==(r=e.employee)?void 0:r.contactEmail)&&(0,s.jsxs)("p",{className:"rounded bg-gray-50 p-2 text-xs text-gray-500 dark:bg-gray-700 dark:text-gray-500",children:["\uD83D\uDCE7 ",e.employee.contactEmail]})]},e.employeeId||a)},title:"Drivers"})}var er=t(45731);function el(e){var a;let{delegation:t,className:r}=e;return(0,s.jsx)(ea,{title:"Escorts",icon:er.A,items:null!=(a=t.escorts)?a:[],renderItem:(e,a)=>{var t,r;return(0,s.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-4 space-y-2 hover:shadow-sm transition-shadow dark:border-gray-700 dark:bg-gray-800",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white",children:e.employee?(0,N.DV)(e.employee):"Employee ID: ".concat(e.employeeId)}),(null==(t=e.employee)?void 0:t.role)&&(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:(0,N.s)(e.employee.role)}),(null==(r=e.employee)?void 0:r.contactEmail)&&(0,s.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-500 bg-gray-50 dark:bg-gray-700 p-2 rounded",children:["\uD83D\uDCE7 ",e.employee.contactEmail]})]},e.employeeId||a)},emptyMessage:"No escorts assigned to this delegation.",className:r||void 0})}t(44838);var en=t(28328);function ed(e){var a;let{delegation:t,className:r}=e;return(0,s.jsx)(ea,{title:"Vehicles",icon:en.A,items:null!=(a=t.vehicles)?a:[],renderItem:(e,a)=>{var t,r;return(0,s.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-4 space-y-2 hover:shadow-sm transition-shadow dark:border-gray-700 dark:bg-gray-800",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white",children:e.vehicle?"".concat(e.vehicle.make," ").concat(e.vehicle.model," (").concat(e.vehicle.year,")"):"Vehicle ID: ".concat(e.vehicleId)}),(null==(t=e.vehicle)?void 0:t.licensePlate)&&(0,s.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["\uD83D\uDE97 License Plate: ",e.vehicle.licensePlate]}),(null==(r=e.vehicle)?void 0:r.ownerName)&&(0,s.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-500 bg-gray-50 dark:bg-gray-700 p-2 rounded",children:["\uD83D\uDC64 Owner: ",e.vehicle.ownerName]})]},e.vehicleId||a)},emptyMessage:"No vehicles assigned to this delegation.",className:r||void 0})}function ei(e){let{delegation:a,onStatusUpdate:t,className:r}=e;return(0,s.jsxs)(W.tU,{defaultValue:"overview",className:r,children:[(0,s.jsxs)(W.j7,{className:"grid w-full grid-cols-4 mb-6",children:[(0,s.jsx)(W.Xi,{value:"overview",className:"text-sm",children:"Overview"}),(0,s.jsx)(W.Xi,{value:"assignments",className:"text-sm",children:"Assignments"}),(0,s.jsx)(W.Xi,{value:"flights",className:"text-sm",children:"Flights"}),(0,s.jsx)(W.Xi,{value:"history",className:"text-sm",children:"History"})]}),(0,s.jsx)(W.av,{value:"overview",className:"space-y-6 mt-0",children:(0,s.jsx)(q,{delegation:a})}),(0,s.jsx)(W.av,{value:"assignments",className:"space-y-6 mt-0",children:(0,s.jsxs)("div",{className:"grid gap-6 lg:grid-cols-2",children:[(0,s.jsx)(et,{delegation:a}),(0,s.jsx)(el,{delegation:a}),(0,s.jsx)(es,{delegation:a}),(0,s.jsx)(ed,{delegation:a})]})}),(0,s.jsx)(W.av,{value:"flights",className:"space-y-6 mt-0",children:(0,s.jsx)(ee,{delegation:a})}),(0,s.jsx)(W.av,{value:"history",className:"space-y-6 mt-0",children:t?(0,s.jsx)(T,{currentStatus:a.status,delegationId:a.id,statusHistory:a.statusHistory||[],onStatusUpdate:t}):(0,s.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-8 text-center dark:border-gray-700 dark:bg-gray-800",children:[(0,s.jsx)("div",{className:"mx-auto mb-4 h-12 w-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Status History"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Status history will be displayed here when available."})]})})]})}var eo=t(6874),ec=t.n(eo),em=t(12543),ex=t(18763),eu=t(77223),eg=t(3561),eh=t(38382),ep=t(6560),eb=t(24865),ef=t(90010);let ej=e=>{switch(e){case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Completed":return"bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20";case"Confirmed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Planned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}};function ey(e){let{delegation:a,onDelete:t,className:r}=e,l=(0,n.useRouter)(),d=()=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(ep.r,{actionType:"tertiary",icon:(0,s.jsx)(em.A,{className:"size-4"}),onClick:()=>l.push("/delegations"),size:"sm",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"Back to List"}),(0,s.jsx)("span",{className:"sm:hidden",children:"Back"})]}),(0,s.jsx)(ep.r,{actionType:"secondary",asChild:!0,icon:(0,s.jsx)(ex.A,{className:"size-4"}),size:"sm",children:(0,s.jsx)(ec(),{href:"/delegations/".concat(a.id,"/edit"),children:"Edit"})}),(0,s.jsx)(eb.M,{href:"/delegations/".concat(a.id,"/report")}),t&&(0,s.jsxs)(ef.Lt,{children:[(0,s.jsx)(ef.tv,{asChild:!0,children:(0,s.jsxs)(ep.r,{actionType:"danger",icon:(0,s.jsx)(eu.A,{className:"size-4"}),size:"sm",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"Delete Delegation"}),(0,s.jsx)("span",{className:"sm:hidden",children:"Delete"})]})}),(0,s.jsxs)(ef.EO,{children:[(0,s.jsxs)(ef.wd,{children:[(0,s.jsx)(ef.r7,{children:"Are you sure?"}),(0,s.jsx)(ef.$v,{children:"This action cannot be undone. This will permanently delete the delegation and all its related information."})]}),(0,s.jsxs)(ef.ck,{children:[(0,s.jsx)(ef.Zr,{children:"Cancel"}),(0,s.jsx)(ef.Rx,{className:"bg-destructive hover:bg-destructive/90",onClick:t,children:"Delete"})]})]})]})]});return(0,s.jsx)("div",{className:(0,Z.cn)("border-b bg-white dark:bg-gray-800",r),children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between lg:hidden",children:[(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white truncate",children:a.eventName}),(0,s.jsxs)("div",{className:"mt-2 flex items-center space-x-2",children:[(0,s.jsx)(z.E,{className:(0,Z.cn)("text-sm py-1 px-3 font-semibold",ej(a.status)),children:(0,N.fZ)(a.status)}),a.location&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{orientation:"vertical",className:"h-4"}),(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400 truncate",children:a.location})]})]})]}),(0,s.jsxs)(eh.cj,{children:[(0,s.jsx)(eh.CG,{asChild:!0,children:(0,s.jsx)(w.$,{variant:"outline",size:"sm",children:(0,s.jsx)(eg.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(eh.h,{side:"bottom",className:"h-[80vh]",children:[(0,s.jsx)(eh.Fm,{children:(0,s.jsx)(eh.qp,{children:"Delegation Actions"})}),(0,s.jsx)("div",{className:"grid gap-4 py-4",children:(0,s.jsx)(d,{})})]})]})]}),(0,s.jsxs)("div",{className:"hidden lg:flex lg:items-center lg:justify-between",children:[(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-gray-900 dark:text-white",children:a.eventName}),(0,s.jsxs)("div",{className:"mt-2 flex items-center space-x-4",children:[(0,s.jsx)(z.E,{className:(0,Z.cn)("text-sm py-1 px-3 font-semibold",ej(a.status)),children:(0,N.fZ)(a.status)}),a.location&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{orientation:"vertical",className:"h-4"}),(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:a.location})]})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsx)(d,{})})]})]})})}var ev=t(15300),eN=t(18018),ew=t(24386);function ek(e){let{icon:a,label:t,value:r,color:l,bgColor:n}=e;return(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-full ".concat(n),children:(0,s.jsx)(a,{className:"h-4 w-4 ".concat(l)})}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:t})]}),(0,s.jsx)(z.E,{variant:"secondary",className:"font-semibold",children:r})]})}let eA=(e,a)=>{if(!e||!a)return"N/A";try{let t=(0,i.H)(e),s=(0,i.H)(a),r=(0,ew.c)(s,t)+1;return"".concat(r," day").concat(1!==r?"s":"")}catch(e){return"Invalid"}};function eC(e){var a,t,r,l,n,d,i;let{delegation:o,className:c}=e,m=[{icon:U.A,label:"Delegates",value:(null==(a=o.delegates)?void 0:a.length)||0,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-50 dark:bg-blue-900/30"},{icon:er.A,label:"Escorts",value:(null==(t=o.escorts)?void 0:t.length)||0,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-50 dark:bg-green-900/30"},{icon:j.A,label:"Drivers",value:(null==(r=o.drivers)?void 0:r.length)||0,color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-50 dark:bg-purple-900/30"},{icon:en.A,label:"Vehicles",value:(null==(l=o.vehicles)?void 0:l.length)||0,color:"text-orange-600 dark:text-orange-400",bgColor:"bg-orange-50 dark:bg-orange-900/30"},{icon:X.A,label:"Flights",value:[o.arrivalFlight,o.departureFlight].filter(Boolean).length,color:"text-indigo-600 dark:text-indigo-400",bgColor:"bg-indigo-50 dark:bg-indigo-900/30"},{icon:y.A,label:"Duration",value:eA(o.durationFrom,o.durationTo),color:"text-gray-600 dark:text-gray-400",bgColor:"bg-gray-50 dark:bg-gray-900/30"}],x=((null==(n=o.delegates)?void 0:n.length)||0)+((null==(d=o.escorts)?void 0:d.length)||0)+((null==(i=o.drivers)?void 0:i.length)||0);return(0,s.jsxs)(R.Zp,{className:c,children:[(0,s.jsx)(R.aR,{children:(0,s.jsxs)(R.ZB,{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(f.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("span",{children:"Delegation Metrics"})]}),(0,s.jsxs)(z.E,{variant:"outline",className:"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800",children:[x," people total"]})]})}),(0,s.jsxs)(R.Wu,{className:"space-y-3",children:[m.map((e,a)=>(0,s.jsx)(ek,{icon:e.icon,label:e.label,value:e.value,color:e.color,bgColor:e.bgColor},a)),o.location&&(0,s.jsxs)("div",{className:"mt-4 p-3 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(B.A,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Location"})]}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400 ml-6",children:o.location})]})]})]})}function eF(e){var a;let{delegation:t,className:r}=e;return(0,s.jsx)("div",{className:r,children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(R.Zp,{children:[(0,s.jsx)(R.aR,{children:(0,s.jsx)(R.ZB,{className:"text-lg",children:"Quick Actions"})}),(0,s.jsxs)(R.Wu,{className:"space-y-3",children:[(0,s.jsx)(w.$,{variant:"outline",className:"w-full justify-start",size:"sm",asChild:!0,children:(0,s.jsxs)(ec(),{href:"/delegations/".concat(t.id,"/edit"),children:[(0,s.jsx)(ex.A,{className:"mr-2 h-4 w-4"}),"Edit Delegation"]})}),(0,s.jsx)(w.$,{variant:"outline",className:"w-full justify-start",size:"sm",asChild:!0,children:(0,s.jsxs)(ec(),{href:"/delegations/".concat(t.id,"/report"),target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)(ev.A,{className:"mr-2 h-4 w-4"}),"View Report"]})}),(0,s.jsxs)(w.$,{variant:"outline",className:"w-full justify-start",size:"sm",onClick:()=>{window.print()},children:[(0,s.jsx)(eN.A,{className:"mr-2 h-4 w-4"}),"Print Details"]})]})]}),(0,s.jsx)(eC,{delegation:t}),(0,s.jsxs)(R.Zp,{children:[(0,s.jsx)(R.aR,{children:(0,s.jsx)(R.ZB,{className:"text-lg",children:"Status Information"})}),(0,s.jsxs)(R.Wu,{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:(null==(a=t.status)?void 0:a.replace("_"," "))||"No Status"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Current Status"})]}),t.durationFrom&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white mb-1",children:(0,d.GP)((0,i.H)(t.durationFrom),"MMM d, yyyy")}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Start Date"})]})]}),t.durationTo&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E.w,{}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white mb-1",children:(0,d.GP)((0,i.H)(t.durationTo),"MMM d, yyyy")}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"End Date"})]})]})]})]})]})})}var eD=t(89440),ez=t(77023),eR=t(95647),eE=t(53712),eI=t(17841);function eZ(){let e=(0,n.useParams)(),a=(0,n.useRouter)(),{showEntityDeleted:t,showEntityDeletionError:d,showEntityUpdated:i,showEntityUpdateError:o}=(0,eE.O_)("delegation"),c=e.id,{data:m,error:x,isLoading:u,refetch:g}=(0,eI.kA)(c),h=(0,eI.nB)(),p=(0,eI.lG)(),b=async()=>{if(m)try{await h.mutateAsync(m.id);let e={event:m.eventName,location:m.location};t(e),a.push("/delegations")}catch(e){console.error("Error deleting delegation:",e),d(e.message||"Failed to delete delegation. Please try again.")}},f=async(e,a)=>{if(m)try{await p.mutateAsync({id:m.id,status:e,statusChangeReason:a});let t={event:m.eventName,location:m.location};i(t)}catch(e){console.error("Error updating delegation status:",e),o(e.message||"Failed to update delegation status. Please try again.")}};return(0,s.jsx)(ez.gO,{data:m,emptyComponent:(0,s.jsxs)("div",{className:"py-10 text-center",children:[(0,s.jsx)(eR.z,{icon:r.A,title:"Delegation Not Found"}),(0,s.jsx)("p",{className:"mb-4",children:"The requested delegation could not be found."})]}),error:x?x.message:null,isLoading:u,loadingComponent:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(eR.z,{icon:l.A,title:"Loading Delegation..."}),(0,s.jsx)(ez.jt,{count:1,variant:"card"}),(0,s.jsxs)("div",{className:"grid items-start gap-6 md:grid-cols-3",children:[(0,s.jsx)(ez.jt,{className:"md:col-span-2",count:1,variant:"card"}),(0,s.jsx)(ez.jt,{count:1,variant:"card"})]})]}),onRetry:g,children:e=>(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)("div",{className:"container mx-auto px-4 pt-6",children:(0,s.jsx)(eD.AppBreadcrumb,{})}),(0,s.jsx)(ey,{delegation:e,onDelete:b}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"grid gap-8 lg:grid-cols-4",children:[(0,s.jsx)("div",{className:"lg:col-span-3",children:(0,s.jsx)(ei,{delegation:e,onStatusUpdate:f})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)(eF,{delegation:e})})]})})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[6476,7047,6897,3860,9664,375,7876,6874,1859,5247,6766,6233,9212,396,4036,4767,303,5067,7841,9258,9139,8441,1684,7358],()=>a(46741)),_N_E=e.O()}]);