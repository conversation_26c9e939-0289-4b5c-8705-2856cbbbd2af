"use strict";exports.id=5348,exports.ids=[5176,5348],exports.modules={15079:(e,a,t)=>{t.d(a,{bq:()=>u,eb:()=>h,gC:()=>f,l6:()=>c,yv:()=>m});var l=t(60687),s=t(22670),i=t(61662),r=t(89743),n=t(58450),d=t(43210),o=t(22482);let c=s.bL;s.YJ;let m=s.WT,u=d.forwardRef(({children:e,className:a,...t},r)=>(0,l.jsxs)(s.l9,{className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),ref:r,...t,children:[e,(0,l.jsx)(s.In,{asChild:!0,children:(0,l.jsx)(i.A,{className:"size-4 opacity-50"})})]}));u.displayName=s.l9.displayName;let p=d.forwardRef(({className:e,...a},t)=>(0,l.jsx)(s.PP,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),ref:t,...a,children:(0,l.jsx)(r.A,{className:"size-4"})}));p.displayName=s.PP.displayName;let x=d.forwardRef(({className:e,...a},t)=>(0,l.jsx)(s.wn,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),ref:t,...a,children:(0,l.jsx)(i.A,{className:"size-4"})}));x.displayName=s.wn.displayName;let f=d.forwardRef(({children:e,className:a,position:t="popper",...i},r)=>(0,l.jsx)(s.ZL,{children:(0,l.jsxs)(s.UC,{className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:t,ref:r,...i,children:[(0,l.jsx)(p,{}),(0,l.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),(0,l.jsx)(x,{})]})}));f.displayName=s.UC.displayName,d.forwardRef(({className:e,...a},t)=>(0,l.jsx)(s.JU,{className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),ref:t,...a})).displayName=s.JU.displayName;let h=d.memo(d.forwardRef(({children:e,className:a,...t},i)=>{let r=d.useCallback(e=>{"function"==typeof i?i(e):i&&(i.current=e)},[i]);return(0,l.jsxs)(s.q7,{className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),ref:r,...t,children:[(0,l.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,l.jsx)(s.VF,{children:(0,l.jsx)(n.A,{className:"size-4"})})}),(0,l.jsx)(s.p4,{children:e})]})}));h.displayName=s.q7.displayName,d.forwardRef(({className:e,...a},t)=>(0,l.jsx)(s.wv,{className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),ref:t,...a})).displayName=s.wv.displayName},34729:(e,a,t)=>{t.d(a,{T:()=>r});var l=t(60687),s=t(43210),i=t(22482);let r=s.forwardRef(({className:e,...a},t)=>(0,l.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...a}));r.displayName="Textarea"},70258:(e,a,t)=>{t.d(a,{x:()=>h});var l=t(60687);t(43210);var s=t(24920),i=t(26622),r=t(88853);let n=(0,t(82614).A)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var d=t(29333),o=t(36644),c=t(92718),m=t(92929),u=t(9275);u.k5(["Active","Inactive","Maintenance","Out_of_Service"]),u.k5(["Gasoline","Diesel","Electric","Hybrid","CNG","LPG"]);let p=u.Ik({make:u.Yj().min(1,"Make is required"),model:u.Yj().min(1,"Model is required"),year:u.au.number().min(1900,"Year must be 1900 or later").max(new Date().getFullYear()+1,`Year cannot be more than ${new Date().getFullYear()+1}`),color:u.Yj().optional(),licensePlate:u.Yj().min(1,"License plate is required"),vin:u.Yj().optional().refine(e=>!e||/^[A-HJ-NPR-Z0-9]{17}$/.test(e),"VIN must be a valid 17-character format (only capital letters A-H, J-N, P-R, Z and numbers 0-9)"),mileage:u.au.number().min(0,"Mileage cannot be negative").optional(),status:u.k5(["active","maintenance","inactive"]).default("active"),notes:u.Yj().optional(),ownerContact:u.Yj().optional(),ownerName:u.Yj().optional(),imageUrl:u.Yj().url("Invalid image URL").optional().or(u.eu("")),initialOdometer:u.au.number().min(0,"Odometer reading cannot be negative").optional()});var x=t(29523),f=t(44493);let h=({initialData:e,isEditing:a=!1,isLoading:t=!1,onSubmit:u})=>(0,l.jsxs)(f.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,l.jsx)(f.aR,{children:(0,l.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(s.A,{className:"h-5 w-5"}),a?"Edit Vehicle":"Add New Vehicle"]})}),(0,l.jsx)(f.Wu,{children:(0,l.jsxs)(c.I,{defaultValues:{status:"active",...e},onSubmit:u,schema:p,className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 border-b pb-2",children:"Basic Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(m.z,{label:"Make",name:"make",placeholder:"e.g., Toyota, Ford, BMW",icon:s.A,required:!0}),(0,l.jsx)(m.z,{label:"Model",name:"model",placeholder:"e.g., Camry, F-150, X3",icon:s.A,required:!0})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(m.z,{label:"Year",name:"year",type:"number",placeholder:"e.g., 2023",icon:i.A,min:1900,max:new Date().getFullYear()+1,required:!0}),(0,l.jsx)(m.z,{label:"Color",name:"color",placeholder:"e.g., Red, Blue, Silver",icon:r.A})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 border-b pb-2",children:"Identification"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(m.z,{label:"License Plate",name:"licensePlate",placeholder:"e.g., ABC-1234",icon:n,required:!0}),(0,l.jsx)(m.z,{label:"VIN (Optional)",name:"vin",placeholder:"17-character VIN (auto-generated if empty)",icon:n,maxLength:17})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 border-b pb-2",children:"Additional Information"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(m.z,{label:"Mileage",name:"mileage",type:"number",placeholder:"Current mileage",icon:d.A,min:0}),(0,l.jsx)(m.z,{label:"Status",name:"status",type:"select",placeholder:"Select status",options:[{value:"active",label:"Active"},{value:"maintenance",label:"In Maintenance"},{value:"inactive",label:"Inactive"}],defaultValue:"active"})]}),(0,l.jsx)(m.z,{label:"Notes",name:"notes",type:"textarea",placeholder:"Additional notes about the vehicle...",icon:o.A,rows:3})]}),(0,l.jsx)("div",{className:"flex justify-end pt-6 border-t",children:(0,l.jsx)(x.$,{type:"submit",disabled:t,className:"min-w-[120px]",children:t?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),a?"Updating...":"Creating..."]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(s.A,{className:"h-4 w-4 mr-2"}),a?"Update Vehicle":"Create Vehicle"]})})})]})})]})},71669:(e,a,t)=>{t.d(a,{C5:()=>b,MJ:()=>h,Rr:()=>g,eI:()=>x,lR:()=>f,lV:()=>o,zB:()=>m});var l=t(60687),s=t(43210),i=t(8730),r=t(27605),n=t(22482),d=t(80013);let o=r.Op,c=s.createContext({}),m=({...e})=>(0,l.jsx)(c.Provider,{value:{name:e.name},children:(0,l.jsx)(r.xI,{...e})}),u=()=>{let e=s.useContext(c),a=s.useContext(p),{getFieldState:t,formState:l}=(0,r.xW)(),i=t(e.name,l);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=a;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...i}},p=s.createContext({}),x=s.forwardRef(({className:e,...a},t)=>{let i=s.useId();return(0,l.jsx)(p.Provider,{value:{id:i},children:(0,l.jsx)("div",{ref:t,className:(0,n.cn)("space-y-2",e),...a})})});x.displayName="FormItem";let f=s.forwardRef(({className:e,...a},t)=>{let{error:s,formItemId:i}=u();return(0,l.jsx)(d.J,{ref:t,className:(0,n.cn)(s&&"text-destructive",e),htmlFor:i,...a})});f.displayName="FormLabel";let h=s.forwardRef(({...e},a)=>{let{error:t,formItemId:s,formDescriptionId:r,formMessageId:n}=u();return(0,l.jsx)(i.DX,{ref:a,id:s,"aria-describedby":t?`${r} ${n}`:`${r}`,"aria-invalid":!!t,...e})});h.displayName="FormControl";let g=s.forwardRef(({className:e,...a},t)=>{let{formDescriptionId:s}=u();return(0,l.jsx)("p",{ref:t,id:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...a})});g.displayName="FormDescription";let b=s.forwardRef(({className:e,children:a,...t},s)=>{let{error:i,formMessageId:r}=u(),d=i?String(i?.message??""):a;return d?(0,l.jsx)("p",{ref:s,id:r,className:(0,n.cn)("text-sm font-medium text-destructive",e),...t,children:d}):null});b.displayName="FormMessage"},72273:(e,a,t)=>{t.d(a,{NS:()=>x,T$:()=>c,W_:()=>m,Y1:()=>u,lR:()=>p});var l=t(8693),s=t(54050),i=t(46349),r=t(87676),n=t(48839),d=t(75176);let o={all:["vehicles"],detail:e=>["vehicles",e]},c=e=>(0,i.GK)([...o.all],async()=>(await d.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),m=(e,a)=>(0,i.GK)([...o.detail(e)],()=>d.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(a?.enabled??!0),staleTime:3e5,...a}),u=()=>{let e=(0,l.jE)(),{showError:a,showSuccess:t}=(0,r.useNotifications)();return(0,s.n)({mutationFn:e=>{let a=n.M.toCreateRequest(e);return d.vehicleApiService.create(a)},onError:e=>{a(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:a=>{e.invalidateQueries({queryKey:o.all}),t(`Vehicle "${a.licensePlate}" has been created successfully!`)}})},p=()=>{let e=(0,l.jE)(),{showError:a,showSuccess:t}=(0,r.useNotifications)();return(0,s.n)({mutationFn:({data:e,id:a})=>{let t=n.M.toUpdateRequest(e);return d.vehicleApiService.update(a,t)},onError:e=>{a(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:a=>{e.invalidateQueries({queryKey:o.all}),e.invalidateQueries({queryKey:o.detail(a.id)}),t(`Vehicle "${a.licensePlate}" has been updated successfully!`)}})},x=()=>{let e=(0,l.jE)(),{showError:a,showSuccess:t}=(0,r.useNotifications)();return(0,s.n)({mutationFn:e=>d.vehicleApiService.delete(e),onError:e=>{a(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(a,l)=>{e.invalidateQueries({queryKey:o.all}),e.removeQueries({queryKey:o.detail(l)}),t("Vehicle has been deleted successfully!")}})}},75176:(e,a,t)=>{t.d(a,{cl:()=>l.cl,delegationApiService:()=>l.ac,employeeApiService:()=>l.aV,reliabilityApiService:()=>l.e_,taskApiService:()=>l.Hg,vehicleApiService:()=>l.oL});var l=t(3302);t(8342)},87676:(e,a,t)=>{t.r(a),t.d(a,{useNotifications:()=>i,useWorkHubNotifications:()=>r});var l=t(43210),s=t(94538);let i=()=>{let e=(0,s.C)(e=>e.addNotification),a=(0,s.C)(e=>e.removeNotification),t=(0,s.C)(e=>e.clearAllNotifications),i=(0,s.C)(e=>e.unreadNotificationCount),r=(0,l.useCallback)(a=>{e({message:a,type:"success"})},[e]),n=(0,l.useCallback)(a=>{e({message:a,type:"error"})},[e]),d=(0,l.useCallback)(a=>{e({message:a,type:"warning"})},[e]),o=(0,l.useCallback)(a=>{e({message:a,type:"info"})},[e]),c=(0,l.useCallback)((e,a,t)=>{e?r(a):n(t)},[r,n]),m=(0,l.useCallback)((t,l,i=5e3)=>{e({message:l,type:t}),setTimeout(()=>{let e=s.C.getState().notifications.at(-1);e&&e.message===l&&a(e.id)},i)},[e,a]),u=(0,l.useCallback)((a="Loading...")=>{e({message:a,type:"info"});let t=s.C.getState().notifications;return t.at(-1)?.id},[e]),p=(0,l.useCallback)((e,t,l)=>{a(e),t?r(l):n(l)},[a,r,n]);return{clearAllNotifications:t,removeNotification:a,showApiResult:c,showError:n,showInfo:o,showLoading:u,showSuccess:r,showTemporary:m,showWarning:d,unreadCount:i,updateLoadingNotification:p}},r=()=>{let{clearAllNotifications:e,removeNotification:a,showError:t,showInfo:r,showSuccess:n,showWarning:d,unreadCount:o}=i(),c=(0,l.useCallback)((e,a)=>{(0,s.C.getState().addNotification)({...a&&{actionUrl:a},category:"delegation",message:e,type:"delegation-update"})},[]),m=(0,l.useCallback)((e,a)=>{(0,s.C.getState().addNotification)({...a&&{actionUrl:a},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),u=(0,l.useCallback)((e,a)=>{(0,s.C.getState().addNotification)({...a&&{actionUrl:a},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:a,showDelegationUpdate:c,showEmployeeUpdate:(0,l.useCallback)((e,a)=>{(0,s.C.getState().addNotification)({...a&&{actionUrl:a},category:"employee",message:e,type:"employee-update"})},[]),showError:t,showInfo:r,showSuccess:n,showTaskAssigned:u,showVehicleMaintenance:m,showWarning:d,unreadCount:o}}},92718:(e,a,t)=>{t.d(a,{I:()=>n});var l=t(60687),s=t(63442),i=t(27605),r=t(71669);let n=({children:e,defaultValues:a,onSubmit:t,schema:n,className:d="",ariaAttributes:o={}})=>{let c=(0,i.mN)({...a&&{defaultValues:a},resolver:(0,s.u)(n)}),m=async e=>{await t(e)};return(0,l.jsx)(r.lV,{...c,children:(0,l.jsx)("form",{onSubmit:c.handleSubmit(m),className:d,...o,children:e})})}},92929:(e,a,t)=>{t.d(a,{z:()=>o});var l=t(60687);t(43210);var s=t(27605),i=t(71669),r=t(89667),n=t(34729),d=t(15079);let o=({className:e="",disabled:a=!1,label:t,name:o,placeholder:c,render:m,type:u="text",options:p=[],defaultValue:x,icon:f,...h})=>{let{control:g}=(0,s.xW)();return(0,l.jsxs)(i.eI,{className:e,children:[(0,l.jsx)(i.lR,{htmlFor:o,children:t}),(0,l.jsx)(s.xI,{control:g,name:o,render:m||(({field:e,fieldState:{error:s}})=>(0,l.jsx)(i.MJ,{children:"select"===u?(0,l.jsxs)(d.l6,{onValueChange:e.onChange,value:e.value||x||"",disabled:a,children:[(0,l.jsx)(d.bq,{className:s?"border-red-500":"",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[f&&(0,l.jsx)(f,{className:"h-4 w-4 text-gray-500"}),(0,l.jsx)(d.yv,{placeholder:c||`Select ${t.toLowerCase()}`})]})}),(0,l.jsx)(d.gC,{children:p.map(e=>(0,l.jsx)(d.eb,{value:String(e.value),disabled:e.disabled||!1,children:e.label},e.value))})]}):"textarea"===u?(0,l.jsxs)("div",{className:"relative",children:[f&&(0,l.jsx)(f,{className:"absolute left-3 top-3 h-4 w-4 text-gray-500"}),(0,l.jsx)(n.T,{...e,...h,value:e.value??"",className:`${s?"border-red-500":""} ${f?"pl-10":""}`,disabled:a,id:o,placeholder:c})]}):(0,l.jsxs)("div",{className:"relative",children:[f&&(0,l.jsx)(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500"}),(0,l.jsx)(r.p,{...e,...h,value:e.value??"",className:`${s?"border-red-500":""} ${f?"pl-10":""}`,disabled:a,id:o,placeholder:c,type:u})]})}))}),(0,l.jsx)(i.C5,{})]})}}};