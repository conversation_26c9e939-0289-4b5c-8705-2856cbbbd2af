"use strict";exports.id=2452,exports.ids=[2452],exports.modules={3940:(e,t,r)=>{r.d(t,{O_:()=>o,t6:()=>n});var i=r(43210),s=r(49278);function n(){let e=(0,i.useCallback)((e,t)=>s.JP.success(e,t),[]),t=(0,i.useCallback)((e,t)=>s.JP.error(e,t),[]),r=(0,i.useCallback)((e,t)=>s.JP.info(e,t),[]),n=(0,i.useCallback)(t=>e(t?.successTitle||"Success",t?.successDescription||"Operation completed successfully"),[e]),o=(0,i.useCallback)((e,r)=>{let i=e instanceof Error?e.message:e;return t(r?.errorTitle||"Error",r?.errorDescription||i||"An unexpected error occurred")},[t]);return{showSuccess:e,showError:t,showInfo:r,showFormSuccess:n,showFormError:o}}function o(e){let t;switch(e){case"employee":t=r(49278).Ok;break;case"vehicle":t=r(49278).G7;break;case"task":t=r(49278).z0;break;case"delegation":t=r(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,t){let{showFormSuccess:r,showFormError:o}=n(),a=t||(e?(0,s.iw)(e):null),c=(0,i.useCallback)(e=>a?a.entityCreated(e):r({successTitle:"Created",successDescription:"Item has been created successfully"}),[a,r]),d=(0,i.useCallback)(e=>a?a.entityUpdated(e):r({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[a,r]),l=(0,i.useCallback)(e=>a?a.entityDeleted(e):r({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[a,r]),u=(0,i.useCallback)(e=>{if(a){let t=e instanceof Error?e.message:e;return a.entityCreationError(t)}return o(e,{errorTitle:"Creation Failed"})},[a,o]);return{showEntityCreated:c,showEntityUpdated:d,showEntityDeleted:l,showEntityCreationError:u,showEntityUpdateError:(0,i.useCallback)(e=>{if(a){let t=e instanceof Error?e.message:e;return a.entityUpdateError(t)}return o(e,{errorTitle:"Update Failed"})},[a,o]),showEntityDeletionError:(0,i.useCallback)(e=>{if(a){let t=e instanceof Error?e.message:e;return a.entityDeletionError(t)}return o(e,{errorTitle:"Deletion Failed"})},[a,o]),showFormSuccess:r,showFormError:o}}(void 0,t)}},28946:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(82614).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},49278:(e,t,r)=>{r.d(t,{G7:()=>h,Gb:()=>c,JP:()=>d,Ok:()=>l,Qu:()=>u,iw:()=>a,oz:()=>g,z0:()=>p});var i=r(3389);class s{show(e){return(0,i.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class n extends s{constructor(e){super(),this.config=e}entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class o extends s{serviceRecordCreated(e,t){return this.success("Service Record Added",`${t} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,t){return this.success("Service Record Updated",`${t} service for "${e}" has been updated.`)}serviceRecordDeleted(e,t){return this.success("Service Record Deleted",`${t} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function a(e){return new n(e)}function c(e,t){return new n({entityName:e,getDisplayName:t,messages:{created:{title:`${e} Created`,description:t=>`The ${e.toLowerCase()} "${t}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:t=>t||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:t=>t||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:t=>t||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let d=new s,l=new n({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new n({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),h=new n({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),p=new n({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),g=new o},54050:(e,t,r)=>{r.d(t,{n:()=>l});var i=r(43210),s=r(65406),n=r(33465),o=r(35536),a=r(31212),c=class extends o.Q{#e;#t=void 0;#r;#i;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#s()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,a.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,a.EN)(t.mutationKey)!==(0,a.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#s(),this.#n(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#s(),this.#n()}mutate(e,t){return this.#i=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#s(){let e=this.#r?.state??(0,s.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#n(e){n.jG.batch(()=>{if(this.#i&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#i.onSuccess?.(e.data,t,r),this.#i.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#i.onError?.(e.error,t,r),this.#i.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},d=r(8693);function l(e,t){let r=(0,d.jE)(t),[s]=i.useState(()=>new c(r,e));i.useEffect(()=>{s.setOptions(e)},[s,e]);let o=i.useSyncExternalStore(i.useCallback(e=>s.subscribe(n.jG.batchCalls(e)),[s]),()=>s.getCurrentResult(),()=>s.getCurrentResult()),l=i.useCallback((e,t)=>{s.mutate(e,t).catch(a.lQ)},[s]);if(o.error&&(0,a.GU)(s.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:l,mutateAsync:o.mutate}}}};