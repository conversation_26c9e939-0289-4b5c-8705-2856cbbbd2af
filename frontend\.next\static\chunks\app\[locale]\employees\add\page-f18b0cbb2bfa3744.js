(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9595],{42363:(e,r,a)=>{Promise.resolve().then(a.bind(a,46653))},46653:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>u});var t=a(95155);let s=(0,a(40157).A)("UserRoundPlus",[["path",{d:"M2 21a8 8 0 0 1 13.292-6",key:"bjp14o"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M19 16v6",key:"tddt3s"}],["path",{d:"M22 19h-6",key:"vcuq98"}]]);var i=a(35695),o=a(12115),l=a(44744),n=a(95647),d=a(53712),c=a(83761);function u(){let e=(0,i.useRouter)(),[r,a]=(0,o.useState)(null),{showEntityCreated:u,showEntityCreationError:m,showFormError:p}=(0,d.O_)("employee"),y=(0,c.Ar)(),h=async r=>{a(null);try{await y.mutateAsync(r);let a={name:r.fullName||r.name};u(a),e.push("/employees")}catch(e){if(console.error("Error adding employee:",e),e.validationErrors&&Array.isArray(e.validationErrors)){let r=e.validationErrors.map(e=>"".concat(e.path,": ").concat(e.message)).join("\n");console.log("Validation errors details:",r),a("Validation failed: ".concat(r)),p("Please check the form for errors",{errorTitle:"Validation Error"})}else{let r=e.message||"Failed to add employee. Please try again.";a(r),m(r)}}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(n.z,{description:"Enter the details for the new employee.",icon:s,title:"Add New Employee"}),r&&(0,t.jsx)("div",{className:"rounded-md bg-destructive/20 p-3 text-sm text-destructive",children:r}),(0,t.jsx)(l.N,{isEditing:!1,isLoading:y.isPending,onSubmit:h})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6476,7047,6897,3860,9664,375,7876,1859,5669,4629,4036,4767,303,5067,7515,970,8441,1684,7358],()=>r(42363)),_N_E=e.O()}]);