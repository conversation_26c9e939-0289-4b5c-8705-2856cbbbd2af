"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2999],{47262:(e,s,r)=>{r.d(s,{S:()=>o});var t=r(95155),a=r(76981),i=r(10518),n=r(12115),l=r(54036);let o=n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,t.jsx)(a.bL,{className:(0,l.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",r),ref:s,...n,children:(0,t.jsx)(a.C1,{className:(0,l.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(i.A,{className:"size-4"})})})});o.displayName=a.bL.displayName},55365:(e,s,r)=>{r.d(s,{Fc:()=>o,TN:()=>c,XL:()=>d});var t=r(95155),a=r(74466),i=r(12115),n=r(54036);let l=(0,a.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),o=i.forwardRef((e,s)=>{let{className:r,variant:a,...i}=e;return(0,t.jsx)("div",{className:(0,n.cn)(l({variant:a}),r),ref:s,role:"alert",...i})});o.displayName="Alert";let d=i.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h5",{className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",r),ref:s,...a})});d.displayName="AlertTitle";let c=i.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("text-sm [&_p]:leading-relaxed",r),ref:s,...a})});c.displayName="AlertDescription"},62523:(e,s,r)=>{r.d(s,{p:()=>n});var t=r(95155),a=r(12115),i=r(54036);let n=a.forwardRef((e,s)=>{let{className:r,type:a,...n}=e;return(0,t.jsx)("input",{className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:s,type:a,...n})});n.displayName="Input"},85057:(e,s,r)=>{r.d(s,{J:()=>d});var t=r(95155),a=r(12115),i=r(40968),n=r(74466),l=r(54036);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(i.b,{ref:s,className:(0,l.cn)(o(),r),...a})});d.displayName=i.b.displayName},86719:(e,s,r)=>{r.d(s,{M:()=>i});var t=r(12115),a=r(17652);function i(){let[e,s]=(0,t.useState)({}),[r,i]=(0,t.useState)(!1),n=(0,a.c3)("auth.validation"),l=(0,t.useCallback)(e=>e?/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)?void 0:n("emailInvalid"):n("emailRequired"),[n]),o=(0,t.useCallback)(e=>e?e.length<6?n("passwordTooShort"):void 0:n("passwordRequired"),[n]),d=(0,t.useCallback)(e=>{let r={},t=l(e.email),a=o(e.password);return t&&(r.email=t),a&&(r.password=a),s(r),{isValid:0===Object.keys(r).length,errors:r}},[l,o]),c=(0,t.useCallback)((e,r)=>{let t;switch(e){case"email":t=l(r);break;case"password":t=o(r);break;default:return}s(s=>({...s,[e]:t}))},[l,o]),m=(0,t.useCallback)(e=>{s(s=>{let r={...s};return delete r[e],r})},[]),u=(0,t.useCallback)(()=>{s({})},[]),x=(0,t.useCallback)(()=>{i(!0)},[]),f=(0,t.useCallback)(()=>{s({}),i(!1)},[]),p=(0,t.useCallback)((s,t)=>r&&t&&!e[s],[e,r]);return{errors:e,isFormTouched:r,validateForm:d,validateField:c,clearFieldError:m,clearAllErrors:u,markFormTouched:x,resetValidation:f,isFieldValid:p}}},92999:(e,s,r)=>{r.d(s,{LoginForm:()=>z});var t=r(95155),a=r(32087),i=r(31573),n=r(48639),l=r(11133),o=r(19637),d=r(10518),c=r(4607),m=r(17607),u=r(50172),x=r(19968),f=r(45731),p=r(17652),g=r(12115),b=r(40283),h=r(86719),v=r(55365),j=r(30285),N=r(47262),y=r(62523),w=r(85057),k=r(54036);function A(e){let{className:s,stage:r="authenticating",message:a}=e,i=(()=>{switch(r){case"authenticating":return{icon:(0,t.jsx)(u.A,{className:"size-6 animate-spin text-primary"}),text:a||"Authenticating credentials...",description:"Verifying your identity securely"};case"verifying":return{icon:(0,t.jsx)(f.A,{className:"size-6 text-accent animate-pulse"}),text:a||"Verifying security...",description:"Checking account permissions"};case"redirecting":return{icon:(0,t.jsx)(u.A,{className:"size-6 animate-spin text-primary"}),text:a||"Preparing your dashboard...",description:"Setting up your workspace"};case"success":return{icon:(0,t.jsx)(d.A,{className:"size-6 text-green-600"}),text:a||"Welcome back!",description:"Login successful"};default:return{icon:(0,t.jsx)(u.A,{className:"size-6 animate-spin text-primary"}),text:a||"Loading...",description:"Please wait"}}})();return(0,t.jsxs)("div",{className:(0,k.cn)("flex flex-col items-center justify-center p-8 text-center space-y-4",s),children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 rounded-full bg-primary/10 animate-ping"}),(0,t.jsx)("div",{className:"relative flex items-center justify-center w-12 h-12 rounded-full bg-background border border-border/60 shadow-lg",children:i.icon})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-lg font-medium text-foreground",children:i.text}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:i.description})]}),(0,t.jsx)("div",{className:"flex space-x-2",children:[0,1,2].map(e=>(0,t.jsx)("div",{className:(0,k.cn)("w-2 h-2 rounded-full bg-primary/30 animate-pulse","transition-all duration-300"),style:{animationDelay:"".concat(.2*e,"s"),animationDuration:"1.5s"}},e))})]})}function z(e){let{onForgotPassword:s,onSignUp:r,onSuccess:k}=e,{clearError:z,error:C,loading:S,signIn:T}=(0,b.useAuthContext)(),{clearAllErrors:F,clearFieldError:L,errors:_,isFieldValid:E,markFormTouched:R,validateForm:I}=(0,h.M)(),M=(0,p.c3)("auth"),[Z,D]=(0,g.useState)({email:"",password:"",rememberMe:!1}),[P,V]=(0,g.useState)(!1),[J,B]=(0,g.useState)("authenticating"),[W,$]=(0,g.useState)(!0),[q,H]=(0,g.useState)(null),[O,X]=(0,g.useState)(!1);(0,g.useEffect)(()=>{if(void 0!==globalThis.window&&"undefined"!=typeof navigator){$(navigator.onLine);let e=()=>$(!0),s=()=>$(!1);return globalThis.addEventListener("online",e),globalThis.addEventListener("offline",s),()=>{globalThis.removeEventListener("online",e),globalThis.removeEventListener("offline",s)}}return()=>{}},[]),(0,g.useEffect)(()=>{if(void 0!==globalThis.window&&"undefined"!=typeof localStorage){let e=localStorage.getItem("workhub_remember_email");e&&D(s=>({...s,email:e,rememberMe:!0}))}},[]);let G=async e=>{if(e.preventDefault(),!O&&!S){if(R(),X(!0),z(),F(),!I(Z).isValid||!W)return void X(!1);try{B("authenticating");let{error:e}=await T(Z.email,Z.password);e?X(!1):(B("success"),void 0!==globalThis.window&&"undefined"!=typeof localStorage&&(Z.rememberMe?localStorage.setItem("workhub_remember_email",Z.email):localStorage.removeItem("workhub_remember_email")),setTimeout(()=>{X(!1),null==k||k()},800))}catch(e){console.error("Login error:",e),X(!1)}}},K=(e,s)=>{D(r=>({...r,[e]:s})),"string"==typeof s&&_[e]&&L(e),C&&z()},Q=e=>{H(e)},U=()=>{H(null)};return O?(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4",children:(0,t.jsx)("div",{className:"w-full max-w-md",children:(0,t.jsx)("div",{className:"rounded-2xl border border-border/60 bg-card shadow-xl backdrop-blur-sm",children:(0,t.jsx)(A,{stage:J})})})}):(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md",children:[!W&&(0,t.jsxs)("div",{className:"mb-4 flex items-center gap-2 rounded-xl border border-destructive/20 bg-destructive/10 p-3 text-destructive",children:[(0,t.jsx)(a.A,{className:"size-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:M("noInternetConnection")})]}),(0,t.jsxs)("div",{className:"mb-8 text-center",children:[(0,t.jsx)("div",{className:"group mx-auto mb-6 flex size-16 items-center justify-center rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg ring-1 ring-primary/20 transition-all duration-300 hover:ring-primary/40",children:(0,t.jsx)(i.A,{className:"size-8 text-primary-foreground transition-transform group-hover:scale-110"})}),(0,t.jsx)("h1",{className:"mb-2 text-3xl font-bold tracking-tight text-foreground",children:M("welcomeBack")}),(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-muted-foreground",children:[(0,t.jsx)("span",{children:M("signInToAccount")}),W&&(0,t.jsx)(n.A,{className:"size-4 text-green-600"})]})]}),(0,t.jsxs)("div",{className:"rounded-2xl border border-border/60 bg-card p-8 shadow-xl backdrop-blur-sm",children:[C&&(0,t.jsxs)(v.Fc,{className:"mb-6 border-destructive/20 bg-destructive/5",variant:"destructive",children:[(0,t.jsx)(l.A,{className:"size-4"}),(0,t.jsx)(v.TN,{className:"text-destructive",children:C})]}),!W&&(0,t.jsxs)(v.Fc,{className:"mb-6 border-yellow-200 bg-yellow-50",children:[(0,t.jsx)(a.A,{className:"size-4"}),(0,t.jsx)(v.TN,{children:M("offlineWarning")})]}),(0,t.jsxs)("form",{className:"space-y-6",onSubmit:G,children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{className:"text-sm font-medium transition-colors ".concat("email"===q?"text-primary":"text-foreground"),htmlFor:"email",children:"Email address"}),(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:(0,t.jsx)(o.A,{className:"size-5 transition-colors ".concat("email"===q?"text-primary":"text-muted-foreground")})}),(0,t.jsx)(y.p,{autoComplete:"email",className:"h-12 pl-10 transition-all duration-200 ".concat(_.email?"border-destructive/50 focus-visible:ring-destructive/20":"hover:border-primary/30 focus-visible:ring-primary/20"," ").concat(E("email",Z.email)?"border-green-500/50 focus-visible:ring-green-500/20":""),disabled:S||!W||O,id:"email",onBlur:U,onChange:e=>K("email",e.target.value),onFocus:()=>Q("email"),placeholder:"Enter your email",type:"email",value:Z.email}),E("email",Z.email)&&(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3",children:(0,t.jsx)(d.A,{className:"size-5 text-green-500 duration-200 animate-in fade-in-0 zoom-in-95"})})]}),_.email&&(0,t.jsxs)("p",{className:"mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1",children:[(0,t.jsx)(l.A,{className:"size-4"}),_.email]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{className:"text-sm font-medium transition-colors ".concat("password"===q?"text-primary":"text-foreground"),htmlFor:"password",children:"Password"}),(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:(0,t.jsx)(i.A,{className:"size-5 transition-colors ".concat("password"===q?"text-primary":"text-muted-foreground")})}),(0,t.jsx)(y.p,{autoComplete:"current-password",className:"h-12 pl-10 pr-12 transition-all duration-200 ".concat(_.password?"border-destructive/50 focus-visible:ring-destructive/20":"hover:border-primary/30 focus-visible:ring-primary/20"," ").concat(E("password",Z.password)?"border-green-500/50 focus-visible:ring-green-500/20":""),disabled:S||!W||O,id:"password",onBlur:U,onChange:e=>K("password",e.target.value),onFocus:()=>Q("password"),placeholder:"Enter your password",type:P?"text":"password",value:Z.password}),(0,t.jsx)("button",{"aria-label":P?"Hide password":"Show password",className:"absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground transition-colors hover:text-foreground",disabled:S||O,onClick:()=>V(!P),type:"button",children:P?(0,t.jsx)(c.A,{className:"size-5"}):(0,t.jsx)(m.A,{className:"size-5"})})]}),_.password&&(0,t.jsxs)("p",{className:"mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1",children:[(0,t.jsx)(l.A,{className:"size-4"}),_.password]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.S,{checked:Z.rememberMe,className:"data-[state=checked]:border-primary data-[state=checked]:bg-primary",disabled:S||O,id:"remember-me",onCheckedChange:e=>K("rememberMe",e)}),(0,t.jsx)(w.J,{className:"cursor-pointer text-sm text-muted-foreground transition-colors hover:text-foreground",htmlFor:"remember-me",children:"Remember me"})]}),s&&(0,t.jsx)("button",{className:"text-sm font-medium text-primary transition-colors hover:text-primary/80",disabled:S||O,onClick:s,type:"button",children:"Forgot password?"})]}),(0,t.jsx)(j.$,{className:"group h-12 w-full rounded-xl bg-gradient-to-r from-primary to-accent font-semibold text-primary-foreground shadow-lg transition-all duration-200 hover:from-primary/90 hover:to-accent/90 hover:shadow-xl disabled:opacity-50",disabled:S||!W||O,type:"submit",children:S||O?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.A,{className:"mr-2 size-5 animate-spin"}),"Signing in..."]}):(0,t.jsxs)(t.Fragment,{children:["Sign in",(0,t.jsx)(x.A,{className:"ml-2 size-5 transition-transform group-hover:translate-x-1"})]})}),(0,t.jsxs)("div",{className:"rounded-xl border border-border/40 bg-muted/30 p-4",children:[(0,t.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Demo Access"})]}),(0,t.jsxs)("div",{className:"space-y-1 text-xs text-muted-foreground",children:[(0,t.jsx)("div",{className:"font-mono",children:"<EMAIL>"}),(0,t.jsx)("div",{className:"font-mono",children:"demo123"})]})]})]})]}),r&&(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,t.jsx)("button",{className:"font-medium text-primary transition-colors hover:text-primary/80",disabled:S||O,onClick:r,children:"Create one now"})]})}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)("div",{className:"inline-flex items-center gap-2 rounded-full border border-border/40 bg-card px-4 py-2 text-xs text-muted-foreground",children:[(0,t.jsx)(f.A,{className:"size-4 text-green-600"}),(0,t.jsx)("span",{children:"Protected by enterprise-grade security"})]})}),(0,t.jsxs)("footer",{className:"mt-8 text-center text-xs text-muted-foreground",children:[(0,t.jsx)("p",{children:"\xa9 2024 WorkHub. All rights reserved."}),(0,t.jsxs)("div",{className:"mt-2 space-x-4",children:[(0,t.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Terms"}),(0,t.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Privacy"}),(0,t.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Support"})]})]})]})})}}}]);