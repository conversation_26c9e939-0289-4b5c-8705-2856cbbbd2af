(()=>{var e={};e.id=6891,e.ids=[6891],e.modules={2756:(e,t,r)=>{Promise.resolve().then(r.bind(r,5277))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3940:(e,t,r)=>{"use strict";r.d(t,{O_:()=>o,t6:()=>a});var s=r(43210),i=r(49278);function a(){let e=(0,s.useCallback)((e,t)=>i.JP.success(e,t),[]),t=(0,s.useCallback)((e,t)=>i.JP.error(e,t),[]),r=(0,s.useCallback)((e,t)=>i.JP.info(e,t),[]),a=(0,s.useCallback)(t=>e(t?.successTitle||"Success",t?.successDescription||"Operation completed successfully"),[e]),o=(0,s.useCallback)((e,r)=>{let s=e instanceof Error?e.message:e;return t(r?.errorTitle||"Error",r?.errorDescription||s||"An unexpected error occurred")},[t]);return{showSuccess:e,showError:t,showInfo:r,showFormSuccess:a,showFormError:o}}function o(e){let t;switch(e){case"employee":t=r(49278).Ok;break;case"vehicle":t=r(49278).G7;break;case"task":t=r(49278).z0;break;case"delegation":t=r(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,t){let{showFormSuccess:r,showFormError:o}=a(),n=t||(e?(0,i.iw)(e):null),c=(0,s.useCallback)(e=>n?n.entityCreated(e):r({successTitle:"Created",successDescription:"Item has been created successfully"}),[n,r]),l=(0,s.useCallback)(e=>n?n.entityUpdated(e):r({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[n,r]),d=(0,s.useCallback)(e=>n?n.entityDeleted(e):r({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[n,r]),u=(0,s.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityCreationError(t)}return o(e,{errorTitle:"Creation Failed"})},[n,o]);return{showEntityCreated:c,showEntityUpdated:l,showEntityDeleted:d,showEntityCreationError:u,showEntityUpdateError:(0,s.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityUpdateError(t)}return o(e,{errorTitle:"Update Failed"})},[n,o]),showEntityDeletionError:(0,s.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityDeletionError(t)}return o(e,{errorTitle:"Deletion Failed"})},[n,o]),showFormSuccess:r,showFormError:o}}(void 0,t)}},5277:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\vehicles\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24501:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]])},26724:(e,t,r)=>{Promise.resolve().then(r.bind(r,41106))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35265:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},41106:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>$});var s=r(60687),i=r(24920),a=r(35265),o=r(77368),n=r(41936),c=r(85814),l=r.n(c),d=r(43210),u=r(77618),h=r(95668),p=r(24501),m=r(29333),g=r(8760),y=r(30474),f=r(68752),v=r(44493),x=r(35950),b=r(28149);function C({vehicle:e}){let t=(e.serviceHistory?.length||0)>0,r=null!==e.initialOdometer,i=t?Math.max(e.initialOdometer||0,...(e.serviceHistory||[]).map(e=>e.odometer)):e.initialOdometer||0;return(0,s.jsxs)(v.Zp,{className:"flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md",children:[(0,s.jsx)(v.aR,{className:"relative p-0",children:(0,s.jsx)("div",{className:"relative aspect-[16/10] w-full",children:(0,s.jsx)(y.default,{alt:`${e.make} ${e.model}`,className:"bg-muted object-cover","data-ai-hint":"luxury car",fill:!0,priority:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",src:(0,b.aI)(e.imageUrl,e.id)})})}),(0,s.jsxs)(v.Wu,{className:"flex grow flex-col p-5",children:[(0,s.jsxs)(v.ZB,{className:"mb-1 text-xl font-semibold text-primary",children:[e.make," ",e.model]}),(0,s.jsxs)(v.BT,{className:"mb-3 text-sm text-muted-foreground",children:[e.year," ",e.color&&`• ${e.color}`," ",e.licensePlate&&`• Plate: ${e.licensePlate}`]}),(0,s.jsx)(x.w,{className:"my-3 bg-border/50"}),(0,s.jsxs)("div",{className:"grow space-y-2.5 text-sm text-foreground",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Latest Odometer: "}),(0,s.jsx)("strong",{className:"font-semibold",children:t||r?`${i.toLocaleString()} miles`:"Not recorded"})]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(m.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Services Logged: "}),(0,s.jsx)("strong",{className:"font-semibold",children:e.serviceHistory?.length||0})]})]})]})]}),(0,s.jsx)(v.wL,{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,s.jsx)(f.r,{actionType:"tertiary",asChild:!0,className:"w-full",icon:(0,s.jsx)(g.A,{className:"size-4"}),children:(0,s.jsx)(l(),{href:`/vehicles/${e.id}`,children:"Manage Vehicle"})})})]})}var k=r(3940),w=r(72273);let j=({children:e})=>{let{showEntityDeleted:t,showEntityDeletionError:r}=(0,k.O_)("vehicle"),{data:s,error:i,isFetching:a,isLoading:o,refetch:n}=(0,w.T$)(),{isPending:c,mutateAsync:l}=(0,w.NS)(),d=async e=>{if(globalThis.confirm("Are you sure you want to delete this vehicle?"))try{await l(e),t({make:"Vehicle",model:""})}catch(e){console.error("Error deleting vehicle:",e),r(e.message||"Could not delete the vehicle.")}};return e({error:i?.message||null,fetchVehicles:n,handleDelete:d,isRefreshing:a,loading:o,vehicles:s||[]})};var E=r(12662),N=r(89667),S=r(52027),A=r(48041);let T=()=>{let e=(0,u.c3)("navigation"),[t,r]=(0,d.useState)("");return(0,s.jsx)(j,{children:({error:c,fetchVehicles:u,handleDelete:h,isRefreshing:p,loading:m,vehicles:g})=>{let y=(0,d.useMemo)(()=>{if(m||c)return[];let e=t.toLowerCase();return g.filter(t=>t.make.toLowerCase().includes(e)||t.model.toLowerCase().includes(e)||t.year.toString().includes(e)||t.licensePlate?.toLowerCase().includes(e)||t.vin?.toLowerCase().includes(e)||t.ownerName?.toLowerCase().includes(e))},[g,t,m,c]);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(E.AppBreadcrumb,{homeHref:"/",homeLabel:e("dashboard")}),(0,s.jsx)(A.z,{description:"Manage, track, and gain insights into your vehicles.",icon:i.A,title:"My Vehicle Fleet",children:(0,s.jsx)("div",{className:"flex gap-2",children:(0,s.jsx)(f.r,{actionType:"primary",asChild:!0,icon:(0,s.jsx)(a.A,{className:"size-4"}),children:(0,s.jsx)(l(),{href:"/vehicles/new",children:"Add New Vehicle"})})})}),(0,s.jsxs)("div",{className:"relative mb-6 rounded-lg bg-card p-4 shadow",children:[p&&!m&&(0,s.jsxs)("div",{className:"absolute right-4 top-4 flex items-center text-xs text-muted-foreground",children:[(0,s.jsx)(o.A,{className:"mr-1 size-3 animate-spin"}),"Updating list..."]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.A,{className:"absolute left-3 top-1/2 size-5 -translate-y-1/2 text-muted-foreground"}),(0,s.jsx)(N.p,{className:"w-full pl-10",onChange:e=>r(e.target.value),placeholder:"Search vehicles (Make, Model, Year, VIN, Plate, Owner...)",type:"text",value:t})]})]}),(0,s.jsx)(S.gO,{data:y,emptyComponent:(0,s.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,s.jsx)(i.A,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,s.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:t?"No Vehicles Match Your Search":"Your Garage is Empty!"}),(0,s.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:t?"Try adjusting your search terms or add a new vehicle to your fleet.":"It looks like you haven't added any vehicles yet. Let's get your first one set up."}),!t&&(0,s.jsx)(f.r,{actionType:"primary",asChild:!0,icon:(0,s.jsx)(a.A,{className:"size-4"}),size:"lg",children:(0,s.jsx)(l(),{href:"/vehicles/new",children:"Add Your First Vehicle"})})]}),error:c,isLoading:m,loadingComponent:(0,s.jsx)(S.jt,{count:3,variant:"card"}),onRetry:u,children:e=>(0,s.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:e.map(e=>(0,s.jsx)(C,{vehicle:e},e.id))})})]})}})};function $(){return(0,s.jsx)(h.A,{children:(0,s.jsx)(T,{})})}},49278:(e,t,r)=>{"use strict";r.d(t,{G7:()=>h,Gb:()=>c,JP:()=>l,Ok:()=>d,Qu:()=>u,iw:()=>n,oz:()=>m,z0:()=>p});var s=r(3389);class i{show(e){return(0,s.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class a extends i{constructor(e){super(),this.config=e}entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class o extends i{serviceRecordCreated(e,t){return this.success("Service Record Added",`${t} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,t){return this.success("Service Record Updated",`${t} service for "${e}" has been updated.`)}serviceRecordDeleted(e,t){return this.success("Service Record Deleted",`${t} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function n(e){return new a(e)}function c(e,t){return new a({entityName:e,getDisplayName:t,messages:{created:{title:`${e} Created`,description:t=>`The ${e.toLowerCase()} "${t}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:t=>t||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:t=>t||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:t=>t||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let l=new i,d=new a({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new a({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),h=new a({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),p=new a({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),m=new o},54050:(e,t,r)=>{"use strict";r.d(t,{n:()=>d});var s=r(43210),i=r(65406),a=r(33465),o=r(35536),n=r(31212),c=class extends o.Q{#e;#t=void 0;#r;#s;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,n.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,n.EN)(t.mutationKey)!==(0,n.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#a(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#i(),this.#a()}mutate(e,t){return this.#s=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#i(){let e=this.#r?.state??(0,i.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#a(e){a.jG.batch(()=>{if(this.#s&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#s.onSuccess?.(e.data,t,r),this.#s.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#s.onError?.(e.error,t,r),this.#s.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},l=r(8693);function d(e,t){let r=(0,l.jE)(t),[i]=s.useState(()=>new c(r,e));s.useEffect(()=>{i.setOptions(e)},[i,e]);let o=s.useSyncExternalStore(s.useCallback(e=>i.subscribe(a.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),d=s.useCallback((e,t)=>{i.mutate(e,t).catch(n.lQ)},[i]);if(o.error&&(0,n.GU)(i.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:d,mutateAsync:o.mutate}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64809:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>l});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(t,c);let l={children:["",{children:["[locale]",{children:["vehicles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5277)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/vehicles/page",pathname:"/[locale]/vehicles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},69795:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},72273:(e,t,r)=>{"use strict";r.d(t,{NS:()=>m,T$:()=>d,W_:()=>u,Y1:()=>h,lR:()=>p});var s=r(8693),i=r(54050),a=r(46349),o=r(87676),n=r(48839),c=r(75176);let l={all:["vehicles"],detail:e=>["vehicles",e]},d=e=>(0,a.GK)([...l.all],async()=>(await c.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,t)=>(0,a.GK)([...l.detail(e)],()=>c.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(t?.enabled??!0),staleTime:3e5,...t}),h=()=>{let e=(0,s.jE)(),{showError:t,showSuccess:r}=(0,o.useNotifications)();return(0,i.n)({mutationFn:e=>{let t=n.M.toCreateRequest(e);return c.vehicleApiService.create(t)},onError:e=>{t(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:t=>{e.invalidateQueries({queryKey:l.all}),r(`Vehicle "${t.licensePlate}" has been created successfully!`)}})},p=()=>{let e=(0,s.jE)(),{showError:t,showSuccess:r}=(0,o.useNotifications)();return(0,i.n)({mutationFn:({data:e,id:t})=>{let r=n.M.toUpdateRequest(e);return c.vehicleApiService.update(t,r)},onError:e=>{t(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:t=>{e.invalidateQueries({queryKey:l.all}),e.invalidateQueries({queryKey:l.detail(t.id)}),r(`Vehicle "${t.licensePlate}" has been updated successfully!`)}})},m=()=>{let e=(0,s.jE)(),{showError:t,showSuccess:r}=(0,o.useNotifications)();return(0,i.n)({mutationFn:e=>c.vehicleApiService.delete(e),onError:e=>{t(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(t,s)=>{e.invalidateQueries({queryKey:l.all}),e.removeQueries({queryKey:l.detail(s)}),r("Vehicle has been deleted successfully!")}})}},74075:e=>{"use strict";e.exports=require("zlib")},77368:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87676:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useNotifications:()=>a,useWorkHubNotifications:()=>o});var s=r(43210),i=r(94538);let a=()=>{let e=(0,i.C)(e=>e.addNotification),t=(0,i.C)(e=>e.removeNotification),r=(0,i.C)(e=>e.clearAllNotifications),a=(0,i.C)(e=>e.unreadNotificationCount),o=(0,s.useCallback)(t=>{e({message:t,type:"success"})},[e]),n=(0,s.useCallback)(t=>{e({message:t,type:"error"})},[e]),c=(0,s.useCallback)(t=>{e({message:t,type:"warning"})},[e]),l=(0,s.useCallback)(t=>{e({message:t,type:"info"})},[e]),d=(0,s.useCallback)((e,t,r)=>{e?o(t):n(r)},[o,n]),u=(0,s.useCallback)((r,s,a=5e3)=>{e({message:s,type:r}),setTimeout(()=>{let e=i.C.getState().notifications.at(-1);e&&e.message===s&&t(e.id)},a)},[e,t]),h=(0,s.useCallback)((t="Loading...")=>{e({message:t,type:"info"});let r=i.C.getState().notifications;return r.at(-1)?.id},[e]),p=(0,s.useCallback)((e,r,s)=>{t(e),r?o(s):n(s)},[t,o,n]);return{clearAllNotifications:r,removeNotification:t,showApiResult:d,showError:n,showInfo:l,showLoading:h,showSuccess:o,showTemporary:u,showWarning:c,unreadCount:a,updateLoadingNotification:p}},o=()=>{let{clearAllNotifications:e,removeNotification:t,showError:r,showInfo:o,showSuccess:n,showWarning:c,unreadCount:l}=a(),d=(0,s.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"delegation",message:e,type:"delegation-update"})},[]),u=(0,s.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),h=(0,s.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:t,showDelegationUpdate:d,showEmployeeUpdate:(0,s.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"employee",message:e,type:"employee-update"})},[]),showError:r,showInfo:o,showSuccess:n,showTaskAssigned:h,showVehicleMaintenance:u,showWarning:c,unreadCount:l}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95668:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(60687),i=r(14975),a=r(77368),o=r(43210),n=r(91821),c=r(29523);class l extends o.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,t){this.setState({errorInfo:t}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",t.componentStack),this.props.onError&&this.props.onError(e,t)}render(){let{description:e="An unexpected error occurred.",resetLabel:t="Try Again",title:r="Something went wrong"}=this.props;return this.state.hasError?this.props.fallback?this.props.fallback:(0,s.jsxs)(n.Fc,{className:"my-4",variant:"destructive",children:[(0,s.jsx)(i.A,{className:"mr-2 size-4"}),(0,s.jsx)(n.XL,{className:"text-lg font-semibold",children:r}),(0,s.jsxs)(n.TN,{className:"mt-2",children:[(0,s.jsx)("p",{className:"mb-2",children:this.state.error?.message||e}),!1,(0,s.jsxs)(c.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,s.jsx)(a.A,{className:"mr-2 size-4"}),t]})]})]}):this.props.children}}let d=l}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,3871,7048,8390,474,8739,3302,2936,3641],()=>r(64809));module.exports=s})();