(()=>{var e={};e.id=2196,e.ids=[2196],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4264:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\tasks\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28726:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>ex});var a=r(60687);let t=(0,r(82614).A)("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);var l=r(35265),i=r(58369),n=r(85814),d=r.n(n),c=r(43210),o=r(77618),m=r(95668),x=r(37392),u=r(29385);let h={entityType:"task",title:"Task Dashboard",description:"Oversee all tasks, assignments, and progress.",viewModes:["cards","table","list"],defaultViewMode:"cards",enableBulkActions:!0,enableExport:!0,refreshInterval:3e4},g=({className:e=""})=>{let{layout:s,monitoring:r,setViewMode:t,setGridColumns:l,toggleCompactMode:i,setMonitoringEnabled:n,setRefreshInterval:d,toggleAutoRefresh:c,resetSettings:o}=(0,u.fX)("task")();return(0,a.jsx)(x.s,{config:h,entityType:"task",layout:s,monitoring:r,setViewMode:t,setGridColumns:l,toggleCompactMode:i,setMonitoringEnabled:n,setRefreshInterval:d,toggleAutoRefresh:c,resetSettings:o,className:e})};var p=r(15036),j=r(58595),f=r(72963),v=r(3662),b=r(97025),y=r(88514),N=r(48206),k=r(92876),C=r(41936),w=r(90586),S=r(78726),A=r(96834),z=r(29523),E=r(89667),$=r(80013),M=r(35950),P=r(67146),R=r(40988),T=r(56896),I=r(26373),q=r(22482),L=r(75699);let D=[{value:"Pending",label:"Pending",icon:p.A,color:"text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-900/20 dark:border-amber-800"},{value:"Assigned",label:"Assigned",icon:j.A,color:"text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:border-blue-800"},{value:"In_Progress",label:"In Progress",icon:f.A,color:"text-purple-600 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-900/20 dark:border-purple-800"},{value:"Completed",label:"Completed",icon:v.A,color:"text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800"},{value:"Cancelled",label:"Cancelled",icon:b.A,color:"text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800"}],_=[{value:"Low",label:"Low Priority",color:"text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800"},{value:"Medium",label:"Medium Priority",color:"text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-900/20 dark:border-amber-800"},{value:"High",label:"High Priority",color:"text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800"}],F=({onFiltersChange:e,className:s,initialFilters:r={},employeesList:t=[]})=>{let[l,i]=(0,c.useState)({search:"",status:[],priority:[],assignee:[],dateRange:{},...r}),[n,d]=(0,c.useState)(!1),o=s=>{let r={...l,...s};i(r),e?.(r)},m=()=>{let s={search:"",status:[],priority:[],assignee:[],dateRange:{}};i(s),e?.(s)},x=e=>{o({status:l.status.includes(e)?l.status.filter(s=>s!==e):[...l.status,e]})},u=e=>{o({priority:l.priority.includes(e)?l.priority.filter(s=>s!==e):[...l.priority,e]})},h=e=>{o({assignee:l.assignee.includes(e)?l.assignee.filter(s=>s!==e):[...l.assignee,e]})},g=e=>{o({dateRange:{from:e?.from??void 0,to:e?.to??void 0}})},p=+!!l.search+l.status.length+l.priority.length+l.assignee.length+(l.dateRange.from||l.dateRange.to?1:0);return(0,a.jsxs)("div",{className:(0,q.cn)("flex flex-col gap-4",s),children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(C.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(E.p,{placeholder:"Search tasks...",value:l.search,onChange:e=>o({search:e.target.value}),className:"pl-10"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,a.jsxs)("div",{className:"hidden md:flex items-center gap-2",children:[(0,a.jsx)(()=>(0,a.jsxs)(R.AM,{children:[(0,a.jsx)(R.Wv,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(v.A,{className:"size-4"}),"Status",l.status.length>0&&(0,a.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:l.status.length})]})}),(0,a.jsx)(R.hl,{className:"w-56 p-3",align:"start",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Task Status"}),(0,a.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>o({status:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)(M.w,{}),(0,a.jsx)("div",{className:"space-y-2",children:D.map(e=>{let s=e.icon;return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(T.S,{id:`status-${e.value}`,checked:l.status.includes(e.value),onCheckedChange:()=>x(e.value)}),(0,a.jsxs)($.J,{htmlFor:`status-${e.value}`,className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,a.jsx)(s,{className:"size-3"}),(0,a.jsx)(A.E,{variant:"outline",className:(0,q.cn)("text-xs border",e.color),children:e.label})]})]},e.value)})})]})})]}),{}),(0,a.jsx)(()=>(0,a.jsxs)(R.AM,{children:[(0,a.jsx)(R.Wv,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(y.A,{className:"size-4"}),"Priority",l.priority.length>0&&(0,a.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:l.priority.length})]})}),(0,a.jsx)(R.hl,{className:"w-48 p-3",align:"start",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Priority Level"}),(0,a.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>o({priority:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)(M.w,{}),(0,a.jsx)("div",{className:"space-y-2",children:_.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(T.S,{id:`priority-${e.value}`,checked:l.priority.includes(e.value),onCheckedChange:()=>u(e.value)}),(0,a.jsx)($.J,{htmlFor:`priority-${e.value}`,className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:(0,a.jsx)(A.E,{variant:"outline",className:(0,q.cn)("text-xs border",e.color),children:e.label})})]},e.value))})]})})]}),{}),t&&t.length>0&&(0,a.jsx)(()=>(0,a.jsxs)(R.AM,{children:[(0,a.jsx)(R.Wv,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(N.A,{className:"size-4"}),"Assignee",l.assignee.length>0&&(0,a.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:l.assignee.length})]})}),(0,a.jsx)(R.hl,{className:"w-64 p-3",align:"start",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Assigned Employee"}),(0,a.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>o({assignee:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)(M.w,{}),(0,a.jsxs)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(T.S,{id:"assignee-unassigned",checked:l.assignee.includes("unassigned"),onCheckedChange:()=>h("unassigned")}),(0,a.jsxs)($.J,{htmlFor:"assignee-unassigned",className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,a.jsx)(j.A,{className:"size-3 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Unassigned"})]})]}),t?.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(T.S,{id:`assignee-${e.id}`,checked:l.assignee.includes(e.id),onCheckedChange:()=>h(e.id)}),(0,a.jsxs)($.J,{htmlFor:`assignee-${e.id}`,className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,a.jsx)(j.A,{className:"size-3"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.role})]})]})]},e.id))]})]})})]}),{}),(0,a.jsx)(()=>(0,a.jsxs)(R.AM,{children:[(0,a.jsx)(R.Wv,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(k.A,{className:"size-4"}),"Date Range",(l.dateRange.from||l.dateRange.to)&&(0,a.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:"1"})]})}),(0,a.jsx)(R.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Task Date Range"}),(0,a.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>o({dateRange:{}}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)(I.V,{mode:"range",selected:{from:l.dateRange.from,to:l.dateRange.to},onSelect:g,numberOfMonths:2,className:"rounded-md border-0"}),(0,a.jsx)("div",{className:"mt-3 text-xs text-muted-foreground text-center",children:l.dateRange.from&&!l.dateRange.to?"Select end date to complete range":"Click start date, then end date"})]})})]}),{})]}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsxs)(P.cj,{open:n,onOpenChange:d,children:[(0,a.jsx)(P.CG,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(w.A,{className:"size-4"}),"Filters",p>0&&(0,a.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:p})]})}),(0,a.jsxs)(P.h,{side:"bottom",className:"h-[80vh]",children:[(0,a.jsxs)(P.Fm,{children:[(0,a.jsx)(P.qp,{children:"Filter Tasks"}),(0,a.jsx)(P.Qs,{children:"Refine your task list with advanced filters"})]}),(0,a.jsxs)("div",{className:"grid gap-6 py-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)($.J,{className:"text-sm font-medium",children:"Status"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:D.map(e=>{let s=e.icon;return(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,a.jsx)(T.S,{id:`mobile-status-${e.value}`,checked:l.status.includes(e.value),onCheckedChange:()=>x(e.value)}),(0,a.jsxs)($.J,{htmlFor:`mobile-status-${e.value}`,className:"flex items-center gap-1 cursor-pointer text-xs flex-1",children:[(0,a.jsx)(s,{className:"size-3"}),e.label]})]},e.value)})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)($.J,{className:"text-sm font-medium",children:"Priority"}),(0,a.jsx)("div",{className:"grid gap-2",children:_.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,a.jsx)(T.S,{id:`mobile-priority-${e.value}`,checked:l.priority.includes(e.value),onCheckedChange:()=>u(e.value)}),(0,a.jsx)($.J,{htmlFor:`mobile-priority-${e.value}`,className:"cursor-pointer text-sm flex-1",children:e.label})]},e.value))})]}),t&&t.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)($.J,{className:"text-sm font-medium",children:"Assignee"}),(0,a.jsxs)("div",{className:"grid gap-2 max-h-48 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,a.jsx)(T.S,{id:"mobile-assignee-unassigned",checked:l.assignee.includes("unassigned"),onCheckedChange:()=>h("unassigned")}),(0,a.jsx)($.J,{htmlFor:"mobile-assignee-unassigned",className:"cursor-pointer text-sm flex-1",children:"Unassigned"})]}),t.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,a.jsx)(T.S,{id:`mobile-assignee-${e.id}`,checked:l.assignee.includes(e.id),onCheckedChange:()=>h(e.id)}),(0,a.jsx)($.J,{htmlFor:`mobile-assignee-${e.id}`,className:"cursor-pointer text-sm flex-1",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.role})]})})]},e.id))]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)($.J,{className:"text-sm font-medium",children:"Date Range"}),(0,a.jsx)("div",{className:"border rounded-md p-3",children:(0,a.jsx)(I.V,{mode:"range",selected:{from:l.dateRange.from,to:l.dateRange.to},onSelect:g,numberOfMonths:1,className:"rounded-md border-0"})})]}),(0,a.jsx)(z.$,{variant:"outline",onClick:m,className:"w-full",children:"Clear All Filters"})]})]})]})}),p>0&&(0,a.jsxs)(z.$,{variant:"ghost",size:"sm",onClick:m,className:"gap-1 text-muted-foreground hover:text-foreground hidden md:flex",children:[(0,a.jsx)(S.A,{className:"size-3"}),"Clear (",p,")"]})]}),p>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[l.search&&(0,a.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:['Search: "',l.search,'"',(0,a.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>o({search:""}),children:(0,a.jsx)(S.A,{className:"size-3"})})]}),l.status.map(e=>{let s=D.find(s=>s.value===e);return s?(0,a.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:[s.label,(0,a.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>x(e),children:(0,a.jsx)(S.A,{className:"size-3"})})]},e):null}),l.priority.map(e=>{let s=_.find(s=>s.value===e);return s?(0,a.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:[s.label,(0,a.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>u(e),children:(0,a.jsx)(S.A,{className:"size-3"})})]},e):null}),l.assignee.map(e=>{let s=t?.find(s=>s.id===e),r="unassigned"===e?"Unassigned":s?.name||"Unknown";return(0,a.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:["Assignee: ",r,(0,a.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>h(e),children:(0,a.jsx)(S.A,{className:"size-3"})})]},e)}),(l.dateRange.from||l.dateRange.to)&&(0,a.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:["Date:"," ",l.dateRange.from?(0,L.GP)(l.dateRange.from,"MMM d"):"?"," ","-"," ",l.dateRange.to?(0,L.GP)(l.dateRange.to,"MMM d, yyyy"):"?",(0,a.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>o({dateRange:{}}),children:(0,a.jsx)(S.A,{className:"size-3"})})]})]})]})};var G=r(58261),J=r(26398),V=r(24920),H=r(14975),O=r(8760),U=r(68752),W=r(44493),B=r(15795);let X=e=>{switch(e){case"Assigned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Completed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-indigo-500/20 text-indigo-700 border-indigo-500/30 dark:text-indigo-400 dark:bg-indigo-500/10 dark:border-indigo-500/20";case"Pending":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},Y=e=>{switch(e){case"High":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Low":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"Medium":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},Z=e=>{if(!e)return"N/A";try{return(0,L.GP)((0,G.H)(e),"MMM d, yyyy HH:mm")}catch{return"Invalid Date"}};function K({task:e}){let s=!!e.staffEmployeeId,r=!!e.driverEmployeeId,t=!!e.vehicleId;return(0,a.jsxs)(W.Zp,{className:"flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md",children:[(0,a.jsxs)(W.aR,{className:"p-5",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,a.jsx)(W.ZB,{className:"line-clamp-2 text-lg font-semibold text-primary",title:e.description,children:e.description}),(0,a.jsxs)("div",{className:"flex shrink-0 flex-col items-end gap-1",children:[(0,a.jsx)(A.E,{className:(0,q.cn)("text-xs py-1 px-2 font-semibold",X(e.status)),children:e.status}),(0,a.jsxs)(A.E,{className:(0,q.cn)("text-xs py-1 px-2 font-semibold",Y(e.priority)),children:[e.priority," Priority"]})]})]}),(0,a.jsxs)(W.BT,{className:"flex items-center pt-1 text-sm text-muted-foreground",children:[(0,a.jsx)(J.A,{className:"mr-1.5 size-4 shrink-0 text-accent"}),e.location]})]}),(0,a.jsxs)(W.Wu,{className:"flex grow flex-col p-5",children:[(0,a.jsx)(M.w,{className:"my-3 bg-border/50"}),(0,a.jsxs)("div",{className:"grow space-y-2.5 text-sm text-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(k.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Start: "}),(0,a.jsx)("strong",{className:"font-semibold",children:Z(e.dateTime)})]})]}),e.deadline&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Deadline: "}),(0,a.jsx)("strong",{className:"font-semibold",children:Z(e.deadline)})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Duration: "}),(0,a.jsxs)("strong",{className:"font-semibold",children:[e.estimatedDuration," mins"]})]})]}),s&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Staff: "}),(0,a.jsx)("strong",{className:"font-semibold",children:e.staffEmployee?(0,B.DV)(e.staffEmployee):`ID: ${e.staffEmployeeId}`})]})]}),r&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Driver: "}),(0,a.jsx)("strong",{className:"font-semibold",children:e.driverEmployee?(0,B.DV)(e.driverEmployee):`ID: ${e.driverEmployeeId}`})]})]}),t&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(V.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Vehicle: "}),(0,a.jsx)("strong",{className:"font-semibold",children:e.vehicle?`${e.vehicle.make} ${e.vehicle.model} (${e.vehicle.licensePlate||`ID: ${e.vehicle.id}`})`:`ID: ${e.vehicleId}`})]})]}),!s&&"Completed"!==e.status&&"Cancelled"!==e.status&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(H.A,{className:"mr-2.5 size-4 shrink-0 text-destructive"}),(0,a.jsx)("strong",{className:"font-semibold text-destructive",children:"No Staff Assigned"})]})]}),e.notes&&(0,a.jsx)("p",{className:"mt-3 line-clamp-2 border-t border-dashed border-border/50 pt-2 text-xs text-muted-foreground",title:e.notes,children:e.notes})]}),(0,a.jsx)(W.wL,{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,a.jsx)(U.r,{actionType:"tertiary",asChild:!0,className:"w-full",icon:(0,a.jsx)(O.A,{className:"size-4"}),children:(0,a.jsx)(d(),{href:`/tasks/${e.id}`,children:"View Details"})})})]})}var Q=r(15209);let ee=({className:e="",compactMode:s,tasks:r,gridColumns:t=3,viewMode:l})=>{switch(l){case"list":return(0,a.jsx)("div",{className:(0,q.cn)("flex flex-col",s?"gap-2":"gap-4",e),children:r.map(e=>(0,a.jsx)(K,{task:e},e.id))});case"table":return(0,a.jsx)(Q.z,{className:e,tasks:r});default:return(0,a.jsx)("div",{className:(0,q.cn)("grid grid-cols-1 gap-6",`md:grid-cols-2 lg:grid-cols-${t}`,s&&"gap-3",e),children:r.map(e=>(0,a.jsx)(K,{task:e},e.id))})}};var es=r(69981),er=r(12662),ea=r(63503),et=r(52027),el=r(48041),ei=r(19599),en=r(73227),ed=r(72273),ec=r(83144);function eo(){return(0,a.jsxs)("div",{className:"flex h-full flex-col overflow-hidden rounded-lg border-border/60 bg-card shadow-md",children:[(0,a.jsxs)("div",{className:"flex grow flex-col p-5",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsx)(et.jt,{className:"mb-1 h-7 w-3/5 bg-muted/50",count:1,variant:"default"}),(0,a.jsx)(et.jt,{className:"mb-1 h-5 w-1/4 rounded-full bg-muted/50",count:1,variant:"default"})]}),(0,a.jsx)(et.jt,{className:"mb-3 h-4 w-1/2 bg-muted/50",count:1,variant:"default"}),(0,a.jsx)(et.jt,{className:"my-3 h-px w-full bg-border/50",count:1,variant:"default"}),(0,a.jsx)("div",{className:"grow space-y-2.5",children:Array.from({length:3}).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(et.jt,{className:"mr-2.5 size-5 rounded-full bg-muted/50",count:1,variant:"default"}),(0,a.jsx)(et.jt,{className:"h-5 w-2/3 bg-muted/50",count:1,variant:"default"})]},s))})]}),(0,a.jsx)("div",{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,a.jsx)(et.jt,{className:"h-10 w-full bg-muted/50",count:1,variant:"default"})})]})}let em=()=>{(0,o.c3)("common");let e=(0,o.c3)("navigation"),{layout:s}=(0,u.fX)("task")(),{data:r=[],error:n,isLoading:m,refetch:x}=(0,en.si)(),{data:h=[],error:p,isLoading:j,refetch:f}=(0,ei.nR)(),{data:v=[],error:b,isLoading:y,refetch:N}=(0,ed.T$)(),[k,C]=(0,c.useState)(""),[w,S]=(0,c.useState)("all"),[A,z]=(0,c.useState)("all"),[E,$]=(0,c.useState)("all"),[M,P]=(0,c.useState)({}),R=(0,c.useMemo)(()=>h.map(e=>({id:String(e.id),name:e.fullName??e.name,role:e.role})),[h]),T=(0,c.useMemo)(()=>{let e=[...r.map(e=>(0,ec.R)(e,h,v))],s=k.toLowerCase();return"all"!==w&&(e=e.filter(e=>e.status===w)),"all"!==A&&(e=e.filter(e=>e.priority===A)),"all"!==E&&(e=e.filter(e=>(e.staffEmployeeId&&String(e.staffEmployeeId)===E||e.driverEmployeeId&&String(e.driverEmployeeId)===E)??("unassigned"===E&&!e.staffEmployeeId&&!e.driverEmployeeId))),s&&(e=e.filter(e=>{let r=e.staffEmployeeId?R.find(s=>s.id===String(e.staffEmployeeId)):null,a=e.driverEmployeeId?R.find(s=>s.id===String(e.driverEmployeeId)):null;return(e.description.toLowerCase().includes(s)||e.location.toLowerCase().includes(s)||e.notes?.toLowerCase().includes(s))??r?.name.toLowerCase().includes(s)??a?.name.toLowerCase().includes(s)})),(M.from||M.to)&&(e=e.filter(e=>{let s=new Date(e.createdAt);return M.from&&M.to?s>=M.from&&s<=M.to:M.from?s>=M.from:!M.to||s<=M.to})),e},[k,r,h,v,w,A,E,R,M.from,M.to]),I=(0,c.useCallback)(async()=>{await Promise.all([x(),f(),N()])},[x,f,N]),q=k||"all"!==w||"all"!==A||"all"!==E;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(er.AppBreadcrumb,{homeHref:"/",homeLabel:e("dashboard")}),(0,a.jsx)(el.z,{description:"Oversee all tasks, assignments, and progress.",icon:t,title:"Manage Tasks",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(U.r,{actionType:"primary",asChild:!0,icon:(0,a.jsx)(l.A,{className:"size-4"}),children:(0,a.jsx)(d(),{href:"/tasks/add",children:"Add New Task"})}),(0,a.jsx)(es.M,{getReportUrl:()=>{let e=new URLSearchParams({employee:E,priority:A,searchTerm:k,status:w}).toString();return`/tasks/report?${e}`},isList:!0}),(0,a.jsxs)(ea.lG,{children:[(0,a.jsx)(ea.zM,{asChild:!0,children:(0,a.jsx)(U.r,{actionType:"secondary",icon:(0,a.jsx)(i.A,{className:"size-4"}),children:"Settings"})}),(0,a.jsxs)(ea.Cf,{className:"sm:max-w-[600px]",children:[(0,a.jsx)(ea.L3,{children:"Dashboard Settings"}),(0,a.jsx)(ea.rr,{children:"Customize how tasks are displayed and managed."}),(0,a.jsx)(g,{})]})]})]})}),(0,a.jsx)(F,{employeesList:R,initialFilters:{assignee:"all"===E?[]:[E],dateRange:M,priority:"all"===A?[]:[A],search:k,status:"all"===w?[]:[w]},onFiltersChange:e=>{C(e.search),S(e.status.length>0?e.status[0]:"all"),z(e.priority.length>0?e.priority[0]:"all"),$(e.assignee.length>0?e.assignee[0]:"all"),P(e.dateRange)}}),(0,a.jsx)(et.gO,{data:T,emptyComponent:(0,a.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,a.jsx)(t,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,a.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:q?"No Tasks Match Your Filters":"No Tasks Created Yet"}),(0,a.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:q?"Try adjusting your search or filter criteria.":"It looks like you haven't created any tasks yet. Get started by adding one."}),!q&&(0,a.jsx)(U.r,{actionType:"primary",asChild:!0,icon:(0,a.jsx)(l.A,{className:"size-4"}),size:"lg",children:(0,a.jsx)(d(),{href:"/tasks/add",children:"Create Your First Task"})})]}),error:n?.message??p?.message??b?.message??null,isLoading:m||j||y,loadingComponent:(0,a.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:Array.from({length:3}).map((e,s)=>(0,a.jsx)(eo,{},s))}),onRetry:I,children:e=>(0,a.jsx)(ee,{compactMode:s.compactMode,gridColumns:s.gridColumns,tasks:e,viewMode:"calendar"===s.viewMode?"cards":s.viewMode})})]})};function ex(){return(0,a.jsx)(m.A,{children:(0,a.jsx)(em,{})})}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30829:(e,s,r)=>{Promise.resolve().then(r.bind(r,28726))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},51529:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=r(65239),t=r(48088),l=r(88170),i=r.n(l),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let c={children:["",{children:["[locale]",{children:["tasks",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4264)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/[locale]/tasks/page",pathname:"/[locale]/tasks",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67213:(e,s,r)=>{Promise.resolve().then(r.bind(r,4264))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[4447,3871,7048,8390,2670,4897,6362,6805,2890,8739,3302,2936,9599,742,5009,3439,3042,4867],()=>r(51529));module.exports=a})();