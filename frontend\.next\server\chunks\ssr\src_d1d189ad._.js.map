{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/i18n/config.ts"], "sourcesContent": ["/**\n * @file i18n Configuration\n * @module i18n/config\n *\n * Central configuration for internationalization settings.\n * Defines supported locales, default locale, and locale-specific settings.\n */\n\n/**\n * Supported locales in the application\n */\nexport const locales = ['en-US', 'ar-IQ'] as const;\n\n/**\n * Default locale for the application\n */\nexport const defaultLocale = 'en-US' as const;\n\n/**\n * Type for supported locales\n */\nexport type Locale = (typeof locales)[number];\n\n/**\n * RTL (Right-to-Left) locales\n */\nexport const rtlLocales = ['ar-IQ'] as const;\n\n/**\n * Check if a locale is RTL\n */\nexport function isRTLLocale(locale: string): boolean {\n  return rtlLocales.includes(locale as any);\n}\n\n/**\n * Locale display names for UI\n */\nexport const localeNames: Record<Locale, string> = {\n  'en-US': 'English',\n  'ar-IQ': 'العربية',\n};\n\n/**\n * Locale configuration for next-intl\n */\nexport const localeConfig = {\n  locales,\n  defaultLocale,\n  localePrefix: 'always' as const,\n};\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;;CAEC;;;;;;;;AACM,MAAM,UAAU;IAAC;IAAS;CAAQ;AAKlC,MAAM,gBAAgB;AAUtB,MAAM,aAAa;IAAC;CAAQ;AAK5B,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B;AAKO,MAAM,cAAsC;IACjD,SAAS;IACT,SAAS;AACX;AAKO,MAAM,eAAe;IAC1B;IACA;IACA,cAAc;AAChB", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\n\nimport { defaultLocale, type Locale, locales } from './config';\nimport { routing } from './routing';\n\nexport default getRequestConfig(async ({ requestLocale }) => {\n  // This typically corresponds to the `[locale]` segment\n  let locale = await requestLocale;\n\n  // Ensure that the incoming locale is valid\n  if (!locale || !locales.includes(locale as Locale)) {\n    locale = defaultLocale;\n  }\n\n  return {\n    formats: {\n      currency: {\n        currency: locale === 'ar-IQ' ? 'IQD' : 'USD',\n        style: 'currency',\n      },\n      dateTime: {\n        short: {\n          day: 'numeric',\n          month: 'short',\n          year: 'numeric',\n        },\n      },\n      list: {\n        enumeration: {\n          style: 'long',\n          type: 'conjunction',\n        },\n      },\n      number: {\n        precise: {\n          maximumFractionDigits: 5,\n        },\n      },\n    },\n    locale,\n    messages: await import(`../messages/${locale}.json`).then(\n      (module: any) => module.default\n    ),\n    now: new Date(),\n    timeZone: 'UTC',\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;AAGA;;;uCAGe,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,aAAa,EAAE;IACtD,uDAAuD;IACvD,IAAI,SAAS,MAAM;IAEnB,2CAA2C;IAC3C,IAAI,CAAC,UAAU,CAAC,qHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAmB;QAClD,SAAS,qHAAA,CAAA,gBAAa;IACxB;IAEA,OAAO;QACL,SAAS;YACP,UAAU;gBACR,UAAU,WAAW,UAAU,QAAQ;gBACvC,OAAO;YACT;YACA,UAAU;gBACR,OAAO;oBACL,KAAK;oBACL,OAAO;oBACP,MAAM;gBACR;YACF;YACA,MAAM;gBACJ,aAAa;oBACX,OAAO;oBACP,MAAM;gBACR;YACF;YACA,QAAQ;gBACN,SAAS;oBACP,uBAAuB;gBACzB;YACF;QACF;QACA;QACA,UAAU,MAAM;;;;;;;;;kBAAO,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,EAAE,IAAI,CACvD,CAAC,SAAgB,OAAO,OAAO;QAEjC,KAAK,IAAI;QACT,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/i18n/RTLProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const RTLProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call RTLProvider() from the server but RTLProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/i18n/RTLProvider.tsx <module evaluation>\",\n    \"RTLProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8DACA", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/i18n/RTLProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const RTLProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call RTLProvider() from the server but RTLProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/i18n/RTLProvider.tsx\",\n    \"RTLProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0CACA", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/security/CSPProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CSPProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CSPProvider() from the server but CSPProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx <module evaluation>\",\n    \"CSPProvider\",\n);\nexport const createSecureScriptProps = registerClientReference(\n    function() { throw new Error(\"Attempted to call createSecureScriptProps() from the server but createSecureScriptProps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx <module evaluation>\",\n    \"createSecureScriptProps\",\n);\nexport const initializeCSPViolationReporting = registerClientReference(\n    function() { throw new Error(\"Attempted to call initializeCSPViolationReporting() from the server but initializeCSPViolationReporting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx <module evaluation>\",\n    \"initializeCSPViolationReporting\",\n);\nexport const useCSP = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCSP() from the server but useCSP is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx <module evaluation>\",\n    \"useCSP\",\n);\nexport const useCSPReporting = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCSPReporting() from the server but useCSPReporting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx <module evaluation>\",\n    \"useCSPReporting\",\n);\nexport const useNonce = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNonce() from the server but useNonce is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx <module evaluation>\",\n    \"useNonce\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,kEACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,kEACA;AAEG,MAAM,kCAAkC,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjE;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,kEACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,kEACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,kEACA", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/security/CSPProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CSPProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CSPProvider() from the server but CSPProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx\",\n    \"CSPProvider\",\n);\nexport const createSecureScriptProps = registerClientReference(\n    function() { throw new Error(\"Attempted to call createSecureScriptProps() from the server but createSecureScriptProps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx\",\n    \"createSecureScriptProps\",\n);\nexport const initializeCSPViolationReporting = registerClientReference(\n    function() { throw new Error(\"Attempted to call initializeCSPViolationReporting() from the server but initializeCSPViolationReporting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx\",\n    \"initializeCSPViolationReporting\",\n);\nexport const useCSP = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCSP() from the server but useCSP is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx\",\n    \"useCSP\",\n);\nexport const useCSPReporting = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCSPReporting() from the server but useCSPReporting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx\",\n    \"useCSPReporting\",\n);\nexport const useNonce = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNonce() from the server but useNonce is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/security/CSPProvider.tsx\",\n    \"useNonce\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8CACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,8CACA;AAEG,MAAM,kCAAkC,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjE;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,8CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8CACA", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/app/%5Blocale%5D/layout-client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/[locale]/layout-client.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/[locale]/layout-client.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/app/%5Blocale%5D/layout-client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/[locale]/layout-client.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/[locale]/layout-client.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/app/%5Blocale%5D/layout-server.tsx"], "sourcesContent": ["/**\r\n * @file Server Layout Wrapper for CSP Nonce Access\r\n * @module app/layout-server\r\n *\r\n * Server component that extracts nonce from middleware headers\r\n * and provides it to the client layout through CSP context.\r\n */\r\n\r\nimport { headers, cookies } from 'next/headers';\r\nimport type { ReactNode } from 'react';\r\nimport { CSPProvider } from '@/lib/security/CSPProvider';\r\nimport ClientLayout from './layout-client';\r\n\r\ninterface ServerLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\n/**\r\n * Server Layout Wrapper\r\n *\r\n * Extracts nonce from middleware cookies and provides CSP context.\r\n * Single responsibility: Server-side nonce extraction and CSP setup.\r\n */\r\nexport default async function ServerLayout({ children }: ServerLayoutProps) {\r\n  // Extract nonce from middleware (try both headers and cookies)\r\n  const headersList = await headers();\r\n  const cookieStore = await cookies();\r\n  const nonceFromHeader = headersList.get('x-nonce');\r\n  const nonceFromCookie = cookieStore.get('x-nonce')?.value;\r\n\r\n  // Fallback: Generate a static nonce for development if middleware isn't working\r\n  const fallbackNonce =\r\n    process.env.NODE_ENV === 'development'\r\n      ? Buffer.from('dev-static-nonce-' + Date.now())\r\n          .toString('base64')\r\n          .substring(0, 16)\r\n      : null;\r\n\r\n  const nonce = nonceFromHeader || nonceFromCookie || fallbackNonce;\r\n\r\n  // Log nonce for debugging (remove in production)\r\n  if (process.env.NODE_ENV === 'development') {\r\n    console.log(\r\n      '🔒 CSP Nonce extracted:',\r\n      nonce ? `Present (${nonce.substring(0, 16)}...)` : 'Missing'\r\n    );\r\n    console.log('🔍 Nonce from header:', nonceFromHeader || 'Not found');\r\n    console.log('🔍 Nonce from cookie:', nonceFromCookie || 'Not found');\r\n    console.log('🔍 Fallback nonce:', fallbackNonce || 'Not generated');\r\n    console.log(\r\n      '🔍 Final nonce source:',\r\n      nonceFromHeader\r\n        ? 'Header'\r\n        : nonceFromCookie\r\n          ? 'Cookie'\r\n          : fallbackNonce\r\n            ? 'Fallback'\r\n            : 'None'\r\n    );\r\n\r\n    // Debug: Show all available headers and cookies\r\n    const availableHeaders = Array.from(headersList.keys()).join(', ');\r\n    const availableCookies = Array.from(cookieStore.getAll())\r\n      .map(cookie => cookie.name)\r\n      .join(', ');\r\n    console.log('🔍 Available headers:', availableHeaders);\r\n    console.log('🔍 Available cookies:', availableCookies);\r\n  }\r\n\r\n  return (\r\n    <CSPProvider nonce={nonce}>\r\n      <ClientLayout>{children}</ClientLayout>\r\n    </CSPProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;AAED;AAEA;AACA;;;;;AAYe,eAAe,aAAa,EAAE,QAAQ,EAAqB;IACxE,+DAA+D;IAC/D,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,kBAAkB,YAAY,GAAG,CAAC;IACxC,MAAM,kBAAkB,YAAY,GAAG,CAAC,YAAY;IAEpD,gFAAgF;IAChF,MAAM,gBACJ,uCACI,OAAO,IAAI,CAAC,sBAAsB,KAAK,GAAG,IACvC,QAAQ,CAAC,UACT,SAAS,CAAC,GAAG;IAGtB,MAAM,QAAQ,mBAAmB,mBAAmB;IAEpD,iDAAiD;IACjD,wCAA4C;QAC1C,QAAQ,GAAG,CACT,2BACA,QAAQ,CAAC,SAAS,EAAE,MAAM,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG;QAErD,QAAQ,GAAG,CAAC,yBAAyB,mBAAmB;QACxD,QAAQ,GAAG,CAAC,yBAAyB,mBAAmB;QACxD,QAAQ,GAAG,CAAC,sBAAsB,iBAAiB;QACnD,QAAQ,GAAG,CACT,0BACA,kBACI,WACA,kBACE,WACA,gBACE,aACA;QAGV,gDAAgD;QAChD,MAAM,mBAAmB,MAAM,IAAI,CAAC,YAAY,IAAI,IAAI,IAAI,CAAC;QAC7D,MAAM,mBAAmB,MAAM,IAAI,CAAC,YAAY,MAAM,IACnD,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI,EACzB,IAAI,CAAC;QACR,QAAQ,GAAG,CAAC,yBAAyB;QACrC,QAAQ,GAAG,CAAC,yBAAyB;IACvC;IAEA,qBACE,8OAAC,sIAAA,CAAA,cAAW;QAAC,OAAO;kBAClB,cAAA,8OAAC,6IAAA,CAAA,UAAY;sBAAE;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["/**\n * @file Locale-Specific Layout - i18n Enabled Layout\n * @module app/[locale]/layout\n *\n * Layout component for locale-specific pages with i18n providers.\n * Handles RTL support, translations, and locale-specific metadata.\n */\n\nimport type { Metadata } from 'next';\nimport type { ReactNode } from 'react';\n\nimport { NextIntlClientProvider } from 'next-intl';\nimport { getMessages, getTranslations } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\n\nimport { type Locale, locales } from '@/i18n/config';\nimport { RTLProvider } from '@/lib/i18n/RTLProvider';\n\nimport ServerLayout from './layout-server';\nimport '../globals.css';\n\nimport 'leaflet/dist/leaflet.css';\n\ninterface LocaleLayoutProps {\n  children: ReactNode;\n  params: Promise<{ locale: string }>;\n}\n\n/**\n * Generate metadata for each locale\n */\nexport async function generateMetadata({\n  params,\n}: {\n  params: Promise<{ locale: string }>;\n}): Promise<Metadata> {\n  const { locale } = await params;\n  const t = await getTranslations({ locale, namespace: 'metadata' });\n\n  return {\n    authors: [{ name: 'WorkHub Team' }],\n    description: t('description'),\n    keywords: t('keywords').split(','),\n    openGraph: {\n      description: t('description'),\n      locale: locale === 'ar-IQ' ? 'ar_IQ' : 'en_US',\n      title: t('title'),\n      type: 'website',\n    },\n    robots: 'index, follow',\n    title: t('title'),\n  };\n}\n\n/**\n * Generate static params for all supported locales\n */\nexport function generateStaticParams() {\n  return locales.map(locale => ({ locale }));\n}\n\n/**\n * Locale-Specific Layout Component\n *\n * Provides i18n context and RTL support for all pages within a locale.\n * Validates locale and sets up translation providers.\n */\nexport default async function LocaleLayout({\n  children,\n  params,\n}: LocaleLayoutProps) {\n  const { locale } = await params;\n\n  // Validate locale\n  if (!locales.includes(locale as Locale)) {\n    notFound();\n  }\n\n  // Get messages for the locale\n  const messages = await getMessages({ locale });\n\n  return (\n    <html className=\"h-full\" lang={locale} suppressHydrationWarning>\n      <body className=\"flex min-h-screen flex-col font-sans antialiased\">\n        <NextIntlClientProvider messages={messages}>\n          <RTLProvider>\n            <ServerLayout>{children}</ServerLayout>\n          </RTLProvider>\n        </NextIntlClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;;AAKD;AACA;AAAA;AACA;AAAA;AAEA;AACA;AAEA;;;;;;;;;;AAaO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAW;IAEhE,OAAO;QACL,SAAS;YAAC;gBAAE,MAAM;YAAe;SAAE;QACnC,aAAa,EAAE;QACf,UAAU,EAAE,YAAY,KAAK,CAAC;QAC9B,WAAW;YACT,aAAa,EAAE;YACf,QAAQ,WAAW,UAAU,UAAU;YACvC,OAAO,EAAE;YACT,MAAM;QACR;QACA,QAAQ;QACR,OAAO,EAAE;IACX;AACF;AAKO,SAAS;IACd,OAAO,qHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;YAAE;QAAO,CAAC;AAC1C;AAQe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EACY;IAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IAEzB,kBAAkB;IAClB,IAAI,CAAC,qHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAmB;QACvC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,8BAA8B;IAC9B,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAO;IAE5C,qBACE,8OAAC;QAAK,WAAU;QAAS,MAAM;QAAQ,wBAAwB;kBAC7D,cAAA,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;gBAAC,UAAU;0BAChC,cAAA,8OAAC,kIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,6IAAA,CAAA,UAAY;kCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}]}