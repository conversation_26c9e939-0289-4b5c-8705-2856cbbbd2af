/**
 * @file API contract types for request/response payloads
 * @module types/apiContracts
 *
 * This file defines the exact structure of API requests and responses
 * as they are sent/received over the wire, separate from domain models.
 */

import type { DelegationStatusPrisma } from './domain';

// =============================================================================
// VEHICLE API CONTRACTS
// =============================================================================

/**
 * Request payload for acknowledging an alert
 */
export interface AcknowledgeAlertRequest {
  acknowledgedBy?: string;
  note?: string;
}

/**
 * Alert data as received from the API
 */
export interface AlertApiResponse {
  acknowledgedAt?: string;
  acknowledgedBy?: string;
  details?: Record<string, any>;
  id: string;
  message: string;
  resolvedAt?: string;
  resolvedBy?: string;
  severity: 'critical' | 'high' | 'low' | 'medium';
  source: string;
  status: 'acknowledged' | 'active' | 'resolved';
  timestamp: string;
  type: string;
}

/**
 * Alert history response with pagination
 */
export interface AlertHistoryApiResponse {
  alerts: AlertApiResponse[];
  pagination: {
    hasNext: boolean;
    hasPrevious: boolean;
    limit: number;
    page: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Alert statistics response
 */
export interface AlertStatisticsApiResponse {
  acknowledged: number;
  active: number;
  averageResolutionTime: number;
  bySeverity: {
    critical: number;
    high: number;
    low: number;
    medium: number;
  };
  recentTrends: {
    last7Days: number;
    last24Hours: number;
    last30Days: number;
  };
  resolved: number;
  total: number;
}

// =============================================================================
// DELEGATION API CONTRACTS
// =============================================================================

/**
 * Audit log data as received from the API
 */
export interface AuditLogApiResponse {
  action: string;
  created_at: string;
  details: string;
  id: string;
  ip_address?: null | string;
  user_agent?: null | string;
  user_id: string;
}

/**
 * Circuit breaker status response from monitoring endpoint
 */
export interface CircuitBreakerStatusApiResponse {
  circuitBreakers: {
    failureCount: number;
    lastFailureTime?: string;
    name: string;
    nextAttempt?: string;
    state: 'CLOSED' | 'HALF_OPEN' | 'OPEN';
    successCount?: number;
    timeout?: number;
  }[];
  summary: {
    closed: number;
    halfOpen: number;
    open: number;
    total: number;
  };
}

/**
 * Request payload for creating a delegate assignment
 */
export interface CreateDelegateRequest {
  name: string;
  notes?: null | string;
  title: string;
}

/**
 * Request payload for creating a new delegation
 * ✅ FIXED: Updated to match backend schema exactly
 */
export interface CreateDelegationRequest {
  delegates?: CreateDelegateRequest[];
  driverEmployeeIds?: number[]; // ✅ Backend expects array of IDs, not objects
  durationFrom: string; // ✅ Backend expects durationFrom, not startDate
  durationTo: string; // ✅ Backend expects durationTo, not endDate
  escortEmployeeIds?: number[]; // ✅ Backend expects array of IDs, not objects
  eventName: string; // ✅ Backend expects eventName, not title
  flightArrivalDetails?: CreateFlightDetailsRequest | undefined;
  flightDepartureDetails?: CreateFlightDetailsRequest | undefined;
  imageUrl?: string | null;
  invitationFrom?: string | null;
  invitationTo?: string | null;
  location: string;
  notes?: string | null; // ✅ Backend expects notes, not description
  status?: string;
  vehicleIds?: number[]; // ✅ Backend expects array of IDs, not vehicleAssignments
}

/**
 * Request payload for creating a driver assignment
 */
export interface CreateDriverRequest {
  employeeId: number;
  notes?: null | string;
}

/**
 * Request payload for creating a new employee
 */
export interface CreateEmployeeRequest {
  availability?: null | string;
  contactEmail?: null | string; // OPTIONAL
  contactInfo: string; // REQUIRED
  contactMobile?: null | string; // OPTIONAL
  contactPhone?: null | string; // OPTIONAL
  currentLocation?: null | string;
  department?: null | string; // OPTIONAL
  employeeId: string;
  fullName?: null | string; // OPTIONAL
  generalAssignments: string[]; // REQUIRED - Fixed from optional
  hireDate?: null | string; // OPTIONAL
  name: string; // REQUIRED
  notes?: null | string;
  position?: null | string; // OPTIONAL
  profileImageUrl?: null | string;
  role: string; // REQUIRED
  shiftSchedule?: null | string;
  skills: string[]; // REQUIRED - Fixed from optional
  status?: null | string; // OPTIONAL
  workingHours?: null | string; // Added to match Prisma schema
}

/**
 * Request payload for creating an escort assignment
 */
export interface CreateEscortRequest {
  employeeId: number;
  notes?: null | string;
}

/**
 * Request payload for creating flight details
 */
export interface CreateFlightDetailsRequest {
  airport: string;
  dateTime: string;
  flightNumber: string;
  notes?: null | string | undefined;
  terminal?: null | string | undefined;
}

/**
 * Request payload for creating a subtask (aligned with domain model)
 */
export interface CreateSubtaskRequest {
  completed?: boolean;
  taskId: string; // REQUIRED
  title: string;
}

// Note: Legacy escort, driver, and vehicle assignment types removed.
// Use DelegationEscortApiResponse, DelegationDriverApiResponse, and DelegationVehicleApiResponse instead.

/**
 * Request payload for creating a new task (aligned with backend schema)
 */
export interface CreateTaskRequest {
  dateTime: string; // REQUIRED - matches Prisma schema
  deadline?: null | string; // OPTIONAL - matches Prisma schema
  description: string; // REQUIRED - matches Prisma schema
  driverEmployeeId?: null | number; // OPTIONAL - matches Prisma schema
  estimatedDuration: number; // REQUIRED - matches Prisma schema
  location: string; // REQUIRED - matches Prisma schema
  notes?: null | string; // OPTIONAL - matches Prisma schema
  priority: string; // REQUIRED - TaskPriorityPrisma
  requiredSkills: string[]; // REQUIRED - matches Prisma schema
  staffEmployeeId: number; // REQUIRED - matches Prisma schema
  status: string; // REQUIRED - TaskStatusPrisma
  subTasks?: CreateSubtaskRequest[]; // OPTIONAL - Backend expects 'subTasks', not 'subtasks'
  vehicleId?: null | number; // OPTIONAL - matches Prisma schema
}

/**
 * Request payload for creating a vehicle assignment
 */
export interface CreateVehicleAssignmentRequest {
  assignedDate: string;
  notes?: null | string;
  returnDate?: null | string;
  vehicleId: number;
}

/**
 * Request payload for creating a new vehicle
 */
export interface CreateVehicleRequest {
  color?: null | string;
  imageUrl?: string;
  initialOdometer?: null | number;
  licensePlate: string;
  make: string;
  model: string;
  ownerContact: string; // Required by backend schema
  ownerName: string; // Required by backend schema
  vin: string; // Required by backend schema (17 characters)
  year: number;
}

/**
 * Request deduplication metrics response
 */
export interface DeduplicationMetricsApiResponse {
  cacheHits: number;
  cacheMisses: number;
  errors: number;
  hitRate: number;
  lastReset: string;
  totalRequests: number;
}

/**
 * Delegate information as received from the API
 */
export interface DelegateApiResponse {
  delegationId: string; // String in Prisma schema
  id: string;
  name: string;
  notes?: null | string; // Optional in Prisma schema
  title: string;
}

/**
 * Delegation data as received from the API
 */
export interface DelegationApiResponse {
  createdAt: string; // Matches backend DateTime, frontend uses string
  // Include relations that might be returned by the API with includes
  delegates?: DelegateApiResponse[]; // API returns nested delegates with lowercase field name
  drivers?: EmployeeApiResponse[]; // API returns Employee objects directly (backend transforms join table)
  durationFrom: string; // Matches backend DateTime, frontend uses string
  durationTo: string; // Matches backend DateTime, frontend uses string
  escorts?: EmployeeApiResponse[]; // API returns Employee objects directly (backend transforms join table)
  eventName: string; // Matches backend
  flightArrivalDetails?: FlightDetailsApiResponse | null; // API returns nested flight details
  flightArrivalId?: null | string; // Matches backend optional unique
  flightDepartureDetails?: FlightDetailsApiResponse | null; // API returns nested flight details
  flightDepartureId?: null | string; // Matches backend optional unique
  id: string; // Backend is String @id
  imageUrl?: null | string; // Matches backend optional
  invitationFrom?: null | string; // Matches backend optional

  invitationTo?: null | string; // Matches backend optional
  location: string; // Matches backend
  notes?: null | string; // Matches backend optional
  status: string; // Matches backend enum, frontend uses string
  statusHistory?: DelegationStatusEntryApiResponse[]; // API returns nested status entries
  updatedAt: string; // Matches backend DateTime, frontend uses string
  vehicles?: VehicleApiResponse[]; // API returns Vehicle objects directly (backend transforms join table)
}

export interface DelegationDriverApiResponse {
  createdAt: string;
  createdBy?: null | string;
  delegationId: string;
  Employee?: EmployeeApiResponse; // Assuming API includes employee details
  employeeId: number;
  id: string;
  updatedAt: string;
}

// =============================================================================
// TASK API CONTRACTS
// =============================================================================

export interface DelegationEscortApiResponse {
  createdAt: string;
  createdBy?: null | string;
  delegationId: string;
  Employee?: EmployeeApiResponse; // Assuming API includes employee details
  employeeId: number;
  id: string;
  updatedAt: string;
}

// =============================================================================
// RELIABILITY API CONTRACTS
// =============================================================================

// API response types for nested relations (matching Prisma schema structure when included)
export interface DelegationStatusEntryApiResponse {
  changedAt: string;
  delegationId: string;
  id: string;
  reason?: null | string;
  status: DelegationStatusPrisma; // DelegationStatusPrisma
}

export interface DelegationVehicleApiResponse {
  createdAt: string;
  createdBy?: null | string;
  delegationId: string;
  id: string;
  updatedAt: string;
  Vehicle?: VehicleApiResponse; // Assuming API includes vehicle details
  vehicleId: number;
}

/**
 * Dependency health check response
 */
export interface DependencyHealthApiResponse {
  dependencies: {
    error?: string;
    lastChecked: string;
    name: string;
    responseTime?: number;
    status: 'degraded' | 'healthy' | 'unhealthy';
  }[];
  summary: {
    degraded: number;
    healthy: number;
    total: number;
    unhealthy: number;
  };
}

/**
 * Detailed health check response with individual component status
 */
export interface DetailedHealthApiResponse {
  checks: {
    businessLogic: {
      status: 'degraded' | 'healthy' | 'unhealthy';
    };
    cache: {
      details?: {
        redis?: {
          status: string;
        };
      };
      status: 'degraded' | 'healthy' | 'unhealthy';
    };
    circuitBreakers: {
      details?: {
        openBreakers: number;
      };
      status: 'degraded' | 'healthy' | 'unhealthy';
    };
    database: {
      responseTime?: number;
      status: 'degraded' | 'healthy' | 'unhealthy';
    };
    supabase: {
      responseTime?: number;
      status: 'degraded' | 'healthy' | 'unhealthy';
    };
    systemResources: {
      details?: {
        memory?: {
          usagePercent: number;
        };
      };
      status: 'degraded' | 'healthy' | 'unhealthy';
    };
  };
  environment: string;
  status: 'degraded' | 'healthy' | 'unhealthy';
  summary: {
    degradedChecks: number;
    healthyChecks: number;
    totalChecks: number;
    unhealthyChecks: number;
  };
  timestamp: string;
  uptime: number;
  version: string;
}

/**
 * Employee data as received from the API
 */
export interface EmployeeApiResponse {
  availability?: null | string;
  contactEmail?: null | string; // OPTIONAL
  contactInfo: string; // REQUIRED - general contact
  contactMobile?: null | string; // OPTIONAL
  contactPhone?: null | string; // OPTIONAL
  createdAt: string;
  currentLocation?: null | string;
  department?: null | string; // OPTIONAL
  employeeId: string;
  fullName?: null | string; // OPTIONAL
  generalAssignments: string[]; // REQUIRED - Fixed from optional
  hireDate?: null | string; // OPTIONAL
  id: number;
  name: string; // REQUIRED - single name field
  notes?: null | string;
  position?: null | string; // OPTIONAL
  profileImageUrl?: null | string;
  role: string; // REQUIRED
  shiftSchedule?: null | string;
  skills: string[]; // REQUIRED - Fixed from optional
  status?: null | string; // OPTIONAL
  updatedAt: string;
  workingHours?: null | string; // Added to match Prisma schema
}

export interface EmployeeStatusEntryApiResponse {
  changedAt: string;
  employeeId: number;
  id: string;
  reason?: null | string;
  status: string; // EmployeeStatusPrisma as string over wire
}

/**
 * Flight details as received from the API (aligned with Prisma schema)
 */
export interface FlightDetailsApiResponse {
  airport: string;
  arrivalDelegationId?: null | string;
  dateTime: string;
  departureDelegationId?: null | string;
  flightNumber: string;
  id: string;
  notes?: null | string;
  terminal?: null | string;
}

/**
 * Basic health check response from the API
 */
export interface HealthCheckApiResponse {
  environment: string;
  status: 'degraded' | 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
}

/**
 * System metrics response from Prometheus endpoint
 */
export interface MetricsApiResponse {
  deduplicationMetrics: {
    cacheHits: number;
    cacheMisses: number;
    errors: number;
    hitRate: number;
    lastReset: string;
    totalRequests: number;
  };
  httpRequestMetrics: {
    help: string;
    name: string;
    type: string;
    values: {
      labels: {
        method: string;
        route: string;
        status_code: string;
        user_role: string;
      };
      value: number;
    }[];
  };
  systemMetrics: {
    connections: {
      active: number;
      total: number;
    };
    cpu: {
      loadAverage: number[];
      usage: number;
    };
    memory: {
      free: number;
      total: number;
      usagePercent: number;
      used: number;
    };
  };
}

/**
 * Request payload for resolving an alert
 */
export interface ResolveAlertRequest {
  reason?: string;
  resolvedBy?: string;
}

/**
 * Service record data as received from the API
 */
export interface ServiceRecordApiResponse {
  cost: null | number; // Decimal? in Prisma, can be null
  createdAt: string;
  date: string;
  employeeId: null | number;
  id: string;
  notes: null | string;
  odometer: number;
  servicePerformed: string[];
  updatedAt: string;
  vehicleId: number;
}

/**
 * Subtask data as received from the API (aligned with domain model)
 */
export interface SubtaskApiResponse {
  completed: boolean;
  id: string;
  taskId: string; // REQUIRED - FK to Task
  title: string;
}

/**
 * Task data as received from the API (aligned with backend schema)
 */
export interface TaskApiResponse {
  createdAt: string; // REQUIRED - matches Prisma schema
  dateTime: string; // REQUIRED - matches Prisma schema
  deadline: null | string; // OPTIONAL - matches Prisma schema
  description: string; // REQUIRED - matches Prisma schema
  driverEmployeeId?: null | number; // OPTIONAL - matches Prisma schema
  Employee_Task_driverEmployeeIdToEmployee?: EmployeeApiResponse | null; // Driver employee relation
  // Backend relation objects (from Prisma includes)
  Employee_Task_staffEmployeeIdToEmployee?: EmployeeApiResponse | null; // Staff employee relation
  estimatedDuration: number; // REQUIRED - matches Prisma schema
  id: string; // REQUIRED - matches Prisma schema
  location: string; // REQUIRED - matches Prisma schema
  notes: null | string; // OPTIONAL - matches Prisma schema
  priority: string; // REQUIRED - TaskPriorityPrisma
  requiredSkills: string[]; // REQUIRED - matches Prisma schema
  staffEmployeeId: number; // REQUIRED - matches Prisma schema
  status: string; // REQUIRED - TaskStatusPrisma
  SubTask: SubtaskApiResponse[]; // Backend returns 'SubTask' array

  updatedAt: string; // REQUIRED - matches Prisma schema
  Vehicle?: null | VehicleApiResponse; // Vehicle relation
  vehicleId?: null | number; // OPTIONAL - matches Prisma schema
}

export interface TaskStatusEntryApiResponse {
  changedAt: string;
  id: string;
  reason?: null | string;
  status: string; // TaskStatusPrisma as string over wire
  taskId: string;
}

/**
 * Test alerts response
 */
export interface TestAlertsApiResponse {
  message: string;
  success: boolean;
  testAlertId?: string;
}

/**
 * Request payload for updating a delegation
 * ✅ PRODUCTION FIX: Align with backend controller expectations
 */
export interface UpdateDelegationRequest {
  // ✅ FIX: Add delegates support for updates
  delegates?: CreateDelegateRequest[] | undefined; // Backend supports delegates in updates
  driverEmployeeIds?: number[] | undefined;
  durationFrom?: string | undefined; // Backend controller expects durationFrom directly
  durationTo?: string | undefined; // Backend controller expects durationTo directly
  // ✅ FIX: Add assignment fields that backend expects
  escortEmployeeIds?: number[] | undefined;
  // ✅ FIX: Backend expects eventName, durationFrom, durationTo, notes (not title, startDate, endDate, description)
  eventName?: string | undefined; // Backend controller expects eventName directly
  flightArrivalDetails?: Partial<CreateFlightDetailsRequest> | undefined;
  flightArrivalId?: null | string | undefined; // FK field
  flightDepartureDetails?: Partial<CreateFlightDetailsRequest> | undefined;
  flightDepartureId?: null | string | undefined; // FK field
  imageUrl?: null | string | undefined;
  invitationFrom?: null | string | undefined;
  invitationTo?: null | string | undefined;
  location?: string | undefined;
  notes?: null | string | undefined; // Backend controller expects notes directly
  status?: string | undefined;
  vehicleIds?: number[] | undefined;
}

// =============================================================================
// EMPLOYEE API CONTRACTS
// =============================================================================

/**
 * Request payload for updating an employee
 */
export interface UpdateEmployeeRequest {
  availability?: null | string;
  contactEmail?: null | string; // OPTIONAL
  contactInfo?: null | string; // OPTIONAL
  contactMobile?: null | string; // OPTIONAL
  contactPhone?: null | string; // OPTIONAL
  currentLocation?: null | string;
  department?: null | string; // OPTIONAL
  employeeId?: string;
  fullName?: null | string; // OPTIONAL
  generalAssignments?: string[]; // Keep optional for updates
  hireDate?: null | string; // OPTIONAL
  name?: null | string; // OPTIONAL
  notes?: null | string;
  position?: null | string; // OPTIONAL
  profileImageUrl?: null | string;
  role?: string; // OPTIONAL
  shiftSchedule?: null | string;
  skills?: string[]; // Keep optional for updates
  status?: null | string; // OPTIONAL
  workingHours?: null | string; // Added to match Prisma schema
}

/**
 * Request payload for updating a task (aligned with backend schema)
 */
export interface UpdateTaskRequest {
  dateTime?: string; // Added
  deadline?: null | string; // Maps from domain.deadline (backend uses 'deadline')
  description?: string; // Maps from domain.description
  driverEmployeeId?: null | number; // Added
  estimatedDuration?: number; // Added
  location?: string; // Added
  notes?: null | string; // Maps from domain.notes
  priority?: string; // TaskPriorityPrisma
  requiredSkills?: string[]; // Keep optional for updates
  staffEmployeeId?: number; // Added
  status?: string; // TaskStatusPrisma
  vehicleId?: null | number; // Added
  // subtasks are usually updated via separate endpoints, not main update payload
}

/**
 * Request payload for updating a vehicle
 */
export interface UpdateVehicleRequest {
  color?: null | string;
  imageUrl?: string;
  initialOdometer?: null | number;
  licensePlate?: string;
  make?: string;
  model?: string;
  ownerContact?: string;
  ownerName?: string;
  vin?: string;
  year?: number;
}

// =============================================================================
// SYSTEM MODEL API CONTRACTS
// =============================================================================

/**
 * User profile data as received from the API
 */
export interface UserProfileApiResponse {
  created_at: string;
  employee_id?: null | number;
  id: string;
  is_active: boolean;
  role: string;
  updated_at: string;
  users?: {
    email: string;
    email_confirmed_at: null | string;
  }[];
}

/**
 * Vehicle data as received from the API
 */
export interface VehicleApiResponse {
  color: null | string;
  createdAt: string;
  id: number;
  imageUrl: null | string;
  initialOdometer: null | number;
  licensePlate: string;
  make: string;
  model: string;
  ownerContact: string;
  ownerName: string;
  serviceHistory?: ServiceRecordApiResponse[];
  ServiceRecord?: ServiceRecordApiResponse[]; // Alternative naming from backend
  updatedAt: string;
  vin: string;
  year: number;
}

// =============================================================================
// GIFT TRACKING API CONTRACTS
// =============================================================================

/**
 * Gift data as received from the API
 */
export interface GiftApiResponse {
  id: string;
  itemDescription: string;
  recipientId: string;
  dateSent: string;
  senderName: string;
  occasion?: string | null;
  notes?: string | null;
  createdAt: string;
  updatedAt: string;
  recipient?: RecipientApiResponse;
}

/**
 * Request payload for creating a new gift
 */
export interface CreateGiftRequest {
  itemDescription: string;
  recipientId: string;
  dateSent: string;
  senderName: string;
  occasion?: string | null;
  notes?: string | null;
}

/**
 * Request payload for updating an existing gift
 */
export interface UpdateGiftRequest {
  itemDescription?: string;
  recipientId?: string;
  dateSent?: string;
  senderName?: string;
  occasion?: string | null;
  notes?: string | null;
}

/**
 * Recipient data as received from the API
 */
export interface RecipientApiResponse {
  id: string;
  name: string;
  role?: string | null;
  worksite?: string | null;
  email?: string | null;
  phone?: string | null;
  address?: string | null;
  notes?: string | null;
  createdAt: string;
  updatedAt: string;
  gifts?: GiftApiResponse[];
}

/**
 * Request payload for creating a new recipient
 */
export interface CreateRecipientRequest {
  name: string;
  role?: string | null;
  worksite?: string | null;
  email?: string | null;
  phone?: string | null;
  address?: string | null;
  notes?: string | null;
}

/**
 * Request payload for updating an existing recipient
 */
export interface UpdateRecipientRequest {
  name?: string;
  role?: string | null;
  worksite?: string | null;
  email?: string | null;
  phone?: string | null;
  address?: string | null;
  notes?: string | null;
}

/**
 * Gift statistics response from API
 */
export interface GiftStatisticsApiResponse {
  totalGifts: number;
  giftsThisMonth: number;
  giftsThisYear: number;
  popularOccasions: Array<{
    occasion: string;
    count: number;
  }>;
  recentActivity: Array<{
    date: string;
    count: number;
  }>;
  topRecipients: Array<{
    recipientId: string;
    recipientName: string;
    giftCount: number;
  }>;
}

/**
 * Recipient statistics response from API
 */
export interface RecipientStatisticsApiResponse {
  totalRecipients: number;
  recipientsThisMonth: number;
  recipientsThisYear: number;
  mostGifted: Array<{
    recipientId: string;
    recipientName: string;
    giftCount: number;
  }>;
}
