(()=>{var e={};e.id=8004,e.ids=[8004],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5320:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\tasks\\\\add\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\add\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19794:(e,r,t)=>{Promise.resolve().then(t.bind(t,56687))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40185:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>l,pages:()=>p,routeModule:()=>u,tree:()=>c});var s=t(65239),i=t(48088),a=t(88170),o=t.n(a),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let c={children:["",{children:["[locale]",{children:["tasks",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5320)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\add\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\tasks\\add\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/tasks/add/page",pathname:"/[locale]/tasks/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56687:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(60687);let i=(0,t(82614).A)("ListPlus",[["path",{d:"M11 12H3",key:"51ecnj"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M16 18H3",key:"12xzn7"}],["path",{d:"M18 9v6",key:"1twb98"}],["path",{d:"M21 12h-6",key:"bt1uis"}]]);var a=t(16189),o=t(78207),n=t(48041),d=t(3940),c=t(73227);function p(){let e=(0,a.useRouter)(),{showEntityCreated:r,showEntityCreationError:t}=(0,d.O_)("task"),{error:p,isPending:l,mutateAsync:u}=(0,c.ZY)(),m=async s=>{try{let t={dateTime:s.dateTime,deadline:s.deadline||void 0,description:s.description,driverEmployeeId:s.driverEmployeeId||void 0,estimatedDuration:s.estimatedDuration,location:s.location,notes:s.notes||void 0,priority:s.priority,requiredSkills:s.requiredSkills,staffEmployeeId:s.staffEmployeeId,status:s.status.replace(" ","_"),subtasks:s.subtasks,vehicleId:s.vehicleId||void 0};await u(t);let i={title:s.description.slice(0,30)+(s.description.length>30?"...":""),name:s.description.slice(0,30)+(s.description.length>30?"...":"")};r(i),e.push("/tasks")}catch(e){console.error("Error adding task:",e),t(e.message||p?.message||"Failed to add task. Please try again.")}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(n.z,{description:"Enter the details for the new task or job.",icon:i,title:"Add New Task"}),p&&(0,s.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error: ",p.message]}),(0,s.jsx)(o.A,{isEditing:!1,onSubmit:m,isLoading:l})]})}},56746:(e,r,t)=>{Promise.resolve().then(t.bind(t,5320))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,3871,7048,8390,2670,9275,6013,8739,3302,2936,9599,2153],()=>t(40185));module.exports=s})();