"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6762],{6560:(e,r,s)=>{s.d(r,{r:()=>d});var a=s(95155),t=s(50172),l=s(12115),i=s(30285),n=s(54036);let d=l.forwardRef((e,r)=>{let{actionType:s="primary",asChild:l=!1,children:d,className:c,disabled:m,icon:o,isLoading:x=!1,loadingText:u,...f}=e,{className:h,variant:p}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[s];return(0,a.jsx)(i.$,{asChild:l,className:(0,n.cn)(h,c),disabled:x||m,ref:r,variant:p,...f,children:x?(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,a.jsx)(t.A,{className:"mr-2 size-4 animate-spin"}),u||d]}):(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",o&&(0,a.jsx)("span",{className:"mr-2",children:o}),d]})})});d.displayName="ActionButton"},19018:(e,r,s)=>{s.d(r,{Breadcrumb:()=>d,BreadcrumbItem:()=>m,BreadcrumbLink:()=>o,BreadcrumbList:()=>c,BreadcrumbPage:()=>x,BreadcrumbSeparator:()=>u});var a=s(95155),t=s(99708),l=s(73158),i=(s(3561),s(12115)),n=s(54036);let d=i.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("nav",{"aria-label":"breadcrumb",className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",s),ref:r,...t})});d.displayName="Breadcrumb";let c=i.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("ol",{className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",s),ref:r,...t})});c.displayName="BreadcrumbList";let m=i.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("li",{className:(0,n.cn)("inline-flex items-center gap-1.5",s),ref:r,...t})});m.displayName="BreadcrumbItem";let o=i.forwardRef((e,r)=>{let{asChild:s,className:l,...i}=e,d=s?t.DX:"a";return(0,a.jsx)(d,{className:(0,n.cn)("transition-colors hover:text-foreground",l),ref:r,...i})});o.displayName="BreadcrumbLink";let x=i.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,n.cn)("font-normal text-foreground",s),ref:r,role:"link",...t})});x.displayName="BreadcrumbPage";let u=e=>{let{children:r,className:s,...t}=e;return(0,a.jsx)("span",{"aria-hidden":"true",className:(0,n.cn)("[&>svg]:size-3.5",s),role:"presentation",...t,children:null!=r?r:(0,a.jsx)(l.A,{className:"size-4"})})};u.displayName="BreadcrumbSeparator"},22346:(e,r,s)=>{s.d(r,{w:()=>n});var a=s(95155),t=s(87489),l=s(12115),i=s(54036);let n=l.forwardRef((e,r)=>{let{className:s,decorative:l=!0,orientation:n="horizontal",...d}=e;return(0,a.jsx)(t.b,{className:(0,i.cn)("shrink-0 bg-border","horizontal"===n?"h-[1px] w-full":"h-full w-[1px]",s),decorative:l,orientation:n,ref:r,...d})});n.displayName=t.b.displayName},26126:(e,r,s)=>{s.d(r,{E:()=>n});var a=s(95155),t=s(74466);s(12115);var l=s(54036);let i=(0,t.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function n(e){let{className:r,variant:s,...t}=e;return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:s}),r),...t})}},55365:(e,r,s)=>{s.d(r,{Fc:()=>d,TN:()=>m,XL:()=>c});var a=s(95155),t=s(74466),l=s(12115),i=s(54036);let n=(0,t.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),d=l.forwardRef((e,r)=>{let{className:s,variant:t,...l}=e;return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:t}),s),ref:r,role:"alert",...l})});d.displayName="Alert";let c=l.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("h5",{className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",s),ref:r,...t})});c.displayName="AlertTitle";let m=l.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,i.cn)("text-sm [&_p]:leading-relaxed",s),ref:r,...t})});m.displayName="AlertDescription"},68856:(e,r,s)=>{s.d(r,{E:()=>l});var a=s(95155),t=s(54036);function l(e){let{className:r,...s}=e;return(0,a.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-md bg-muted",r),...s})}},77023:(e,r,s)=>{s.d(r,{gO:()=>x,jt:()=>p,pp:()=>u});var a=s(95155),t=s(11133),l=s(50172);s(12115);var i=s(6560),n=s(55365),d=s(68856),c=s(54036);let m={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},o={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function x(e){let{children:r,className:s,data:t,emptyComponent:l,error:i,errorComponent:n,isLoading:d,loadingComponent:m,onRetry:o}=e;return d?m||(0,a.jsx)(h,{...s&&{className:s},text:"Loading..."}):i?n||(0,a.jsx)(f,{...s&&{className:s},message:i,...o&&{onRetry:o}}):!t||Array.isArray(t)&&0===t.length?l||(0,a.jsx)("div",{className:(0,c.cn)("text-center py-8",s),children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,a.jsx)("div",{className:s,children:r(t)})}function u(e){let{className:r,description:s,icon:t,primaryAction:l,secondaryAction:n,title:d}=e;return(0,a.jsxs)("div",{className:(0,c.cn)("space-y-6 text-center py-12",r),children:[t&&(0,a.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,a.jsx)(t,{className:"h-10 w-10 text-muted-foreground"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:d}),s&&(0,a.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:s})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[l&&(0,a.jsx)(i.r,{actionType:"primary",asChild:!!l.href,icon:l.icon,onClick:l.onClick,children:l.href?(0,a.jsx)("a",{href:l.href,children:l.label}):l.label}),n&&(0,a.jsx)(i.r,{actionType:"tertiary",asChild:!!n.href,icon:n.icon,onClick:n.onClick,children:n.href?(0,a.jsx)("a",{href:n.href,children:n.label}):n.label})]})]})}function f(e){let{className:r,message:s,onRetry:d}=e;return(0,a.jsxs)(n.Fc,{className:(0,c.cn)("my-4",r),variant:"destructive",children:[(0,a.jsx)(t.A,{className:"size-4"}),(0,a.jsx)(n.XL,{children:"Error"}),(0,a.jsx)(n.TN,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:s}),d&&(0,a.jsx)(i.r,{actionType:"tertiary",icon:(0,a.jsx)(l.A,{className:"size-4"}),onClick:d,size:"sm",children:"Try Again"})]})})]})}function h(e){let{className:r,fullPage:s=!1,size:t="md",text:i}=e;return(0,a.jsx)("div",{className:(0,c.cn)("flex items-center justify-center",s&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",r),children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(l.A,{className:(0,c.cn)("animate-spin text-primary",m[t])}),i&&(0,a.jsx)("span",{className:(0,c.cn)("mt-2 text-muted-foreground",o[t]),children:i})]})})}function p(e){let{className:r,count:s=1,testId:t="loading-skeleton",variant:l="default"}=e;return"card"===l?(0,a.jsx)("div",{className:(0,c.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",r),"data-testid":t,children:Array(s).fill(0).map((e,r)=>(0,a.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,a.jsx)(d.E,{className:"aspect-[16/10] w-full"}),(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)(d.E,{className:"mb-1 h-7 w-3/4"}),(0,a.jsx)(d.E,{className:"mb-3 h-4 w-1/2"}),(0,a.jsx)(d.E,{className:"my-3 h-px w-full"}),(0,a.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.E,{className:"mr-2.5 size-5 rounded-full"}),(0,a.jsx)(d.E,{className:"h-5 w-2/3"})]},r))})]})]},r))}):"table"===l?(0,a.jsxs)("div",{className:(0,c.cn)("space-y-3",r),"data-testid":t,children:[(0,a.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,r)=>(0,a.jsx)(d.E,{className:"h-8 flex-1"},r))}),Array(s).fill(0).map((e,r)=>(0,a.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,r)=>(0,a.jsx)(d.E,{className:"h-6 flex-1"},r))},r))]}):"list"===l?(0,a.jsx)("div",{className:(0,c.cn)("space-y-3",r),"data-testid":t,children:Array(s).fill(0).map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(d.E,{className:"size-12 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)(d.E,{className:"h-4 w-1/3"}),(0,a.jsx)(d.E,{className:"h-4 w-full"})]})]},r))}):"stats"===l?(0,a.jsx)("div",{className:(0,c.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",r),"data-testid":t,children:Array(s).fill(0).map((e,r)=>(0,a.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(d.E,{className:"h-5 w-1/3"}),(0,a.jsx)(d.E,{className:"size-5 rounded-full"})]}),(0,a.jsx)(d.E,{className:"mt-3 h-8 w-1/2"}),(0,a.jsx)(d.E,{className:"mt-2 h-4 w-2/3"})]},r))}):(0,a.jsx)("div",{className:(0,c.cn)("space-y-2",r),"data-testid":t,children:Array(s).fill(0).map((e,r)=>(0,a.jsx)(d.E,{className:"h-5 w-full"},r))})}},89440:(e,r,s)=>{s.d(r,{AppBreadcrumb:()=>m});var a=s(95155),t=s(6874),l=s.n(t),i=s(35695),n=s(12115),d=s(19018),c=s(54036);function m(e){let{className:r,homeHref:s="/",homeLabel:t="Dashboard",showContainer:m=!0}=e,o=(0,i.usePathname)(),x=o?o.split("/").filter(Boolean):[],u=e=>{if(/^\d+$/.test(e))return"ID: ".concat(e);if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let r={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return r[e]?r[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},f=x.map((e,r)=>{let s="/"+x.slice(0,r+1).join("/"),t=r===x.length-1,i=u(e);return(0,a.jsxs)(n.Fragment,{children:[(0,a.jsx)(d.BreadcrumbItem,{children:t?(0,a.jsx)(d.BreadcrumbPage,{className:"font-medium text-foreground",children:i}):(0,a.jsx)(d.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(l(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:i})})}),!t&&(0,a.jsx)(d.BreadcrumbSeparator,{})]},s)}),h=(0,a.jsx)(d.Breadcrumb,{className:(0,c.cn)("text-sm",r),children:(0,a.jsxs)(d.BreadcrumbList,{className:"flex-wrap",children:[(0,a.jsx)(d.BreadcrumbItem,{children:(0,a.jsx)(d.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(l(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:t})})}),x.length>0&&(0,a.jsx)(d.BreadcrumbSeparator,{}),f]})});return m?(0,a.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,a.jsx)("div",{className:"flex items-center",children:h})}):h}},95647:(e,r,s)=>{s.d(r,{z:()=>t});var a=s(95155);function t(e){let{children:r,description:s,icon:t,title:l}=e;return(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[t&&(0,a.jsx)(t,{className:"size-8 text-primary"}),(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:l})]}),s&&(0,a.jsx)("p",{className:"mt-1 text-muted-foreground",children:s})]}),r&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:r})]})}s(12115)}}]);