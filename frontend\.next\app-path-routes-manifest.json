{"/api/csp-report/route": "/api/csp-report", "/favicon.ico/route": "/favicon.ico", "/_not-found/page": "/_not-found", "/page": "/", "/[locale]/add-vehicle/page": "/[locale]/add-vehicle", "/[locale]/delegations/[id]/page": "/[locale]/delegations/[id]", "/[locale]/delegations/add/page": "/[locale]/delegations/add", "/[locale]/delegations/page": "/[locale]/delegations", "/[locale]/auth-test/page": "/[locale]/auth-test", "/[locale]/employees/add/page": "/[locale]/employees/add", "/[locale]/delegations/[id]/edit/page": "/[locale]/delegations/[id]/edit", "/[locale]/employees/[id]/page": "/[locale]/employees/[id]", "/[locale]/font-size-demo/page": "/[locale]/font-size-demo", "/[locale]/employees/new/page": "/[locale]/employees/new", "/[locale]/gifts/[id]/edit/page": "/[locale]/gifts/[id]/edit", "/[locale]/employees/page": "/[locale]/employees", "/[locale]/employees/[id]/edit/page": "/[locale]/employees/[id]/edit", "/[locale]/page": "/[locale]", "/[locale]/gifts/[id]/page": "/[locale]/gifts/[id]", "/[locale]/gifts/page": "/[locale]/gifts", "/[locale]/login/page": "/[locale]/login", "/[locale]/gifts/add/page": "/[locale]/gifts/add", "/[locale]/profile/page": "/[locale]/profile", "/[locale]/recipients/[id]/page": "/[locale]/recipients/[id]", "/[locale]/recipients/[id]/edit/page": "/[locale]/recipients/[id]/edit", "/[locale]/recipients/page": "/[locale]/recipients", "/[locale]/service-history/page": "/[locale]/service-history", "/[locale]/recipients/add/page": "/[locale]/recipients/add", "/[locale]/settings/page": "/[locale]/settings", "/[locale]/service-records/[id]/edit/page": "/[locale]/service-records/[id]/edit", "/[locale]/tasks/page": "/[locale]/tasks", "/[locale]/tasks/[id]/edit/page": "/[locale]/tasks/[id]/edit", "/[locale]/service-records/[id]/page": "/[locale]/service-records/[id]", "/[locale]/vehicles/[id]/page": "/[locale]/vehicles/[id]", "/[locale]/tasks/add/page": "/[locale]/tasks/add", "/[locale]/vehicles/new/page": "/[locale]/vehicles/new", "/[locale]/vehicles/edit/[id]/page": "/[locale]/vehicles/edit/[id]", "/[locale]/zustand-test/page": "/[locale]/zustand-test", "/[locale]/tasks/[id]/page": "/[locale]/tasks/[id]", "/[locale]/vehicles/page": "/[locale]/vehicles", "/[locale]/delegations/[id]/report/page": "/[locale]/delegations/[id]/report", "/[locale]/delegations/report/list/page": "/[locale]/delegations/report/list", "/[locale]/vehicles/[id]/report/service-history/page": "/[locale]/vehicles/[id]/report/service-history", "/[locale]/tasks/report/page": "/[locale]/tasks/report", "/[locale]/vehicles/[id]/report/page": "/[locale]/vehicles/[id]/report", "/[locale]/admin/page": "/[locale]/admin", "/[locale]/reliability/page": "/[locale]/reliability", "/[locale]/reports/analytics/page": "/[locale]/reports/analytics", "/[locale]/reports/data/page": "/[locale]/reports/data", "/[locale]/reports/page": "/[locale]/reports"}