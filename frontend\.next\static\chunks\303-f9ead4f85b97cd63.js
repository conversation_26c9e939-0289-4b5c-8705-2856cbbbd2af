"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[303],{36973:(e,t,i)=>{i.d(t,{cl:()=>w,ac:()=>C,aV:()=>W,Oo:()=>D,df:()=>T,e_:()=>I,Hg:()=>R,oL:()=>b});var n=i(55411),r=i(976),s=i(12430),c=i(21876);let o={fromApi:e=>({id:e.id,itemDescription:e.itemDescription,recipientId:e.recipientId,dateSent:e.dateSent,senderName:e.senderName,occasion:e.occasion||null,notes:e.notes||null,createdAt:e.createdAt,updatedAt:e.updatedAt,...e.recipient&&{recipient:{id:e.recipient.id,name:e.recipient.name,email:e.recipient.email||null,phone:e.recipient.phone||null,address:e.recipient.address||null,notes:e.recipient.notes||null,createdAt:e.recipient.createdAt,updatedAt:e.recipient.updatedAt}}}),toCreateRequest(e){var t,i;return{itemDescription:e.itemDescription,recipientId:e.recipientId,dateSent:(0,c.B7)(e.dateSent),senderName:e.senderName,occasion:null!=(t=e.occasion)?t:null,notes:null!=(i=e.notes)?i:null}},toUpdateRequest(e){let t={};return void 0!==e.itemDescription&&(t.itemDescription=e.itemDescription),void 0!==e.recipientId&&(t.recipientId=e.recipientId),void 0!==e.dateSent&&(t.dateSent=(0,c.B7)(e.dateSent)),void 0!==e.senderName&&(t.senderName=e.senderName),void 0!==e.occasion&&(t.occasion=e.occasion),void 0!==e.notes&&(t.notes=e.notes),t},fromApiArray(e){return e.map(this.fromApi)}};var a=i(25982);let l={fromApi:e=>o.fromApi(e),toApi:e=>e};class h extends a.v{async getByDateRange(e,t){return(await this.getAll({endDate:t,startDate:e})).data}async getByOccasion(e){return(await this.getAll({occasion:e})).data}async getByRecipient(e){return(await this.getAll({recipientId:e})).data}async getBySender(e){return(await this.getAll({senderName:e})).data}async getRecent(){let e=new Date;return e.setDate(e.getDate()-30),this.getByDateRange(e.toISOString(),new Date().toISOString())}async getStatistics(){return this.executeWithInfrastructure("getStatistics",async()=>{let e=await this.apiClient.get("".concat(this.endpoint,"/statistics"));return{giftsThisMonth:e.giftsThisMonth,giftsThisYear:e.giftsThisYear,popularOccasions:e.popularOccasions,recentActivity:e.recentActivity,topRecipients:e.topRecipients,totalGifts:e.totalGifts}})}async searchByDescription(e){return(await this.getAll({search:e})).data}constructor(e,t){super(e,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/gifts",this.transformer=l}}let u={fromApi:e=>({id:e.id,name:e.name,role:e.role||null,worksite:e.worksite||null,email:e.email||null,phone:e.phone||null,address:e.address||null,notes:e.notes||null,createdAt:e.createdAt,updatedAt:e.updatedAt,...e.gifts&&{gifts:e.gifts.map(e=>({id:e.id,itemDescription:e.itemDescription,recipientId:e.recipientId,dateSent:e.dateSent,senderName:e.senderName,occasion:e.occasion||null,notes:e.notes||null,createdAt:e.createdAt,updatedAt:e.updatedAt}))}}),toCreateRequest(e){var t,i,n,r,s,c;return{name:e.name,role:null!=(t=e.role)?t:null,worksite:null!=(i=e.worksite)?i:null,email:null!=(n=e.email)?n:null,phone:null!=(r=e.phone)?r:null,address:null!=(s=e.address)?s:null,notes:null!=(c=e.notes)?c:null}},toUpdateRequest(e){let t={};return void 0!==e.name&&(t.name=e.name),void 0!==e.role&&(t.role=e.role),void 0!==e.worksite&&(t.worksite=e.worksite),void 0!==e.email&&(t.email=e.email),void 0!==e.phone&&(t.phone=e.phone),void 0!==e.address&&(t.address=e.address),void 0!==e.notes&&(t.notes=e.notes),t},fromApiArray(e){return e.map(this.fromApi)}},d={fromApi:e=>u.fromApi(e),toApi:e=>e};class p extends a.v{async searchByName(e){return(await this.getAll({search:e})).data}async getWithGiftCounts(){return this.executeWithInfrastructure("getWithGiftCounts",async()=>{let e=await this.apiClient.get("".concat(this.endpoint,"/with-gift-counts"));return u.fromApiArray(e)})}async getStatistics(){return this.executeWithInfrastructure("getStatistics",async()=>{let e=await this.apiClient.get("".concat(this.endpoint,"/statistics"));return{totalRecipients:e.totalRecipients,recipientsThisMonth:e.recipientsThisMonth,recipientsThisYear:e.recipientsThisYear,mostGifted:e.mostGifted}})}async getByEmailDomain(e){return(await this.getAll({emailDomain:e})).data}async getWithGifts(){return(await this.getAll({hasGifts:!0})).data}async getWithoutGifts(){return(await this.getAll({hasGifts:!1})).data}async getForAutocomplete(e){return this.executeWithInfrastructure("autocomplete:".concat(e),async()=>{let t=await this.apiClient.get("".concat(this.endpoint,"/search?q=").concat(encodeURIComponent(e),"&limit=10"));return u.fromApiArray(t)})}constructor(e,t){super(e,{cacheDuration:6e5,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/recipients",this.transformer=d}}let g={fromApi:e=>e,toApi:e=>e};class m extends a.v{async acknowledgeAlert(e,t,i){return this.executeWithInfrastructure(null,async()=>{let n=await this.apiClient.post("/alerts/".concat(e,"/acknowledge"),{acknowledgedBy:i,note:t});return this.cache.invalidatePattern(RegExp("^alerts:")),n})}async getActiveAlerts(){return this.executeWithInfrastructure("alerts:active",async()=>{try{let e=await this.apiClient.get("/alerts");return(null==e?void 0:e.alerts)||[]}catch(e){return console.error("Failed to get active alerts:",e),[]}})}async getAlertHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return this.executeWithInfrastructure("alerts:history:".concat(e,":").concat(t),async()=>{let i=new URLSearchParams({limit:t.toString(),page:e.toString()});return await this.apiClient.get("/alerts/history?".concat(i.toString()))})}async getAlertStatistics(){return this.executeWithInfrastructure("alerts:statistics",async()=>{try{return await this.apiClient.get("/alerts/statistics")}catch(e){return console.error("Failed to get alert statistics:",e),{acknowledged:0,active:0,averageResolutionTime:0,bySeverity:{critical:0,high:0,low:0,medium:0},recentTrends:{last7Days:0,last24Hours:0,last30Days:0},resolved:0,total:0}}})}async getCircuitBreakerHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"24h",t=arguments.length>1?arguments[1]:void 0;return this.executeWithInfrastructure("circuit-breakers:history:".concat(e,":").concat(t||"all"),async()=>{let i=new URLSearchParams({timeframe:e});return t&&i.append("breakerName",t),await this.apiClient.get("/monitoring/circuit-breakers/history?".concat(i.toString()))})}async getCircuitBreakerStatus(){return this.executeWithInfrastructure("monitoring:circuit-breakers",async()=>{try{let e=await this.apiClient.get("/circuit-breakers"),t=(null==e?void 0:e.circuitBreakers)||[];return{circuitBreakers:t||[],summary:{closed:(null==t?void 0:t.filter(e=>"CLOSED"===e.state).length)||0,halfOpen:(null==t?void 0:t.filter(e=>"HALF_OPEN"===e.state).length)||0,open:(null==t?void 0:t.filter(e=>"OPEN"===e.state).length)||0,total:(null==t?void 0:t.length)||0}}}catch(e){return console.error("Failed to get circuit breaker status:",e),{circuitBreakers:[],summary:{closed:0,halfOpen:0,open:0,total:0}}}})}async getCriticalAlertCount(){try{return(await this.getAlertStatistics()).bySeverity.critical}catch(e){return 0}}async getDeduplicationMetrics(){return this.executeWithInfrastructure("monitoring:deduplication",async()=>await this.apiClient.get("/monitoring/deduplication"))}async getDependencyHealth(){return this.executeWithInfrastructure("health:dependencies",async()=>await this.apiClient.get("/health/dependencies"))}async getDetailedHealth(){return this.executeWithInfrastructure("health:detailed",async()=>await this.apiClient.get("/health/detailed"))}async getHealthTrends(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"24h";return this.executeWithInfrastructure("health:trends:".concat(e),async()=>await this.apiClient.get("/health/trends?timeframe=".concat(e)))}async getHttpRequestMetrics(){return this.executeWithInfrastructure("http:metrics",async()=>await this.apiClient.get("/http-request-metrics"))}async getMetrics(){return this.executeWithInfrastructure("metrics:system",async()=>await this.apiClient.get("/metrics",{headers:{Accept:"application/json"}}))}async getReliabilityDashboardData(){let[e,t,i,n,r,s]=await Promise.all([this.getSystemHealth(),this.getDetailedHealth(),this.getCircuitBreakerStatus(),this.getMetrics(),this.getActiveAlerts(),this.getAlertStatistics()]);return{activeAlerts:r,alertStatistics:s,circuitBreakers:i,detailedHealth:t,metrics:n,systemHealth:e}}async getSystemHealth(){return this.executeWithInfrastructure("health:system",async()=>await this.apiClient.get("/health"))}async isSystemHealthy(){try{let e=await this.getSystemHealth();return"healthy"===e.status}catch(e){return!1}}async resolveAlert(e,t,i){return this.executeWithInfrastructure(null,async()=>{let n=await this.apiClient.post("/alerts/".concat(e,"/resolve"),{reason:t,resolvedBy:i});return this.cache.invalidatePattern(RegExp("^alerts:")),n})}async testAlerts(){return this.executeWithInfrastructure(null,async()=>{var e;let t=await this.apiClient.post("/alerts/test");return{message:(null==t?void 0:t.message)||"Test alert triggered",success:(null==t?void 0:t.status)==="success",testAlertId:null==t||null==(e=t.data)?void 0:e.id}})}constructor(e,t){super(e,{cacheDuration:6e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/reliability",this.transformer=g}}var f=i(90137),y=i(97966),v=i(38549),S=i(72248);function k(){let e=(0,S.Sk)();if(!e)return null;try{return e()}catch(e){return console.error("❌ Factory: Error getting auth token from secure provider:",e),null}}class A{getApiClient(){return this.apiClient}getDelegationService(){return this.delegationService||(this.delegationService=new r.y(this.apiClient)),this.delegationService}getEmployeeService(){return this.employeeService||(this.employeeService=new s.Q(this.apiClient)),this.employeeService}getReliabilityService(){return this.reliabilityService||(this.reliabilityService=new m(this.apiClient)),this.reliabilityService}getTaskService(){return this.taskService||(this.taskService=new f.D(this.apiClient)),this.taskService}getVehicleService(){return this.vehicleService||(this.vehicleService=new y.C(this.apiClient)),this.vehicleService}getGiftService(){return this.giftService||(this.giftService=new h(this.apiClient)),this.giftService}getRecipientService(){return this.recipientService||(this.recipientService=new p(this.apiClient)),this.recipientService}constructor(e){this.apiClient=new n.O({...e,getAuthToken:k})}}let w=new A({baseURL:(0,v.Qq)().apiBaseUrl,headers:{"Content-Type":"application/json"},retryAttempts:3,timeout:1e4}),b=w.getVehicleService(),C=w.getDelegationService(),R=w.getTaskService(),W=w.getEmployeeService(),I=w.getReliabilityService(),D=w.getGiftService(),T=w.getRecipientService()},55012:(e,t,i)=>{i.d(t,{gv:()=>h,j9:()=>l});var n=i(14298),r=i(38549),s=i(14163),c=i(28113),o=i(87358);class a{static getInstance(e){var t;return null!=a.instance||(a.instance=new a(e)),a.instance}async connect(){var e;if(null==(e=this.socket)?void 0:e.connected)return void console.debug("WebSocket already connected");this.setConnectionState("connecting");try{let{data:{session:e},error:t}=await s.N.auth.getSession();t&&console.warn("Failed to get session for WebSocket connection:",t);let i={forceNew:!0,timeout:this.config.timeout,transports:["websocket","polling"],withCredentials:!0};if(null==e?void 0:e.access_token){i.auth={token:e.access_token},console.debug("\uD83D\uDD10 WebSocket connecting with authentication token");let t=e.expires_at?1e3*e.expires_at:0,n=Date.now();t-n<=6e4&&console.warn("⚠️ WebSocket token expires soon, may need refresh")}else console.warn("⚠️ WebSocket connecting without authentication token - connection may fail");this.socket=(0,n.io)(this.config.url,i),this.setupEventHandlers()}catch(e){console.error("Failed to connect WebSocket:",e),this.setConnectionState("error"),this.scheduleReconnect()}}destroy(){this.disconnect(),this.subscriptions.clear(),this.stateListeners.clear(),a.instance=null}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionState("disconnected"),this.reconnectAttempts=0}emit(e,t,i){var n;if(!(null==(n=this.socket)?void 0:n.connected))return void console.warn("Cannot emit ".concat(e,":").concat(t," - WebSocket not connected"));this.socket.emit(t,i)}getConnectionState(){return this.connectionState}isConnected(){var e;return"connected"===this.connectionState&&(null==(e=this.socket)?void 0:e.connected)===!0}joinRoom(e){var t;if(!(null==(t=this.socket)?void 0:t.connected))return void console.warn("Cannot join room ".concat(e," - WebSocket not connected"));this.socket.emit("join-room",e)}leaveRoom(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.socket.emit("leave-room",e)}onStateChange(e){return this.stateListeners.add(e),()=>{this.stateListeners.delete(e)}}subscribe(e,t,i){var n;let r="".concat(e,":").concat(t);return this.subscriptions.has(r)||this.subscriptions.set(r,new Set),this.subscriptions.get(r).add(i),(null==(n=this.socket)?void 0:n.connected)&&t&&this.socket.on(t,i),()=>{let e=this.subscriptions.get(r);e&&(e.delete(i),0===e.size&&this.subscriptions.delete(r)),this.socket&&t&&this.socket.off(t,i)}}handleAuthenticationError(){let e=(0,c.Q)();console.log("\uD83D\uDD10 Handling WebSocket authentication error..."),this.socket&&(this.socket.disconnect(),this.socket=null),e.refreshNow().then(e=>{e?console.log("\uD83D\uDD04 Token refresh successful, retrying WebSocket connection"):(console.error("\uD83D\uDD04 Token refresh failed, scheduling normal reconnect"),this.scheduleReconnect())}).catch(e=>{console.error("\uD83D\uDD04 Token refresh error:",e),this.scheduleReconnect()})}resubscribeToEvents(){if(this.socket)for(let[e,t]of this.subscriptions){let[,i]=e.split(":");for(let e of t)i&&this.socket.on(i,e)}}scheduleReconnect(){if(this.reconnectAttempts>=this.config.reconnectAttempts){console.error("Max reconnection attempts reached"),this.setConnectionState("error");return}this.setConnectionState("reconnecting"),this.reconnectAttempts++,setTimeout(()=>{console.info("Attempting to reconnect (".concat(this.reconnectAttempts,"/").concat(this.config.reconnectAttempts,")")),this.connect()},this.config.reconnectDelay*Math.pow(2,this.reconnectAttempts-1))}setConnectionState(e){if(this.connectionState!==e)for(let t of(this.connectionState=e,this.stateListeners))t(e)}setupEventHandlers(){this.socket&&(this.socket.on("connect",()=>{console.info("WebSocket connected"),this.setConnectionState("connected"),this.reconnectAttempts=0,this.resubscribeToEvents()}),this.socket.on("disconnect",e=>{console.warn("WebSocket disconnected:",e),this.setConnectionState("disconnected"),"io server disconnect"!==e&&this.scheduleReconnect()}),this.socket.on("connect_error",e=>{var t,i,n,r;console.error("WebSocket connection error:",e),this.setConnectionState("error"),(null==(t=e.message)?void 0:t.includes("Authentication"))||(null==(i=e.message)?void 0:i.includes("token"))||(null==(n=e.message)?void 0:n.includes("No token provided"))||(null==(r=e.message)?void 0:r.includes("Unauthorized"))?(console.warn("\uD83D\uDD10 Authentication error detected, attempting token refresh"),this.handleAuthenticationError()):this.scheduleReconnect()}),this.socket.on("auth_error",e=>{console.error("\uD83D\uDD10 Server authentication error:",e),this.handleAuthenticationError()}),this.socket.on("token_refresh_required",()=>{console.warn("\uD83D\uDD04 Server requested token refresh"),this.handleAuthenticationError()}))}setupTokenRefreshHandling(){(0,c.Q)().subscribe((e,t)=>{switch(e){case"critical_refresh_failed":console.error("\uD83D\uDD04 Critical token refresh failure, disconnecting WebSocket"),this.disconnect(),this.setConnectionState("error");break;case"refresh_failed":console.error("\uD83D\uDD04 Token refresh failed, WebSocket may lose connection");break;case"refresh_success":console.log("\uD83D\uDD04 Token refreshed, reconnecting WebSocket with new token"),this.socket&&(this.socket.disconnect(),this.socket=null),setTimeout(()=>this.connect(),500)}})}constructor(e={}){var t,i,n,s,c,a;this.connectionState="disconnected",this.reconnectAttempts=0,this.socket=null,this.stateListeners=new Set,this.subscriptions=new Map,this.config={autoConnect:null==(t=e.autoConnect)||t,reconnectAttempts:null!=(i=e.reconnectAttempts)?i:5,reconnectDelay:null!=(n=e.reconnectDelay)?n:1e3,timeout:null!=(s=e.timeout)?s:1e4,url:null!=(a=null!=(c=e.url)?c:o.env.NEXT_PUBLIC_WEBSOCKET_URL)?a:(0,r.Qq)().wsUrl.replace("ws://","http://").replace("wss://","https://")},this.config.autoConnect&&this.connect(),this.setupTokenRefreshHandling()}}a.instance=null;let l=e=>a.getInstance(e),h=()=>{let e=l();return{connectionState:e.getConnectionState(),isConnected:e.isConnected()}}},90111:(e,t,i)=>{i.d(t,{GK:()=>l,ol:()=>h});var n=i(26715),r=i(28755),s=i(12115),c=i(55012),o=i(40283);let a={crud:"entity-updates",notifications:"notifications-monitoring",reliability:"reliability-monitoring",system:"system-monitoring"};function l(e,t,i,n){var r;let{isInitialized:s,loading:c}=(0,o.useAuthContext)();return u(e,t,{channel:"crud",events:["".concat(i,":created"),"".concat(i,":updated"),"".concat(i,":deleted"),"refresh:".concat(i)],fallbackInterval:3e4},{enabled:s&&!c&&(null==(r=null==n?void 0:n.enabled)||r),...n})}function h(e,t,i,n){let r=(0,c.j9)();return(0,s.useEffect)(()=>{r.isConnected()&&(console.debug("[ReliabilityQuery] Joining reliability-monitoring room for ".concat(i)),r.joinRoom("reliability-monitoring"));let e=r.onStateChange(e=>{"connected"===e&&(console.debug("[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ".concat(i)),r.joinRoom("reliability-monitoring"))});return()=>{e(),r.isConnected()&&r.leaveRoom("reliability-monitoring")}},[r,i]),u(e,t,{channel:"reliability",events:["".concat(i,"-update"),"".concat(i,"-created"),"".concat(i,"-resolved")],fallbackInterval:{alerts:3e4,"circuit-breakers":6e4,health:45e3,metrics:6e4}[i]},n)}function u(e,t,i,o){let{channel:l,enableFallback:h=!0,enableWebSocket:u=!0,events:d,fallbackInterval:p=3e4}=i,[g,m]=(0,s.useState)(!1),f=(0,c.j9)();(0,s.useEffect)(()=>{let e=()=>{m(f.isConnected())};return e(),f.onStateChange(e)},[f]);let y=h&&(!u||!g),v={gcTime:6e5,queryFn:t,queryKey:e,refetchInterval:!!y&&p,refetchOnReconnect:!0,refetchOnWindowFocus:y,staleTime:3e4*!g,...o},S=(0,n.jE)(),k=(0,r.I)(v);return(0,s.useEffect)(()=>{if(!u||!g)return;let e=a[l];if(!e)return void console.warn("[SmartQuery] No room mapping found for channel: ".concat(l));try{f.joinRoom(e),console.log("[SmartQuery] Joined room: ".concat(e," for channel: ").concat(l))}catch(t){console.error("[SmartQuery] Failed to join room ".concat(e,":"),t)}return()=>{try{f.leaveRoom(e),console.log("[SmartQuery] Left room: ".concat(e," for channel: ").concat(l))}catch(t){console.error("[SmartQuery] Failed to leave room ".concat(e,":"),t)}}},[u,g,l,f]),(0,s.useEffect)(()=>{if(!u||!g||0===d.length)return;let t=[];for(let i of d){let n=f.subscribe(l,i,t=>{console.log("[SmartQuery] WebSocket event received: ".concat(l,":").concat(i),t),S.invalidateQueries({queryKey:e})});t.push(n)}return()=>{for(let e of t)e()}},[u,g,d,l,f,S,e]),{...k,isUsingFallback:y,isWebSocketConnected:g}}}}]);