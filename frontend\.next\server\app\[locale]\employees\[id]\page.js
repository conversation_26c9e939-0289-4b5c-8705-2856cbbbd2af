(()=>{var t={};t.id=5283,t.ids=[5283],t.modules={3173:(t,e,i)=>{Promise.resolve().then(i.bind(i,77985))},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:t=>{"use strict";t.exports=require("punycode")},16325:(t,e,i)=>{Promise.resolve().then(i.bind(i,25422))},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:t=>{"use strict";t.exports=require("os")},25422:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>Q});var n=i(60687),o=i(75699),s=i(58261),r=i(14975),a=i(55817),h=i(28399),l=i(82614);let c=(0,l.A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var u=i(25915),d=i(77368),p=i(35137),_=i(57207),m=i(27247),f=i(53678),g=i(92876),v=i(33886),y=i(17612),x=i(21724),w=i(24920),b=i(29333),P=i(15036),T=i(99196),k=i(30474),M=i(85814),z=i.n(M),C=i(16189),S=i(43210),E=i(97638),A=i(38765),j=i(27805);let Z=(0,l.A)("Navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);var N=i(26398);let O=(0,l.A)("Fuel",[["line",{x1:"3",x2:"15",y1:"22",y2:"22",key:"xegly4"}],["line",{x1:"4",x2:"14",y1:"9",y2:"9",key:"xcnuvu"}],["path",{d:"M14 22V4a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v18",key:"16j0yd"}],["path",{d:"M14 13h2a2 2 0 0 1 2 2v2a2 2 0 0 0 2 2a2 2 0 0 0 2-2V9.83a2 2 0 0 0-.59-1.42L18 5",key:"7cu91f"}]]);var B=i(96834),I=i(29523),D=i(44493),R=i(3389);let F=({employee:t,refreshInterval:e=3e4})=>{let{toast:i}=(0,R.dj)(),[o,s]=(0,S.useState)(null),[a,h]=(0,S.useState)(null),[l,c]=(0,S.useState)({lastUpdate:new Date,status:"active"}),[u,d]=(0,S.useState)(!1),[p,_]=(0,S.useState)(null),m=(0,S.useRef)(null),g=(0,S.useRef)(null),v=(0,S.useRef)(null);return(0,S.useEffect)(()=>{console.log("Vehicle assignment is now context-specific for employee:",t.id)},[t.id,t.currentLocation]),(0,S.useEffect)(()=>((async()=>{if(m.current)try{let e=E.map(m.current).setView([40.7128,-74.006],13);E.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:'&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',maxZoom:19}).addTo(e),g.current=e,v.current=E.marker([40.7128,-74.006]).addTo(e).bindPopup(`${t.name} - ${t.role}`)}catch(t){console.error("Failed to initialize map:",t),i({description:"Failed to load map component.",title:"Map Error",variant:"destructive"})}})(),()=>{g.current&&g.current.remove()}),[t.name,t.role,i]),(0,S.useEffect)(()=>{if(!u)return;let t=setInterval(()=>{let t={accuracy:Math.floor(10*Math.random())+5,heading:Math.floor(360*Math.random()),latitude:40.7128+(Math.random()-.5)*.01,longitude:-74.006+(Math.random()-.5)*.01,speed:Math.floor(60*Math.random())+20,timestamp:new Date};if(h(t),_(new Date),g.current&&v.current){let e=E.latLng(t.latitude,t.longitude);v.current.setLatLng(e),g.current.setView(e)}let e=["active","break","active","active"],i=e[Math.floor(Math.random()*e.length)];c(t=>({...t,currentTask:t.currentTask||"No current task",lastUpdate:new Date,status:i}))},e);return()=>clearInterval(t)},[u,e]),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)(D.Zp,{children:[(0,n.jsx)(D.aR,{children:(0,n.jsxs)(D.ZB,{className:"flex items-center justify-between",children:[(0,n.jsxs)("span",{className:"flex items-center gap-2",children:[(0,n.jsx)(j.A,{className:"size-5"}),"Driver Status"]}),(0,n.jsx)("div",{className:"flex gap-2",children:u?(0,n.jsx)(I.$,{onClick:()=>{d(!1),i({description:`Real-time tracking disabled for ${t.name}`,title:"Tracking Stopped",variant:"default"})},size:"sm",variant:"outline",children:"Stop Tracking"}):(0,n.jsxs)(I.$,{onClick:()=>{d(!0),i({description:`Real-time tracking enabled for ${t.name}`,title:"Tracking Started",variant:"default"})},size:"sm",children:[(0,n.jsx)(Z,{className:"mr-2 size-4"}),"Start Tracking"]})})]})}),(0,n.jsx)(D.Wu,{children:(0,n.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:`size-3 rounded-full ${(t=>{switch(t){case"active":return"bg-green-500";case"break":return"bg-yellow-500";case"emergency":return"bg-red-500";default:return"bg-gray-500"}})(l.status)}`}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(t=>{switch(t){case"active":return(0,n.jsx)(A.A,{className:"size-4"});case"break":return(0,n.jsx)(P.A,{className:"size-4"});case"emergency":return(0,n.jsx)(r.A,{className:"size-4"});default:return(0,n.jsx)(j.A,{className:"size-4"})}})(l.status),(0,n.jsx)("span",{className:"font-medium capitalize",children:l.status})]}),(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Updated: ",l.lastUpdate.toLocaleTimeString()]})]})]}),l.currentTask&&(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(N.A,{className:"size-4 text-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium",children:"Current Task"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:l.currentTask})]})]}),p&&(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(P.A,{className:"size-4 text-green-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium",children:"Last Location Update"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:p.toLocaleTimeString()})]})]})]})})]}),a&&(0,n.jsxs)(D.Zp,{children:[(0,n.jsx)(D.aR,{children:(0,n.jsxs)(D.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(N.A,{className:"size-5"}),"Live Location"]})}),(0,n.jsxs)(D.Wu,{children:[(0,n.jsxs)("div",{className:"mb-4 grid grid-cols-2 gap-4 md:grid-cols-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Coordinates"}),(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:[a.latitude.toFixed(6),","," ",a.longitude.toFixed(6)]})]}),a.speed&&(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Speed"}),(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:[a.speed," mph"]})]}),a.accuracy&&(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Accuracy"}),(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:["\xb1",a.accuracy,"m"]})]}),a.heading&&(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Heading"}),(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:[a.heading,"\xb0"]})]})]}),(0,n.jsx)("div",{className:"h-64 w-full rounded-lg border",ref:m,style:{minHeight:"250px"}})]})]}),o&&(0,n.jsxs)(D.Zp,{children:[(0,n.jsx)(D.aR,{children:(0,n.jsxs)(D.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(w.A,{className:"size-5"}),"Assigned Vehicle"]})}),(0,n.jsx)(D.Wu,{children:(0,n.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Vehicle"}),(0,n.jsxs)("p",{className:"text-lg",children:[o.make," ",o.model," (",o.year,")"]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"License Plate"}),(0,n.jsx)("p",{className:"w-fit rounded bg-muted px-2 py-1 font-mono text-sm",children:o.licensePlate})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Status"}),(0,n.jsx)(B.E,{variant:"Available"===o.status?"default":"secondary",children:o.status})]})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[o.mileage&&(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Mileage"}),(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:[o.mileage.toLocaleString()," miles"]})]}),o.fuelLevel&&(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Fuel Level"}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(O,{className:"size-4"}),(0,n.jsx)("div",{className:"h-2 flex-1 rounded-full bg-muted",children:(0,n.jsx)("div",{className:"h-2 rounded-full bg-blue-500",style:{width:`${o.fuelLevel}%`}})}),(0,n.jsxs)("span",{className:"text-sm",children:[o.fuelLevel,"%"]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Location"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:o.currentLocation||"Location unknown"})]})]})]})})]}),(0,n.jsxs)(D.Zp,{children:[(0,n.jsx)(D.aR,{children:(0,n.jsxs)(D.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(f.A,{className:"size-5"}),"Communication"]})}),(0,n.jsx)(D.Wu,{children:(0,n.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,n.jsxs)(I.$,{className:"justify-start",variant:"outline",children:[(0,n.jsx)(f.A,{className:"mr-2 size-4"}),"Call Driver"]}),(0,n.jsxs)(I.$,{className:"justify-start",variant:"outline",children:[(0,n.jsx)(N.A,{className:"mr-2 size-4"}),"Send Location"]})]})})]})]})};var H=i(68752),W=i(91821),U=i(12662),q=i(52027),V=i(48041),G=i(35950),$=i(49278),K=i(19599),Y=i(22482);let X=t=>{if(!t)return"bg-gray-500/20 text-gray-700 border-gray-500/30";switch(t){case"Active":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"On_Leave":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Terminated":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},J=t=>{if(!t)return"bg-gray-500/20 text-gray-700 border-gray-500/30";switch(t){case"Busy":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Off_Shift":default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20";case"On_Break":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"On_Shift":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20"}},Q=()=>{let t=(0,C.useRouter)(),e=(0,C.useParams)(),i=e?.id,l=Number.parseInt(i,10),{data:M,error:S,isLoading:E,refetch:A}=(0,K.uC)(i),{isPending:j,mutate:Z}=(0,K.FT)(),N=async()=>{if(isNaN(l)||!M)return void $.Ok.entityDeletionError("Invalid employee ID or employee data missing.");let e=M.name||"Employee";globalThis.confirm(`Are you sure you want to permanently delete ${e}?

This action cannot be undone and will remove all employee data from the system.`)&&Z(i,{onError:t=>{console.error("Failed to delete employee:",t);let e="Could not delete employee. Please try again.";t.message?.includes("Network error")?e="Network error. Please check your connection and try again.":t.message?.includes("404")?e="Employee not found or already deleted.":t.message?.includes("403")?e="You do not have permission to delete this employee.":t.message?.includes("500")?e="Server error. Please contact support if this persists.":t.response?.data?.error?e=t.response.data.error:t.message&&(e=t.message),$.Ok.entityDeletionError(e)},onSuccess:()=>{$.Ok.entityDeleted({name:e}),t.push("/employees")}})},O=({icon:t,label:e,value:i,valueClassName:o})=>i||0===i?(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)(t,{className:"mr-3 mt-0.5 size-5 shrink-0 text-accent"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:e}),(0,n.jsx)("p",{className:(0,Y.cn)("text-base font-semibold text-foreground",o),children:i})]})]}):null;return(0,n.jsxs)("div",{className:"container mx-auto space-y-6 py-8",children:[(0,n.jsx)(U.AppBreadcrumb,{}),(0,n.jsx)(q.gO,{data:M,emptyComponent:(0,n.jsxs)("div",{className:"py-10 text-center",children:[(0,n.jsx)(V.z,{icon:r.A,title:"Employee Not Found"}),(0,n.jsx)("p",{className:"mb-4",children:"The requested employee could not be found."}),(0,n.jsx)(H.r,{actionType:"primary",icon:(0,n.jsx)(a.A,{className:"size-4"}),onClick:()=>t.push("/employees"),children:"Back to Employees"})]}),error:S?S.message:null,isLoading:E,loadingComponent:(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)(V.z,{icon:h.A,title:"Loading Employee..."}),(0,n.jsx)(q.jt,{count:1,variant:"card"})]}),onRetry:()=>{A()},children:t=>{let e=t.name||"Employee";return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(V.z,{description:`Details for Employee ID: ${t.id}`,icon:h.A,title:e}),S&&(0,n.jsxs)(W.Fc,{variant:"destructive",children:[(0,n.jsx)(c,{className:"size-5"}),(0,n.jsx)(W.XL,{children:"An Error Occurred"}),(0,n.jsx)(W.TN,{children:S.message})]}),(0,n.jsxs)(D.Zp,{className:"shadow-lg",children:[(0,n.jsx)(D.aR,{className:"p-5",children:(0,n.jsxs)("div",{className:"flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)("div",{className:"relative flex size-20 items-center justify-center overflow-hidden rounded-full bg-muted ring-2 ring-primary/30",children:t.profileImageUrl?(0,n.jsx)(k.default,{alt:e,"data-ai-hint":"employee profile",layout:"fill",objectFit:"cover",src:t.profileImageUrl}):(0,n.jsx)(u.A,{className:"size-12 text-muted-foreground"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)(D.ZB,{className:"text-2xl font-bold text-primary",children:e}),(0,n.jsxs)(D.BT,{className:"text-sm",children:[t.position," -"," ",t.department]}),(0,n.jsxs)("div",{className:"mt-1 flex items-center gap-2",children:[(0,n.jsx)(B.E,{className:(0,Y.cn)("text-xs",X(t.status)),children:t.status}),"driver"===t.role&&t.availability&&(0,n.jsx)(B.E,{className:(0,Y.cn)("text-xs",J(t.availability)),children:t.availability?.replace("_"," ")})]})]})]}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-2 self-start sm:self-center",children:[(0,n.jsxs)(H.r,{actionType:"tertiary",className:"group relative",disabled:E,onClick:()=>{A()},size:"icon",title:"Refresh Employee Data",children:[(0,n.jsx)(d.A,{className:(0,Y.cn)("h-4 w-4",E&&"animate-spin")}),(0,n.jsx)("span",{className:"sr-only",children:E?"Refreshing...":"Refresh employee data"})]}),(0,n.jsx)(H.r,{actionType:"secondary",asChild:!0,className:"group relative",size:"icon",title:"Edit Employee Details",children:(0,n.jsxs)(z(),{className:"inline-flex items-center justify-center",href:`/employees/${t.id}/edit`,children:[(0,n.jsx)(p.A,{className:"size-4"}),(0,n.jsxs)("span",{className:"sr-only",children:["Edit ",e]})]})}),(0,n.jsxs)(H.r,{actionType:"danger",className:"group relative",disabled:j,isLoading:j,loadingText:"",onClick:N,size:"icon",title:`Delete ${e}`,children:[(0,n.jsx)(_.A,{className:"size-4"}),(0,n.jsx)("span",{className:"sr-only",children:j?"Deleting...":`Delete ${e}`})]})]})]})}),(0,n.jsxs)(D.Wu,{className:"mt-2 space-y-8 p-5",children:[(0,n.jsxs)("section",{children:[(0,n.jsxs)("h3",{className:"mb-3 flex items-center text-xl font-semibold text-primary",children:[(0,n.jsx)(u.A,{className:"mr-2 size-5 text-accent"})," ","Contact & Employment"]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 gap-x-6 gap-y-4 md:grid-cols-2 lg:grid-cols-3",children:[(0,n.jsx)(O,{icon:m.A,label:"Email / Primary Contact",value:t.contactEmail}),t.contactMobile&&(0,n.jsx)(O,{icon:f.A,label:"Mobile",value:t.contactMobile}),t.contactPhone&&!t.contactMobile&&(0,n.jsx)(O,{icon:f.A,label:"Phone",value:t.contactPhone}),(0,n.jsx)(O,{icon:g.A,label:"Hire Date",value:t.hireDate?(0,o.GP)((0,s.H)(t.hireDate),"MMMM d, yyyy"):"N/A"}),(0,n.jsx)(O,{icon:v.A,label:"Role",value:t.role?t.role.charAt(0).toUpperCase()+t.role.slice(1).replace("_"," "):"N/A"}),(0,n.jsx)(O,{icon:y.A,label:"Department",value:t.department}),(0,n.jsx)(O,{icon:x.A,label:"Employee ID (System)",value:t.id.toString()})]})]}),("driver"===t.role||t.skills&&t.skills.length>0||t.shiftSchedule||t.generalAssignments&&t.generalAssignments.length>0)&&(0,n.jsx)(G.w,{className:"my-4"}),"driver"===t.role&&(0,n.jsxs)("section",{children:[(0,n.jsxs)("h3",{className:"mb-3 flex items-center text-xl font-semibold text-primary",children:[(0,n.jsx)(w.A,{className:"mr-2 size-5 text-accent"})," Driver Information"]}),(0,n.jsx)(F,{employee:t})]}),t.skills&&t.skills.length>0||t.shiftSchedule||t.generalAssignments&&t.generalAssignments.length>0?(0,n.jsxs)("section",{children:[(0,n.jsxs)("h3",{className:"mb-3 flex items-center text-xl font-semibold text-primary",children:[(0,n.jsx)(b.A,{className:"mr-2 size-5 text-accent"})," Work Details"]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 gap-x-6 gap-y-4 md:grid-cols-2",children:[t.skills&&t.skills.length>0&&(0,n.jsx)(O,{icon:b.A,label:"Skills",value:t.skills.join(", ")}),(0,n.jsx)(O,{icon:P.A,label:"Shift Schedule",value:t.shiftSchedule}),t.generalAssignments&&t.generalAssignments.length>0&&(0,n.jsx)(O,{icon:v.A,label:"General Assignments",value:t.generalAssignments.join(", ")})]})]}):null,t.notes&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(G.w,{className:"my-4"}),(0,n.jsxs)("section",{children:[(0,n.jsxs)("h3",{className:"mb-2 flex items-center text-xl font-semibold text-primary",children:[(0,n.jsx)(T.A,{className:"mr-2 size-5 text-accent"})," Notes"]}),(0,n.jsx)("p",{className:"whitespace-pre-wrap rounded-md bg-muted/50 p-3 text-sm text-foreground",children:t.notes})]})]})]}),(0,n.jsxs)(D.wL,{className:"border-t p-5 pt-4 text-xs text-muted-foreground",children:["Registered:"," ",new Date(t.createdAt).toLocaleString()," | Last updated: ",new Date(t.updatedAt).toLocaleString()]})]})]})}})]})}},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31905:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>r.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>d,tree:()=>l});var n=i(65239),o=i(48088),s=i(88170),r=i.n(s),a=i(30893),h={};for(let t in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(h[t]=()=>a[t]);i.d(e,h);let l={children:["",{children:["[locale]",{children:["employees",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,77985)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\employees\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\employees\\[id]\\page.tsx"],u={require:i,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/employees/[id]/page",pathname:"/[locale]/employees/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},33873:t=>{"use strict";t.exports=require("path")},34631:t=>{"use strict";t.exports=require("tls")},35137:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});let n=(0,i(82614).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},38765:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});let n=(0,i(82614).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},48041:(t,e,i)=>{"use strict";i.d(e,{z:()=>o});var n=i(60687);function o({children:t,description:e,icon:i,title:o}){return(0,n.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[i&&(0,n.jsx)(i,{className:"size-8 text-primary"}),(0,n.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:o})]}),e&&(0,n.jsx)("p",{className:"mt-1 text-muted-foreground",children:e})]}),t&&(0,n.jsx)("div",{className:"flex items-center gap-2",children:t})]})}i(43210)},49278:(t,e,i)=>{"use strict";i.d(e,{G7:()=>d,Gb:()=>h,JP:()=>l,Ok:()=>c,Qu:()=>u,iw:()=>a,oz:()=>_,z0:()=>p});var n=i(3389);class o{show(t){return(0,n.oR)({title:t.title,description:t.description,variant:t.variant||"default",...t.duration&&{duration:t.duration}})}success(t,e){return this.show({title:t,description:e,variant:"default"})}error(t,e){return this.show({title:t,description:e,variant:"destructive"})}info(t,e){return this.show({title:t,description:e,variant:"default"})}}class s extends o{constructor(t){super(),this.config=t}entityCreated(t){let e=this.config.getDisplayName(t);return this.success(this.config.messages.created.title,this.config.messages.created.description(e))}entityUpdated(t){let e=this.config.getDisplayName(t);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(e))}entityDeleted(t){let e=this.config.getDisplayName(t);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(e))}entityCreationError(t){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(t))}entityUpdateError(t){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(t))}entityDeletionError(t){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(t))}}class r extends o{serviceRecordCreated(t,e){return this.success("Service Record Added",`${e} service for "${t}" has been successfully logged.`)}serviceRecordUpdated(t,e){return this.success("Service Record Updated",`${e} service for "${t}" has been updated.`)}serviceRecordDeleted(t,e){return this.success("Service Record Deleted",`${e} service record for "${t}" has been permanently removed.`)}serviceRecordCreationError(t){return this.error("Failed to Log Service Record",t||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(t){return this.error("Update Failed",t||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(t){return this.error("Failed to Delete Service Record",t||"An unexpected error occurred while deleting the service record.")}}function a(t){return new s(t)}function h(t,e){return new s({entityName:t,getDisplayName:e,messages:{created:{title:`${t} Created`,description:e=>`The ${t.toLowerCase()} "${e}" has been successfully created.`},updated:{title:`${t} Updated Successfully`,description:t=>`${t} has been updated.`},deleted:{title:`${t} Deleted Successfully`,description:t=>`${t} has been permanently removed.`},creationError:{title:`Failed to Create ${t}`,description:e=>e||`An unexpected error occurred while creating the ${t.toLowerCase()}.`},updateError:{title:"Update Failed",description:e=>e||`An unexpected error occurred while updating the ${t.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${t}`,description:e=>e||`An unexpected error occurred while deleting the ${t.toLowerCase()}.`}}})}let l=new o,c=new s({entityName:"Employee",getDisplayName:t=>t.name,messages:{created:{title:"Employee Added",description:t=>`The employee "${t}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:t=>`${t} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:t=>`${t} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:t=>t||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:t=>t||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:t=>t||"An unexpected error occurred while deleting the employee."}}}),u=new s({entityName:"Delegation",getDisplayName:t=>t.event||t.location||"Delegation",messages:{created:{title:"Delegation Created",description:t=>`The delegation "${t}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:t=>`${t} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:t=>`${t} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:t=>t||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:t=>t||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:t=>t||"An unexpected error occurred while deleting the delegation."}}}),d=new s({entityName:"Vehicle",getDisplayName:t=>`${t.make} ${t.model}`,messages:{created:{title:"Vehicle Added",description:t=>`The vehicle "${t}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:t=>`${t} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:t=>`${t} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:t=>t||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:t=>t||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:t=>t||"An unexpected error occurred while deleting the vehicle."}}}),p=new s({entityName:"Task",getDisplayName:t=>t.title||t.name||"Task",messages:{created:{title:"Task Created",description:t=>`The task "${t}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:t=>`${t} has been updated.`},deleted:{title:"Task Deleted Successfully",description:t=>`${t} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:t=>t||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:t=>t||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:t=>t||"An unexpected error occurred while deleting the task."}}}),_=new r},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},55817:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});let n=(0,i(82614).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},57207:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});let n=(0,i(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:t=>{"use strict";t.exports=require("zlib")},77985:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>n});let n=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\employees\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\employees\\[id]\\page.tsx","default")},79428:t=>{"use strict";t.exports=require("buffer")},79551:t=>{"use strict";t.exports=require("url")},79646:t=>{"use strict";t.exports=require("child_process")},81630:t=>{"use strict";t.exports=require("http")},83997:t=>{"use strict";t.exports=require("tty")},91645:t=>{"use strict";t.exports=require("net")},92876:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});let n=(0,i(82614).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},94735:t=>{"use strict";t.exports=require("events")},97638:function(t,e){(function(t){"use strict";function e(t){var e,i,n,o;for(i=1,n=arguments.length;i<n;i++)for(e in o=arguments[i])t[e]=o[e];return t}var i,n,o,s,r,a,h,l,c,u,d=Object.create||function(){function t(){}return function(e){return t.prototype=e,new t}}();function p(t,e){var i=Array.prototype.slice;if(t.bind)return t.bind.apply(t,i.call(arguments,1));var n=i.call(arguments,2);return function(){return t.apply(e,n.length?n.concat(i.call(arguments)):arguments)}}var _=0;function m(t){return"_leaflet_id"in t||(t._leaflet_id=++_),t._leaflet_id}function f(t,e,i){var n,o,s,r;return r=function(){n=!1,o&&(s.apply(i,o),o=!1)},s=function(){n?o=arguments:(t.apply(i,arguments),setTimeout(r,e),n=!0)}}function g(t,e,i){var n=e[1],o=e[0],s=n-o;return t===n&&i?t:((t-o)%s+s)%s+o}function v(){return!1}function y(t,e){if(!1===e)return t;var i=Math.pow(10,void 0===e?6:e);return Math.round(t*i)/i}function x(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function w(t){return x(t).split(/\s+/)}function b(t,e){for(var i in Object.prototype.hasOwnProperty.call(t,"options")||(t.options=t.options?d(t.options):{}),e)t.options[i]=e[i];return t.options}function P(t,e,i){var n=[];for(var o in t)n.push(encodeURIComponent(i?o.toUpperCase():o)+"="+encodeURIComponent(t[o]));return(e&&-1!==e.indexOf("?")?"&":"?")+n.join("&")}var T=/\{ *([\w_ -]+) *\}/g;function k(t,e){return t.replace(T,function(t,i){var n=e[i];if(void 0===n)throw Error("No value provided for variable "+t);return"function"==typeof n&&(n=n(e)),n})}var M=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function z(t,e){for(var i=0;i<t.length;i++)if(t[i]===e)return i;return -1}var C="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function S(t){return window["webkit"+t]||window["moz"+t]||window["ms"+t]}var E=0;function A(t){var e=+new Date,i=Math.max(0,16-(e-E));return E=e+i,window.setTimeout(t,i)}var j=window.requestAnimationFrame||S("RequestAnimationFrame")||A,Z=window.cancelAnimationFrame||S("CancelAnimationFrame")||S("CancelRequestAnimationFrame")||function(t){window.clearTimeout(t)};function N(t,e,i){if(!i||j!==A)return j.call(window,p(t,e));t.call(e)}function O(t){t&&Z.call(window,t)}function B(){}B.extend=function(t){var i=function(){b(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},n=i.__super__=this.prototype,o=d(n);for(var s in o.constructor=i,i.prototype=o,this)Object.prototype.hasOwnProperty.call(this,s)&&"prototype"!==s&&"__super__"!==s&&(i[s]=this[s]);return t.statics&&e(i,t.statics),t.includes&&(function(t){if("undefined"!=typeof L&&L&&L.Mixin){t=M(t)?t:[t];for(var e=0;e<t.length;e++)t[e]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",Error().stack)}}(t.includes),e.apply(null,[o].concat(t.includes))),e(o,t),delete o.statics,delete o.includes,o.options&&(o.options=n.options?d(n.options):{},e(o.options,t.options)),o._initHooks=[],o.callInitHooks=function(){if(!this._initHooksCalled){n.callInitHooks&&n.callInitHooks.call(this),this._initHooksCalled=!0;for(var t=0,e=o._initHooks.length;t<e;t++)o._initHooks[t].call(this)}},i},B.include=function(t){var i=this.prototype.options;return e(this.prototype,t),t.options&&(this.prototype.options=i,this.mergeOptions(t.options)),this},B.mergeOptions=function(t){return e(this.prototype.options,t),this},B.addInitHook=function(t){var e=Array.prototype.slice.call(arguments,1),i="function"==typeof t?t:function(){this[t].apply(this,e)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(i),this};var I={on:function(t,e,i){if("object"==typeof t)for(var n in t)this._on(n,t[n],e);else{t=w(t);for(var o=0,s=t.length;o<s;o++)this._on(t[o],e,i)}return this},off:function(t,e,i){if(arguments.length)if("object"==typeof t)for(var n in t)this._off(n,t[n],e);else{t=w(t);for(var o=1==arguments.length,s=0,r=t.length;s<r;s++)o?this._off(t[s]):this._off(t[s],e,i)}else delete this._events;return this},_on:function(t,e,i,n){if("function"!=typeof e)return void console.warn("wrong listener type: "+typeof e);if(!1===this._listens(t,e,i)){i===this&&(i=void 0);var o={fn:e,ctx:i};n&&(o.once=!0),this._events=this._events||{},this._events[t]=this._events[t]||[],this._events[t].push(o)}},_off:function(t,e,i){if(this._events&&(n=this._events[t])){if(1==arguments.length){if(this._firingCount)for(o=0,s=n.length;o<s;o++)n[o].fn=v;delete this._events[t];return}if("function"!=typeof e)return void console.warn("wrong listener type: "+typeof e);var n,o,s,r=this._listens(t,e,i);if(!1!==r){var a=n[r];this._firingCount&&(a.fn=v,this._events[t]=n=n.slice()),n.splice(r,1)}}},fire:function(t,i,n){if(!this.listens(t,n))return this;var o=e({},i,{type:t,target:this,sourceTarget:i&&i.sourceTarget||this});if(this._events){var s=this._events[t];if(s){this._firingCount=this._firingCount+1||1;for(var r=0,a=s.length;r<a;r++){var h=s[r],l=h.fn;h.once&&this.off(t,l,h.ctx),l.call(h.ctx||this,o)}this._firingCount--}}return n&&this._propagateEvent(o),this},listens:function(t,e,i,n){"string"!=typeof t&&console.warn('"string" type argument expected');var o=e;"function"!=typeof e&&(n=!!e,o=void 0,i=void 0);var s=this._events&&this._events[t];if(s&&s.length&&!1!==this._listens(t,o,i))return!0;if(n){for(var r in this._eventParents)if(this._eventParents[r].listens(t,e,i,n))return!0}return!1},_listens:function(t,e,i){if(!this._events)return!1;var n=this._events[t]||[];if(!e)return!!n.length;i===this&&(i=void 0);for(var o=0,s=n.length;o<s;o++)if(n[o].fn===e&&n[o].ctx===i)return o;return!1},once:function(t,e,i){if("object"==typeof t)for(var n in t)this._on(n,t[n],e,!0);else{t=w(t);for(var o=0,s=t.length;o<s;o++)this._on(t[o],e,i,!0)}return this},addEventParent:function(t){return this._eventParents=this._eventParents||{},this._eventParents[m(t)]=t,this},removeEventParent:function(t){return this._eventParents&&delete this._eventParents[m(t)],this},_propagateEvent:function(t){for(var i in this._eventParents)this._eventParents[i].fire(t.type,e({layer:t.target,propagatedFrom:t.target},t),!0)}};I.addEventListener=I.on,I.removeEventListener=I.clearAllEventListeners=I.off,I.addOneTimeEventListener=I.once,I.fireEvent=I.fire,I.hasEventListeners=I.listens;var D=B.extend(I);function R(t,e,i){this.x=i?Math.round(t):t,this.y=i?Math.round(e):e}var F=Math.trunc||function(t){return t>0?Math.floor(t):Math.ceil(t)};function H(t,e,i){return t instanceof R?t:M(t)?new R(t[0],t[1]):null==t?t:"object"==typeof t&&"x"in t&&"y"in t?new R(t.x,t.y):new R(t,e,i)}function W(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}function U(t,e){return!t||t instanceof W?t:new W(t,e)}function q(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}function V(t,e){return t instanceof q?t:new q(t,e)}function G(t,e,i){if(isNaN(t)||isNaN(e))throw Error("Invalid LatLng object: ("+t+", "+e+")");this.lat=+t,this.lng=+e,void 0!==i&&(this.alt=+i)}function $(t,e,i){return t instanceof G?t:M(t)&&"object"!=typeof t[0]?3===t.length?new G(t[0],t[1],t[2]):2===t.length?new G(t[0],t[1]):null:null==t?t:"object"==typeof t&&"lat"in t?new G(t.lat,"lng"in t?t.lng:t.lon,t.alt):void 0===e?null:new G(t,e,i)}R.prototype={clone:function(){return new R(this.x,this.y)},add:function(t){return this.clone()._add(H(t))},_add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.clone()._subtract(H(t))},_subtract:function(t){return this.x-=t.x,this.y-=t.y,this},divideBy:function(t){return this.clone()._divideBy(t)},_divideBy:function(t){return this.x/=t,this.y/=t,this},multiplyBy:function(t){return this.clone()._multiplyBy(t)},_multiplyBy:function(t){return this.x*=t,this.y*=t,this},scaleBy:function(t){return new R(this.x*t.x,this.y*t.y)},unscaleBy:function(t){return new R(this.x/t.x,this.y/t.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=F(this.x),this.y=F(this.y),this},distanceTo:function(t){var e=(t=H(t)).x-this.x,i=t.y-this.y;return Math.sqrt(e*e+i*i)},equals:function(t){return(t=H(t)).x===this.x&&t.y===this.y},contains:function(t){return Math.abs((t=H(t)).x)<=Math.abs(this.x)&&Math.abs(t.y)<=Math.abs(this.y)},toString:function(){return"Point("+y(this.x)+", "+y(this.y)+")"}},W.prototype={extend:function(t){var e,i;if(!t)return this;if(t instanceof R||"number"==typeof t[0]||"x"in t)e=i=H(t);else if(e=(t=U(t)).min,i=t.max,!e||!i)return this;return this.min||this.max?(this.min.x=Math.min(e.x,this.min.x),this.max.x=Math.max(i.x,this.max.x),this.min.y=Math.min(e.y,this.min.y),this.max.y=Math.max(i.y,this.max.y)):(this.min=e.clone(),this.max=i.clone()),this},getCenter:function(t){return H((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,t)},getBottomLeft:function(){return H(this.min.x,this.max.y)},getTopRight:function(){return H(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(t){var e,i;return(t="number"==typeof t[0]||t instanceof R?H(t):U(t))instanceof W?(e=t.min,i=t.max):e=i=t,e.x>=this.min.x&&i.x<=this.max.x&&e.y>=this.min.y&&i.y<=this.max.y},intersects:function(t){t=U(t);var e=this.min,i=this.max,n=t.min,o=t.max,s=o.x>=e.x&&n.x<=i.x,r=o.y>=e.y&&n.y<=i.y;return s&&r},overlaps:function(t){t=U(t);var e=this.min,i=this.max,n=t.min,o=t.max,s=o.x>e.x&&n.x<i.x,r=o.y>e.y&&n.y<i.y;return s&&r},isValid:function(){return!!(this.min&&this.max)},pad:function(t){var e=this.min,i=this.max,n=Math.abs(e.x-i.x)*t,o=Math.abs(e.y-i.y)*t;return U(H(e.x-n,e.y-o),H(i.x+n,i.y+o))},equals:function(t){return!!t&&(t=U(t),this.min.equals(t.getTopLeft())&&this.max.equals(t.getBottomRight()))}},q.prototype={extend:function(t){var e,i,n=this._southWest,o=this._northEast;if(t instanceof G)e=t,i=t;else if(!(t instanceof q))return t?this.extend($(t)||V(t)):this;else if(e=t._southWest,i=t._northEast,!e||!i)return this;return n||o?(n.lat=Math.min(e.lat,n.lat),n.lng=Math.min(e.lng,n.lng),o.lat=Math.max(i.lat,o.lat),o.lng=Math.max(i.lng,o.lng)):(this._southWest=new G(e.lat,e.lng),this._northEast=new G(i.lat,i.lng)),this},pad:function(t){var e=this._southWest,i=this._northEast,n=Math.abs(e.lat-i.lat)*t,o=Math.abs(e.lng-i.lng)*t;return new q(new G(e.lat-n,e.lng-o),new G(i.lat+n,i.lng+o))},getCenter:function(){return new G((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new G(this.getNorth(),this.getWest())},getSouthEast:function(){return new G(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(t){t="number"==typeof t[0]||t instanceof G||"lat"in t?$(t):V(t);var e,i,n=this._southWest,o=this._northEast;return t instanceof q?(e=t.getSouthWest(),i=t.getNorthEast()):e=i=t,e.lat>=n.lat&&i.lat<=o.lat&&e.lng>=n.lng&&i.lng<=o.lng},intersects:function(t){t=V(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),o=t.getNorthEast(),s=o.lat>=e.lat&&n.lat<=i.lat,r=o.lng>=e.lng&&n.lng<=i.lng;return s&&r},overlaps:function(t){t=V(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),o=t.getNorthEast(),s=o.lat>e.lat&&n.lat<i.lat,r=o.lng>e.lng&&n.lng<i.lng;return s&&r},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(t,e){return!!t&&(t=V(t),this._southWest.equals(t.getSouthWest(),e)&&this._northEast.equals(t.getNorthEast(),e))},isValid:function(){return!!(this._southWest&&this._northEast)}},G.prototype={equals:function(t,e){return!!t&&(t=$(t),Math.max(Math.abs(this.lat-t.lat),Math.abs(this.lng-t.lng))<=(void 0===e?1e-9:e))},toString:function(t){return"LatLng("+y(this.lat,t)+", "+y(this.lng,t)+")"},distanceTo:function(t){return Y.distance(this,$(t))},wrap:function(){return Y.wrapLatLng(this)},toBounds:function(t){var e=180*t/0x2637f09,i=e/Math.cos(Math.PI/180*this.lat);return V([this.lat-e,this.lng-i],[this.lat+e,this.lng+i])},clone:function(){return new G(this.lat,this.lng,this.alt)}};var K={latLngToPoint:function(t,e){var i=this.projection.project(t),n=this.scale(e);return this.transformation._transform(i,n)},pointToLatLng:function(t,e){var i=this.scale(e),n=this.transformation.untransform(t,i);return this.projection.unproject(n)},project:function(t){return this.projection.project(t)},unproject:function(t){return this.projection.unproject(t)},scale:function(t){return 256*Math.pow(2,t)},zoom:function(t){return Math.log(t/256)/Math.LN2},getProjectedBounds:function(t){if(this.infinite)return null;var e=this.projection.bounds,i=this.scale(t);return new W(this.transformation.transform(e.min,i),this.transformation.transform(e.max,i))},infinite:!1,wrapLatLng:function(t){var e=this.wrapLng?g(t.lng,this.wrapLng,!0):t.lng;return new G(this.wrapLat?g(t.lat,this.wrapLat,!0):t.lat,e,t.alt)},wrapLatLngBounds:function(t){var e=t.getCenter(),i=this.wrapLatLng(e),n=e.lat-i.lat,o=e.lng-i.lng;if(0===n&&0===o)return t;var s=t.getSouthWest(),r=t.getNorthEast();return new q(new G(s.lat-n,s.lng-o),new G(r.lat-n,r.lng-o))}},Y=e({},K,{wrapLng:[-180,180],R:6371e3,distance:function(t,e){var i=Math.PI/180,n=t.lat*i,o=e.lat*i,s=Math.sin((e.lat-t.lat)*i/2),r=Math.sin((e.lng-t.lng)*i/2),a=s*s+Math.cos(n)*Math.cos(o)*r*r,h=2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a));return this.R*h}}),X={R:6378137,MAX_LATITUDE:85.0511287798,project:function(t){var e=Math.PI/180,i=this.MAX_LATITUDE,n=Math.sin(Math.max(Math.min(i,t.lat),-i)*e);return new R(this.R*t.lng*e,this.R*Math.log((1+n)/(1-n))/2)},unproject:function(t){var e=180/Math.PI;return new G((2*Math.atan(Math.exp(t.y/this.R))-Math.PI/2)*e,t.x*e/this.R)},bounds:new W([-(h=6378137*Math.PI),-h],[h,h])};function J(t,e,i,n){if(M(t)){this._a=t[0],this._b=t[1],this._c=t[2],this._d=t[3];return}this._a=t,this._b=e,this._c=i,this._d=n}function Q(t,e,i,n){return new J(t,e,i,n)}J.prototype={transform:function(t,e){return this._transform(t.clone(),e)},_transform:function(t,e){return t.x=(e=e||1)*(this._a*t.x+this._b),t.y=e*(this._c*t.y+this._d),t},untransform:function(t,e){return e=e||1,new R((t.x/e-this._b)/this._a,(t.y/e-this._d)/this._c)}};var tt=e({},Y,{code:"EPSG:3857",projection:X,transformation:Q(l=.5/(Math.PI*X.R),.5,-l,.5)}),te=e({},tt,{code:"EPSG:900913"});function ti(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function tn(t,e){var i,n,o,s,r,a,h="";for(i=0,o=t.length;i<o;i++){for(n=0,s=(r=t[i]).length;n<s;n++)a=r[n],h+=(n?"L":"M")+a.x+" "+a.y;h+=e?tO.svg?"z":"x":""}return h||"M0 0"}var to=document.documentElement.style,ts="ActiveXObject"in window,tr=ts&&!document.addEventListener,ta="msLaunchUri"in navigator&&!("documentMode"in document),th=tN("webkit"),tl=tN("android"),tc=tN("android 2")||tN("android 3"),tu=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),td=tl&&tN("Google")&&tu<537&&!("AudioNode"in window),tp=!!window.opera,t_=!ta&&tN("chrome"),tm=tN("gecko")&&!th&&!tp&&!ts,tf=!t_&&tN("safari"),tg=tN("phantom"),tv="OTransition"in to,ty=0===navigator.platform.indexOf("Win"),tx=ts&&"transition"in to,tw="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!tc,tb="MozPerspective"in to,tP=!window.L_DISABLE_3D&&(tx||tw||tb)&&!tv&&!tg,tL="undefined"!=typeof orientation||tN("mobile"),tT=!window.PointerEvent&&window.MSPointerEvent,tk=!!(window.PointerEvent||tT),tM="ontouchstart"in window||!!window.TouchEvent,tz=!window.L_NO_TOUCH&&(tM||tk),tC=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,tS=function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("testPassiveEventSupport",v,e),window.removeEventListener("testPassiveEventSupport",v,e)}catch(t){}return t}(),tE=!!document.createElement("canvas").getContext,tA=!!(document.createElementNS&&ti("svg").createSVGRect),tj=!!tA&&((c=document.createElement("div")).innerHTML="<svg/>","http://www.w3.org/2000/svg"===(c.firstChild&&c.firstChild.namespaceURI)),tZ=!tA&&function(){try{var t=document.createElement("div");t.innerHTML='<v:shape adj="1"/>';var e=t.firstChild;return e.style.behavior="url(#default#VML)",e&&"object"==typeof e.adj}catch(t){return!1}}();function tN(t){return navigator.userAgent.toLowerCase().indexOf(t)>=0}var tO={ie:ts,ielt9:tr,edge:ta,webkit:th,android:tl,android23:tc,androidStock:td,opera:tp,chrome:t_,gecko:tm,safari:tf,phantom:tg,opera12:tv,win:ty,ie3d:tx,webkit3d:tw,gecko3d:tb,any3d:tP,mobile:tL,mobileWebkit:tL&&th,mobileWebkit3d:tL&&tw,msPointer:tT,pointer:tk,touch:tz,touchNative:tM,mobileOpera:tL&&tp,mobileGecko:tL&&tm,retina:tC,passiveEvents:tS,canvas:tE,svg:tA,vml:tZ,inlineSvg:tj,mac:0===navigator.platform.indexOf("Mac"),linux:0===navigator.platform.indexOf("Linux")},tB=tO.msPointer?"MSPointerDown":"pointerdown",tI=tO.msPointer?"MSPointerMove":"pointermove",tD=tO.msPointer?"MSPointerUp":"pointerup",tR=tO.msPointer?"MSPointerCancel":"pointercancel",tF={touchstart:tB,touchmove:tI,touchend:tD,touchcancel:tR},tH={touchstart:function(t,e){e.MSPOINTER_TYPE_TOUCH&&e.pointerType===e.MSPOINTER_TYPE_TOUCH&&eb(e),t$(t,e)},touchmove:t$,touchend:t$,touchcancel:t$},tW={},tU=!1;function tq(t){tW[t.pointerId]=t}function tV(t){tW[t.pointerId]&&(tW[t.pointerId]=t)}function tG(t){delete tW[t.pointerId]}function t$(t,e){if(e.pointerType!==(e.MSPOINTER_TYPE_MOUSE||"mouse")){for(var i in e.touches=[],tW)e.touches.push(tW[i]);e.changedTouches=[e],t(e)}}var tK=ee(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),tY=ee(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),tX="webkitTransition"===tY||"OTransition"===tY?tY+"End":"transitionend";function tJ(t){return"string"==typeof t?document.getElementById(t):t}function tQ(t,e){var i=t.style[e]||t.currentStyle&&t.currentStyle[e];if((!i||"auto"===i)&&document.defaultView){var n=document.defaultView.getComputedStyle(t,null);i=n?n[e]:null}return"auto"===i?null:i}function t0(t,e,i){var n=document.createElement(t);return n.className=e||"",i&&i.appendChild(n),n}function t1(t){var e=t.parentNode;e&&e.removeChild(t)}function t2(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function t3(t){var e=t.parentNode;e&&e.lastChild!==t&&e.appendChild(t)}function t5(t){var e=t.parentNode;e&&e.firstChild!==t&&e.insertBefore(t,e.firstChild)}function t4(t,e){if(void 0!==t.classList)return t.classList.contains(e);var i=t6(t);return i.length>0&&RegExp("(^|\\s)"+e+"(\\s|$)").test(i)}function t8(t,e){if(void 0!==t.classList)for(var i=w(e),n=0,o=i.length;n<o;n++)t.classList.add(i[n]);else if(!t4(t,e)){var s=t6(t);t9(t,(s?s+" ":"")+e)}}function t7(t,e){void 0!==t.classList?t.classList.remove(e):t9(t,x((" "+t6(t)+" ").replace(" "+e+" "," ")))}function t9(t,e){void 0===t.className.baseVal?t.className=e:t.className.baseVal=e}function t6(t){return t.correspondingElement&&(t=t.correspondingElement),void 0===t.className.baseVal?t.className:t.className.baseVal}function et(t,e){"opacity"in t.style?t.style.opacity=e:"filter"in t.style&&function(t,e){var i=!1,n="DXImageTransform.Microsoft.Alpha";try{i=t.filters.item(n)}catch(t){if(1===e)return}e=Math.round(100*e),i?(i.Enabled=100!==e,i.Opacity=e):t.style.filter+=" progid:"+n+"(opacity="+e+")"}(t,e)}function ee(t){for(var e=document.documentElement.style,i=0;i<t.length;i++)if(t[i]in e)return t[i];return!1}function ei(t,e,i){var n=e||new R(0,0);t.style[tK]=(tO.ie3d?"translate("+n.x+"px,"+n.y+"px)":"translate3d("+n.x+"px,"+n.y+"px,0)")+(i?" scale("+i+")":"")}function en(t,e){t._leaflet_pos=e,tO.any3d?ei(t,e):(t.style.left=e.x+"px",t.style.top=e.y+"px")}function eo(t){return t._leaflet_pos||new R(0,0)}if("onselectstart"in document)i=function(){ed(window,"selectstart",eb)},n=function(){e_(window,"selectstart",eb)};else{var es=ee(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);i=function(){if(es){var t=document.documentElement.style;o=t[es],t[es]="none"}},n=function(){es&&(document.documentElement.style[es]=o,o=void 0)}}function er(){ed(window,"dragstart",eb)}function ea(){e_(window,"dragstart",eb)}function eh(t){for(;-1===t.tabIndex;)t=t.parentNode;t.style&&(el(),s=t,r=t.style.outlineStyle,t.style.outlineStyle="none",ed(window,"keydown",el))}function el(){s&&(s.style.outlineStyle=r,s=void 0,r=void 0,e_(window,"keydown",el))}function ec(t){do t=t.parentNode;while((!t.offsetWidth||!t.offsetHeight)&&t!==document.body);return t}function eu(t){var e=t.getBoundingClientRect();return{x:e.width/t.offsetWidth||1,y:e.height/t.offsetHeight||1,boundingClientRect:e}}function ed(t,e,i,n){if(e&&"object"==typeof e)for(var o in e)eg(t,o,e[o],i);else{e=w(e);for(var s=0,r=e.length;s<r;s++)eg(t,e[s],i,n)}return this}var ep="_leaflet_events";function e_(t,e,i,n){if(1==arguments.length)em(t),delete t[ep];else if(e&&"object"==typeof e)for(var o in e)ev(t,o,e[o],i);else if(e=w(e),2==arguments.length)em(t,function(t){return -1!==z(e,t)});else for(var s=0,r=e.length;s<r;s++)ev(t,e[s],i,n);return this}function em(t,e){for(var i in t[ep]){var n=i.split(/\d/)[0];(!e||e(n))&&ev(t,n,null,null,i)}}var ef={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function eg(t,e,i,n){var o=e+m(i)+(n?"_"+m(n):"");if(t[ep]&&t[ep][o])return this;var s=function(e){return i.call(n||t,e||window.event)},r=s;!tO.touchNative&&tO.pointer&&0===e.indexOf("touch")?s=function(t,e,i){return("touchstart"===e&&(tU||(document.addEventListener(tB,tq,!0),document.addEventListener(tI,tV,!0),document.addEventListener(tD,tG,!0),document.addEventListener(tR,tG,!0),tU=!0)),tH[e])?(i=tH[e].bind(this,i),t.addEventListener(tF[e],i,!1),i):(console.warn("wrong event specified:",e),v)}(t,e,s):tO.touch&&"dblclick"===e?s=function(t,e){t.addEventListener("dblclick",e);var i,n=0;function o(t){if(1!==t.detail){i=t.detail;return}if("mouse"!==t.pointerType&&(!t.sourceCapabilities||t.sourceCapabilities.firesTouchEvents)){var o=eL(t);if(!o.some(function(t){return t instanceof HTMLLabelElement&&t.attributes.for})||o.some(function(t){return t instanceof HTMLInputElement||t instanceof HTMLSelectElement})){var s=Date.now();s-n<=200?2==++i&&e(function(t){var e,i,n={};for(i in t)e=t[i],n[i]=e&&e.bind?e.bind(t):e;return t=n,n.type="dblclick",n.detail=2,n.isTrusted=!1,n._simulated=!0,n}(t)):i=1,n=s}}}return t.addEventListener("click",o),{dblclick:e,simDblclick:o}}(t,s):"addEventListener"in t?"touchstart"===e||"touchmove"===e||"wheel"===e||"mousewheel"===e?t.addEventListener(ef[e]||e,s,!!tO.passiveEvents&&{passive:!1}):"mouseenter"===e||"mouseleave"===e?(s=function(e){ez(t,e=e||window.event)&&r(e)},t.addEventListener(ef[e],s,!1)):t.addEventListener(e,r,!1):t.attachEvent("on"+e,s),t[ep]=t[ep]||{},t[ep][o]=s}function ev(t,e,i,n,o){o=o||e+m(i)+(n?"_"+m(n):"");var s=t[ep]&&t[ep][o];if(!s)return this;!tO.touchNative&&tO.pointer&&0===e.indexOf("touch")?!function(t,e,i){if(!tF[e])return console.warn("wrong event specified:",e);t.removeEventListener(tF[e],i,!1)}(t,e,s):tO.touch&&"dblclick"===e?(t.removeEventListener("dblclick",s.dblclick),t.removeEventListener("click",s.simDblclick)):"removeEventListener"in t?t.removeEventListener(ef[e]||e,s,!1):t.detachEvent("on"+e,s),t[ep][o]=null}function ey(t){return t.stopPropagation?t.stopPropagation():t.originalEvent?t.originalEvent._stopped=!0:t.cancelBubble=!0,this}function ex(t){return eg(t,"wheel",ey),this}function ew(t){return ed(t,"mousedown touchstart dblclick contextmenu",ey),t._leaflet_disable_click=!0,this}function eb(t){return t.preventDefault?t.preventDefault():t.returnValue=!1,this}function eP(t){return eb(t),ey(t),this}function eL(t){if(t.composedPath)return t.composedPath();for(var e=[],i=t.target;i;)e.push(i),i=i.parentNode;return e}function eT(t,e){if(!e)return new R(t.clientX,t.clientY);var i=eu(e),n=i.boundingClientRect;return new R((t.clientX-n.left)/i.x-e.clientLeft,(t.clientY-n.top)/i.y-e.clientTop)}var ek=tO.linux&&tO.chrome?window.devicePixelRatio:tO.mac?3*window.devicePixelRatio:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function eM(t){return tO.edge?t.wheelDeltaY/2:t.deltaY&&0===t.deltaMode?-t.deltaY/ek:t.deltaY&&1===t.deltaMode?-(20*t.deltaY):t.deltaY&&2===t.deltaMode?-(60*t.deltaY):t.deltaX||t.deltaZ?0:t.wheelDelta?(t.wheelDeltaY||t.wheelDelta)/2:t.detail&&32765>Math.abs(t.detail)?-(20*t.detail):t.detail?-(60*(t.detail/32765)):0}function ez(t,e){var i=e.relatedTarget;if(!i)return!0;try{for(;i&&i!==t;)i=i.parentNode}catch(t){return!1}return i!==t}var eC=D.extend({run:function(t,e,i,n){this.stop(),this._el=t,this._inProgress=!0,this._duration=i||.25,this._easeOutPower=1/Math.max(n||.5,.2),this._startPos=eo(t),this._offset=e.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=N(this._animate,this),this._step()},_step:function(t){var e=new Date-this._startTime,i=1e3*this._duration;e<i?this._runFrame(this._easeOut(e/i),t):(this._runFrame(1),this._complete())},_runFrame:function(t,e){var i=this._startPos.add(this._offset.multiplyBy(t));e&&i._round(),en(this._el,i),this.fire("step")},_complete:function(){O(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(t){return 1-Math.pow(1-t,this._easeOutPower)}}),eS=D.extend({options:{crs:tt,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(t,e){e=b(this,e),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(t),this._initLayout(),this._onResize=p(this._onResize,this),this._initEvents(),e.maxBounds&&this.setMaxBounds(e.maxBounds),void 0!==e.zoom&&(this._zoom=this._limitZoom(e.zoom)),e.center&&void 0!==e.zoom&&this.setView($(e.center),e.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=tY&&tO.any3d&&!tO.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),ed(this._proxy,tX,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(t,i,n){return(i=void 0===i?this._zoom:this._limitZoom(i),t=this._limitCenter($(t),i,this.options.maxBounds),n=n||{},this._stop(),this._loaded&&!n.reset&&!0!==n&&(void 0!==n.animate&&(n.zoom=e({animate:n.animate},n.zoom),n.pan=e({animate:n.animate,duration:n.duration},n.pan)),this._zoom!==i?this._tryAnimatedZoom&&this._tryAnimatedZoom(t,i,n.zoom):this._tryAnimatedPan(t,n.pan)))?(clearTimeout(this._sizeTimer),this):(this._resetView(t,i,n.pan&&n.pan.noMoveStart),this)},setZoom:function(t,e){return this._loaded?this.setView(this.getCenter(),t,{zoom:e}):(this._zoom=t,this)},zoomIn:function(t,e){return t=t||(tO.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+t,e)},zoomOut:function(t,e){return t=t||(tO.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-t,e)},setZoomAround:function(t,e,i){var n=this.getZoomScale(e),o=this.getSize().divideBy(2),s=(t instanceof R?t:this.latLngToContainerPoint(t)).subtract(o).multiplyBy(1-1/n),r=this.containerPointToLatLng(o.add(s));return this.setView(r,e,{zoom:i})},_getBoundsCenterZoom:function(t,e){e=e||{},t=t.getBounds?t.getBounds():V(t);var i=H(e.paddingTopLeft||e.padding||[0,0]),n=H(e.paddingBottomRight||e.padding||[0,0]),o=this.getBoundsZoom(t,!1,i.add(n));if((o="number"==typeof e.maxZoom?Math.min(e.maxZoom,o):o)===1/0)return{center:t.getCenter(),zoom:o};var s=n.subtract(i).divideBy(2),r=this.project(t.getSouthWest(),o),a=this.project(t.getNorthEast(),o);return{center:this.unproject(r.add(a).divideBy(2).add(s),o),zoom:o}},fitBounds:function(t,e){if(!(t=V(t)).isValid())throw Error("Bounds are not valid.");var i=this._getBoundsCenterZoom(t,e);return this.setView(i.center,i.zoom,e)},fitWorld:function(t){return this.fitBounds([[-90,-180],[90,180]],t)},panTo:function(t,e){return this.setView(t,this._zoom,{pan:e})},panBy:function(t,e){if(t=H(t).round(),e=e||{},!t.x&&!t.y)return this.fire("moveend");if(!0!==e.animate&&!this.getSize().contains(t))return this._resetView(this.unproject(this.project(this.getCenter()).add(t)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new eC,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),e.noMoveStart||this.fire("movestart"),!1!==e.animate){t8(this._mapPane,"leaflet-pan-anim");var i=this._getMapPanePos().subtract(t).round();this._panAnim.run(this._mapPane,i,e.duration||.25,e.easeLinearity)}else this._rawPanBy(t),this.fire("move").fire("moveend");return this},flyTo:function(t,e,i){if(!1===(i=i||{}).animate||!tO.any3d)return this.setView(t,e,i);this._stop();var n=this.project(this.getCenter()),o=this.project(t),s=this.getSize(),r=this._zoom;t=$(t),e=void 0===e?r:e;var a=Math.max(s.x,s.y),h=a*this.getZoomScale(r,e),l=o.distanceTo(n)||1;function c(t){var e=(h*h-a*a+(t?-1:1)*4.0658689599999995*l*l)/(2*(t?h:a)*2.0164*l),i=Math.sqrt(e*e+1)-e;return i<1e-9?-18:Math.log(i)}function u(t){return(Math.exp(t)-Math.exp(-t))/2}function d(t){return(Math.exp(t)+Math.exp(-t))/2}var p=c(0),_=Date.now(),m=(c(1)-p)/1.42,f=i.duration?1e3*i.duration:1e3*m*.8;function g(){var i,s=(Date.now()-_)/f,h=(1-Math.pow(1-s,1.5))*m;s<=1?(this._flyToFrame=N(g,this),this._move(this.unproject(n.add(o.subtract(n).multiplyBy(a*(d(p)*(u(i=p+1.42*h)/d(i))-u(p))/2.0164/l)),r),this.getScaleZoom(a/(a*(d(p)/d(p+1.42*h))),r),{flyTo:!0})):this._move(t,e)._moveEnd(!0)}return this._moveStart(!0,i.noMoveStart),g.call(this),this},flyToBounds:function(t,e){var i=this._getBoundsCenterZoom(t,e);return this.flyTo(i.center,i.zoom,e)},setMaxBounds:function(t){return(t=V(t),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),t.isValid())?(this.options.maxBounds=t,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(t){var e=this.options.minZoom;return(this.options.minZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom))?this.setZoom(t):this},setMaxZoom:function(t){var e=this.options.maxZoom;return(this.options.maxZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom))?this.setZoom(t):this},panInsideBounds:function(t,e){this._enforcingBounds=!0;var i=this.getCenter(),n=this._limitCenter(i,this._zoom,V(t));return i.equals(n)||this.panTo(n,e),this._enforcingBounds=!1,this},panInside:function(t,e){var i=H((e=e||{}).paddingTopLeft||e.padding||[0,0]),n=H(e.paddingBottomRight||e.padding||[0,0]),o=this.project(this.getCenter()),s=this.project(t),r=this.getPixelBounds(),a=U([r.min.add(i),r.max.subtract(n)]),h=a.getSize();if(!a.contains(s)){this._enforcingBounds=!0;var l=s.subtract(a.getCenter()),c=a.extend(s).getSize().subtract(h);o.x+=l.x<0?-c.x:c.x,o.y+=l.y<0?-c.y:c.y,this.panTo(this.unproject(o),e),this._enforcingBounds=!1}return this},invalidateSize:function(t){if(!this._loaded)return this;t=e({animate:!1,pan:!0},!0===t?{animate:!0}:t);var i=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var n=this.getSize(),o=i.divideBy(2).round(),s=n.divideBy(2).round(),r=o.subtract(s);return r.x||r.y?(t.animate&&t.pan?this.panBy(r):(t.pan&&this._rawPanBy(r),this.fire("move"),t.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(p(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:i,newSize:n})):this},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(t){if(t=this._locateOptions=e({timeout:1e4,watch:!1},t),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var i=p(this._handleGeolocationResponse,this),n=p(this._handleGeolocationError,this);return t.watch?this._locationWatchId=navigator.geolocation.watchPosition(i,n,t):navigator.geolocation.getCurrentPosition(i,n,t),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(t){if(this._container._leaflet_id){var e=t.code,i=t.message||(1===e?"permission denied":2===e?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:e,message:"Geolocation error: "+i+"."})}},_handleGeolocationResponse:function(t){if(this._container._leaflet_id){var e=new G(t.coords.latitude,t.coords.longitude),i=e.toBounds(2*t.coords.accuracy),n=this._locateOptions;if(n.setView){var o=this.getBoundsZoom(i);this.setView(e,n.maxZoom?Math.min(o,n.maxZoom):o)}var s={latlng:e,bounds:i,timestamp:t.timestamp};for(var r in t.coords)"number"==typeof t.coords[r]&&(s[r]=t.coords[r]);this.fire("locationfound",s)}},addHandler:function(t,e){if(!e)return this;var i=this[t]=new e(this);return this._handlers.push(i),this.options[t]&&i.enable(),this},remove:function(){var t;if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch(t){this._container._leaflet_id=void 0,this._containerId=void 0}for(t in void 0!==this._locationWatchId&&this.stopLocate(),this._stop(),t1(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(O(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload"),this._layers)this._layers[t].remove();for(t in this._panes)t1(this._panes[t]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(t,e){var i=t0("div","leaflet-pane"+(t?" leaflet-"+t.replace("Pane","")+"-pane":""),e||this._mapPane);return t&&(this._panes[t]=i),i},getCenter:function(){return(this._checkIfLoaded(),this._lastCenter&&!this._moved())?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var t=this.getPixelBounds();return new q(this.unproject(t.getBottomLeft()),this.unproject(t.getTopRight()))},getMinZoom:function(){return void 0===this.options.minZoom?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return void 0===this.options.maxZoom?void 0===this._layersMaxZoom?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(t,e,i){t=V(t),i=H(i||[0,0]);var n=this.getZoom()||0,o=this.getMinZoom(),s=this.getMaxZoom(),r=t.getNorthWest(),a=t.getSouthEast(),h=this.getSize().subtract(i),l=U(this.project(a,n),this.project(r,n)).getSize(),c=tO.any3d?this.options.zoomSnap:1,u=h.x/l.x,d=h.y/l.y,p=e?Math.max(u,d):Math.min(u,d);return n=this.getScaleZoom(p,n),c&&(n=c/100*Math.round(n/(c/100)),n=e?Math.ceil(n/c)*c:Math.floor(n/c)*c),Math.max(o,Math.min(s,n))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new R(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(t,e){var i=this._getTopLeftPoint(t,e);return new W(i,i.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(t){return this.options.crs.getProjectedBounds(void 0===t?this.getZoom():t)},getPane:function(t){return"string"==typeof t?this._panes[t]:t},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(t,e){var i=this.options.crs;return e=void 0===e?this._zoom:e,i.scale(t)/i.scale(e)},getScaleZoom:function(t,e){var i=this.options.crs;e=void 0===e?this._zoom:e;var n=i.zoom(t*i.scale(e));return isNaN(n)?1/0:n},project:function(t,e){return e=void 0===e?this._zoom:e,this.options.crs.latLngToPoint($(t),e)},unproject:function(t,e){return e=void 0===e?this._zoom:e,this.options.crs.pointToLatLng(H(t),e)},layerPointToLatLng:function(t){var e=H(t).add(this.getPixelOrigin());return this.unproject(e)},latLngToLayerPoint:function(t){return this.project($(t))._round()._subtract(this.getPixelOrigin())},wrapLatLng:function(t){return this.options.crs.wrapLatLng($(t))},wrapLatLngBounds:function(t){return this.options.crs.wrapLatLngBounds(V(t))},distance:function(t,e){return this.options.crs.distance($(t),$(e))},containerPointToLayerPoint:function(t){return H(t).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(t){return H(t).add(this._getMapPanePos())},containerPointToLatLng:function(t){var e=this.containerPointToLayerPoint(H(t));return this.layerPointToLatLng(e)},latLngToContainerPoint:function(t){return this.layerPointToContainerPoint(this.latLngToLayerPoint($(t)))},mouseEventToContainerPoint:function(t){return eT(t,this._container)},mouseEventToLayerPoint:function(t){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(t))},mouseEventToLatLng:function(t){return this.layerPointToLatLng(this.mouseEventToLayerPoint(t))},_initContainer:function(t){var e=this._container=tJ(t);if(e){if(e._leaflet_id)throw Error("Map container is already initialized.")}else throw Error("Map container not found.");ed(e,"scroll",this._onScroll,this),this._containerId=m(e)},_initLayout:function(){var t=this._container;this._fadeAnimated=this.options.fadeAnimation&&tO.any3d,t8(t,"leaflet-container"+(tO.touch?" leaflet-touch":"")+(tO.retina?" leaflet-retina":"")+(tO.ielt9?" leaflet-oldie":"")+(tO.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var e=tQ(t,"position");"absolute"!==e&&"relative"!==e&&"fixed"!==e&&"sticky"!==e&&(t.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var t=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),en(this._mapPane,new R(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(t8(t.markerPane,"leaflet-zoom-hide"),t8(t.shadowPane,"leaflet-zoom-hide"))},_resetView:function(t,e,i){en(this._mapPane,new R(0,0));var n=!this._loaded;this._loaded=!0,e=this._limitZoom(e),this.fire("viewprereset");var o=this._zoom!==e;this._moveStart(o,i)._move(t,e)._moveEnd(o),this.fire("viewreset"),n&&this.fire("load")},_moveStart:function(t,e){return t&&this.fire("zoomstart"),e||this.fire("movestart"),this},_move:function(t,e,i,n){void 0===e&&(e=this._zoom);var o=this._zoom!==e;return this._zoom=e,this._lastCenter=t,this._pixelOrigin=this._getNewPixelOrigin(t),n?i&&i.pinch&&this.fire("zoom",i):((o||i&&i.pinch)&&this.fire("zoom",i),this.fire("move",i)),this},_moveEnd:function(t){return t&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return O(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(t){en(this._mapPane,this._getMapPanePos().subtract(t))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw Error("Set map center and zoom first.")},_initEvents:function(t){this._targets={},this._targets[m(this._container)]=this;var e=t?e_:ed;e(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&e(window,"resize",this._onResize,this),tO.any3d&&this.options.transform3DLimit&&(t?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){O(this._resizeRequest),this._resizeRequest=N(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var t=this._getMapPanePos();Math.max(Math.abs(t.x),Math.abs(t.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(t,e){for(var i,n=[],o="mouseout"===e||"mouseover"===e,s=t.target||t.srcElement,r=!1;s;){if((i=this._targets[m(s)])&&("click"===e||"preclick"===e)&&this._draggableMoved(i)){r=!0;break}if(i&&i.listens(e,!0)&&(o&&!ez(s,t)||(n.push(i),o)))break;if(s===this._container)break;s=s.parentNode}return!n.length&&!r&&!o&&this.listens(e,!0)&&(n=[this]),n},_isClickDisabled:function(t){for(;t&&t!==this._container;){if(t._leaflet_disable_click)return!0;t=t.parentNode}},_handleDOMEvent:function(t){var e=t.target||t.srcElement;if(!(!this._loaded||e._leaflet_disable_events||"click"===t.type&&this._isClickDisabled(e))){var i=t.type;"mousedown"===i&&eh(e),this._fireDOMEvent(t,i)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(t,i,n){if("click"===t.type){var o=e({},t);o.type="preclick",this._fireDOMEvent(o,o.type,n)}var s=this._findEventTargets(t,i);if(n){for(var r=[],a=0;a<n.length;a++)n[a].listens(i,!0)&&r.push(n[a]);s=r.concat(s)}if(s.length){"contextmenu"===i&&eb(t);var h=s[0],l={originalEvent:t};if("keypress"!==t.type&&"keydown"!==t.type&&"keyup"!==t.type){var c=h.getLatLng&&(!h._radius||h._radius<=10);l.containerPoint=c?this.latLngToContainerPoint(h.getLatLng()):this.mouseEventToContainerPoint(t),l.layerPoint=this.containerPointToLayerPoint(l.containerPoint),l.latlng=c?h.getLatLng():this.layerPointToLatLng(l.layerPoint)}for(a=0;a<s.length;a++)if(s[a].fire(i,l,!0),l.originalEvent._stopped||!1===s[a].options.bubblingMouseEvents&&-1!==z(this._mouseEvents,i))return}},_draggableMoved:function(t){return(t=t.dragging&&t.dragging.enabled()?t:this).dragging&&t.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var t=0,e=this._handlers.length;t<e;t++)this._handlers[t].disable()},whenReady:function(t,e){return this._loaded?t.call(e||this,{target:this}):this.on("load",t,e),this},_getMapPanePos:function(){return eo(this._mapPane)||new R(0,0)},_moved:function(){var t=this._getMapPanePos();return t&&!t.equals([0,0])},_getTopLeftPoint:function(t,e){return(t&&void 0!==e?this._getNewPixelOrigin(t,e):this.getPixelOrigin()).subtract(this._getMapPanePos())},_getNewPixelOrigin:function(t,e){var i=this.getSize()._divideBy(2);return this.project(t,e)._subtract(i)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(t,e,i){var n=this._getNewPixelOrigin(i,e);return this.project(t,e)._subtract(n)},_latLngBoundsToNewLayerBounds:function(t,e,i){var n=this._getNewPixelOrigin(i,e);return U([this.project(t.getSouthWest(),e)._subtract(n),this.project(t.getNorthWest(),e)._subtract(n),this.project(t.getSouthEast(),e)._subtract(n),this.project(t.getNorthEast(),e)._subtract(n)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(t){return this.latLngToLayerPoint(t).subtract(this._getCenterLayerPoint())},_limitCenter:function(t,e,i){if(!i)return t;var n=this.project(t,e),o=this.getSize().divideBy(2),s=new W(n.subtract(o),n.add(o)),r=this._getBoundsOffset(s,i,e);return 1>=Math.abs(r.x)&&1>=Math.abs(r.y)?t:this.unproject(n.add(r),e)},_limitOffset:function(t,e){if(!e)return t;var i=this.getPixelBounds(),n=new W(i.min.add(t),i.max.add(t));return t.add(this._getBoundsOffset(n,e))},_getBoundsOffset:function(t,e,i){var n=U(this.project(e.getNorthEast(),i),this.project(e.getSouthWest(),i)),o=n.min.subtract(t.min),s=n.max.subtract(t.max);return new R(this._rebound(o.x,-s.x),this._rebound(o.y,-s.y))},_rebound:function(t,e){return t+e>0?Math.round(t-e)/2:Math.max(0,Math.ceil(t))-Math.max(0,Math.floor(e))},_limitZoom:function(t){var e=this.getMinZoom(),i=this.getMaxZoom(),n=tO.any3d?this.options.zoomSnap:1;return n&&(t=Math.round(t/n)*n),Math.max(e,Math.min(i,t))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){t7(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(t,e){var i=this._getCenterOffset(t)._trunc();return(!0===(e&&e.animate)||!!this.getSize().contains(i))&&(this.panBy(i,e),!0)},_createAnimProxy:function(){var t=this._proxy=t0("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(t),this.on("zoomanim",function(t){var e=this._proxy.style[tK];ei(this._proxy,this.project(t.center,t.zoom),this.getZoomScale(t.zoom,1)),e===this._proxy.style[tK]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){t1(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var t=this.getCenter(),e=this.getZoom();ei(this._proxy,this.project(t,e),this.getZoomScale(e,1))},_catchTransitionEnd:function(t){this._animatingZoom&&t.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(t,e,i){if(this._animatingZoom)return!0;if(i=i||{},!this._zoomAnimated||!1===i.animate||this._nothingToAnimate()||Math.abs(e-this._zoom)>this.options.zoomAnimationThreshold)return!1;var n=this.getZoomScale(e),o=this._getCenterOffset(t)._divideBy(1-1/n);return(!0===i.animate||!!this.getSize().contains(o))&&(N(function(){this._moveStart(!0,i.noMoveStart||!1)._animateZoom(t,e,!0)},this),!0)},_animateZoom:function(t,e,i,n){this._mapPane&&(i&&(this._animatingZoom=!0,this._animateToCenter=t,this._animateToZoom=e,t8(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:t,zoom:e,noUpdate:n}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(p(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&t7(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}}),eE=B.extend({options:{position:"topright"},initialize:function(t){b(this,t)},getPosition:function(){return this.options.position},setPosition:function(t){var e=this._map;return e&&e.removeControl(this),this.options.position=t,e&&e.addControl(this),this},getContainer:function(){return this._container},addTo:function(t){this.remove(),this._map=t;var e=this._container=this.onAdd(t),i=this.getPosition(),n=t._controlCorners[i];return t8(e,"leaflet-control"),-1!==i.indexOf("bottom")?n.insertBefore(e,n.firstChild):n.appendChild(e),this._map.on("unload",this.remove,this),this},remove:function(){return this._map&&(t1(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null),this},_refocusOnMap:function(t){this._map&&t&&t.screenX>0&&t.screenY>0&&this._map.getContainer().focus()}}),eA=function(t){return new eE(t)};eS.include({addControl:function(t){return t.addTo(this),this},removeControl:function(t){return t.remove(),this},_initControlPos:function(){var t=this._controlCorners={},e="leaflet-",i=this._controlContainer=t0("div",e+"control-container",this._container);function n(n,o){t[n+o]=t0("div",e+n+" "+e+o,i)}n("top","left"),n("top","right"),n("bottom","left"),n("bottom","right")},_clearControlPos:function(){for(var t in this._controlCorners)t1(this._controlCorners[t]);t1(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var ej=eE.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(t,e,i,n){return i<n?-1:+(n<i)}},initialize:function(t,e,i){for(var n in b(this,i),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1,t)this._addLayer(t[n],n);for(n in e)this._addLayer(e[n],n,!0)},onAdd:function(t){this._initLayout(),this._update(),this._map=t,t.on("zoomend",this._checkDisabledLayers,this);for(var e=0;e<this._layers.length;e++)this._layers[e].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(t){return eE.prototype.addTo.call(this,t),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var t=0;t<this._layers.length;t++)this._layers[t].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(t,e){return this._addLayer(t,e),this._map?this._update():this},addOverlay:function(t,e){return this._addLayer(t,e,!0),this._map?this._update():this},removeLayer:function(t){t.off("add remove",this._onLayerChange,this);var e=this._getLayer(m(t));return e&&this._layers.splice(this._layers.indexOf(e),1),this._map?this._update():this},expand:function(){t8(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var t=this._map.getSize().y-(this._container.offsetTop+50);return t<this._section.clientHeight?(t8(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=t+"px"):t7(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return t7(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var t="leaflet-control-layers",e=this._container=t0("div",t),i=this.options.collapsed;e.setAttribute("aria-haspopup",!0),ew(e),ex(e);var n=this._section=t0("section",t+"-list");i&&(this._map.on("click",this.collapse,this),ed(e,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var o=this._layersLink=t0("a",t+"-toggle",e);o.href="#",o.title="Layers",o.setAttribute("role","button"),ed(o,{keydown:function(t){13===t.keyCode&&this._expandSafely()},click:function(t){eb(t),this._expandSafely()}},this),i||this.expand(),this._baseLayersList=t0("div",t+"-base",n),this._separator=t0("div",t+"-separator",n),this._overlaysList=t0("div",t+"-overlays",n),e.appendChild(n)},_getLayer:function(t){for(var e=0;e<this._layers.length;e++)if(this._layers[e]&&m(this._layers[e].layer)===t)return this._layers[e]},_addLayer:function(t,e,i){this._map&&t.on("add remove",this._onLayerChange,this),this._layers.push({layer:t,name:e,overlay:i}),this.options.sortLayers&&this._layers.sort(p(function(t,e){return this.options.sortFunction(t.layer,e.layer,t.name,e.name)},this)),this.options.autoZIndex&&t.setZIndex&&(this._lastZIndex++,t.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;t2(this._baseLayersList),t2(this._overlaysList),this._layerControlInputs=[];var t,e,i,n,o=0;for(i=0;i<this._layers.length;i++)n=this._layers[i],this._addItem(n),e=e||n.overlay,t=t||!n.overlay,o+=+!n.overlay;return this.options.hideSingleBase&&(t=t&&o>1,this._baseLayersList.style.display=t?"":"none"),this._separator.style.display=e&&t?"":"none",this},_onLayerChange:function(t){this._handlingClick||this._update();var e=this._getLayer(m(t.target)),i=e.overlay?"add"===t.type?"overlayadd":"overlayremove":"add"===t.type?"baselayerchange":null;i&&this._map.fire(i,e)},_createRadioElement:function(t,e){var i=document.createElement("div");return i.innerHTML='<input type="radio" class="leaflet-control-layers-selector" name="'+t+'"'+(e?' checked="checked"':"")+"/>",i.firstChild},_addItem:function(t){var e,i=document.createElement("label"),n=this._map.hasLayer(t.layer);t.overlay?((e=document.createElement("input")).type="checkbox",e.className="leaflet-control-layers-selector",e.defaultChecked=n):e=this._createRadioElement("leaflet-base-layers_"+m(this),n),this._layerControlInputs.push(e),e.layerId=m(t.layer),ed(e,"click",this._onInputClick,this);var o=document.createElement("span");o.innerHTML=" "+t.name;var s=document.createElement("span");return i.appendChild(s),s.appendChild(e),s.appendChild(o),(t.overlay?this._overlaysList:this._baseLayersList).appendChild(i),this._checkDisabledLayers(),i},_onInputClick:function(){if(!this._preventClick){var t,e,i=this._layerControlInputs,n=[],o=[];this._handlingClick=!0;for(var s=i.length-1;s>=0;s--)t=i[s],e=this._getLayer(t.layerId).layer,t.checked?n.push(e):t.checked||o.push(e);for(s=0;s<o.length;s++)this._map.hasLayer(o[s])&&this._map.removeLayer(o[s]);for(s=0;s<n.length;s++)this._map.hasLayer(n[s])||this._map.addLayer(n[s]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var t,e,i=this._layerControlInputs,n=this._map.getZoom(),o=i.length-1;o>=0;o--)t=i[o],e=this._getLayer(t.layerId).layer,t.disabled=void 0!==e.options.minZoom&&n<e.options.minZoom||void 0!==e.options.maxZoom&&n>e.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var t=this._section;this._preventClick=!0,ed(t,"click",eb),this.expand();var e=this;setTimeout(function(){e_(t,"click",eb),e._preventClick=!1})}}),eZ=eE.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(t){var e="leaflet-control-zoom",i=t0("div",e+" leaflet-bar"),n=this.options;return this._zoomInButton=this._createButton(n.zoomInText,n.zoomInTitle,e+"-in",i,this._zoomIn),this._zoomOutButton=this._createButton(n.zoomOutText,n.zoomOutTitle,e+"-out",i,this._zoomOut),this._updateDisabled(),t.on("zoomend zoomlevelschange",this._updateDisabled,this),i},onRemove:function(t){t.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(t){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(t.shiftKey?3:1))},_zoomOut:function(t){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(t.shiftKey?3:1))},_createButton:function(t,e,i,n,o){var s=t0("a",i,n);return s.innerHTML=t,s.href="#",s.title=e,s.setAttribute("role","button"),s.setAttribute("aria-label",e),ew(s),ed(s,"click",eP),ed(s,"click",o,this),ed(s,"click",this._refocusOnMap,this),s},_updateDisabled:function(){var t=this._map,e="leaflet-disabled";t7(this._zoomInButton,e),t7(this._zoomOutButton,e),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||t._zoom===t.getMinZoom())&&(t8(this._zoomOutButton,e),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||t._zoom===t.getMaxZoom())&&(t8(this._zoomInButton,e),this._zoomInButton.setAttribute("aria-disabled","true"))}});eS.mergeOptions({zoomControl:!0}),eS.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new eZ,this.addControl(this.zoomControl))});var eN=eE.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(t){var e="leaflet-control-scale",i=t0("div",e),n=this.options;return this._addScales(n,e+"-line",i),t.on(n.updateWhenIdle?"moveend":"move",this._update,this),t.whenReady(this._update,this),i},onRemove:function(t){t.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(t,e,i){t.metric&&(this._mScale=t0("div",e,i)),t.imperial&&(this._iScale=t0("div",e,i))},_update:function(){var t=this._map,e=t.getSize().y/2,i=t.distance(t.containerPointToLatLng([0,e]),t.containerPointToLatLng([this.options.maxWidth,e]));this._updateScales(i)},_updateScales:function(t){this.options.metric&&t&&this._updateMetric(t),this.options.imperial&&t&&this._updateImperial(t)},_updateMetric:function(t){var e=this._getRoundNum(t);this._updateScale(this._mScale,e<1e3?e+" m":e/1e3+" km",e/t)},_updateImperial:function(t){var e,i,n,o=3.2808399*t;o>5280?(e=o/5280,i=this._getRoundNum(e),this._updateScale(this._iScale,i+" mi",i/e)):(n=this._getRoundNum(o),this._updateScale(this._iScale,n+" ft",n/o))},_updateScale:function(t,e,i){t.style.width=Math.round(this.options.maxWidth*i)+"px",t.innerHTML=e},_getRoundNum:function(t){var e=Math.pow(10,(Math.floor(t)+"").length-1),i=t/e;return e*(i=i>=10?10:i>=5?5:i>=3?3:i>=2?2:1)}}),eO=eE.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(tO.inlineSvg?'<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg> ':"")+"Leaflet</a>"},initialize:function(t){b(this,t),this._attributions={}},onAdd:function(t){for(var e in t.attributionControl=this,this._container=t0("div","leaflet-control-attribution"),ew(this._container),t._layers)t._layers[e].getAttribution&&this.addAttribution(t._layers[e].getAttribution());return this._update(),t.on("layeradd",this._addAttribution,this),this._container},onRemove:function(t){t.off("layeradd",this._addAttribution,this)},_addAttribution:function(t){t.layer.getAttribution&&(this.addAttribution(t.layer.getAttribution()),t.layer.once("remove",function(){this.removeAttribution(t.layer.getAttribution())},this))},setPrefix:function(t){return this.options.prefix=t,this._update(),this},addAttribution:function(t){return t&&(this._attributions[t]||(this._attributions[t]=0),this._attributions[t]++,this._update()),this},removeAttribution:function(t){return t&&this._attributions[t]&&(this._attributions[t]--,this._update()),this},_update:function(){if(this._map){var t=[];for(var e in this._attributions)this._attributions[e]&&t.push(e);var i=[];this.options.prefix&&i.push(this.options.prefix),t.length&&i.push(t.join(", ")),this._container.innerHTML=i.join(' <span aria-hidden="true">|</span> ')}}});eS.mergeOptions({attributionControl:!0}),eS.addInitHook(function(){this.options.attributionControl&&new eO().addTo(this)}),eE.Layers=ej,eE.Zoom=eZ,eE.Scale=eN,eE.Attribution=eO,eA.layers=function(t,e,i){return new ej(t,e,i)},eA.zoom=function(t){return new eZ(t)},eA.scale=function(t){return new eN(t)},eA.attribution=function(t){return new eO(t)};var eB=B.extend({initialize:function(t){this._map=t},enable:function(){return this._enabled||(this._enabled=!0,this.addHooks()),this},disable:function(){return this._enabled&&(this._enabled=!1,this.removeHooks()),this},enabled:function(){return!!this._enabled}});eB.addTo=function(t,e){return t.addHandler(e,this),this};var eI=tO.touch?"touchstart mousedown":"mousedown",eD=D.extend({options:{clickTolerance:3},initialize:function(t,e,i,n){b(this,n),this._element=t,this._dragStartTarget=e||t,this._preventOutline=i},enable:function(){this._enabled||(ed(this._dragStartTarget,eI,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(eD._dragging===this&&this.finishDrag(!0),e_(this._dragStartTarget,eI,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(t){if(!(!this._enabled||(this._moved=!1,t4(this._element,"leaflet-zoom-anim")))){if(t.touches&&1!==t.touches.length){eD._dragging===this&&this.finishDrag();return}if(!eD._dragging&&!t.shiftKey&&(1===t.which||1===t.button||t.touches)&&(eD._dragging=this,this._preventOutline&&eh(this._element),er(),i(),!this._moving)){this.fire("down");var e=t.touches?t.touches[0]:t,n=ec(this._element);this._startPoint=new R(e.clientX,e.clientY),this._startPos=eo(this._element),this._parentScale=eu(n);var o="mousedown"===t.type;ed(document,o?"mousemove":"touchmove",this._onMove,this),ed(document,o?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(t){if(this._enabled){if(t.touches&&t.touches.length>1){this._moved=!0;return}var e=t.touches&&1===t.touches.length?t.touches[0]:t,i=new R(e.clientX,e.clientY)._subtract(this._startPoint);(i.x||i.y)&&(Math.abs(i.x)+Math.abs(i.y)<this.options.clickTolerance||(i.x/=this._parentScale.x,i.y/=this._parentScale.y,eb(t),this._moved||(this.fire("dragstart"),this._moved=!0,t8(document.body,"leaflet-dragging"),this._lastTarget=t.target||t.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),t8(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(i),this._moving=!0,this._lastEvent=t,this._updatePosition()))}},_updatePosition:function(){var t={originalEvent:this._lastEvent};this.fire("predrag",t),en(this._element,this._newPos),this.fire("drag",t)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(t){t7(document.body,"leaflet-dragging"),this._lastTarget&&(t7(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),e_(document,"mousemove touchmove",this._onMove,this),e_(document,"mouseup touchend touchcancel",this._onUp,this),ea(),n();var e=this._moved&&this._moving;this._moving=!1,eD._dragging=!1,e&&this.fire("dragend",{noInertia:t,distance:this._newPos.distanceTo(this._startPos)})}});function eR(t,e,i){var n,o,s,r,a,h,l,c,u,d=[1,4,2,8];for(o=0,l=t.length;o<l;o++)t[o]._code=eG(t[o],e);for(r=0;r<4;r++){for(o=0,c=d[r],n=[],s=(l=t.length)-1;o<l;s=o++)a=t[o],h=t[s],a._code&c?h._code&c||((u=eV(h,a,c,e,i))._code=eG(u,e),n.push(u)):(h._code&c&&((u=eV(h,a,c,e,i))._code=eG(u,e),n.push(u)),n.push(a));t=n}return t}function eF(t,e){if(!t||0===t.length)throw Error("latlngs not passed");eK(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var i,n,o,s,r,a,h,l,c,u=$([0,0]),d=V(t);d.getNorthWest().distanceTo(d.getSouthWest())*d.getNorthEast().distanceTo(d.getNorthWest())<1700&&(u=eH(t));var p=t.length,_=[];for(i=0;i<p;i++){var m=$(t[i]);_.push(e.project($([m.lat-u.lat,m.lng-u.lng])))}for(i=0,a=h=l=0,n=p-1;i<p;n=i++)o=_[i],s=_[n],r=o.y*s.x-s.y*o.x,h+=(o.x+s.x)*r,l+=(o.y+s.y)*r,a+=3*r;c=0===a?_[0]:[h/a,l/a];var f=e.unproject(H(c));return $([f.lat+u.lat,f.lng+u.lng])}function eH(t){for(var e=0,i=0,n=0,o=0;o<t.length;o++){var s=$(t[o]);e+=s.lat,i+=s.lng,n++}return $([e/n,i/n])}function eW(t,e){if(!e||!t.length)return t.slice();var i=e*e;return t=function(t,e){var i=t.length,n=new("undefined"!=typeof Uint8Array?Uint8Array:Array)(i);n[0]=n[i-1]=1,function t(e,i,n,o,s){var r,a,h,l=0;for(a=o+1;a<=s-1;a++)(h=e$(e[a],e[o],e[s],!0))>l&&(r=a,l=h);l>n&&(i[r]=1,t(e,i,n,o,r),t(e,i,n,r,s))}(t,n,e,0,i-1);var o,s=[];for(o=0;o<i;o++)n[o]&&s.push(t[o]);return s}(t=function(t,e){for(var i=[t[0]],n=1,o=0,s=t.length;n<s;n++)(function(t,e){var i=e.x-t.x,n=e.y-t.y;return i*i+n*n})(t[n],t[o])>e&&(i.push(t[n]),o=n);return o<s-1&&i.push(t[s-1]),i}(t,i),i)}function eU(t,e,i){return Math.sqrt(e$(t,e,i,!0))}function eq(t,e,i,n,o){var s,r,h,l=n?a:eG(t,i),c=eG(e,i);for(a=c;;){if(!(l|c))return[t,e];if(l&c)return!1;h=eG(r=eV(t,e,s=l||c,i,o),i),s===l?(t=r,l=h):(e=r,c=h)}}function eV(t,e,i,n,o){var s,r,a=e.x-t.x,h=e.y-t.y,l=n.min,c=n.max;return 8&i?(s=t.x+a*(c.y-t.y)/h,r=c.y):4&i?(s=t.x+a*(l.y-t.y)/h,r=l.y):2&i?(s=c.x,r=t.y+h*(c.x-t.x)/a):1&i&&(s=l.x,r=t.y+h*(l.x-t.x)/a),new R(s,r,o)}function eG(t,e){var i=0;return t.x<e.min.x?i|=1:t.x>e.max.x&&(i|=2),t.y<e.min.y?i|=4:t.y>e.max.y&&(i|=8),i}function e$(t,e,i,n){var o,s=e.x,r=e.y,a=i.x-s,h=i.y-r,l=a*a+h*h;return l>0&&((o=((t.x-s)*a+(t.y-r)*h)/l)>1?(s=i.x,r=i.y):o>0&&(s+=a*o,r+=h*o)),a=t.x-s,h=t.y-r,n?a*a+h*h:new R(s,r)}function eK(t){return!M(t[0])||"object"!=typeof t[0][0]&&void 0!==t[0][0]}function eY(t){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),eK(t)}function eX(t,e){if(!t||0===t.length)throw Error("latlngs not passed");eK(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var i,n,o,s,r,a,h,l,c=$([0,0]),u=V(t);u.getNorthWest().distanceTo(u.getSouthWest())*u.getNorthEast().distanceTo(u.getNorthWest())<1700&&(c=eH(t));var d=t.length,p=[];for(i=0;i<d;i++){var _=$(t[i]);p.push(e.project($([_.lat-c.lat,_.lng-c.lng])))}for(i=0,n=0;i<d-1;i++)n+=p[i].distanceTo(p[i+1])/2;if(0===n)l=p[0];else for(i=0,s=0;i<d-1;i++)if(r=p[i],a=p[i+1],(s+=o=r.distanceTo(a))>n){h=(s-n)/o,l=[a.x-h*(a.x-r.x),a.y-h*(a.y-r.y)];break}var m=e.unproject(H(l));return $([m.lat+c.lat,m.lng+c.lng])}var eJ={project:function(t){return new R(t.lng,t.lat)},unproject:function(t){return new G(t.y,t.x)},bounds:new W([-180,-90],[180,90])},eQ={R:6378137,R_MINOR:6356752.314245179,bounds:new W([-20037508.34279,-15496570.73972],[20037508.34279,18764656.23138]),project:function(t){var e=Math.PI/180,i=this.R,n=t.lat*e,o=this.R_MINOR/i,s=Math.sqrt(1-o*o),r=s*Math.sin(n);return n=-i*Math.log(Math.max(Math.tan(Math.PI/4-n/2)/Math.pow((1-r)/(1+r),s/2),1e-10)),new R(t.lng*e*i,n)},unproject:function(t){for(var e,i=180/Math.PI,n=this.R,o=this.R_MINOR/n,s=Math.sqrt(1-o*o),r=Math.exp(-t.y/n),a=Math.PI/2-2*Math.atan(r),h=0,l=.1;h<15&&Math.abs(l)>1e-7;h++)l=Math.PI/2-2*Math.atan(r*(e=Math.pow((1-(e=s*Math.sin(a)))/(1+e),s/2)))-a,a+=l;return new G(a*i,t.x*i/n)}},e0=e({},Y,{code:"EPSG:3395",projection:eQ,transformation:Q(u=.5/(Math.PI*eQ.R),.5,-u,.5)}),e1=e({},Y,{code:"EPSG:4326",projection:eJ,transformation:Q(1/180,1,-1/180,.5)}),e2=e({},K,{projection:eJ,transformation:Q(1,0,-1,0),scale:function(t){return Math.pow(2,t)},zoom:function(t){return Math.log(t)/Math.LN2},distance:function(t,e){var i=e.lng-t.lng,n=e.lat-t.lat;return Math.sqrt(i*i+n*n)},infinite:!0});K.Earth=Y,K.EPSG3395=e0,K.EPSG3857=tt,K.EPSG900913=te,K.EPSG4326=e1,K.Simple=e2;var e3=D.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(t){return t.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(t){return t&&t.removeLayer(this),this},getPane:function(t){return this._map.getPane(t?this.options[t]||t:this.options.pane)},addInteractiveTarget:function(t){return this._map._targets[m(t)]=this,this},removeInteractiveTarget:function(t){return delete this._map._targets[m(t)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(t){var e=t.target;if(e.hasLayer(this)){if(this._map=e,this._zoomAnimated=e._zoomAnimated,this.getEvents){var i=this.getEvents();e.on(i,this),this.once("remove",function(){e.off(i,this)},this)}this.onAdd(e),this.fire("add"),e.fire("layeradd",{layer:this})}}});eS.include({addLayer:function(t){if(!t._layerAdd)throw Error("The provided object is not a Layer.");var e=m(t);return this._layers[e]||(this._layers[e]=t,t._mapToAdd=this,t.beforeAdd&&t.beforeAdd(this),this.whenReady(t._layerAdd,t)),this},removeLayer:function(t){var e=m(t);return this._layers[e]&&(this._loaded&&t.onRemove(this),delete this._layers[e],this._loaded&&(this.fire("layerremove",{layer:t}),t.fire("remove")),t._map=t._mapToAdd=null),this},hasLayer:function(t){return m(t)in this._layers},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},_addLayers:function(t){t=t?M(t)?t:[t]:[];for(var e=0,i=t.length;e<i;e++)this.addLayer(t[e])},_addZoomLimit:function(t){isNaN(t.options.maxZoom)&&isNaN(t.options.minZoom)||(this._zoomBoundLayers[m(t)]=t,this._updateZoomLevels())},_removeZoomLimit:function(t){var e=m(t);this._zoomBoundLayers[e]&&(delete this._zoomBoundLayers[e],this._updateZoomLevels())},_updateZoomLevels:function(){var t=1/0,e=-1/0,i=this._getZoomSpan();for(var n in this._zoomBoundLayers){var o=this._zoomBoundLayers[n].options;t=void 0===o.minZoom?t:Math.min(t,o.minZoom),e=void 0===o.maxZoom?e:Math.max(e,o.maxZoom)}this._layersMaxZoom=e===-1/0?void 0:e,this._layersMinZoom=t===1/0?void 0:t,i!==this._getZoomSpan()&&this.fire("zoomlevelschange"),void 0===this.options.maxZoom&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),void 0===this.options.minZoom&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var e5=e3.extend({initialize:function(t,e){var i,n;if(b(this,e),this._layers={},t)for(i=0,n=t.length;i<n;i++)this.addLayer(t[i])},addLayer:function(t){var e=this.getLayerId(t);return this._layers[e]=t,this._map&&this._map.addLayer(t),this},removeLayer:function(t){var e=t in this._layers?t:this.getLayerId(t);return this._map&&this._layers[e]&&this._map.removeLayer(this._layers[e]),delete this._layers[e],this},hasLayer:function(t){return("number"==typeof t?t:this.getLayerId(t))in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(t){var e,i,n=Array.prototype.slice.call(arguments,1);for(e in this._layers)(i=this._layers[e])[t]&&i[t].apply(i,n);return this},onAdd:function(t){this.eachLayer(t.addLayer,t)},onRemove:function(t){this.eachLayer(t.removeLayer,t)},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},getLayer:function(t){return this._layers[t]},getLayers:function(){var t=[];return this.eachLayer(t.push,t),t},setZIndex:function(t){return this.invoke("setZIndex",t)},getLayerId:function(t){return m(t)}}),e4=e5.extend({addLayer:function(t){return this.hasLayer(t)?this:(t.addEventParent(this),e5.prototype.addLayer.call(this,t),this.fire("layeradd",{layer:t}))},removeLayer:function(t){return this.hasLayer(t)?(t in this._layers&&(t=this._layers[t]),t.removeEventParent(this),e5.prototype.removeLayer.call(this,t),this.fire("layerremove",{layer:t})):this},setStyle:function(t){return this.invoke("setStyle",t)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var t=new q;for(var e in this._layers){var i=this._layers[e];t.extend(i.getBounds?i.getBounds():i.getLatLng())}return t}}),e8=B.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(t){b(this,t)},createIcon:function(t){return this._createIcon("icon",t)},createShadow:function(t){return this._createIcon("shadow",t)},_createIcon:function(t,e){var i=this._getIconUrl(t);if(!i){if("icon"===t)throw Error("iconUrl not set in Icon options (see the docs).");return null}var n=this._createImg(i,e&&"IMG"===e.tagName?e:null);return this._setIconStyles(n,t),(this.options.crossOrigin||""===this.options.crossOrigin)&&(n.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),n},_setIconStyles:function(t,e){var i=this.options,n=i[e+"Size"];"number"==typeof n&&(n=[n,n]);var o=H(n),s=H("shadow"===e&&i.shadowAnchor||i.iconAnchor||o&&o.divideBy(2,!0));t.className="leaflet-marker-"+e+" "+(i.className||""),s&&(t.style.marginLeft=-s.x+"px",t.style.marginTop=-s.y+"px"),o&&(t.style.width=o.x+"px",t.style.height=o.y+"px")},_createImg:function(t,e){return(e=e||document.createElement("img")).src=t,e},_getIconUrl:function(t){return tO.retina&&this.options[t+"RetinaUrl"]||this.options[t+"Url"]}}),e7=e8.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(t){return"string"!=typeof e7.imagePath&&(e7.imagePath=this._detectIconPath()),(this.options.imagePath||e7.imagePath)+e8.prototype._getIconUrl.call(this,t)},_stripUrl:function(t){var e=function(t,e,i){var n=e.exec(t);return n&&n[i]};return(t=e(t,/^url\((['"])?(.+)\1\)$/,2))&&e(t,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var t=t0("div","leaflet-default-icon-path",document.body),e=tQ(t,"background-image")||tQ(t,"backgroundImage");if(document.body.removeChild(t),e=this._stripUrl(e))return e;var i=document.querySelector('link[href$="leaflet.css"]');return i?i.href.substring(0,i.href.length-11-1):""}}),e9=eB.extend({initialize:function(t){this._marker=t},addHooks:function(){var t=this._marker._icon;this._draggable||(this._draggable=new eD(t,t,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),t8(t,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&t7(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(t){var e=this._marker,i=e._map,n=this._marker.options.autoPanSpeed,o=this._marker.options.autoPanPadding,s=eo(e._icon),r=i.getPixelBounds(),a=i.getPixelOrigin(),h=U(r.min._subtract(a).add(o),r.max._subtract(a).subtract(o));if(!h.contains(s)){var l=H((Math.max(h.max.x,s.x)-h.max.x)/(r.max.x-h.max.x)-(Math.min(h.min.x,s.x)-h.min.x)/(r.min.x-h.min.x),(Math.max(h.max.y,s.y)-h.max.y)/(r.max.y-h.max.y)-(Math.min(h.min.y,s.y)-h.min.y)/(r.min.y-h.min.y)).multiplyBy(n);i.panBy(l,{animate:!1}),this._draggable._newPos._add(l),this._draggable._startPos._add(l),en(e._icon,this._draggable._newPos),this._onDrag(t),this._panRequest=N(this._adjustPan.bind(this,t))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(t){this._marker.options.autoPan&&(O(this._panRequest),this._panRequest=N(this._adjustPan.bind(this,t)))},_onDrag:function(t){var e=this._marker,i=e._shadow,n=eo(e._icon),o=e._map.layerPointToLatLng(n);i&&en(i,n),e._latlng=o,t.latlng=o,t.oldLatLng=this._oldLatLng,e.fire("move",t).fire("drag",t)},_onDragEnd:function(t){O(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",t)}}),e6=e3.extend({options:{icon:new e7,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(t,e){b(this,e),this._latlng=$(t)},onAdd:function(t){this._zoomAnimated=this._zoomAnimated&&t.options.markerZoomAnimation,this._zoomAnimated&&t.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(t){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&t.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(t){var e=this._latlng;return this._latlng=$(t),this.update(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},setZIndexOffset:function(t){return this.options.zIndexOffset=t,this.update()},getIcon:function(){return this.options.icon},setIcon:function(t){return this.options.icon=t,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var t=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(t)}return this},_initIcon:function(){var t=this.options,e="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),i=t.icon.createIcon(this._icon),n=!1;i!==this._icon&&(this._icon&&this._removeIcon(),n=!0,t.title&&(i.title=t.title),"IMG"===i.tagName&&(i.alt=t.alt||"")),t8(i,e),t.keyboard&&(i.tabIndex="0",i.setAttribute("role","button")),this._icon=i,t.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&ed(i,"focus",this._panOnFocus,this);var o=t.icon.createShadow(this._shadow),s=!1;o!==this._shadow&&(this._removeShadow(),s=!0),o&&(t8(o,e),o.alt=""),this._shadow=o,t.opacity<1&&this._updateOpacity(),n&&this.getPane().appendChild(this._icon),this._initInteraction(),o&&s&&this.getPane(t.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&e_(this._icon,"focus",this._panOnFocus,this),t1(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&t1(this._shadow),this._shadow=null},_setPos:function(t){this._icon&&en(this._icon,t),this._shadow&&en(this._shadow,t),this._zIndex=t.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(t){this._icon&&(this._icon.style.zIndex=this._zIndex+t)},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center).round();this._setPos(e)},_initInteraction:function(){if(this.options.interactive&&(t8(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),e9)){var t=this.options.draggable;this.dragging&&(t=this.dragging.enabled(),this.dragging.disable()),this.dragging=new e9(this),t&&this.dragging.enable()}},setOpacity:function(t){return this.options.opacity=t,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var t=this.options.opacity;this._icon&&et(this._icon,t),this._shadow&&et(this._shadow,t)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var t=this._map;if(t){var e=this.options.icon.options,i=e.iconSize?H(e.iconSize):H(0,0),n=e.iconAnchor?H(e.iconAnchor):H(0,0);t.panInside(this._latlng,{paddingTopLeft:n,paddingBottomRight:i.subtract(n)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}}),it=e3.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(t){this._renderer=t.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(t){return b(this,t),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&t&&Object.prototype.hasOwnProperty.call(t,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),ie=it.extend({options:{fill:!0,radius:10},initialize:function(t,e){b(this,e),this._latlng=$(t),this._radius=this.options.radius},setLatLng:function(t){var e=this._latlng;return this._latlng=$(t),this.redraw(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(t){return this.options.radius=this._radius=t,this.redraw()},getRadius:function(){return this._radius},setStyle:function(t){var e=t&&t.radius||this._radius;return it.prototype.setStyle.call(this,t),this.setRadius(e),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var t=this._radius,e=this._radiusY||t,i=this._clickTolerance(),n=[t+i,e+i];this._pxBounds=new W(this._point.subtract(n),this._point.add(n))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(t){return t.distanceTo(this._point)<=this._radius+this._clickTolerance()}}),ii=ie.extend({initialize:function(t,i,n){if("number"==typeof i&&(i=e({},n,{radius:i})),b(this,i),this._latlng=$(t),isNaN(this.options.radius))throw Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(t){return this._mRadius=t,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var t=[this._radius,this._radiusY||this._radius];return new q(this._map.layerPointToLatLng(this._point.subtract(t)),this._map.layerPointToLatLng(this._point.add(t)))},setStyle:it.prototype.setStyle,_project:function(){var t=this._latlng.lng,e=this._latlng.lat,i=this._map,n=i.options.crs;if(n.distance===Y.distance){var o=Math.PI/180,s=this._mRadius/Y.R/o,r=i.project([e+s,t]),a=i.project([e-s,t]),h=r.add(a).divideBy(2),l=i.unproject(h).lat,c=Math.acos((Math.cos(s*o)-Math.sin(e*o)*Math.sin(l*o))/(Math.cos(e*o)*Math.cos(l*o)))/o;(isNaN(c)||0===c)&&(c=s/Math.cos(Math.PI/180*e)),this._point=h.subtract(i.getPixelOrigin()),this._radius=isNaN(c)?0:h.x-i.project([l,t-c]).x,this._radiusY=h.y-r.y}else{var u=n.unproject(n.project(this._latlng).subtract([this._mRadius,0]));this._point=i.latLngToLayerPoint(this._latlng),this._radius=this._point.x-i.latLngToLayerPoint(u).x}this._updateBounds()}}),io=it.extend({options:{smoothFactor:1,noClip:!1},initialize:function(t,e){b(this,e),this._setLatLngs(t)},getLatLngs:function(){return this._latlngs},setLatLngs:function(t){return this._setLatLngs(t),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(t){for(var e,i,n=1/0,o=null,s=e$,r=0,a=this._parts.length;r<a;r++)for(var h=this._parts[r],l=1,c=h.length;l<c;l++){var u=s(t,e=h[l-1],i=h[l],!0);u<n&&(n=u,o=s(t,e,i))}return o&&(o.distance=Math.sqrt(n)),o},getCenter:function(){if(!this._map)throw Error("Must add layer to map before using getCenter()");return eX(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(t,e){return e=e||this._defaultShape(),t=$(t),e.push(t),this._bounds.extend(t),this.redraw()},_setLatLngs:function(t){this._bounds=new q,this._latlngs=this._convertLatLngs(t)},_defaultShape:function(){return eK(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(t){for(var e=[],i=eK(t),n=0,o=t.length;n<o;n++)i?(e[n]=$(t[n]),this._bounds.extend(e[n])):e[n]=this._convertLatLngs(t[n]);return e},_project:function(){var t=new W;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,t),this._bounds.isValid()&&t.isValid()&&(this._rawPxBounds=t,this._updateBounds())},_updateBounds:function(){var t=this._clickTolerance(),e=new R(t,t);this._rawPxBounds&&(this._pxBounds=new W([this._rawPxBounds.min.subtract(e),this._rawPxBounds.max.add(e)]))},_projectLatlngs:function(t,e,i){var n,o,s=t[0]instanceof G,r=t.length;if(s){for(n=0,o=[];n<r;n++)o[n]=this._map.latLngToLayerPoint(t[n]),i.extend(o[n]);e.push(o)}else for(n=0;n<r;n++)this._projectLatlngs(t[n],e,i)},_clipPoints:function(){var t=this._renderer._bounds;if(this._parts=[],this._pxBounds&&this._pxBounds.intersects(t)){if(this.options.noClip){this._parts=this._rings;return}var e,i,n,o,s,r,a,h=this._parts;for(e=0,n=0,o=this._rings.length;e<o;e++)for(i=0,s=(a=this._rings[e]).length;i<s-1;i++)(r=eq(a[i],a[i+1],t,i,!0))&&(h[n]=h[n]||[],h[n].push(r[0]),(r[1]!==a[i+1]||i===s-2)&&(h[n].push(r[1]),n++))}},_simplifyPoints:function(){for(var t=this._parts,e=this.options.smoothFactor,i=0,n=t.length;i<n;i++)t[i]=eW(t[i],e)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(t,e){var i,n,o,s,r,a,h=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(i=0,s=this._parts.length;i<s;i++)for(n=0,o=(r=(a=this._parts[i]).length)-1;n<r;o=n++)if((e||0!==n)&&eU(t,a[o],a[n])<=h)return!0;return!1}});io._flat=eY;var is=io.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw Error("Must add layer to map before using getCenter()");return eF(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(t){var e=io.prototype._convertLatLngs.call(this,t),i=e.length;return i>=2&&e[0]instanceof G&&e[0].equals(e[i-1])&&e.pop(),e},_setLatLngs:function(t){io.prototype._setLatLngs.call(this,t),eK(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return eK(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var t=this._renderer._bounds,e=this.options.weight,i=new R(e,e);if(t=new W(t.min.subtract(i),t.max.add(i)),this._parts=[],this._pxBounds&&this._pxBounds.intersects(t)){if(this.options.noClip){this._parts=this._rings;return}for(var n,o=0,s=this._rings.length;o<s;o++)(n=eR(this._rings[o],t,!0)).length&&this._parts.push(n)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(t){var e,i,n,o,s,r,a,h,l=!1;if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(o=0,a=this._parts.length;o<a;o++)for(s=0,r=(h=(e=this._parts[o]).length)-1;s<h;r=s++)i=e[s],n=e[r],i.y>t.y!=n.y>t.y&&t.x<(n.x-i.x)*(t.y-i.y)/(n.y-i.y)+i.x&&(l=!l);return l||io.prototype._containsPoint.call(this,t,!0)}}),ir=e4.extend({initialize:function(t,e){b(this,e),this._layers={},t&&this.addData(t)},addData:function(t){var e,i,n,o=M(t)?t:t.features;if(o){for(e=0,i=o.length;e<i;e++)((n=o[e]).geometries||n.geometry||n.features||n.coordinates)&&this.addData(n);return this}var s=this.options;if(s.filter&&!s.filter(t))return this;var r=ia(t,s);return r?(r.feature=i_(t),r.defaultOptions=r.options,this.resetStyle(r),s.onEachFeature&&s.onEachFeature(t,r),this.addLayer(r)):this},resetStyle:function(t){return void 0===t?this.eachLayer(this.resetStyle,this):(t.options=e({},t.defaultOptions),this._setLayerStyle(t,this.options.style),this)},setStyle:function(t){return this.eachLayer(function(e){this._setLayerStyle(e,t)},this)},_setLayerStyle:function(t,e){t.setStyle&&("function"==typeof e&&(e=e(t.feature)),t.setStyle(e))}});function ia(t,e){var i,n,o,s,r="Feature"===t.type?t.geometry:t,a=r?r.coordinates:null,h=[],l=e&&e.pointToLayer,c=e&&e.coordsToLatLng||il;if(!a&&!r)return null;switch(r.type){case"Point":return ih(l,t,i=c(a),e);case"MultiPoint":for(o=0,s=a.length;o<s;o++)i=c(a[o]),h.push(ih(l,t,i,e));return new e4(h);case"LineString":case"MultiLineString":return new io(ic(a,+("LineString"!==r.type),c),e);case"Polygon":case"MultiPolygon":return new is(ic(a,"Polygon"===r.type?1:2,c),e);case"GeometryCollection":for(o=0,s=r.geometries.length;o<s;o++){var u=ia({geometry:r.geometries[o],type:"Feature",properties:t.properties},e);u&&h.push(u)}return new e4(h);case"FeatureCollection":for(o=0,s=r.features.length;o<s;o++){var d=ia(r.features[o],e);d&&h.push(d)}return new e4(h);default:throw Error("Invalid GeoJSON object.")}}function ih(t,e,i,n){return t?t(e,i):new e6(i,n&&n.markersInheritOptions&&n)}function il(t){return new G(t[1],t[0],t[2])}function ic(t,e,i){for(var n=[],o=0,s=t.length;o<s;o++)n.push(e?ic(t[o],e-1,i):(i||il)(t[o]));return n}function iu(t,e){return void 0!==(t=$(t)).alt?[y(t.lng,e),y(t.lat,e),y(t.alt,e)]:[y(t.lng,e),y(t.lat,e)]}function id(t,e,i,n){for(var o=[],s=0,r=t.length;s<r;s++)o.push(e?id(t[s],eK(t[s])?0:e-1,i,n):iu(t[s],n));return!e&&i&&o.length>0&&o.push(o[0].slice()),o}function ip(t,i){return t.feature?e({},t.feature,{geometry:i}):i_(i)}function i_(t){return"Feature"===t.type||"FeatureCollection"===t.type?t:{type:"Feature",properties:{},geometry:t}}var im={toGeoJSON:function(t){return ip(this,{type:"Point",coordinates:iu(this.getLatLng(),t)})}};function ig(t,e){return new ir(t,e)}e6.include(im),ii.include(im),ie.include(im),io.include({toGeoJSON:function(t){var e=!eK(this._latlngs),i=id(this._latlngs,+!!e,!1,t);return ip(this,{type:(e?"Multi":"")+"LineString",coordinates:i})}}),is.include({toGeoJSON:function(t){var e=!eK(this._latlngs),i=e&&!eK(this._latlngs[0]),n=id(this._latlngs,i?2:+!!e,!0,t);return e||(n=[n]),ip(this,{type:(i?"Multi":"")+"Polygon",coordinates:n})}}),e5.include({toMultiPoint:function(t){var e=[];return this.eachLayer(function(i){e.push(i.toGeoJSON(t).geometry.coordinates)}),ip(this,{type:"MultiPoint",coordinates:e})},toGeoJSON:function(t){var e=this.feature&&this.feature.geometry&&this.feature.geometry.type;if("MultiPoint"===e)return this.toMultiPoint(t);var i="GeometryCollection"===e,n=[];return(this.eachLayer(function(e){if(e.toGeoJSON){var o=e.toGeoJSON(t);if(i)n.push(o.geometry);else{var s=i_(o);"FeatureCollection"===s.type?n.push.apply(n,s.features):n.push(s)}}}),i)?ip(this,{geometries:n,type:"GeometryCollection"}):{type:"FeatureCollection",features:n}}});var iv=e3.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(t,e,i){this._url=t,this._bounds=V(e),b(this,i)},onAdd:function(){!this._image&&(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(t8(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){t1(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(t){return this.options.opacity=t,this._image&&this._updateOpacity(),this},setStyle:function(t){return t.opacity&&this.setOpacity(t.opacity),this},bringToFront:function(){return this._map&&t3(this._image),this},bringToBack:function(){return this._map&&t5(this._image),this},setUrl:function(t){return this._url=t,this._image&&(this._image.src=t),this},setBounds:function(t){return this._bounds=V(t),this._map&&this._reset(),this},getEvents:function(){var t={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var t="IMG"===this._url.tagName,e=this._image=t?this._url:t0("img");if(t8(e,"leaflet-image-layer"),this._zoomAnimated&&t8(e,"leaflet-zoom-animated"),this.options.className&&t8(e,this.options.className),e.onselectstart=v,e.onmousemove=v,e.onload=p(this.fire,this,"load"),e.onerror=p(this._overlayOnError,this,"error"),(this.options.crossOrigin||""===this.options.crossOrigin)&&(e.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),t){this._url=e.src;return}e.src=this._url,e.alt=this.options.alt},_animateZoom:function(t){var e=this._map.getZoomScale(t.zoom),i=this._map._latLngBoundsToNewLayerBounds(this._bounds,t.zoom,t.center).min;ei(this._image,i,e)},_reset:function(){var t=this._image,e=new W(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),i=e.getSize();en(t,e.min),t.style.width=i.x+"px",t.style.height=i.y+"px"},_updateOpacity:function(){et(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&void 0!==this.options.zIndex&&null!==this.options.zIndex&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var t=this.options.errorOverlayUrl;t&&this._url!==t&&(this._url=t,this._image.src=t)},getCenter:function(){return this._bounds.getCenter()}}),iy=iv.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var t="VIDEO"===this._url.tagName,e=this._image=t?this._url:t0("video");if(t8(e,"leaflet-image-layer"),this._zoomAnimated&&t8(e,"leaflet-zoom-animated"),this.options.className&&t8(e,this.options.className),e.onselectstart=v,e.onmousemove=v,e.onloadeddata=p(this.fire,this,"load"),t){for(var i=e.getElementsByTagName("source"),n=[],o=0;o<i.length;o++)n.push(i[o].src);this._url=i.length>0?n:[e.src];return}M(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(e.style,"objectFit")&&(e.style.objectFit="fill"),e.autoplay=!!this.options.autoplay,e.loop=!!this.options.loop,e.muted=!!this.options.muted,e.playsInline=!!this.options.playsInline;for(var s=0;s<this._url.length;s++){var r=t0("source");r.src=this._url[s],e.appendChild(r)}}}),ix=iv.extend({_initImage:function(){var t=this._image=this._url;t8(t,"leaflet-image-layer"),this._zoomAnimated&&t8(t,"leaflet-zoom-animated"),this.options.className&&t8(t,this.options.className),t.onselectstart=v,t.onmousemove=v}}),iw=e3.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(t,e){t&&(t instanceof G||M(t))?(this._latlng=$(t),b(this,e)):(b(this,t),this._source=e),this.options.content&&(this._content=this.options.content)},openOn:function(t){return t=arguments.length?t:this._source._map,t.hasLayer(this)||t.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(t){return this._map?this.close():(arguments.length?this._source=t:t=this._source,this._prepareOpen(),this.openOn(t._map)),this},onAdd:function(t){this._zoomAnimated=t._zoomAnimated,this._container||this._initLayout(),t._fadeAnimated&&et(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),t._fadeAnimated&&et(this._container,1),this.bringToFront(),this.options.interactive&&(t8(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(t){t._fadeAnimated?(et(this._container,0),this._removeTimeout=setTimeout(p(t1,void 0,this._container),200)):t1(this._container),this.options.interactive&&(t7(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(t){return this._latlng=$(t),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(t){return this._content=t,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var t={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&t3(this._container),this},bringToBack:function(){return this._map&&t5(this._container),this},_prepareOpen:function(t){var e=this._source;if(!e._map)return!1;if(e instanceof e4){e=null;var i=this._source._layers;for(var n in i)if(i[n]._map){e=i[n];break}if(!e)return!1;this._source=e}if(!t)if(e.getCenter)t=e.getCenter();else if(e.getLatLng)t=e.getLatLng();else if(e.getBounds)t=e.getBounds().getCenter();else throw Error("Unable to get source layer LatLng.");return this.setLatLng(t),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var t=this._contentNode,e="function"==typeof this._content?this._content(this._source||this):this._content;if("string"==typeof e)t.innerHTML=e;else{for(;t.hasChildNodes();)t.removeChild(t.firstChild);t.appendChild(e)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var t=this._map.latLngToLayerPoint(this._latlng),e=H(this.options.offset),i=this._getAnchor();this._zoomAnimated?en(this._container,t.add(i)):e=e.add(t).add(i);var n=this._containerBottom=-e.y,o=this._containerLeft=-Math.round(this._containerWidth/2)+e.x;this._container.style.bottom=n+"px",this._container.style.left=o+"px"}},_getAnchor:function(){return[0,0]}});eS.include({_initOverlay:function(t,e,i,n){var o=e;return o instanceof t||(o=new t(n).setContent(e)),i&&o.setLatLng(i),o}}),e3.include({_initOverlay:function(t,e,i,n){var o=i;return o instanceof t?(b(o,n),o._source=this):(o=e&&!n?e:new t(n,this)).setContent(i),o}});var ib=iw.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(t){return t=arguments.length?t:this._source._map,!t.hasLayer(this)&&t._popup&&t._popup.options.autoClose&&t.removeLayer(t._popup),t._popup=this,iw.prototype.openOn.call(this,t)},onAdd:function(t){iw.prototype.onAdd.call(this,t),t.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof it||this._source.on("preclick",ey))},onRemove:function(t){iw.prototype.onRemove.call(this,t),t.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof it||this._source.off("preclick",ey))},getEvents:function(){var t=iw.prototype.getEvents.call(this);return(void 0!==this.options.closeOnClick?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(t.preclick=this.close),this.options.keepInView&&(t.moveend=this._adjustPan),t},_initLayout:function(){var t="leaflet-popup",e=this._container=t0("div",t+" "+(this.options.className||"")+" leaflet-zoom-animated"),i=this._wrapper=t0("div",t+"-content-wrapper",e);if(this._contentNode=t0("div",t+"-content",i),ew(e),ex(this._contentNode),ed(e,"contextmenu",ey),this._tipContainer=t0("div",t+"-tip-container",e),this._tip=t0("div",t+"-tip",this._tipContainer),this.options.closeButton){var n=this._closeButton=t0("a",t+"-close-button",e);n.setAttribute("role","button"),n.setAttribute("aria-label","Close popup"),n.href="#close",n.innerHTML='<span aria-hidden="true">&#215;</span>',ed(n,"click",function(t){eb(t),this.close()},this)}},_updateLayout:function(){var t=this._contentNode,e=t.style;e.width="",e.whiteSpace="nowrap";var i=t.offsetWidth;e.width=(i=Math.max(i=Math.min(i,this.options.maxWidth),this.options.minWidth))+1+"px",e.whiteSpace="",e.height="";var n=t.offsetHeight,o=this.options.maxHeight,s="leaflet-popup-scrolled";o&&n>o?(e.height=o+"px",t8(t,s)):t7(t,s),this._containerWidth=this._container.offsetWidth},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center),i=this._getAnchor();en(this._container,e.add(i))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var t=this._map,e=parseInt(tQ(this._container,"marginBottom"),10)||0,i=this._container.offsetHeight+e,n=this._containerWidth,o=new R(this._containerLeft,-i-this._containerBottom);o._add(eo(this._container));var s=t.layerPointToContainerPoint(o),r=H(this.options.autoPanPadding),a=H(this.options.autoPanPaddingTopLeft||r),h=H(this.options.autoPanPaddingBottomRight||r),l=t.getSize(),c=0,u=0;s.x+n+h.x>l.x&&(c=s.x+n-l.x+h.x),s.x-c-a.x<0&&(c=s.x-a.x),s.y+i+h.y>l.y&&(u=s.y+i-l.y+h.y),s.y-u-a.y<0&&(u=s.y-a.y),(c||u)&&(this.options.keepInView&&(this._autopanning=!0),t.fire("autopanstart").panBy([c,u]))}},_getAnchor:function(){return H(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}});eS.mergeOptions({closePopupOnClick:!0}),eS.include({openPopup:function(t,e,i){return this._initOverlay(ib,t,e,i).openOn(this),this},closePopup:function(t){return t=arguments.length?t:this._popup,t&&t.close(),this}}),e3.include({bindPopup:function(t,e){return this._popup=this._initOverlay(ib,this._popup,t,e),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(t){return this._popup&&(this instanceof e4||(this._popup._source=this),this._popup._prepareOpen(t||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return!!this._popup&&this._popup.isOpen()},setPopupContent:function(t){return this._popup&&this._popup.setContent(t),this},getPopup:function(){return this._popup},_openPopup:function(t){if(this._popup&&this._map){eP(t);var e=t.layer||t.target;if(this._popup._source===e&&!(e instanceof it))return void(this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(t.latlng));this._popup._source=e,this.openPopup(t.latlng)}},_movePopup:function(t){this._popup.setLatLng(t.latlng)},_onKeyPress:function(t){13===t.originalEvent.keyCode&&this._openPopup(t)}});var iP=iw.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(t){iw.prototype.onAdd.call(this,t),this.setOpacity(this.options.opacity),t.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(t){iw.prototype.onRemove.call(this,t),t.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var t=iw.prototype.getEvents.call(this);return this.options.permanent||(t.preclick=this.close),t},_initLayout:function(){var t="leaflet-tooltip "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=t0("div",t),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+m(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(t){var e,i,n=this._map,o=this._container,s=n.latLngToContainerPoint(n.getCenter()),r=n.layerPointToContainerPoint(t),a=this.options.direction,h=o.offsetWidth,l=o.offsetHeight,c=H(this.options.offset),u=this._getAnchor();"top"===a?(e=h/2,i=l):"bottom"===a?(e=h/2,i=0):("center"===a?e=h/2:"right"===a?e=0:"left"===a?e=h:r.x<s.x?(a="right",e=0):(a="left",e=h+(c.x+u.x)*2),i=l/2),t=t.subtract(H(e,i,!0)).add(c).add(u),t7(o,"leaflet-tooltip-right"),t7(o,"leaflet-tooltip-left"),t7(o,"leaflet-tooltip-top"),t7(o,"leaflet-tooltip-bottom"),t8(o,"leaflet-tooltip-"+a),en(o,t)},_updatePosition:function(){var t=this._map.latLngToLayerPoint(this._latlng);this._setPosition(t)},setOpacity:function(t){this.options.opacity=t,this._container&&et(this._container,t)},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center);this._setPosition(e)},_getAnchor:function(){return H(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}});eS.include({openTooltip:function(t,e,i){return this._initOverlay(iP,t,e,i).openOn(this),this},closeTooltip:function(t){return t.close(),this}}),e3.include({bindTooltip:function(t,e){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(iP,this._tooltip,t,e),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(t){if(t||!this._tooltipHandlersAdded){var e={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?e.add=this._openTooltip:(e.mouseover=this._openTooltip,e.mouseout=this.closeTooltip,e.click=this._openTooltip,this._map?this._addFocusListeners():e.add=this._addFocusListeners),this._tooltip.options.sticky&&(e.mousemove=this._moveTooltip),this[t?"off":"on"](e),this._tooltipHandlersAdded=!t}},openTooltip:function(t){return this._tooltip&&(this instanceof e4||(this._tooltip._source=this),this._tooltip._prepareOpen(t)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(t){return this._tooltip&&this._tooltip.setContent(t),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(t){var e="function"==typeof t.getElement&&t.getElement();e&&(ed(e,"focus",function(){this._tooltip._source=t,this.openTooltip()},this),ed(e,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(t){var e="function"==typeof t.getElement&&t.getElement();e&&e.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(t){if(this._tooltip&&this._map){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var e=this;this._map.once("moveend",function(){e._openOnceFlag=!1,e._openTooltip(t)});return}this._tooltip._source=t.layer||t.target,this.openTooltip(this._tooltip.options.sticky?t.latlng:void 0)}},_moveTooltip:function(t){var e,i,n=t.latlng;this._tooltip.options.sticky&&t.originalEvent&&(e=this._map.mouseEventToContainerPoint(t.originalEvent),i=this._map.containerPointToLayerPoint(e),n=this._map.layerPointToLatLng(i)),this._tooltip.setLatLng(n)}});var iL=e8.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(t){var e=t&&"DIV"===t.tagName?t:document.createElement("div"),i=this.options;if(i.html instanceof Element?(t2(e),e.appendChild(i.html)):e.innerHTML=!1!==i.html?i.html:"",i.bgPos){var n=H(i.bgPos);e.style.backgroundPosition=-n.x+"px "+-n.y+"px"}return this._setIconStyles(e,"icon"),e},createShadow:function(){return null}});e8.Default=e7;var iT=e3.extend({options:{tileSize:256,opacity:1,updateWhenIdle:tO.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(t){b(this,t)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(t){t._addZoomLimit(this)},onRemove:function(t){this._removeAllTiles(),t1(this._container),t._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(t3(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(t5(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(t){return this.options.opacity=t,this._updateOpacity(),this},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var t=this._clampZoom(this._map.getZoom());t!==this._tileZoom&&(this._tileZoom=t,this._updateLevels()),this._update()}return this},getEvents:function(){var t={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=f(this._onMoveEnd,this.options.updateInterval,this)),t.move=this._onMove),this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},createTile:function(){return document.createElement("div")},getTileSize:function(){var t=this.options.tileSize;return t instanceof R?t:new R(t,t)},_updateZIndex:function(){this._container&&void 0!==this.options.zIndex&&null!==this.options.zIndex&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(t){for(var e,i=this.getPane().children,n=-t(-1/0,1/0),o=0,s=i.length;o<s;o++)e=i[o].style.zIndex,i[o]!==this._container&&e&&(n=t(n,+e));isFinite(n)&&(this.options.zIndex=n+t(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!tO.ielt9){et(this._container,this.options.opacity);var t=+new Date,e=!1,i=!1;for(var n in this._tiles){var o=this._tiles[n];if(o.current&&o.loaded){var s=Math.min(1,(t-o.loaded)/200);et(o.el,s),s<1?e=!0:(o.active?i=!0:this._onOpaqueTile(o),o.active=!0)}}i&&!this._noPrune&&this._pruneTiles(),e&&(O(this._fadeFrame),this._fadeFrame=N(this._updateOpacity,this))}},_onOpaqueTile:v,_initContainer:function(){this._container||(this._container=t0("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var t=this._tileZoom,e=this.options.maxZoom;if(void 0!==t){for(var i in this._levels)i=Number(i),this._levels[i].el.children.length||i===t?(this._levels[i].el.style.zIndex=e-Math.abs(t-i),this._onUpdateLevel(i)):(t1(this._levels[i].el),this._removeTilesAtZoom(i),this._onRemoveLevel(i),delete this._levels[i]);var n=this._levels[t],o=this._map;return n||((n=this._levels[t]={}).el=t0("div","leaflet-tile-container leaflet-zoom-animated",this._container),n.el.style.zIndex=e,n.origin=o.project(o.unproject(o.getPixelOrigin()),t).round(),n.zoom=t,this._setZoomTransform(n,o.getCenter(),o.getZoom()),v(n.el.offsetWidth),this._onCreateLevel(n)),this._level=n,n}},_onUpdateLevel:v,_onRemoveLevel:v,_onCreateLevel:v,_pruneTiles:function(){if(this._map){var t,e,i=this._map.getZoom();if(i>this.options.maxZoom||i<this.options.minZoom)return void this._removeAllTiles();for(t in this._tiles)(e=this._tiles[t]).retain=e.current;for(t in this._tiles)if((e=this._tiles[t]).current&&!e.active){var n=e.coords;this._retainParent(n.x,n.y,n.z,n.z-5)||this._retainChildren(n.x,n.y,n.z,n.z+2)}for(t in this._tiles)this._tiles[t].retain||this._removeTile(t)}},_removeTilesAtZoom:function(t){for(var e in this._tiles)this._tiles[e].coords.z===t&&this._removeTile(e)},_removeAllTiles:function(){for(var t in this._tiles)this._removeTile(t)},_invalidateAll:function(){for(var t in this._levels)t1(this._levels[t].el),this._onRemoveLevel(Number(t)),delete this._levels[t];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(t,e,i,n){var o=Math.floor(t/2),s=Math.floor(e/2),r=i-1,a=new R(+o,+s);a.z=+r;var h=this._tileCoordsToKey(a),l=this._tiles[h];return l&&l.active?(l.retain=!0,!0):(l&&l.loaded&&(l.retain=!0),r>n&&this._retainParent(o,s,r,n))},_retainChildren:function(t,e,i,n){for(var o=2*t;o<2*t+2;o++)for(var s=2*e;s<2*e+2;s++){var r=new R(o,s);r.z=i+1;var a=this._tileCoordsToKey(r),h=this._tiles[a];if(h&&h.active){h.retain=!0;continue}h&&h.loaded&&(h.retain=!0),i+1<n&&this._retainChildren(o,s,i+1,n)}},_resetView:function(t){var e=t&&(t.pinch||t.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),e,e)},_animateZoom:function(t){this._setView(t.center,t.zoom,!0,t.noUpdate)},_clampZoom:function(t){var e=this.options;return void 0!==e.minNativeZoom&&t<e.minNativeZoom?e.minNativeZoom:void 0!==e.maxNativeZoom&&e.maxNativeZoom<t?e.maxNativeZoom:t},_setView:function(t,e,i,n){var o=Math.round(e);o=void 0!==this.options.maxZoom&&o>this.options.maxZoom||void 0!==this.options.minZoom&&o<this.options.minZoom?void 0:this._clampZoom(o);var s=this.options.updateWhenZooming&&o!==this._tileZoom;(!n||s)&&(this._tileZoom=o,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),void 0!==o&&this._update(t),i||this._pruneTiles(),this._noPrune=!!i),this._setZoomTransforms(t,e)},_setZoomTransforms:function(t,e){for(var i in this._levels)this._setZoomTransform(this._levels[i],t,e)},_setZoomTransform:function(t,e,i){var n=this._map.getZoomScale(i,t.zoom),o=t.origin.multiplyBy(n).subtract(this._map._getNewPixelOrigin(e,i)).round();tO.any3d?ei(t.el,o,n):en(t.el,o)},_resetGrid:function(){var t=this._map,e=t.options.crs,i=this._tileSize=this.getTileSize(),n=this._tileZoom,o=this._map.getPixelWorldBounds(this._tileZoom);o&&(this._globalTileRange=this._pxBoundsToTileRange(o)),this._wrapX=e.wrapLng&&!this.options.noWrap&&[Math.floor(t.project([0,e.wrapLng[0]],n).x/i.x),Math.ceil(t.project([0,e.wrapLng[1]],n).x/i.y)],this._wrapY=e.wrapLat&&!this.options.noWrap&&[Math.floor(t.project([e.wrapLat[0],0],n).y/i.x),Math.ceil(t.project([e.wrapLat[1],0],n).y/i.y)]},_onMoveEnd:function(){this._map&&!this._map._animatingZoom&&this._update()},_getTiledPixelBounds:function(t){var e=this._map,i=e._animatingZoom?Math.max(e._animateToZoom,e.getZoom()):e.getZoom(),n=e.getZoomScale(i,this._tileZoom),o=e.project(t,this._tileZoom).floor(),s=e.getSize().divideBy(2*n);return new W(o.subtract(s),o.add(s))},_update:function(t){var e=this._map;if(e){var i=this._clampZoom(e.getZoom());if(void 0===t&&(t=e.getCenter()),void 0!==this._tileZoom){var n=this._getTiledPixelBounds(t),o=this._pxBoundsToTileRange(n),s=o.getCenter(),r=[],a=this.options.keepBuffer,h=new W(o.getBottomLeft().subtract([a,-a]),o.getTopRight().add([a,-a]));if(!(isFinite(o.min.x)&&isFinite(o.min.y)&&isFinite(o.max.x)&&isFinite(o.max.y)))throw Error("Attempted to load an infinite number of tiles");for(var l in this._tiles){var c=this._tiles[l].coords;c.z===this._tileZoom&&h.contains(new R(c.x,c.y))||(this._tiles[l].current=!1)}if(Math.abs(i-this._tileZoom)>1)return void this._setView(t,i);for(var u=o.min.y;u<=o.max.y;u++)for(var d=o.min.x;d<=o.max.x;d++){var p=new R(d,u);if(p.z=this._tileZoom,this._isValidTile(p)){var _=this._tiles[this._tileCoordsToKey(p)];_?_.current=!0:r.push(p)}}if(r.sort(function(t,e){return t.distanceTo(s)-e.distanceTo(s)}),0!==r.length){this._loading||(this._loading=!0,this.fire("loading"));var m=document.createDocumentFragment();for(d=0;d<r.length;d++)this._addTile(r[d],m);this._level.el.appendChild(m)}}}},_isValidTile:function(t){var e=this._map.options.crs;if(!e.infinite){var i=this._globalTileRange;if(!e.wrapLng&&(t.x<i.min.x||t.x>i.max.x)||!e.wrapLat&&(t.y<i.min.y||t.y>i.max.y))return!1}if(!this.options.bounds)return!0;var n=this._tileCoordsToBounds(t);return V(this.options.bounds).overlaps(n)},_keyToBounds:function(t){return this._tileCoordsToBounds(this._keyToTileCoords(t))},_tileCoordsToNwSe:function(t){var e=this._map,i=this.getTileSize(),n=t.scaleBy(i),o=n.add(i);return[e.unproject(n,t.z),e.unproject(o,t.z)]},_tileCoordsToBounds:function(t){var e=this._tileCoordsToNwSe(t),i=new q(e[0],e[1]);return this.options.noWrap||(i=this._map.wrapLatLngBounds(i)),i},_tileCoordsToKey:function(t){return t.x+":"+t.y+":"+t.z},_keyToTileCoords:function(t){var e=t.split(":"),i=new R(+e[0],+e[1]);return i.z=+e[2],i},_removeTile:function(t){var e=this._tiles[t];e&&(t1(e.el),delete this._tiles[t],this.fire("tileunload",{tile:e.el,coords:this._keyToTileCoords(t)}))},_initTile:function(t){t8(t,"leaflet-tile");var e=this.getTileSize();t.style.width=e.x+"px",t.style.height=e.y+"px",t.onselectstart=v,t.onmousemove=v,tO.ielt9&&this.options.opacity<1&&et(t,this.options.opacity)},_addTile:function(t,e){var i=this._getTilePos(t),n=this._tileCoordsToKey(t),o=this.createTile(this._wrapCoords(t),p(this._tileReady,this,t));this._initTile(o),this.createTile.length<2&&N(p(this._tileReady,this,t,null,o)),en(o,i),this._tiles[n]={el:o,coords:t,current:!0},e.appendChild(o),this.fire("tileloadstart",{tile:o,coords:t})},_tileReady:function(t,e,i){e&&this.fire("tileerror",{error:e,tile:i,coords:t});var n=this._tileCoordsToKey(t);(i=this._tiles[n])&&(i.loaded=+new Date,this._map._fadeAnimated?(et(i.el,0),O(this._fadeFrame),this._fadeFrame=N(this._updateOpacity,this)):(i.active=!0,this._pruneTiles()),e||(t8(i.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:i.el,coords:t})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),tO.ielt9||!this._map._fadeAnimated?N(this._pruneTiles,this):setTimeout(p(this._pruneTiles,this),250)))},_getTilePos:function(t){return t.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(t){var e=new R(this._wrapX?g(t.x,this._wrapX):t.x,this._wrapY?g(t.y,this._wrapY):t.y);return e.z=t.z,e},_pxBoundsToTileRange:function(t){var e=this.getTileSize();return new W(t.min.unscaleBy(e).floor(),t.max.unscaleBy(e).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var t in this._tiles)if(!this._tiles[t].loaded)return!1;return!0}}),ik=iT.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(t,e){this._url=t,(e=b(this,e)).detectRetina&&tO.retina&&e.maxZoom>0?(e.tileSize=Math.floor(e.tileSize/2),e.zoomReverse?(e.zoomOffset--,e.minZoom=Math.min(e.maxZoom,e.minZoom+1)):(e.zoomOffset++,e.maxZoom=Math.max(e.minZoom,e.maxZoom-1)),e.minZoom=Math.max(0,e.minZoom)):e.zoomReverse?e.minZoom=Math.min(e.maxZoom,e.minZoom):e.maxZoom=Math.max(e.minZoom,e.maxZoom),"string"==typeof e.subdomains&&(e.subdomains=e.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(t,e){return this._url===t&&void 0===e&&(e=!0),this._url=t,e||this.redraw(),this},createTile:function(t,e){var i=document.createElement("img");return ed(i,"load",p(this._tileOnLoad,this,e,i)),ed(i,"error",p(this._tileOnError,this,e,i)),(this.options.crossOrigin||""===this.options.crossOrigin)&&(i.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),"string"==typeof this.options.referrerPolicy&&(i.referrerPolicy=this.options.referrerPolicy),i.alt="",i.src=this.getTileUrl(t),i},getTileUrl:function(t){var i={r:tO.retina?"@2x":"",s:this._getSubdomain(t),x:t.x,y:t.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var n=this._globalTileRange.max.y-t.y;this.options.tms&&(i.y=n),i["-y"]=n}return k(this._url,e(i,this.options))},_tileOnLoad:function(t,e){tO.ielt9?setTimeout(p(t,this,null,e),0):t(null,e)},_tileOnError:function(t,e,i){var n=this.options.errorTileUrl;n&&e.getAttribute("src")!==n&&(e.src=n),t(i,e)},_onTileRemove:function(t){t.tile.onload=null},_getZoomForUrl:function(){var t=this._tileZoom,e=this.options.maxZoom,i=this.options.zoomReverse,n=this.options.zoomOffset;return i&&(t=e-t),t+n},_getSubdomain:function(t){var e=Math.abs(t.x+t.y)%this.options.subdomains.length;return this.options.subdomains[e]},_abortLoading:function(){var t,e;for(t in this._tiles)if(this._tiles[t].coords.z!==this._tileZoom&&((e=this._tiles[t].el).onload=v,e.onerror=v,!e.complete)){e.src=C;var i=this._tiles[t].coords;t1(e),delete this._tiles[t],this.fire("tileabort",{tile:e,coords:i})}},_removeTile:function(t){var e=this._tiles[t];if(e)return e.el.setAttribute("src",C),iT.prototype._removeTile.call(this,t)},_tileReady:function(t,e,i){if(this._map&&(!i||i.getAttribute("src")!==C))return iT.prototype._tileReady.call(this,t,e,i)}});function iM(t,e){return new ik(t,e)}var iz=ik.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(t,i){this._url=t;var n=e({},this.defaultWmsParams);for(var o in i)o in this.options||(n[o]=i[o]);var s=(i=b(this,i)).detectRetina&&tO.retina?2:1,r=this.getTileSize();n.width=r.x*s,n.height=r.y*s,this.wmsParams=n},onAdd:function(t){this._crs=this.options.crs||t.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var e=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[e]=this._crs.code,ik.prototype.onAdd.call(this,t)},getTileUrl:function(t){var e=this._tileCoordsToNwSe(t),i=this._crs,n=U(i.project(e[0]),i.project(e[1])),o=n.min,s=n.max,r=(this._wmsVersion>=1.3&&this._crs===e1?[o.y,o.x,s.y,s.x]:[o.x,o.y,s.x,s.y]).join(","),a=ik.prototype.getTileUrl.call(this,t);return a+P(this.wmsParams,a,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+r},setParams:function(t,i){return e(this.wmsParams,t),i||this.redraw(),this}});ik.WMS=iz,iM.wms=function(t,e){return new iz(t,e)};var iC=e3.extend({options:{padding:.1},initialize:function(t){b(this,t),m(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),t8(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var t={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(t.zoomanim=this._onAnimZoom),t},_onAnimZoom:function(t){this._updateTransform(t.center,t.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(t,e){var i=this._map.getZoomScale(e,this._zoom),n=this._map.getSize().multiplyBy(.5+this.options.padding),o=this._map.project(this._center,e),s=n.multiplyBy(-i).add(o).subtract(this._map._getNewPixelOrigin(t,e));tO.any3d?ei(this._container,s,i):en(this._container,s)},_reset:function(){for(var t in this._update(),this._updateTransform(this._center,this._zoom),this._layers)this._layers[t]._reset()},_onZoomEnd:function(){for(var t in this._layers)this._layers[t]._project()},_updatePaths:function(){for(var t in this._layers)this._layers[t]._update()},_update:function(){var t=this.options.padding,e=this._map.getSize(),i=this._map.containerPointToLayerPoint(e.multiplyBy(-t)).round();this._bounds=new W(i,i.add(e.multiplyBy(1+2*t)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),iS=iC.extend({options:{tolerance:0},getEvents:function(){var t=iC.prototype.getEvents.call(this);return t.viewprereset=this._onViewPreReset,t},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){iC.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var t=this._container=document.createElement("canvas");ed(t,"mousemove",this._onMouseMove,this),ed(t,"click dblclick mousedown mouseup contextmenu",this._onClick,this),ed(t,"mouseout",this._handleMouseOut,this),t._leaflet_disable_events=!0,this._ctx=t.getContext("2d")},_destroyContainer:function(){O(this._redrawRequest),delete this._ctx,t1(this._container),e_(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){for(var t in this._redrawBounds=null,this._layers)this._layers[t]._update();this._redraw()}},_update:function(){if(!this._map._animatingZoom||!this._bounds){iC.prototype._update.call(this);var t=this._bounds,e=this._container,i=t.getSize(),n=tO.retina?2:1;en(e,t.min),e.width=n*i.x,e.height=n*i.y,e.style.width=i.x+"px",e.style.height=i.y+"px",tO.retina&&this._ctx.scale(2,2),this._ctx.translate(-t.min.x,-t.min.y),this.fire("update")}},_reset:function(){iC.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(t){this._updateDashArray(t),this._layers[m(t)]=t;var e=t._order={layer:t,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=e),this._drawLast=e,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(t){this._requestRedraw(t)},_removePath:function(t){var e=t._order,i=e.next,n=e.prev;i?i.prev=n:this._drawLast=n,n?n.next=i:this._drawFirst=i,delete t._order,delete this._layers[m(t)],this._requestRedraw(t)},_updatePath:function(t){this._extendRedrawBounds(t),t._project(),t._update(),this._requestRedraw(t)},_updateStyle:function(t){this._updateDashArray(t),this._requestRedraw(t)},_updateDashArray:function(t){if("string"==typeof t.options.dashArray){var e,i,n=t.options.dashArray.split(/[, ]+/),o=[];for(i=0;i<n.length;i++){if(isNaN(e=Number(n[i])))return;o.push(e)}t.options._dashArray=o}else t.options._dashArray=t.options.dashArray},_requestRedraw:function(t){this._map&&(this._extendRedrawBounds(t),this._redrawRequest=this._redrawRequest||N(this._redraw,this))},_extendRedrawBounds:function(t){if(t._pxBounds){var e=(t.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new W,this._redrawBounds.extend(t._pxBounds.min.subtract([e,e])),this._redrawBounds.extend(t._pxBounds.max.add([e,e]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var t=this._redrawBounds;if(t){var e=t.getSize();this._ctx.clearRect(t.min.x,t.min.y,e.x,e.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var t,e=this._redrawBounds;if(this._ctx.save(),e){var i=e.getSize();this._ctx.beginPath(),this._ctx.rect(e.min.x,e.min.y,i.x,i.y),this._ctx.clip()}this._drawing=!0;for(var n=this._drawFirst;n;n=n.next)t=n.layer,(!e||t._pxBounds&&t._pxBounds.intersects(e))&&t._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(t,e){if(this._drawing){var i,n,o,s,r=t._parts,a=r.length,h=this._ctx;if(a){for(h.beginPath(),i=0;i<a;i++){for(n=0,o=r[i].length;n<o;n++)s=r[i][n],h[n?"lineTo":"moveTo"](s.x,s.y);e&&h.closePath()}this._fillStroke(h,t)}}},_updateCircle:function(t){if(!(!this._drawing||t._empty())){var e=t._point,i=this._ctx,n=Math.max(Math.round(t._radius),1),o=(Math.max(Math.round(t._radiusY),1)||n)/n;1!==o&&(i.save(),i.scale(1,o)),i.beginPath(),i.arc(e.x,e.y/o,n,0,2*Math.PI,!1),1!==o&&i.restore(),this._fillStroke(i,t)}},_fillStroke:function(t,e){var i=e.options;i.fill&&(t.globalAlpha=i.fillOpacity,t.fillStyle=i.fillColor||i.color,t.fill(i.fillRule||"evenodd")),i.stroke&&0!==i.weight&&(t.setLineDash&&t.setLineDash(e.options&&e.options._dashArray||[]),t.globalAlpha=i.opacity,t.lineWidth=i.weight,t.strokeStyle=i.color,t.lineCap=i.lineCap,t.lineJoin=i.lineJoin,t.stroke())},_onClick:function(t){for(var e,i,n=this._map.mouseEventToLayerPoint(t),o=this._drawFirst;o;o=o.next)(e=o.layer).options.interactive&&e._containsPoint(n)&&("click"!==t.type&&"preclick"!==t.type||!this._map._draggableMoved(e))&&(i=e);this._fireEvent(!!i&&[i],t)},_onMouseMove:function(t){if(!(!this._map||this._map.dragging.moving())&&!this._map._animatingZoom){var e=this._map.mouseEventToLayerPoint(t);this._handleMouseHover(t,e)}},_handleMouseOut:function(t){var e=this._hoveredLayer;e&&(t7(this._container,"leaflet-interactive"),this._fireEvent([e],t,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(t,e){if(!this._mouseHoverThrottled){for(var i,n,o=this._drawFirst;o;o=o.next)(i=o.layer).options.interactive&&i._containsPoint(e)&&(n=i);n!==this._hoveredLayer&&(this._handleMouseOut(t),n&&(t8(this._container,"leaflet-interactive"),this._fireEvent([n],t,"mouseover"),this._hoveredLayer=n)),this._fireEvent(!!this._hoveredLayer&&[this._hoveredLayer],t),this._mouseHoverThrottled=!0,setTimeout(p(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(t,e,i){this._map._fireDOMEvent(e,i||e.type,t)},_bringToFront:function(t){var e=t._order;if(e){var i=e.next,n=e.prev;if(!i)return;i.prev=n,n?n.next=i:i&&(this._drawFirst=i),e.prev=this._drawLast,this._drawLast.next=e,e.next=null,this._drawLast=e,this._requestRedraw(t)}},_bringToBack:function(t){var e=t._order;if(e){var i=e.next,n=e.prev;if(!n)return;n.next=i,i?i.prev=n:n&&(this._drawLast=n),e.prev=null,e.next=this._drawFirst,this._drawFirst.prev=e,this._drawFirst=e,this._requestRedraw(t)}}});function iE(t){return tO.canvas?new iS(t):null}var iA=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(t){return document.createElement("<lvml:"+t+' class="lvml">')}}catch(t){}return function(t){return document.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),ij=tO.vml?iA:ti,iZ=iC.extend({_initContainer:function(){this._container=ij("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=ij("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){t1(this._container),e_(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!this._map._animatingZoom||!this._bounds){iC.prototype._update.call(this);var t=this._bounds,e=t.getSize(),i=this._container;this._svgSize&&this._svgSize.equals(e)||(this._svgSize=e,i.setAttribute("width",e.x),i.setAttribute("height",e.y)),en(i,t.min),i.setAttribute("viewBox",[t.min.x,t.min.y,e.x,e.y].join(" ")),this.fire("update")}},_initPath:function(t){var e=t._path=ij("path");t.options.className&&t8(e,t.options.className),t.options.interactive&&t8(e,"leaflet-interactive"),this._updateStyle(t),this._layers[m(t)]=t},_addPath:function(t){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(t._path),t.addInteractiveTarget(t._path)},_removePath:function(t){t1(t._path),t.removeInteractiveTarget(t._path),delete this._layers[m(t)]},_updatePath:function(t){t._project(),t._update()},_updateStyle:function(t){var e=t._path,i=t.options;e&&(i.stroke?(e.setAttribute("stroke",i.color),e.setAttribute("stroke-opacity",i.opacity),e.setAttribute("stroke-width",i.weight),e.setAttribute("stroke-linecap",i.lineCap),e.setAttribute("stroke-linejoin",i.lineJoin),i.dashArray?e.setAttribute("stroke-dasharray",i.dashArray):e.removeAttribute("stroke-dasharray"),i.dashOffset?e.setAttribute("stroke-dashoffset",i.dashOffset):e.removeAttribute("stroke-dashoffset")):e.setAttribute("stroke","none"),i.fill?(e.setAttribute("fill",i.fillColor||i.color),e.setAttribute("fill-opacity",i.fillOpacity),e.setAttribute("fill-rule",i.fillRule||"evenodd")):e.setAttribute("fill","none"))},_updatePoly:function(t,e){this._setPath(t,tn(t._parts,e))},_updateCircle:function(t){var e=t._point,i=Math.max(Math.round(t._radius),1),n=Math.max(Math.round(t._radiusY),1)||i,o="a"+i+","+n+" 0 1,0 ",s=t._empty()?"M0 0":"M"+(e.x-i)+","+e.y+o+2*i+",0 "+o+-(2*i)+",0 ";this._setPath(t,s)},_setPath:function(t,e){t._path.setAttribute("d",e)},_bringToFront:function(t){t3(t._path)},_bringToBack:function(t){t5(t._path)}});function iN(t){return tO.svg||tO.vml?new iZ(t):null}tO.vml&&iZ.include({_initContainer:function(){this._container=t0("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(iC.prototype._update.call(this),this.fire("update"))},_initPath:function(t){var e=t._container=iA("shape");t8(e,"leaflet-vml-shape "+(this.options.className||"")),e.coordsize="1 1",t._path=iA("path"),e.appendChild(t._path),this._updateStyle(t),this._layers[m(t)]=t},_addPath:function(t){var e=t._container;this._container.appendChild(e),t.options.interactive&&t.addInteractiveTarget(e)},_removePath:function(t){var e=t._container;t1(e),t.removeInteractiveTarget(e),delete this._layers[m(t)]},_updateStyle:function(t){var e=t._stroke,i=t._fill,n=t.options,o=t._container;o.stroked=!!n.stroke,o.filled=!!n.fill,n.stroke?(e||(e=t._stroke=iA("stroke")),o.appendChild(e),e.weight=n.weight+"px",e.color=n.color,e.opacity=n.opacity,n.dashArray?e.dashStyle=M(n.dashArray)?n.dashArray.join(" "):n.dashArray.replace(/( *, *)/g," "):e.dashStyle="",e.endcap=n.lineCap.replace("butt","flat"),e.joinstyle=n.lineJoin):e&&(o.removeChild(e),t._stroke=null),n.fill?(i||(i=t._fill=iA("fill")),o.appendChild(i),i.color=n.fillColor||n.color,i.opacity=n.fillOpacity):i&&(o.removeChild(i),t._fill=null)},_updateCircle:function(t){var e=t._point.round(),i=Math.round(t._radius),n=Math.round(t._radiusY||i);this._setPath(t,t._empty()?"M0 0":"AL "+e.x+","+e.y+" "+i+","+n+" 0,23592600")},_setPath:function(t,e){t._path.v=e},_bringToFront:function(t){t3(t._container)},_bringToBack:function(t){t5(t._container)}}),eS.include({getRenderer:function(t){var e=t.options.renderer||this._getPaneRenderer(t.options.pane)||this.options.renderer||this._renderer;return e||(e=this._renderer=this._createRenderer()),this.hasLayer(e)||this.addLayer(e),e},_getPaneRenderer:function(t){if("overlayPane"===t||void 0===t)return!1;var e=this._paneRenderers[t];return void 0===e&&(e=this._createRenderer({pane:t}),this._paneRenderers[t]=e),e},_createRenderer:function(t){return this.options.preferCanvas&&iE(t)||iN(t)}});var iO=is.extend({initialize:function(t,e){is.prototype.initialize.call(this,this._boundsToLatLngs(t),e)},setBounds:function(t){return this.setLatLngs(this._boundsToLatLngs(t))},_boundsToLatLngs:function(t){return[(t=V(t)).getSouthWest(),t.getNorthWest(),t.getNorthEast(),t.getSouthEast()]}});iZ.create=ij,iZ.pointsToPath=tn,ir.geometryToLayer=ia,ir.coordsToLatLng=il,ir.coordsToLatLngs=ic,ir.latLngToCoords=iu,ir.latLngsToCoords=id,ir.getFeature=ip,ir.asFeature=i_,eS.mergeOptions({boxZoom:!0});var iB=eB.extend({initialize:function(t){this._map=t,this._container=t._container,this._pane=t._panes.overlayPane,this._resetStateTimeout=0,t.on("unload",this._destroy,this)},addHooks:function(){ed(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){e_(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){t1(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){0!==this._resetStateTimeout&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(t){if(!t.shiftKey||1!==t.which&&1!==t.button)return!1;this._clearDeferredResetState(),this._resetState(),i(),er(),this._startPoint=this._map.mouseEventToContainerPoint(t),ed(document,{contextmenu:eP,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(t){this._moved||(this._moved=!0,this._box=t0("div","leaflet-zoom-box",this._container),t8(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(t);var e=new W(this._point,this._startPoint),i=e.getSize();en(this._box,e.min),this._box.style.width=i.x+"px",this._box.style.height=i.y+"px"},_finish:function(){this._moved&&(t1(this._box),t7(this._container,"leaflet-crosshair")),n(),ea(),e_(document,{contextmenu:eP,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(t){if((1===t.which||1===t.button)&&(this._finish(),this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(p(this._resetState,this),0);var e=new q(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(e).fire("boxzoomend",{boxZoomBounds:e})}},_onKeyDown:function(t){27===t.keyCode&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});eS.addInitHook("addHandler","boxZoom",iB),eS.mergeOptions({doubleClickZoom:!0});var iI=eB.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(t){var e=this._map,i=e.getZoom(),n=e.options.zoomDelta,o=t.originalEvent.shiftKey?i-n:i+n;"center"===e.options.doubleClickZoom?e.setZoom(o):e.setZoomAround(t.containerPoint,o)}});eS.addInitHook("addHandler","doubleClickZoom",iI),eS.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var iD=eB.extend({addHooks:function(){if(!this._draggable){var t=this._map;this._draggable=new eD(t._mapPane,t._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),t.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),t.on("zoomend",this._onZoomEnd,this),t.whenReady(this._onZoomEnd,this))}t8(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){t7(this._map._container,"leaflet-grab"),t7(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var t=this._map;if(t._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var e=V(this._map.options.maxBounds);this._offsetLimit=U(this._map.latLngToContainerPoint(e.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(e.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;t.fire("movestart").fire("dragstart"),t.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(t){if(this._map.options.inertia){var e=this._lastTime=+new Date,i=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(i),this._times.push(e),this._prunePositions(e)}this._map.fire("move",t).fire("drag",t)},_prunePositions:function(t){for(;this._positions.length>1&&t-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var t=this._map.getSize().divideBy(2),e=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=e.subtract(t).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(t,e){return t-(t-e)*this._viscosity},_onPreDragLimit:function(){if(this._viscosity&&this._offsetLimit){var t=this._draggable._newPos.subtract(this._draggable._startPos),e=this._offsetLimit;t.x<e.min.x&&(t.x=this._viscousLimit(t.x,e.min.x)),t.y<e.min.y&&(t.y=this._viscousLimit(t.y,e.min.y)),t.x>e.max.x&&(t.x=this._viscousLimit(t.x,e.max.x)),t.y>e.max.y&&(t.y=this._viscousLimit(t.y,e.max.y)),this._draggable._newPos=this._draggable._startPos.add(t)}},_onPreDragWrap:function(){var t=this._worldWidth,e=Math.round(t/2),i=this._initialWorldOffset,n=this._draggable._newPos.x,o=(n-e+i)%t+e-i,s=(n+e+i)%t-e-i,r=Math.abs(o+i)<Math.abs(s+i)?o:s;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=r},_onDragEnd:function(t){var e=this._map,i=e.options,n=!i.inertia||t.noInertia||this._times.length<2;if(e.fire("dragend",t),n)e.fire("moveend");else{this._prunePositions(+new Date);var o=this._lastPos.subtract(this._positions[0]),s=(this._lastTime-this._times[0])/1e3,r=i.easeLinearity,a=o.multiplyBy(r/s),h=a.distanceTo([0,0]),l=Math.min(i.inertiaMaxSpeed,h),c=a.multiplyBy(l/h),u=l/(i.inertiaDeceleration*r),d=c.multiplyBy(-u/2).round();d.x||d.y?(d=e._limitOffset(d,e.options.maxBounds),N(function(){e.panBy(d,{duration:u,easeLinearity:r,noMoveStart:!0,animate:!0})})):e.fire("moveend")}}});eS.addInitHook("addHandler","dragging",iD),eS.mergeOptions({keyboard:!0,keyboardPanDelta:80});var iR=eB.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(t){this._map=t,this._setPanDelta(t.options.keyboardPanDelta),this._setZoomDelta(t.options.zoomDelta)},addHooks:function(){var t=this._map._container;t.tabIndex<=0&&(t.tabIndex="0"),ed(t,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),e_(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var t=document.body,e=document.documentElement,i=t.scrollTop||e.scrollTop,n=t.scrollLeft||e.scrollLeft;this._map._container.focus(),window.scrollTo(n,i)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(t){var e,i,n=this._panKeys={},o=this.keyCodes;for(e=0,i=o.left.length;e<i;e++)n[o.left[e]]=[-1*t,0];for(e=0,i=o.right.length;e<i;e++)n[o.right[e]]=[t,0];for(e=0,i=o.down.length;e<i;e++)n[o.down[e]]=[0,t];for(e=0,i=o.up.length;e<i;e++)n[o.up[e]]=[0,-1*t]},_setZoomDelta:function(t){var e,i,n=this._zoomKeys={},o=this.keyCodes;for(e=0,i=o.zoomIn.length;e<i;e++)n[o.zoomIn[e]]=t;for(e=0,i=o.zoomOut.length;e<i;e++)n[o.zoomOut[e]]=-t},_addHooks:function(){ed(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){e_(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(t){if(!t.altKey&&!t.ctrlKey&&!t.metaKey){var e,i=t.keyCode,n=this._map;if(i in this._panKeys){if(!n._panAnim||!n._panAnim._inProgress)if(e=this._panKeys[i],t.shiftKey&&(e=H(e).multiplyBy(3)),n.options.maxBounds&&(e=n._limitOffset(H(e),n.options.maxBounds)),n.options.worldCopyJump){var o=n.wrapLatLng(n.unproject(n.project(n.getCenter()).add(e)));n.panTo(o)}else n.panBy(e)}else if(i in this._zoomKeys)n.setZoom(n.getZoom()+(t.shiftKey?3:1)*this._zoomKeys[i]);else{if(27!==i||!n._popup||!n._popup.options.closeOnEscapeKey)return;n.closePopup()}eP(t)}}});eS.addInitHook("addHandler","keyboard",iR),eS.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var iF=eB.extend({addHooks:function(){ed(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){e_(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(t){var e=eM(t),i=this._map.options.wheelDebounceTime;this._delta+=e,this._lastMousePos=this._map.mouseEventToContainerPoint(t),this._startTime||(this._startTime=+new Date);var n=Math.max(i-(new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(p(this._performZoom,this),n),eP(t)},_performZoom:function(){var t=this._map,e=t.getZoom(),i=this._map.options.zoomSnap||0;t._stop();var n=4*Math.log(2/(1+Math.exp(-Math.abs(this._delta/(4*this._map.options.wheelPxPerZoomLevel)))))/Math.LN2,o=i?Math.ceil(n/i)*i:n,s=t._limitZoom(e+(this._delta>0?o:-o))-e;this._delta=0,this._startTime=null,s&&("center"===t.options.scrollWheelZoom?t.setZoom(e+s):t.setZoomAround(this._lastMousePos,e+s))}});eS.addInitHook("addHandler","scrollWheelZoom",iF),eS.mergeOptions({tapHold:tO.touchNative&&tO.safari&&tO.mobile,tapTolerance:15});var iH=eB.extend({addHooks:function(){ed(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){e_(this._map._container,"touchstart",this._onDown,this)},_onDown:function(t){if(clearTimeout(this._holdTimeout),1===t.touches.length){var e=t.touches[0];this._startPos=this._newPos=new R(e.clientX,e.clientY),this._holdTimeout=setTimeout(p(function(){this._cancel(),this._isTapValid()&&(ed(document,"touchend",eb),ed(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",e))},this),600),ed(document,"touchend touchcancel contextmenu",this._cancel,this),ed(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function t(){e_(document,"touchend",eb),e_(document,"touchend touchcancel",t)},_cancel:function(){clearTimeout(this._holdTimeout),e_(document,"touchend touchcancel contextmenu",this._cancel,this),e_(document,"touchmove",this._onMove,this)},_onMove:function(t){var e=t.touches[0];this._newPos=new R(e.clientX,e.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(t,e){var i=new MouseEvent(t,{bubbles:!0,cancelable:!0,view:window,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY});i._simulated=!0,e.target.dispatchEvent(i)}});eS.addInitHook("addHandler","tapHold",iH),eS.mergeOptions({touchZoom:tO.touch,bounceAtZoomLimits:!0});var iW=eB.extend({addHooks:function(){t8(this._map._container,"leaflet-touch-zoom"),ed(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){t7(this._map._container,"leaflet-touch-zoom"),e_(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(t){var e=this._map;if(t.touches&&2===t.touches.length&&!e._animatingZoom&&!this._zooming){var i=e.mouseEventToContainerPoint(t.touches[0]),n=e.mouseEventToContainerPoint(t.touches[1]);this._centerPoint=e.getSize()._divideBy(2),this._startLatLng=e.containerPointToLatLng(this._centerPoint),"center"!==e.options.touchZoom&&(this._pinchStartLatLng=e.containerPointToLatLng(i.add(n)._divideBy(2))),this._startDist=i.distanceTo(n),this._startZoom=e.getZoom(),this._moved=!1,this._zooming=!0,e._stop(),ed(document,"touchmove",this._onTouchMove,this),ed(document,"touchend touchcancel",this._onTouchEnd,this),eb(t)}},_onTouchMove:function(t){if(t.touches&&2===t.touches.length&&this._zooming){var e=this._map,i=e.mouseEventToContainerPoint(t.touches[0]),n=e.mouseEventToContainerPoint(t.touches[1]),o=i.distanceTo(n)/this._startDist;if(this._zoom=e.getScaleZoom(o,this._startZoom),!e.options.bounceAtZoomLimits&&(this._zoom<e.getMinZoom()&&o<1||this._zoom>e.getMaxZoom()&&o>1)&&(this._zoom=e._limitZoom(this._zoom)),"center"===e.options.touchZoom){if(this._center=this._startLatLng,1===o)return}else{var s=i._add(n)._divideBy(2)._subtract(this._centerPoint);if(1===o&&0===s.x&&0===s.y)return;this._center=e.unproject(e.project(this._pinchStartLatLng,this._zoom).subtract(s),this._zoom)}this._moved||(e._moveStart(!0,!1),this._moved=!0),O(this._animRequest);var r=p(e._move,e,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=N(r,this,!0),eb(t)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,O(this._animRequest),e_(document,"touchmove",this._onTouchMove,this),e_(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});eS.addInitHook("addHandler","touchZoom",iW),eS.BoxZoom=iB,eS.DoubleClickZoom=iI,eS.Drag=iD,eS.Keyboard=iR,eS.ScrollWheelZoom=iF,eS.TapHold=iH,eS.TouchZoom=iW,t.Bounds=W,t.Browser=tO,t.CRS=K,t.Canvas=iS,t.Circle=ii,t.CircleMarker=ie,t.Class=B,t.Control=eE,t.DivIcon=iL,t.DivOverlay=iw,t.DomEvent={__proto__:null,on:ed,off:e_,stopPropagation:ey,disableScrollPropagation:ex,disableClickPropagation:ew,preventDefault:eb,stop:eP,getPropagationPath:eL,getMousePosition:eT,getWheelDelta:eM,isExternalTarget:ez,addListener:ed,removeListener:e_},t.DomUtil={__proto__:null,TRANSFORM:tK,TRANSITION:tY,TRANSITION_END:tX,get:tJ,getStyle:tQ,create:t0,remove:t1,empty:t2,toFront:t3,toBack:t5,hasClass:t4,addClass:t8,removeClass:t7,setClass:t9,getClass:t6,setOpacity:et,testProp:ee,setTransform:ei,setPosition:en,getPosition:eo,get disableTextSelection(){return i},get enableTextSelection(){return n},disableImageDrag:er,enableImageDrag:ea,preventOutline:eh,restoreOutline:el,getSizedParentNode:ec,getScale:eu},t.Draggable=eD,t.Evented=D,t.FeatureGroup=e4,t.GeoJSON=ir,t.GridLayer=iT,t.Handler=eB,t.Icon=e8,t.ImageOverlay=iv,t.LatLng=G,t.LatLngBounds=q,t.Layer=e3,t.LayerGroup=e5,t.LineUtil={__proto__:null,simplify:eW,pointToSegmentDistance:eU,closestPointOnSegment:function(t,e,i){return e$(t,e,i)},clipSegment:eq,_getEdgeIntersection:eV,_getBitCode:eG,_sqClosestPointOnSegment:e$,isFlat:eK,_flat:eY,polylineCenter:eX},t.Map=eS,t.Marker=e6,t.Mixin={Events:I},t.Path=it,t.Point=R,t.PolyUtil={__proto__:null,clipPolygon:eR,polygonCenter:eF,centroid:eH},t.Polygon=is,t.Polyline=io,t.Popup=ib,t.PosAnimation=eC,t.Projection={__proto__:null,LonLat:eJ,Mercator:eQ,SphericalMercator:X},t.Rectangle=iO,t.Renderer=iC,t.SVG=iZ,t.SVGOverlay=ix,t.TileLayer=ik,t.Tooltip=iP,t.Transformation=J,t.Util={__proto__:null,extend:e,create:d,bind:p,get lastId(){return _},stamp:m,throttle:f,wrapNum:g,falseFn:v,formatNum:y,trim:x,splitWords:w,setOptions:b,getParamString:P,template:k,isArray:M,indexOf:z,emptyImageUrl:C,requestFn:j,cancelFn:Z,requestAnimFrame:N,cancelAnimFrame:O},t.VideoOverlay=iy,t.bind=p,t.bounds=U,t.canvas=iE,t.circle=function(t,e,i){return new ii(t,e,i)},t.circleMarker=function(t,e){return new ie(t,e)},t.control=eA,t.divIcon=function(t){return new iL(t)},t.extend=e,t.featureGroup=function(t,e){return new e4(t,e)},t.geoJSON=ig,t.geoJson=ig,t.gridLayer=function(t){return new iT(t)},t.icon=function(t){return new e8(t)},t.imageOverlay=function(t,e,i){return new iv(t,e,i)},t.latLng=$,t.latLngBounds=V,t.layerGroup=function(t,e){return new e5(t,e)},t.map=function(t,e){return new eS(t,e)},t.marker=function(t,e){return new e6(t,e)},t.point=H,t.polygon=function(t,e){return new is(t,e)},t.polyline=function(t,e){return new io(t,e)},t.popup=function(t,e){return new ib(t,e)},t.rectangle=function(t,e){return new iO(t,e)},t.setOptions=b,t.stamp=m,t.svg=iN,t.svgOverlay=function(t,e,i){return new ix(t,e,i)},t.tileLayer=iM,t.tooltip=function(t,e){return new iP(t,e)},t.transformation=Q,t.version="1.9.4",t.videoOverlay=function(t,e,i){return new iy(t,e,i)};var iU=window.L;t.noConflict=function(){return window.L=iU,this},window.L=t})(e)}};var e=require("../../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),n=e.X(0,[4447,3871,7048,8390,474,8739,3302,2936,9599,4539],()=>i(31905));module.exports=n})();