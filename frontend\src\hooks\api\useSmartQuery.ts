/**
 * @file Smart Query Hook with WebSocket Integration
 * Automatically disables polling when WebSocket is connected
 * Follows modern best practices for real-time data management
 * @module hooks/useSmartQuery
 */

import type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

import type { DomainChannel } from '../../lib/services/WebSocketManager';

import { getWebSocketManager } from '../../lib/services/WebSocketManager';

/**
 * Mapping of domain channels to Socket.IO room names
 * This ensures the frontend joins the correct rooms that the backend emits events to
 */
const CHANNEL_ROOM_MAPPING: Record<DomainChannel, string> = {
  crud: 'entity-updates',
  notifications: 'notifications-monitoring',
  reliability: 'reliability-monitoring',
  system: 'system-monitoring',
} as const;

/**
 * Smart query configuration for WebSocket integration
 * @template T - The data type returned by the query function
 */
export interface SmartQueryConfig<T = unknown> {
  /**
   * Domain channel for WebSocket events
   * Automatically maps to appropriate Socket.IO room via CHANNEL_ROOM_MAPPING
   */
  channel: DomainChannel;
  /** Whether to enable fallback polling when WebSocket is disconnected */
  enableFallback?: boolean;
  /** Whether to enable WebSocket integration and room joining */
  enableWebSocket?: boolean;
  /** Events that should trigger data refetch when received via WebSocket */
  events: string[];
  /** Fallback polling interval when WebSocket is disconnected (ms) */
  fallbackInterval?: number;
}

/**
 * Hook for CRUD operations with smart real-time updates
 */
export function useCrudQuery<T = unknown, E = Error>(
  queryKey: unknown[],
  queryFn: () => Promise<T>,
  entityType: string,
  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>
) {
  return useSmartQuery(
    queryKey,
    queryFn,
    {
      channel: 'crud',
      events: [
        `${entityType}:created`,
        `${entityType}:updated`,
        `${entityType}:deleted`,
        `refresh:${entityType}`,
      ],
      fallbackInterval: 30_000,
    },
    options
  );
}

/**
 * Hook for system notifications with smart real-time updates
 */
export function useNotificationQuery<T = unknown, E = Error>(
  queryKey: unknown[],
  queryFn: () => Promise<T>,
  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>
) {
  return useSmartQuery(
    queryKey,
    queryFn,
    {
      channel: 'notifications',
      events: ['notification-created', 'notification-updated'],
      fallbackInterval: 60_000, // 1 minute for notifications
    },
    options
  );
}

/**
 * Hook for reliability monitoring with smart real-time updates
 */
export function useReliabilityQuery<T = unknown, E = Error>(
  queryKey: unknown[],
  queryFn: () => Promise<T>,
  monitoringType: 'alerts' | 'circuit-breakers' | 'health' | 'metrics',
  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>
) {
  // Increased intervals to reduce aggressive polling and cancellations
  const intervalMap = {
    alerts: 30_000, // 30 seconds for alerts (was 10s)
    'circuit-breakers': 60_000, // 60 seconds for circuit breakers (was 30s)
    health: 45_000, // 45 seconds for health (was 15s)
    metrics: 60_000, // 60 seconds for metrics (was 30s)
  };

  const webSocketManager = getWebSocketManager();

  // Join reliability monitoring room when WebSocket is connected
  useEffect(() => {
    if (webSocketManager.isConnected()) {
      console.debug(
        `[ReliabilityQuery] Joining reliability-monitoring room for ${monitoringType}`
      );
      webSocketManager.joinRoom('reliability-monitoring');
    }

    // Subscribe to connection state changes to join room when connected
    const unsubscribe = webSocketManager.onStateChange(state => {
      if (state === 'connected') {
        console.debug(
          `[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ${monitoringType}`
        );
        webSocketManager.joinRoom('reliability-monitoring');
      }
    });

    return () => {
      unsubscribe();
      // Leave room when component unmounts
      if (webSocketManager.isConnected()) {
        webSocketManager.leaveRoom('reliability-monitoring');
      }
    };
  }, [webSocketManager, monitoringType]);

  return useSmartQuery(
    queryKey,
    queryFn,
    {
      channel: 'reliability',
      events: [
        `${monitoringType}-update`,
        `${monitoringType}-created`,
        `${monitoringType}-resolved`,
      ],
      fallbackInterval: intervalMap[monitoringType],
    },
    options
  );
}

/**
 * Smart Query Hook with Socket.IO Room Management
 *
 * Combines React Query with WebSocket real-time updates and automatic Socket.IO room joining.
 * This hook automatically:
 * - Joins the appropriate Socket.IO room based on the domain channel
 * - Subscribes to WebSocket events for real-time data updates
 * - Switches between WebSocket and polling based on connection state
 * - Handles room cleanup when component unmounts
 *
 * **Room Mapping:**
 * - `crud` channel → `entity-updates` room
 * - `reliability` channel → `reliability-monitoring` room
 * - `notifications` channel → `notifications-monitoring` room
 * - `system` channel → `system-monitoring` room
 *
 * @template T - The data type returned by the query function
 * @template E - The error type for failed queries
 * @param queryKey - React Query key for caching and invalidation
 * @param queryFn - Data fetching function that returns a Promise<T>
 * @param config - Smart query configuration including channel and events
 * @param options - Additional React Query options (merged with smart defaults)
 * @returns Enhanced query result with WebSocket integration and connection state
 */
export function useSmartQuery<T = unknown, E = Error>(
  queryKey: unknown[],
  queryFn: () => Promise<T>,
  config: SmartQueryConfig<T>,
  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>
): UseQueryResult<T, E> & {
  isUsingFallback: boolean;
  isWebSocketConnected: boolean;
} {
  const {
    channel,
    enableFallback = true,
    enableWebSocket = true,
    events,
    fallbackInterval = 30_000,
  } = config;

  const [isWebSocketConnected, setIsWebSocketConnected] = useState(false);
  const webSocketManager = getWebSocketManager();

  // Track WebSocket connection state
  useEffect(() => {
    const updateConnectionState = () => {
      setIsWebSocketConnected(webSocketManager.isConnected());
    };

    // Initial state
    updateConnectionState();

    // Subscribe to state changes
    const unsubscribe = webSocketManager.onStateChange(updateConnectionState);

    return unsubscribe;
  }, [webSocketManager]);

  // Determine if we should use fallback polling
  const isUsingFallback =
    enableFallback && (!enableWebSocket || !isWebSocketConnected);

  // Configure React Query options based on WebSocket state
  const queryOptions: UseQueryOptions<T, E> = {
    // Longer cache time for better performance
    gcTime: 10 * 60 * 1000, // 10 minutes
    queryFn,
    queryKey,
    // Disable polling when WebSocket is connected
    refetchInterval: isUsingFallback ? fallbackInterval : false,
    refetchOnReconnect: true, // Always refetch on network reconnect
    // Enable background refetch only when using fallback
    refetchOnWindowFocus: isUsingFallback,
    // Shorter stale time when using WebSocket (real-time updates)
    staleTime: isWebSocketConnected ? 0 : 30_000,
    ...options,
  };

  const queryClient = useQueryClient();
  const queryResult = useQuery<T, E>(queryOptions);

  // Manage Socket.IO room joining/leaving based on channel
  useEffect(() => {
    if (!enableWebSocket || !isWebSocketConnected) {
      return;
    }

    const roomName = CHANNEL_ROOM_MAPPING[channel];
    if (!roomName) {
      console.warn(
        `[SmartQuery] No room mapping found for channel: ${channel}`
      );
      return;
    }

    // Join the appropriate room for this channel
    try {
      webSocketManager.joinRoom(roomName);
      console.log(
        `[SmartQuery] Joined room: ${roomName} for channel: ${channel}`
      );
    } catch (error) {
      console.error(`[SmartQuery] Failed to join room ${roomName}:`, error);
    }

    // Cleanup: leave room when component unmounts or dependencies change
    return () => {
      try {
        webSocketManager.leaveRoom(roomName);
        console.log(
          `[SmartQuery] Left room: ${roomName} for channel: ${channel}`
        );
      } catch (error) {
        console.error(`[SmartQuery] Failed to leave room ${roomName}:`, error);
      }
    };
  }, [enableWebSocket, isWebSocketConnected, channel, webSocketManager]);

  // Subscribe to WebSocket events for real-time updates
  useEffect(() => {
    if (!enableWebSocket || !isWebSocketConnected || events.length === 0) {
      return;
    }

    const unsubscribers: (() => void)[] = [];

    // Subscribe to each event
    for (const event of events) {
      const unsubscribe = webSocketManager.subscribe(
        channel,
        event,
        (data: unknown) => {
          console.log(
            `[SmartQuery] WebSocket event received: ${channel}:${event}`,
            data
          );

          // Invalidate the specific query to trigger refetch
          queryClient.invalidateQueries({ queryKey });
        }
      );

      unsubscribers.push(unsubscribe);
    }

    return () => {
      for (const unsubscribe of unsubscribers) unsubscribe();
    };
  }, [
    enableWebSocket,
    isWebSocketConnected,
    events,
    channel,
    webSocketManager,
    queryClient,
    queryKey,
  ]);

  return {
    ...queryResult,
    isUsingFallback,
    isWebSocketConnected,
  };
}

/**
 * Hook for system-wide events with smart real-time updates
 */
export function useSystemQuery<T = unknown, E = Error>(
  queryKey: unknown[],
  queryFn: () => Promise<T>,
  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>
) {
  return useSmartQuery(
    queryKey,
    queryFn,
    {
      channel: 'system',
      events: ['system-update', 'config-changed'],
      fallbackInterval: 120_000, // 2 minutes for system events
    },
    options
  );
}
