"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8128],{6560:(e,s,a)=>{a.d(s,{r:()=>d});var t=a(95155),r=a(50172),l=a(12115),n=a(30285),i=a(54036);let d=l.forwardRef((e,s)=>{let{actionType:a="primary",asChild:l=!1,children:d,className:c,disabled:o,icon:m,isLoading:u=!1,loadingText:f,...x}=e,{className:p,variant:h}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[a];return(0,t.jsx)(n.$,{asChild:l,className:(0,i.cn)(p,c),disabled:u||o,ref:s,variant:h,...x,children:u?(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,t.jsx)(r.A,{className:"mr-2 size-4 animate-spin"}),f||d]}):(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",m&&(0,t.jsx)("span",{className:"mr-2",children:m}),d]})})});d.displayName="ActionButton"},26126:(e,s,a)=>{a.d(s,{E:()=>i});var t=a(95155),r=a(74466);a(12115);var l=a(54036);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function i(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)(n({variant:a}),s),...r})}},44838:(e,s,a)=>{a.d(s,{SQ:()=>u,_2:()=>f,hO:()=>x,lp:()=>p,mB:()=>h,rI:()=>o,ty:()=>m});var t=a(95155),r=a(12115),l=a(48698),n=a(73158),i=a(10518),d=a(70154),c=a(54036);let o=l.bL,m=l.l9;l.YJ,l.ZL,l.Pb,l.z6,r.forwardRef((e,s)=>{let{className:a,inset:r,children:i,...d}=e;return(0,t.jsxs)(l.ZP,{ref:s,className:(0,c.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",a),...d,children:[i,(0,t.jsx)(n.A,{className:"ml-auto"})]})}).displayName=l.ZP.displayName,r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.G5,{ref:s,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...r})}).displayName=l.G5.displayName;let u=r.forwardRef((e,s)=>{let{className:a,sideOffset:r=4,...n}=e;return(0,t.jsx)(l.ZL,{children:(0,t.jsx)(l.UC,{ref:s,sideOffset:r,className:(0,c.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...n})})});u.displayName=l.UC.displayName;let f=r.forwardRef((e,s)=>{let{className:a,inset:r,...n}=e;return(0,t.jsx)(l.q7,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",a),...n})});f.displayName=l.q7.displayName;let x=r.forwardRef((e,s)=>{let{className:a,children:r,checked:n,...d}=e;return(0,t.jsxs)(l.H_,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...void 0!==n&&{checked:n},...d,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})}),r]})});x.displayName=l.H_.displayName,r.forwardRef((e,s)=>{let{className:a,children:r,...n}=e;return(0,t.jsxs)(l.hN,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(d.A,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=l.hN.displayName;let p=r.forwardRef((e,s)=>{let{className:a,inset:r,...n}=e;return(0,t.jsx)(l.JU,{ref:s,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",a),...n})});p.displayName=l.JU.displayName;let h=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),...r})});h.displayName=l.wv.displayName},47262:(e,s,a)=>{a.d(s,{S:()=>d});var t=a(95155),r=a(76981),l=a(10518),n=a(12115),i=a(54036);let d=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)(r.bL,{className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),ref:s,...n,children:(0,t.jsx)(r.C1,{className:(0,i.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(l.A,{className:"size-4"})})})});d.displayName=r.bL.displayName},55365:(e,s,a)=>{a.d(s,{Fc:()=>d,TN:()=>o,XL:()=>c});var t=a(95155),r=a(74466),l=a(12115),n=a(54036);let i=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),d=l.forwardRef((e,s)=>{let{className:a,variant:r,...l}=e;return(0,t.jsx)("div",{className:(0,n.cn)(i({variant:r}),a),ref:s,role:"alert",...l})});d.displayName="Alert";let c=l.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h5",{className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",a),ref:s,...r})});c.displayName="AlertTitle";let o=l.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:(0,n.cn)("text-sm [&_p]:leading-relaxed",a),ref:s,...r})});o.displayName="AlertDescription"},59409:(e,s,a)=>{a.d(s,{bq:()=>u,eb:()=>h,gC:()=>p,l6:()=>o,yv:()=>m});var t=a(95155),r=a(31992),l=a(79556),n=a(77381),i=a(10518),d=a(12115),c=a(54036);let o=r.bL;r.YJ;let m=r.WT,u=d.forwardRef((e,s)=>{let{children:a,className:n,...i}=e;return(0,t.jsxs)(r.l9,{className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",n),ref:s,...i,children:[a,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(l.A,{className:"size-4 opacity-50"})})]})});u.displayName=r.l9.displayName;let f=d.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.PP,{className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),ref:s,...l,children:(0,t.jsx)(n.A,{className:"size-4"})})});f.displayName=r.PP.displayName;let x=d.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)(r.wn,{className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),ref:s,...n,children:(0,t.jsx)(l.A,{className:"size-4"})})});x.displayName=r.wn.displayName;let p=d.forwardRef((e,s)=>{let{children:a,className:l,position:n="popper",...i}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",l),position:n,ref:s,...i,children:[(0,t.jsx)(f,{}),(0,t.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,t.jsx)(x,{})]})})});p.displayName=r.UC.displayName,d.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.JU,{className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),ref:s,...l})}).displayName=r.JU.displayName;let h=d.memo(d.forwardRef((e,s)=>{let{children:a,className:l,...n}=e,o=d.useCallback(e=>{"function"==typeof s?s(e):s&&(s.current=e)},[s]);return(0,t.jsxs)(r.q7,{className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l),ref:o,...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(i.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:a})]})}));h.displayName=r.q7.displayName,d.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.wv,{className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),ref:s,...l})}).displayName=r.wv.displayName},62523:(e,s,a)=>{a.d(s,{p:()=>n});var t=a(95155),r=a(12115),l=a(54036);let n=r.forwardRef((e,s)=>{let{className:a,type:r,...n}=e;return(0,t.jsx)("input",{className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,type:r,...n})});n.displayName="Input"},68856:(e,s,a)=>{a.d(s,{E:()=>l});var t=a(95155),r=a(54036);function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",s),...a})}},77023:(e,s,a)=>{a.d(s,{gO:()=>u,jt:()=>h,pp:()=>f});var t=a(95155),r=a(11133),l=a(50172);a(12115);var n=a(6560),i=a(55365),d=a(68856),c=a(54036);let o={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function u(e){let{children:s,className:a,data:r,emptyComponent:l,error:n,errorComponent:i,isLoading:d,loadingComponent:o,onRetry:m}=e;return d?o||(0,t.jsx)(p,{...a&&{className:a},text:"Loading..."}):n?i||(0,t.jsx)(x,{...a&&{className:a},message:n,...m&&{onRetry:m}}):!r||Array.isArray(r)&&0===r.length?l||(0,t.jsx)("div",{className:(0,c.cn)("text-center py-8",a),children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,t.jsx)("div",{className:a,children:s(r)})}function f(e){let{className:s,description:a,icon:r,primaryAction:l,secondaryAction:i,title:d}=e;return(0,t.jsxs)("div",{className:(0,c.cn)("space-y-6 text-center py-12",s),children:[r&&(0,t.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,t.jsx)(r,{className:"h-10 w-10 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:d}),a&&(0,t.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:a})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[l&&(0,t.jsx)(n.r,{actionType:"primary",asChild:!!l.href,icon:l.icon,onClick:l.onClick,children:l.href?(0,t.jsx)("a",{href:l.href,children:l.label}):l.label}),i&&(0,t.jsx)(n.r,{actionType:"tertiary",asChild:!!i.href,icon:i.icon,onClick:i.onClick,children:i.href?(0,t.jsx)("a",{href:i.href,children:i.label}):i.label})]})]})}function x(e){let{className:s,message:a,onRetry:d}=e;return(0,t.jsxs)(i.Fc,{className:(0,c.cn)("my-4",s),variant:"destructive",children:[(0,t.jsx)(r.A,{className:"size-4"}),(0,t.jsx)(i.XL,{children:"Error"}),(0,t.jsx)(i.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:a}),d&&(0,t.jsx)(n.r,{actionType:"tertiary",icon:(0,t.jsx)(l.A,{className:"size-4"}),onClick:d,size:"sm",children:"Try Again"})]})})]})}function p(e){let{className:s,fullPage:a=!1,size:r="md",text:n}=e;return(0,t.jsx)("div",{className:(0,c.cn)("flex items-center justify-center",a&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(l.A,{className:(0,c.cn)("animate-spin text-primary",o[r])}),n&&(0,t.jsx)("span",{className:(0,c.cn)("mt-2 text-muted-foreground",m[r]),children:n})]})})}function h(e){let{className:s,count:a=1,testId:r="loading-skeleton",variant:l="default"}=e;return"card"===l?(0,t.jsx)("div",{className:(0,c.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",s),"data-testid":r,children:Array(a).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,t.jsx)(d.E,{className:"aspect-[16/10] w-full"}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)(d.E,{className:"mb-1 h-7 w-3/4"}),(0,t.jsx)(d.E,{className:"mb-3 h-4 w-1/2"}),(0,t.jsx)(d.E,{className:"my-3 h-px w-full"}),(0,t.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.E,{className:"mr-2.5 size-5 rounded-full"}),(0,t.jsx)(d.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===l?(0,t.jsxs)("div",{className:(0,c.cn)("space-y-3",s),"data-testid":r,children:[(0,t.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,t.jsx)(d.E,{className:"h-8 flex-1"},s))}),Array(a).fill(0).map((e,s)=>(0,t.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,t.jsx)(d.E,{className:"h-6 flex-1"},s))},s))]}):"list"===l?(0,t.jsx)("div",{className:(0,c.cn)("space-y-3",s),"data-testid":r,children:Array(a).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(d.E,{className:"size-12 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)(d.E,{className:"h-4 w-1/3"}),(0,t.jsx)(d.E,{className:"h-4 w-full"})]})]},s))}):"stats"===l?(0,t.jsx)("div",{className:(0,c.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",s),"data-testid":r,children:Array(a).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(d.E,{className:"h-5 w-1/3"}),(0,t.jsx)(d.E,{className:"size-5 rounded-full"})]}),(0,t.jsx)(d.E,{className:"mt-3 h-8 w-1/2"}),(0,t.jsx)(d.E,{className:"mt-2 h-4 w-2/3"})]},s))}):(0,t.jsx)("div",{className:(0,c.cn)("space-y-2",s),"data-testid":r,children:Array(a).fill(0).map((e,s)=>(0,t.jsx)(d.E,{className:"h-5 w-full"},s))})}},83506:(e,s,a)=>{a.d(s,{BZ:()=>C,K:()=>w,Wy:()=>k,YB:()=>N,YW:()=>j,ZI:()=>b,e7:()=>S,nh:()=>z,vk:()=>y,yX:()=>A});var t=a(95155),r=a(41784),l=a(3561),n=a(17607),i=a(18763),d=a(44956),c=a(20203),o=a(73926),m=a(27150),u=a(6874),f=a.n(u),x=a(26126),p=a(30285),h=a(47262),g=a(44838),v=a(54036);let j=(e,s,a)=>({createActionsColumn:a=>({cell:e=>{var r;let{row:c}=e,o=c.original;return(0,t.jsxs)(g.rI,{children:[(0,t.jsx)(g.ty,{asChild:!0,children:(0,t.jsxs)(p.$,{className:"size-8 p-0",variant:"ghost",children:[(0,t.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,t.jsx)(l.A,{className:"size-4"})]})}),(0,t.jsxs)(g.SQ,{align:"end",children:[(0,t.jsx)(g.lp,{children:s("actions")}),(a.onView||a.viewHref)&&(0,t.jsx)(t.Fragment,{children:a.viewHref?(0,t.jsx)(g._2,{asChild:!0,children:(0,t.jsxs)(f(),{href:a.viewHref(o),children:[(0,t.jsx)(n.A,{className:"mr-2 size-4"}),s("viewDetails")]})}):(0,t.jsxs)(g._2,{onClick:()=>{var e;return null==(e=a.onView)?void 0:e.call(a,o)},children:[(0,t.jsx)(n.A,{className:"mr-2 size-4"}),s("viewDetails")]})}),(a.onEdit||a.editHref)&&(0,t.jsx)(t.Fragment,{children:a.editHref?(0,t.jsx)(g._2,{asChild:!0,children:(0,t.jsxs)(f(),{href:a.editHref(o),children:[(0,t.jsx)(i.A,{className:"mr-2 size-4"}),s("edit")]})}):(0,t.jsxs)(g._2,{onClick:()=>{var e;return null==(e=a.onEdit)?void 0:e.call(a,o)},children:[(0,t.jsx)(i.A,{className:"mr-2 size-4"}),s("edit")]})}),null==(r=a.customActions)?void 0:r.map((e,a)=>(0,t.jsxs)(g._2,{className:"destructive"===e.variant?"text-destructive":"",onClick:()=>e.onClick(o),children:[e.icon&&(0,t.jsx)(e.icon,{className:"mr-2 size-4"}),s(e.labelKey)]},a)),a.showCopyId&&(0,t.jsx)(g._2,{onClick:()=>navigator.clipboard.writeText(String(o.id)),children:s("copyId")}),a.onDelete&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.mB,{}),(0,t.jsxs)(g._2,{className:"text-destructive",onClick:()=>{var e;return null==(e=a.onDelete)?void 0:e.call(a,o)},children:[(0,t.jsx)(d.A,{className:"mr-2 size-4"}),s("delete")]})]})]})]})},header:e("actions"),id:"actions"}),createDateColumn:function(s,a){let l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"MMM dd, yyyy";return{accessorKey:s,cell:e=>{let{getValue:s}=e,a=s();if(!a)return"-";try{let e="string"==typeof a?new Date(a):a;return(0,r.GP)(e,l)}catch(e){return"-"}},header:s=>{let{column:r}=s,l=r.getIsSorted();return(0,t.jsxs)(p.$,{className:"h-auto p-0 font-semibold hover:bg-transparent",onClick:()=>r.toggleSorting("asc"===r.getIsSorted()),variant:"ghost",children:[e(a),"asc"===l?(0,t.jsx)(c.A,{className:"ml-2 size-4"}):"desc"===l?(0,t.jsx)(o.A,{className:"ml-2 size-4"}):(0,t.jsx)(m.A,{className:"ml-2 size-4 opacity-50"})]})}}},createSortableHeader:s=>a=>{let{column:r}=a,l=r.getIsSorted();return(0,t.jsxs)(p.$,{className:"h-auto p-0 font-semibold hover:bg-transparent",onClick:()=>r.toggleSorting("asc"===r.getIsSorted()),variant:"ghost",children:[e(s),"asc"===l?(0,t.jsx)(c.A,{className:"ml-2 size-4"}):"desc"===l?(0,t.jsx)(o.A,{className:"ml-2 size-4"}):(0,t.jsx)(m.A,{className:"ml-2 size-4 opacity-50"})]})},createStatusColumn:(s,r,l)=>({accessorKey:s,cell:e=>{let{getValue:s}=e,r=s();if(!r)return"-";let n=(null==l?void 0:l[r])||{variant:"secondary"},i=n.label||a(r.toLowerCase());return(0,t.jsx)(x.E,{variant:n.variant,children:i})},header:s=>{let{column:a}=s,l=a.getIsSorted();return(0,t.jsxs)(p.$,{className:"h-auto p-0 font-semibold hover:bg-transparent",onClick:()=>a.toggleSorting("asc"===a.getIsSorted()),variant:"ghost",children:[e(r),"asc"===l?(0,t.jsx)(c.A,{className:"ml-2 size-4"}):"desc"===l?(0,t.jsx)(o.A,{className:"ml-2 size-4"}):(0,t.jsx)(m.A,{className:"ml-2 size-4 opacity-50"})]})}}),createTextColumn:(s,a,r)=>({accessorKey:s,cell:e=>{let{getValue:s}=e,a=s();if(!a)return"-";let l=(null==r?void 0:r.maxLength)&&a.length>r.maxLength?"".concat(a.slice(0,r.maxLength),"..."):a;return(0,t.jsx)("span",{className:(0,v.cn)("line-clamp-2",null==r?void 0:r.className),title:a,children:l})},header:s=>{let{column:r}=s,l=r.getIsSorted();return(0,t.jsxs)(p.$,{className:"h-auto p-0 font-semibold hover:bg-transparent",onClick:()=>r.toggleSorting("asc"===r.getIsSorted()),variant:"ghost",children:[e(a),"asc"===l?(0,t.jsx)(c.A,{className:"ml-2 size-4"}):"desc"===l?(0,t.jsx)(o.A,{className:"ml-2 size-4"}):(0,t.jsx)(m.A,{className:"ml-2 size-4 opacity-50"})]})}})}),N=e=>s=>{let{column:a}=s,r=a.getIsSorted();return(0,t.jsxs)(p.$,{className:"h-auto p-0 font-semibold hover:bg-transparent",onClick:()=>a.toggleSorting("asc"===a.getIsSorted()),variant:"ghost",children:[e,"asc"===r?(0,t.jsx)(c.A,{className:"ml-2 size-4"}):"desc"===r?(0,t.jsx)(o.A,{className:"ml-2 size-4"}):(0,t.jsx)(m.A,{className:"ml-2 size-4 opacity-50"})]})},y=function(e,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"MMM dd, yyyy";return{accessorKey:e,cell:e=>{let{getValue:s}=e,t=s();if(!t)return"-";try{let e="string"==typeof t?new Date(t):t;return(0,r.GP)(e,a)}catch(e){return"-"}},header:N(s)}},b=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Status",a=arguments.length>2?arguments[2]:void 0;return{accessorKey:e,cell:e=>{let{getValue:s}=e,r=s();if(!r)return"-";let l=(null==a?void 0:a[r])||{variant:"secondary"},n=l.label||r;return(0,t.jsx)(x.E,{variant:l.variant,children:n})},header:N(s)}},w=(e,s,a)=>({accessorKey:e,cell:e=>{let{getValue:s}=e,r=s();if(!r)return"-";let l=(null==a?void 0:a.maxLength)&&r.length>a.maxLength?"".concat(r.slice(0,Math.max(0,a.maxLength)),"..."):r;return(0,t.jsx)("span",{className:null==a?void 0:a.className,title:r,children:l})},header:N(s)}),A=(e,s,a)=>({accessorKey:e,cell:e=>{var s,r;let{getValue:l}=e,n=l(),i="string"==typeof n?Number.parseFloat(n):n;if(null==i||isNaN(i))return"-";let d=i.toString();return(null==a?void 0:a.format)==="currency"?d=new Intl.NumberFormat("en-US",{currency:"USD",minimumFractionDigits:null!=(s=a.decimals)?s:2,style:"currency"}).format(i):(null==a?void 0:a.format)==="percentage"?d="".concat((100*i).toFixed(null!=(r=a.decimals)?r:1),"%"):(null==a?void 0:a.decimals)!==void 0&&(d=i.toFixed(a.decimals)),(null==a?void 0:a.prefix)&&(d=a.prefix+d),(null==a?void 0:a.suffix)&&(d+=a.suffix),(0,t.jsx)("span",{className:"font-mono",children:d})},header:N(s)}),C=()=>({cell:e=>{let{row:s}=e;return(0,t.jsx)(h.S,{"aria-label":"Select row",checked:s.getIsSelected(),onCheckedChange:e=>s.toggleSelected(!!e)})},enableHiding:!1,enableSorting:!1,header:e=>{let{table:s}=e;return(0,t.jsx)(h.S,{"aria-label":"Select all",checked:s.getIsAllPageRowsSelected()||s.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:e=>s.toggleAllPageRowsSelected(!!e)})},id:"select"}),z=(e,s,a,r)=>({accessorKey:e,cell:s=>{let{row:l}=s,n=l.getValue(e),i=(null==r?void 0:r.formatter)?r.formatter(n):n;return(0,t.jsxs)("div",{className:(0,v.cn)("flex items-center justify-center gap-1 text-sm",null==r?void 0:r.className),children:[(0,t.jsx)(a,{className:"size-3 text-muted-foreground"}),String(i)]})},header:N(s)}),k=(e,s)=>({cell:a=>{var r;let{row:c}=a,o=c.original;return(0,t.jsxs)(g.rI,{children:[(0,t.jsx)(g.ty,{asChild:!0,children:(0,t.jsxs)(p.$,{className:"size-8 p-0",variant:"ghost",children:[(0,t.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,t.jsx)(l.A,{className:"size-4"})]})}),(0,t.jsxs)(g.SQ,{align:"end",children:[(0,t.jsx)(g.lp,{children:(null==s?void 0:s.actions)||"Actions"}),(e.onView||e.viewHref)&&(0,t.jsx)(t.Fragment,{children:e.viewHref?(0,t.jsx)(g._2,{asChild:!0,children:(0,t.jsxs)(f(),{href:e.viewHref(o),children:[(0,t.jsx)(n.A,{className:"mr-2 size-4"}),(null==s?void 0:s.viewDetails)||"View Details"]})}):(0,t.jsxs)(g._2,{onClick:()=>{var s;return null==(s=e.onView)?void 0:s.call(e,o)},children:[(0,t.jsx)(n.A,{className:"mr-2 size-4"}),(null==s?void 0:s.viewDetails)||"View Details"]})}),(e.onEdit||e.editHref)&&(0,t.jsx)(t.Fragment,{children:e.editHref?(0,t.jsx)(g._2,{asChild:!0,children:(0,t.jsxs)(f(),{href:e.editHref(o),children:[(0,t.jsx)(i.A,{className:"mr-2 size-4"}),(null==s?void 0:s.edit)||"Edit"]})}):(0,t.jsxs)(g._2,{onClick:()=>{var s;return null==(s=e.onEdit)?void 0:s.call(e,o)},children:[(0,t.jsx)(i.A,{className:"mr-2 size-4"}),(null==s?void 0:s.edit)||"Edit"]})}),null==(r=e.customActions)?void 0:r.map((e,s)=>(0,t.jsxs)(g._2,{className:"destructive"===e.variant?"text-destructive":"",onClick:()=>e.onClick(o),children:[e.icon&&(0,t.jsx)(e.icon,{className:"mr-2 size-4"}),e.label]},s)),e.showCopyId&&(0,t.jsx)(g._2,{onClick:()=>navigator.clipboard.writeText(String(o.id)),children:"Copy ID"}),e.onDelete&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.mB,{}),(0,t.jsxs)(g._2,{className:"text-destructive",onClick:()=>{var s;return null==(s=e.onDelete)?void 0:s.call(e,o)},children:[(0,t.jsx)(d.A,{className:"mr-2 size-4"}),(null==s?void 0:s.delete)||"Delete"]})]})]})]})},header:(null==s?void 0:s.actions)||"Actions",id:"actions"}),S=e=>({delegation:{Cancelled:{label:e("cancelled"),variant:"destructive"},Completed:{label:e("completed"),variant:"success"},"In Progress":{label:e("inProgress"),variant:"default"},Planned:{label:e("planned"),variant:"secondary"}},employee:{Active:{label:e("active"),variant:"success"},Inactive:{label:e("inactive"),variant:"secondary"},"On Leave":{label:e("onLeave"),variant:"warning"}},task:{Assigned:{label:e("assigned"),variant:"default"},Cancelled:{label:e("cancelled"),variant:"destructive"},Completed:{label:e("completed"),variant:"success"},In_Progress:{label:e("inProgress"),variant:"default"},Overdue:{label:e("overdue"),variant:"destructive"},Pending:{label:e("pending"),variant:"secondary"}}})},85127:(e,s,a)=>{a.d(s,{A0:()=>i,BF:()=>d,Hj:()=>c,XI:()=>n,nA:()=>m,nd:()=>o});var t=a(95155),r=a(12115),l=a(54036);let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{className:(0,l.cn)("w-full caption-bottom text-sm",a),ref:s,...r})})});n.displayName="Table";let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("thead",{className:(0,l.cn)("[&_tr]:border-b",a),ref:s,...r})});i.displayName="TableHeader";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tbody",{className:(0,l.cn)("[&_tr:last-child]:border-0",a),ref:s,...r})});d.displayName="TableBody",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tfoot",{className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),ref:s,...r})}).displayName="TableFooter";let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tr",{className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),ref:s,...r})});c.displayName="TableRow";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("th",{className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),ref:s,...r})});o.displayName="TableHead";let m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("td",{className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),ref:s,...r})});m.displayName="TableCell",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("caption",{className:(0,l.cn)("mt-4 text-sm text-muted-foreground",a),ref:s,...r})}).displayName="TableCaption"}}]);