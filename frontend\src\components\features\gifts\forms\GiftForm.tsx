'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Save } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import type { CreateGiftData, Gift, UpdateGiftData } from '@/lib/types/domain';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

// Create validation schema with translations
const createGiftFormSchema = (
  tValidation: (key: string, values?: any) => string
) =>
  z.object({
    dateSent: z.string().min(1, tValidation('required')),
    itemDescription: z
      .string()
      .min(1, tValidation('required'))
      .max(500, tValidation('maxLength', { max: 500 })),
    notes: z
      .string()
      .max(1000, tValidation('maxLength', { max: 1000 }))
      .default(''),
    occasion: z
      .string()
      .max(100, tValidation('maxLength', { max: 100 }))
      .default(''),
    recipientId: z.string().min(1, tValidation('required')),
    senderName: z
      .string()
      .min(1, tValidation('required'))
      .max(255, tValidation('maxLength', { max: 255 })),
  });

type GiftFormData = z.infer<ReturnType<typeof createGiftFormSchema>>;

interface GiftFormProps {
  initialData?: Partial<Gift>;
  isEditing?: boolean;
  isLoading?: boolean;
  onSubmit: (data: CreateGiftData | UpdateGiftData) => Promise<void>;
  recipients?: {
    id: string;
    name: string;
    role?: string | null;
    worksite?: string | null;
  }[];
}

// Religious and National occasions for quick selection
const commonOccasionKeys = [
  'eidAlFitr',
  'eidAlAdha',
  'hijraNewYear',
  'eidAlMawlid',
  'newYear',
  'independenceManifesto',
  'amazighNewYear',
  'labourDay',
  'feastOfThrone',
  'ouedEdDahabRecovery',
  'revolutionKingPeople',
  'youthDay',
  'greenMarch',
  'independenceDay',
];

export const GiftForm: React.FC<GiftFormProps> = ({
  initialData,
  isEditing = false,
  isLoading = false,
  onSubmit,
  recipients = [],
}) => {
  const router = useRouter();
  const tLabels = useTranslations('forms.labels');
  const tButtons = useTranslations('forms.buttons');
  const tPlaceholders = useTranslations('forms.placeholders');
  const tTitles = useTranslations('forms.titles');
  const tDescriptions = useTranslations('forms.descriptions');
  const tOccasions = useTranslations('forms.occasions');
  const tValidation = useTranslations('forms.validation');

  const giftFormSchema = createGiftFormSchema(tValidation);

  const form = useForm({
    defaultValues: {
      dateSent: (initialData?.dateSent
        ? initialData.dateSent.split('T')[0]
        : new Date().toISOString().split('T')[0]) as string,
      itemDescription: initialData?.itemDescription ?? '',
      notes: initialData?.notes ?? '',
      occasion: initialData?.occasion ?? '',
      recipientId: initialData?.recipientId ?? '',
      senderName: initialData?.senderName ?? '',
    },
    resolver: zodResolver(giftFormSchema),
  });

  const handleSubmit = async (data: GiftFormData) => {
    try {
      // Convert date to ISO string
      const formattedData = {
        ...data,
        dateSent: new Date(data.dateSent).toISOString(),
        notes: data.notes || null,
        occasion: data.occasion || null,
      };

      await onSubmit(formattedData);
    } catch (error) {
      console.error('Error submitting gift form:', error);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <Card className="mx-auto max-w-2xl">
      <CardHeader>
        <div className="flex items-center gap-4">
          <Button
            className="p-2"
            onClick={handleCancel}
            size="sm"
            variant="ghost"
          >
            <ArrowLeft className="size-4" />
          </Button>
          <CardTitle>
            {isEditing ? tTitles('editGift') : tTitles('addGift')}
          </CardTitle>
        </div>
      </CardHeader>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardContent className="space-y-6">
            {/* Item Description */}
            <FormField
              control={form.control}
              name="itemDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('itemDescription')} *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={tPlaceholders('enterDescription')}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Recipient */}
            <FormField
              control={form.control}
              name="recipientId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('recipient')} *</FormLabel>
                  <Select
                    defaultValue={field.value}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={tPlaceholders('selectRecipient')}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {recipients.map(recipient => (
                        <SelectItem key={recipient.id} value={recipient.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {recipient.name}
                            </span>
                            {(recipient.role || recipient.worksite) && (
                              <span className="text-xs text-muted-foreground">
                                {[recipient.role, recipient.worksite]
                                  .filter(Boolean)
                                  .join(' • ')}
                              </span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {tDescriptions('chooseRecipient')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date Sent */}
            <FormField
              control={form.control}
              name="dateSent"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('dateSent')} *</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormDescription>
                    {tDescriptions('whenGiftSent')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Sender Name */}
            <FormField
              control={form.control}
              name="senderName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('senderName')} *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={tPlaceholders('whoSentThisGift')}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {tDescriptions('senderNameDesc')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Occasion */}
            <FormField
              control={form.control}
              name="occasion"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('occasion')}</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={tPlaceholders('selectOccasionOptional')}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {commonOccasionKeys.map(occasionKey => (
                        <SelectItem
                          key={occasionKey}
                          value={tOccasions(occasionKey)}
                        >
                          {tOccasions(occasionKey)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {tDescriptions('giftOccasion')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tLabels('notes')}</FormLabel>
                  <FormControl>
                    <Textarea
                      className="min-h-[100px]"
                      placeholder={tPlaceholders('enterGiftNotes')}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {tDescriptions('optionalNotes')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>

          <CardFooter className="flex justify-end gap-4">
            <Button
              disabled={isLoading}
              onClick={handleCancel}
              type="button"
              variant="outline"
            >
              {tButtons('cancel')}
            </Button>
            <Button
              className="min-w-[100px]"
              disabled={isLoading}
              type="submit"
            >
              {isLoading ? (
                tButtons('saving')
              ) : (
                <>
                  <Save className="mr-2 size-4" />
                  {isEditing ? tButtons('updateGift') : tButtons('saveGift')}
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
};
