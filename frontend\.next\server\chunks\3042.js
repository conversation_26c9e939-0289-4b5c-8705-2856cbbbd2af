"use strict";exports.id=3042,exports.ids=[3042],exports.modules={15795:(e,s,a)=>{function t(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function l(e){if(e.fullName?.trim())return e.fullName.trim();if(e.name?.trim()){let s=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(s.toLowerCase())||s.includes("_")){let e=s.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${e} (Role)`}return s}if(e.role){let s=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${s} (Role)`}return"Unknown Employee"}function r(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function i(e){return e.replaceAll("_"," ")}a.d(s,{DV:()=>l,fZ:()=>t,s:()=>r,vq:()=>i})},18116:(e,s,a)=>{a.d(s,{A:()=>n});var t=a(60687),l=a(24851),r=a(43210),i=a(22482);let n=r.forwardRef(({className:e,...s},a)=>(0,t.jsxs)(l.bL,{className:(0,i.cn)("relative flex w-full touch-none select-none items-center",e),ref:a,...s,children:[(0,t.jsx)(l.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,t.jsx)(l.Q6,{className:"absolute h-full bg-primary"})}),(0,t.jsx)(l.zi,{className:"block size-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));n.displayName=l.bL.displayName},25279:(e,s,a)=>{a.d(s,{BZ:()=>t.BZ,K:()=>t.K,Wy:()=>t.Wy,YB:()=>t.YB,ZI:()=>t.ZI,bQ:()=>l.b,nh:()=>t.nh,vk:()=>t.vk,yX:()=>t.yX});var t=a(19484),l=a(95009);a(6211)},26373:(e,s,a)=>{a.d(s,{V:()=>o});var t=a(60687),l=a(43967),r=a(74158);a(43210);var i=a(16488),n=a(29523),d=a(22482);function o({className:e,classNames:s,showOutsideDays:a=!0,...o}){return(0,t.jsx)(i.hv,{className:(0,d.cn)("p-3",e),classNames:{caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,d.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_range_end:"day-range-end",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",head_row:"flex",month:"space-y-4",months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",nav:"space-x-1 flex items-center",nav_button:(0,d.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_next:"absolute right-1",nav_button_previous:"absolute left-1",row:"flex w-full mt-2",table:"w-full border-collapse space-y-1",...s},components:{IconLeft:({className:e,...s})=>(0,t.jsx)(l.A,{className:(0,d.cn)("h-4 w-4",e),...s}),IconRight:({className:e,...s})=>(0,t.jsx)(r.A,{className:(0,d.cn)("h-4 w-4",e),...s})},showOutsideDays:a,...o})}o.displayName="Calendar"},37392:(e,s,a)=>{a.d(s,{s:()=>v});var t=a(60687),l=a(86447),r=a(3746),i=a(93704),n=a(26622),d=a(36141),o=a(58369),c=a(2093),m=a(77368);a(43210);var u=a(29523),f=a(80013),x=a(50812),p=a(18116),h=a(54987),b=a(85763);let g={cards:{icon:l.A,label:"Cards"},table:{icon:r.A,label:"Table"},list:{icon:i.A,label:"List"},calendar:{icon:n.A,label:"Calendar"},grid:{icon:d.A,label:"Grid"}},v=({config:e,entityType:s,layout:a,monitoring:r,setViewMode:i,setGridColumns:n,toggleCompactMode:d,setMonitoringEnabled:v,setRefreshInterval:y,toggleAutoRefresh:j,resetSettings:N,className:w=""})=>{let A=e.viewModes||["cards","table","list"];return(0,t.jsxs)("div",{className:`space-y-6 p-4 ${w}`,children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"size-6"}),e.title," Settings"]}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Customize your ",s," dashboard experience"]})]}),(0,t.jsxs)(b.tU,{className:"w-full",defaultValue:"layout",children:[(0,t.jsxs)(b.j7,{className:"grid w-full grid-cols-3",children:[(0,t.jsx)(b.Xi,{value:"layout",children:"Layout"}),(0,t.jsx)(b.Xi,{value:"display",children:"Display"}),(0,t.jsx)(b.Xi,{value:"refresh",children:"Refresh"})]}),(0,t.jsx)(b.av,{className:"mt-4 space-y-6",value:"layout",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{className:"text-lg font-semibold",children:"View Mode"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Choose how ",s,"s are displayed"]}),(0,t.jsx)(x.z,{className:"grid grid-cols-2 gap-4 pt-2",onValueChange:e=>i(e),value:a.viewMode,children:A.map(e=>{let s=g[e]?.icon||l.A,a=g[e]?.label||e;return(0,t.jsxs)(f.J,{className:"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary cursor-pointer",htmlFor:`layout-${e}`,children:[(0,t.jsx)(x.C,{className:"sr-only",id:`layout-${e}`,value:e}),(0,t.jsx)(s,{className:"mb-3 size-6"}),a]},e)})})]}),("cards"===a.viewMode||"grid"===a.viewMode)&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(f.J,{className:"text-lg font-semibold",children:["Grid Columns: ",a.gridColumns]}),(0,t.jsx)(p.A,{defaultValue:[a.gridColumns],max:6,min:1,onValueChange:([e])=>void 0!==e&&n(e),step:1}),(0,t.jsxs)("div",{className:"flex justify-between text-sm text-muted-foreground",children:[(0,t.jsx)("span",{children:"1 column"}),(0,t.jsx)("span",{children:"6 columns"})]})]})]})}),(0,t.jsx)(b.av,{className:"mt-4 space-y-6",value:"display",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(f.J,{className:"text-lg font-semibold",children:"Compact Mode"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Show more ",s,"s in less space"]})]}),(0,t.jsx)(h.d,{checked:a.compactMode,onCheckedChange:d})]}),e.enableBulkActions&&(0,t.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(f.J,{className:"text-lg font-semibold",children:"Bulk Actions"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable selection and bulk operations"})]}),(0,t.jsx)(h.d,{checked:!0,disabled:!0})]})]})}),(0,t.jsx)(b.av,{className:"mt-4 space-y-6",value:"refresh",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsxs)(f.J,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"size-4"}),"Auto Refresh"]}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Automatically refresh ",s," data"]})]}),(0,t.jsx)(h.d,{checked:r.autoRefresh,onCheckedChange:j})]}),r.autoRefresh&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(f.J,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"size-4"}),"Refresh Interval"]}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{label:"5 seconds",value:5e3},{label:"10 seconds",value:1e4},{label:"30 seconds",value:3e4},{label:"1 minute",value:6e4},{label:"5 minutes",value:3e5}].map(e=>(0,t.jsx)(u.$,{variant:r.refreshInterval===e.value?"default":"outline",size:"sm",onClick:()=>y(e.value),children:e.label},e.value))})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(f.J,{className:"text-lg font-semibold",children:"Real-time Updates"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable live data updates"})]}),(0,t.jsx)(h.d,{checked:r.enabled,onCheckedChange:v})]})]})})]}),(0,t.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,t.jsx)(u.$,{onClick:N,variant:"outline",children:"Reset to Defaults"})})]})}},40988:(e,s,a)=>{a.d(s,{AM:()=>n,Wv:()=>d,hl:()=>o});var t=a(60687),l=a(40599),r=a(43210),i=a(22482);let n=l.bL,d=l.l9;l.bm;let o=r.forwardRef(({align:e="center",className:s,sideOffset:a=4,...r},n)=>(0,t.jsx)(l.ZL,{children:(0,t.jsx)(l.UC,{align:e,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",s),ref:n,sideOffset:a,...r})}));o.displayName=l.UC.displayName},48041:(e,s,a)=>{a.d(s,{z:()=>l});var t=a(60687);function l({children:e,description:s,icon:a,title:l}){return(0,t.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[a&&(0,t.jsx)(a,{className:"size-8 text-primary"}),(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:l})]}),s&&(0,t.jsx)("p",{className:"mt-1 text-muted-foreground",children:s})]}),e&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:e})]})}a(43210)},50812:(e,s,a)=>{a.d(s,{C:()=>o,z:()=>d});var t=a(60687),l=a(14555),r=a(73256),i=a(43210),n=a(22482);let d=i.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.bL,{className:(0,n.cn)("grid gap-2",e),...s,ref:a}));d.displayName=l.bL.displayName;let o=i.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.q7,{className:(0,n.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s,children:(0,t.jsx)(l.C1,{className:"flex items-center justify-center",children:(0,t.jsx)(r.A,{className:"size-2.5 fill-current text-current"})})}));o.displayName=l.q7.displayName},67146:(e,s,a)=>{a.d(s,{CG:()=>c,Fm:()=>p,Qs:()=>b,cj:()=>o,h:()=>x,qp:()=>h});var t=a(60687),l=a(26134),r=a(24224),i=a(78726),n=a(43210),d=a(22482);let o=l.bL,c=l.l9;l.bm;let m=l.ZL,u=n.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.hJ,{className:(0,d.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s,ref:a}));u.displayName=l.hJ.displayName;let f=(0,r.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{defaultVariants:{side:"right"},variants:{side:{bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm",top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top"}}}),x=n.forwardRef(({children:e,className:s,side:a="right",...r},n)=>(0,t.jsxs)(m,{children:[(0,t.jsx)(u,{}),(0,t.jsxs)(l.UC,{className:(0,d.cn)(f({side:a}),s),ref:n,...r,children:[e,(0,t.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,t.jsx)(i.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));x.displayName=l.UC.displayName;let p=({className:e,...s})=>(0,t.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...s});p.displayName="SheetHeader";let h=n.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.hE,{className:(0,d.cn)("text-lg font-semibold text-foreground",e),ref:a,...s}));h.displayName=l.hE.displayName;let b=n.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.VY,{className:(0,d.cn)("text-sm text-muted-foreground",e),ref:a,...s}));b.displayName=l.VY.displayName},69981:(e,s,a)=>{a.d(s,{M:()=>o});var t=a(60687),l=a(36644),r=a(60368),i=a(85814),n=a.n(i);a(43210);var d=a(68752);function o({className:e,getReportUrl:s,href:a,isList:i=!1}){if(!a&&!s)return console.error("ViewReportButton requires either href or getReportUrl prop"),null;let o=i?"View List Report":"View Report";return a?(0,t.jsx)(d.r,{actionType:"secondary",asChild:!0,className:e,icon:(0,t.jsx)(l.A,{className:"size-4"}),children:(0,t.jsxs)(n(),{href:a,rel:"noopener noreferrer",target:"_blank",children:[o,(0,t.jsx)(r.A,{"aria-hidden":"true",className:"ml-1.5 inline-block size-3"}),(0,t.jsx)("span",{className:"sr-only",children:"(opens in new tab)"})]})}):(0,t.jsxs)(d.r,{actionType:"secondary",className:e,icon:(0,t.jsx)(l.A,{className:"size-4"}),onClick:()=>{if(s){let e=s();window.open(e,"_blank","noopener,noreferrer")}},children:[o,(0,t.jsx)(r.A,{"aria-hidden":"true",className:"ml-1.5 inline-block size-3"})]})}},72273:(e,s,a)=>{a.d(s,{NS:()=>x,T$:()=>c,W_:()=>m,Y1:()=>u,lR:()=>f});var t=a(8693),l=a(54050),r=a(46349),i=a(87676),n=a(48839),d=a(75176);let o={all:["vehicles"],detail:e=>["vehicles",e]},c=e=>(0,r.GK)([...o.all],async()=>(await d.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),m=(e,s)=>(0,r.GK)([...o.detail(e)],()=>d.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(s?.enabled??!0),staleTime:3e5,...s}),u=()=>{let e=(0,t.jE)(),{showError:s,showSuccess:a}=(0,i.useNotifications)();return(0,l.n)({mutationFn:e=>{let s=n.M.toCreateRequest(e);return d.vehicleApiService.create(s)},onError:e=>{s(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:s=>{e.invalidateQueries({queryKey:o.all}),a(`Vehicle "${s.licensePlate}" has been created successfully!`)}})},f=()=>{let e=(0,t.jE)(),{showError:s,showSuccess:a}=(0,i.useNotifications)();return(0,l.n)({mutationFn:({data:e,id:s})=>{let a=n.M.toUpdateRequest(e);return d.vehicleApiService.update(s,a)},onError:e=>{s(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:s=>{e.invalidateQueries({queryKey:o.all}),e.invalidateQueries({queryKey:o.detail(s.id)}),a(`Vehicle "${s.licensePlate}" has been updated successfully!`)}})},x=()=>{let e=(0,t.jE)(),{showError:s,showSuccess:a}=(0,i.useNotifications)();return(0,l.n)({mutationFn:e=>d.vehicleApiService.delete(e),onError:e=>{s(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(s,t)=>{e.invalidateQueries({queryKey:o.all}),e.removeQueries({queryKey:o.detail(t)}),a("Vehicle has been deleted successfully!")}})}}};