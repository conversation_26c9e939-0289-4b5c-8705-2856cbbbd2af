'use client';

import { Globe, Languages } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import React from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Locale configuration following DRY principle
const locales = [
  { code: 'en-US', name: 'English', flag: '🇺🇸' },
  { code: 'ar-IQ', name: 'العربية', flag: '🇮🇶' },
] as const;

type LocaleCode = (typeof locales)[number]['code'];

interface LanguageSwitcherProps {
  /**
   * Variant of the trigger button
   */
  variant?: 'default' | 'outline' | 'ghost' | 'link';
  /**
   * Size of the trigger button
   */
  size?: 'default' | 'sm' | 'lg' | 'icon';
  /**
   * Whether to show only the icon or include text
   */
  iconOnly?: boolean;
  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Language Switcher Component
 *
 * Single Responsibility: Handles language switching functionality only
 * DRY: Reusable component that can be placed anywhere in the app
 * Separation of Concerns: UI logic separate from routing logic
 */
export function LanguageSwitcher({
  variant = 'ghost',
  size = 'default',
  iconOnly = false,
  className,
}: LanguageSwitcherProps) {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();
  const t = useTranslations('common');

  const currentLocale = (params.locale as LocaleCode) || 'en-US';
  const currentLocaleInfo =
    locales.find(locale => locale.code === currentLocale) || locales[0];

  /**
   * Switch to a new locale while preserving the current path
   */
  const switchLocale = (newLocale: LocaleCode) => {
    if (newLocale === currentLocale) return;

    // Remove current locale from pathname to get the path without locale
    const pathWithoutLocale = pathname.replace(`/${currentLocale}`, '') || '/';

    // Navigate to the same path with the new locale
    const newPath = `/${newLocale}${pathWithoutLocale}`;
    router.push(newPath);
  };

  const triggerContent = iconOnly ? (
    <Globe className="h-4 w-4" />
  ) : (
    <>
      <Languages className="h-4 w-4" />
      <span className="hidden sm:inline-block">{currentLocaleInfo.name}</span>
      <span className="sm:hidden">{currentLocaleInfo.flag}</span>
    </>
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={className}
          aria-label={t('settings')}
        >
          {triggerContent}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {locales.map(locale => (
          <DropdownMenuItem
            key={locale.code}
            onClick={() => switchLocale(locale.code)}
            className={`flex items-center gap-2 ${
              currentLocale === locale.code ? 'bg-accent' : ''
            }`}
          >
            <span className="text-lg">{locale.flag}</span>
            <span>{locale.name}</span>
            {currentLocale === locale.code && (
              <span className="ml-auto text-xs text-muted-foreground">✓</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
