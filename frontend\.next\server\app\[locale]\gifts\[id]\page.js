(()=>{var e={};e.id=7989,e.ids=[7989],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7489:(e,t,s)=>{"use strict";s.d(t,{HJ:()=>x,Kv:()=>p,N:()=>m,Zj:()=>d,oe:()=>u,xj:()=>h});var r=s(43612),i=s(8693),a=s(54050),n=s(46349),o=s(87676),l=s(3302);let c={all:["gifts"],lists:()=>[...c.all,"list"],list:e=>[...c.lists(),{filters:e}],details:()=>[...c.all,"detail"],detail:e=>[...c.details(),e],byRecipient:e=>[...c.all,"recipient",e],byOccasion:e=>[...c.all,"occasion",e],bySender:e=>[...c.all,"sender",e],recent:()=>[...c.all,"recent"]},d=(e,t)=>(0,n.GK)(c.list(e||{}),()=>l.Oo.getAll(e).then(e=>e.data),"gift",{staleTime:3e5,...t}),u=(e,t)=>(0,n.GK)(c.detail(e),()=>l.Oo.getById(e),"gift",{enabled:!!e&&(t?.enabled??!0),staleTime:3e5,...t}),m=(e,t)=>(0,r.I)({queryKey:c.byRecipient(e),queryFn:()=>l.Oo.getByRecipient(e),enabled:!!e,staleTime:3e5,...t}),p=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:s}=(0,o.useNotifications)();return(0,a.n)({mutationFn:e=>l.Oo.create(e),onSuccess:t=>{e.invalidateQueries({queryKey:c.all}),t.recipientId&&e.invalidateQueries({queryKey:["recipients","detail",t.recipientId]}),s("Gift created successfully")},onError:e=>{t(`Failed to create gift: ${e.message}`)}})},h=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:s}=(0,o.useNotifications)();return(0,a.n)({mutationFn:({id:e,data:t})=>l.Oo.update(e,t),onSuccess:t=>{e.setQueryData(c.detail(t.id),t),e.invalidateQueries({queryKey:c.lists()}),t.recipientId&&e.invalidateQueries({queryKey:["recipients","detail",t.recipientId]}),s("Gift updated successfully")},onError:e=>{t(`Failed to update gift: ${e.message}`)}})},x=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:s}=(0,o.useNotifications)();return(0,a.n)({mutationFn:e=>l.Oo.delete(e),onSuccess:(t,r)=>{e.removeQueries({queryKey:c.detail(r)}),e.invalidateQueries({queryKey:c.lists()}),e.invalidateQueries({queryKey:["recipients"]}),s("Gift deleted successfully")},onError:e=>{t(`Failed to delete gift: ${e.message}`)}})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12662:(e,t,s)=>{"use strict";s.d(t,{AppBreadcrumb:()=>u});var r=s(60687),i=s(85814),a=s.n(i),n=s(16189),o=s(43210),l=s.n(o),c=s(70640),d=s(22482);function u({className:e,homeHref:t="/",homeLabel:s="Dashboard",showContainer:i=!0}){let o=(0,n.usePathname)(),u=o?o.split("/").filter(Boolean):[],m=e=>{if(/^\d+$/.test(e))return`ID: ${e}`;if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let t={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return t[e]?t[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},p=u.map((e,t)=>{let s="/"+u.slice(0,t+1).join("/"),i=t===u.length-1,n=m(e);return(0,r.jsxs)(l().Fragment,{children:[(0,r.jsx)(c.BreadcrumbItem,{children:i?(0,r.jsx)(c.BreadcrumbPage,{className:"font-medium text-foreground",children:n}):(0,r.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,r.jsx)(a(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:n})})}),!i&&(0,r.jsx)(c.BreadcrumbSeparator,{})]},s)}),h=(0,r.jsx)(c.Breadcrumb,{className:(0,d.cn)("text-sm",e),children:(0,r.jsxs)(c.BreadcrumbList,{className:"flex-wrap",children:[(0,r.jsx)(c.BreadcrumbItem,{children:(0,r.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,r.jsx)(a(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:s})})}),u.length>0&&(0,r.jsx)(c.BreadcrumbSeparator,{}),p]})});return i?(0,r.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,r.jsx)("div",{className:"flex items-center",children:h})}):h}},17011:(e,t,s)=>{Promise.resolve().then(s.bind(s,24455))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24455:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\gifts\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\gifts\\[id]\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},53963:(e,t,s)=>{Promise.resolve().then(s.bind(s,71569))},54050:(e,t,s)=>{"use strict";s.d(t,{n:()=>d});var r=s(43210),i=s(65406),a=s(33465),n=s(35536),o=s(31212),l=class extends n.Q{#e;#t=void 0;#s;#r;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#a(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#i(),this.#a()}mutate(e,t){return this.#r=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#i(){let e=this.#s?.state??(0,i.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#a(e){a.jG.batch(()=>{if(this.#r&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#r.onSuccess?.(e.data,t,s),this.#r.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#r.onError?.(e.error,t,s),this.#r.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},c=s(8693);function d(e,t){let s=(0,c.jE)(t),[i]=r.useState(()=>new l(s,e));r.useEffect(()=>{i.setOptions(e)},[i,e]);let n=r.useSyncExternalStore(r.useCallback(e=>i.subscribe(a.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),d=r.useCallback((e,t)=>{i.mutate(e,t).catch(o.lQ)},[i]);if(n.error&&(0,o.GU)(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:d,mutateAsync:n.mutate}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57349:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),i=s(48088),a=s(88170),n=s.n(a),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["[locale]",{children:["gifts",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,24455)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\gifts\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\gifts\\[id]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/gifts/[id]/page",pathname:"/[locale]/gifts/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70640:(e,t,s)=>{"use strict";s.d(t,{Breadcrumb:()=>l,BreadcrumbItem:()=>d,BreadcrumbLink:()=>u,BreadcrumbList:()=>c,BreadcrumbPage:()=>m,BreadcrumbSeparator:()=>p});var r=s(60687),i=s(8730),a=s(74158),n=(s(69795),s(43210)),o=s(22482);let l=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("nav",{"aria-label":"breadcrumb",className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),ref:s,...t}));l.displayName="Breadcrumb";let c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("ol",{className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),ref:s,...t}));c.displayName="BreadcrumbList";let d=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("li",{className:(0,o.cn)("inline-flex items-center gap-1.5",e),ref:s,...t}));d.displayName="BreadcrumbItem";let u=n.forwardRef(({asChild:e,className:t,...s},a)=>{let n=e?i.DX:"a";return(0,r.jsx)(n,{className:(0,o.cn)("transition-colors hover:text-foreground",t),ref:a,...s})});u.displayName="BreadcrumbLink";let m=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,o.cn)("font-normal text-foreground",e),ref:s,role:"link",...t}));m.displayName="BreadcrumbPage";let p=({children:e,className:t,...s})=>(0,r.jsx)("span",{"aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",t),role:"presentation",...s,children:e??(0,r.jsx)(a.A,{className:"size-4"})});p.displayName="BreadcrumbSeparator"},71569:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var r=s(60687),i=s(24482),a=s(35137),n=s(57207),o=s(77618),l=s(85814),c=s.n(l),d=s(16189);s(43210);var u=s(68752),m=s(12662),p=s(96834),h=s(44493),x=s(63503),f=s(52027),j=s(48041),g=s(35950),b=s(7489),y=s(51812);function v(){let e=(0,d.useParams)(),t=(0,d.useRouter)(),s=(0,o.c3)("gifts"),l=(0,o.c3)("common"),v=(0,o.c3)("navigation"),N=(0,o.c3)("forms"),S=e.id,{data:C,error:k,isLoading:q}=(0,b.oe)(S),{data:B}=(0,y.$Q)(C?.recipientId||null),{isPending:R,mutateAsync:w}=(0,b.HJ)(),O=async()=>{try{await w(S),t.push("/gifts")}catch(e){console.error("Error deleting gift:",e)}},P=e=>new Date(e).toLocaleDateString();return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(m.AppBreadcrumb,{homeHref:"/",homeLabel:v("dashboard")}),(0,r.jsx)(f.gO,{data:C,emptyComponent:(0,r.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,r.jsx)(i.A,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,r.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:s("giftNotFound")}),(0,r.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:s("giftNotFoundDescription")}),(0,r.jsx)(u.r,{asChild:!0,children:(0,r.jsx)(c(),{href:"../",children:s("backToGifts")})})]}),error:k?.message||null,isLoading:q,loadingComponent:(0,r.jsx)(f.jt,{count:1,variant:"card"}),children:e=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.z,{description:s("giftDetailsDescription"),icon:i.A,title:e.itemDescription,children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.r,{actionType:"secondary",asChild:!0,icon:(0,r.jsx)(a.A,{className:"size-4"}),children:(0,r.jsx)(c(),{href:`/gifts/${S}/edit`,children:N("buttons.edit")})}),(0,r.jsxs)(x.lG,{children:[(0,r.jsx)(x.zM,{asChild:!0,children:(0,r.jsx)(u.r,{actionType:"danger",icon:(0,r.jsx)(n.A,{className:"size-4"}),children:N("buttons.delete")})}),(0,r.jsxs)(x.Cf,{children:[(0,r.jsxs)(x.c7,{children:[(0,r.jsx)(x.L3,{children:s("deleteGiftTitle")}),(0,r.jsx)(x.rr,{children:s("deleteGiftConfirmation",{item:e.itemDescription})})]}),(0,r.jsxs)(x.Es,{children:[(0,r.jsx)(u.r,{actionType:"secondary",children:l("cancel")}),(0,r.jsx)(u.r,{actionType:"danger",isLoading:R,onClick:O,children:N("buttons.delete")})]})]})]})]})}),(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,r.jsxs)(h.Zp,{children:[(0,r.jsx)(h.aR,{children:(0,r.jsx)(h.ZB,{children:s("giftInformation")})}),(0,r.jsxs)(h.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:N("labels.itemDescription")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:e.itemDescription})]}),(0,r.jsx)(g.w,{}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:N("labels.dateSent")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:P(e.dateSent)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:N("labels.senderName")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:e.senderName})]}),e.occasion&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:N("labels.occasion")}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(p.E,{variant:"secondary",children:e.occasion})})]}),e.notes&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:N("labels.notes")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:e.notes})]})]})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsx)(h.aR,{children:(0,r.jsx)(h.ZB,{children:s("recipientInformation")})}),(0,r.jsx)(h.Wu,{className:"space-y-4",children:B?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:N("labels.name")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:(0,r.jsx)(c(),{className:"text-primary hover:underline",href:`/recipients/${B.id}`,children:B.name})})]}),B.email&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:N("labels.email")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:B.email})]}),B.phone&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:N("labels.phone")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:B.phone})]}),B.address&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:N("labels.address")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:B.address})]})]}):(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:s("recipientNotFound")})})]})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsx)(h.aR,{children:(0,r.jsx)(h.ZB,{children:l("metadata")})}),(0,r.jsxs)(h.Wu,{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:l("createdAt")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:P(e.createdAt)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:l("updatedAt")}),(0,r.jsx)("p",{className:"mt-1 text-sm",children:P(e.updatedAt)})]})]})]})]})})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,3871,7048,8390,8739,3302,2936,6866],()=>s(57349));module.exports=r})();