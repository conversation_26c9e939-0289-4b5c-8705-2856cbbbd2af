"use strict";exports.id=9623,exports.ids=[9623],exports.modules={916:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},1950:(e,t,r)=>{r.d(t,{m:()=>u});var n=r(82592),a=r(47138);function i(e,t){let r=(0,a.a)(e),n=(0,a.a)(t),i=r.getTime()-n.getTime();return i<0?-1:i>0?1:i}var s=r(11392),o=r(79186),l=r(46127),c=r(3211),f=r(9903),h=r(79943);function u(e,t){return function(e,t,r){let n,u,d,p=(0,f.q)(),m=r?.locale??p.locale??c.c,g=i(e,t);if(isNaN(g))throw RangeError("Invalid time value");let v=Object.assign({},r,{addSuffix:r?.addSuffix,comparison:g});g>0?(n=(0,a.a)(t),u=(0,a.a)(e)):(n=(0,a.a)(e),u=(0,a.a)(t));let b=function(e,t,r){var n;return(n=void 0,e=>{let t=(n?Math[n]:Math.trunc)(e);return 0===t?0:t})(((0,a.a)(e)-(0,a.a)(t))/1e3)}(u,n),w=Math.round((b-((0,h.G)(u)-(0,h.G)(n))/1e3)/60);if(w<2)if(r?.includeSeconds)if(b<5)return m.formatDistance("lessThanXSeconds",5,v);else if(b<10)return m.formatDistance("lessThanXSeconds",10,v);else if(b<20)return m.formatDistance("lessThanXSeconds",20,v);else if(b<40)return m.formatDistance("halfAMinute",0,v);else if(b<60)return m.formatDistance("lessThanXMinutes",1,v);else return m.formatDistance("xMinutes",1,v);else if(0===w)return m.formatDistance("lessThanXMinutes",1,v);else return m.formatDistance("xMinutes",w,v);if(w<45)return m.formatDistance("xMinutes",w,v);if(w<90)return m.formatDistance("aboutXHours",1,v);if(w<s.F6){let e=Math.round(w/60);return m.formatDistance("aboutXHours",e,v)}if(w<2520)return m.formatDistance("xDays",1,v);else if(w<s.Nw){let e=Math.round(w/s.F6);return m.formatDistance("xDays",e,v)}else if(w<2*s.Nw)return d=Math.round(w/s.Nw),m.formatDistance("aboutXMonths",d,v);if((d=function(e,t){let r,n=(0,a.a)(e),s=(0,a.a)(t),c=i(n,s),f=Math.abs((0,o.U)(n,s));if(f<1)r=0;else{1===n.getMonth()&&n.getDate()>27&&n.setDate(30),n.setMonth(n.getMonth()-c*f);let t=i(n,s)===-c;(function(e){let t=(0,a.a)(e);return+function(e){let t=(0,a.a)(e);return t.setHours(23,59,59,999),t}(t)==+(0,l.p)(t)})((0,a.a)(e))&&1===f&&1===i(e,s)&&(t=!1),r=c*(f-Number(t))}return 0===r?0:r}(u,n))<12){let e=Math.round(w/s.Nw);return m.formatDistance("xMonths",e,v)}{let e=d%12,t=Math.trunc(d/12);return e<3?m.formatDistance("aboutXYears",t,v):e<9?m.formatDistance("overXYears",t,v):m.formatDistance("almostXYears",t+1,v)}}(e,(0,n.A)(e),t)}},3341:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ChartLine",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},3746:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},5068:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},14328:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]])},14769:(e,t,r)=>{r.d(t,{K:()=>h});var n=r(43210),a=r.n(n);function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(s=function(){return!!e})()}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function l(e,t){return(l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function c(e,t,r){return(t=f(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=i(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}var h=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=o(e),function(e,t){if(t&&("object"===i(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,s()?Reflect.construct(e,t||[],o(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&l(r,e),t=[{key:"render",value:function(){return null}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,f(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);c(h,"displayName","ZAxis"),c(h,"defaultProps",{zAxisId:0,range:[64,64],scale:"auto",type:"number"})},17612:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},20620:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},20659:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},26398:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},27127:(e,t,r)=>{r.d(t,{k:()=>l});var n=r(43210);let a=e=>Symbol.iterator in e,i=e=>"entries"in e,s=(e,t)=>{let r=e instanceof Map?e:new Map(e.entries()),n=t instanceof Map?t:new Map(t.entries());if(r.size!==n.size)return!1;for(let[e,t]of r)if(!Object.is(t,n.get(e)))return!1;return!0},o=(e,t)=>{let r=e[Symbol.iterator](),n=t[Symbol.iterator](),a=r.next(),i=n.next();for(;!a.done&&!i.done;){if(!Object.is(a.value,i.value))return!1;a=r.next(),i=n.next()}return!!a.done&&!!i.done};function l(e){let t=n.useRef(void 0);return r=>{var n,l;let c=e(r);return(n=t.current,Object.is(n,l=c)||"object"==typeof n&&null!==n&&"object"==typeof l&&null!==l&&Object.getPrototypeOf(n)===Object.getPrototypeOf(l)&&(a(n)&&a(l)?i(n)&&i(l)?s(n,l):o(n,l):s({entries:()=>Object.entries(n)},{entries:()=>Object.entries(l)})))?t.current:t.current=c}}},28274:(e,t,r)=>{r.d(t,{X:()=>B});var n=r(43210),a=r.n(n),i=r(93492),s=r(37456),o=r.n(s),l=r(71967),c=r.n(l),f=r(5231),h=r.n(f),u=r(49384),d=r(98986),p=r(98845),m=r(54186),g=r(20237),v=r(14769),b=r(81888),w=r(60927),T=r(25679),y=r(22989),E=r(30087),S=r(4057),x=r(10919),A=r(67629),_=["option","isActive"];function k(){return(k=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function O(e){var t=e.option,r=e.isActive,n=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,_);return"string"==typeof t?a().createElement(A.yp,k({option:a().createElement(x.i,k({type:t},n)),isActive:r,shapeType:"symbols"},n)):a().createElement(A.yp,k({option:t,isActive:r,shapeType:"symbols"},n))}function C(e){return(C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function R(){return(R=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function D(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function N(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?D(Object(r),!0).forEach(function(t){F(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):D(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function I(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,U(n.key),n)}}function P(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(P=function(){return!!e})()}function M(e){return(M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function L(e,t){return(L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function F(e,t,r){return(t=U(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function U(e){var t=function(e,t){if("object"!=C(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=C(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==C(t)?t:t+""}var B=function(e){var t,r;function n(){var e,t,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var a=arguments.length,i=Array(a),s=0;s<a;s++)i[s]=arguments[s];return t=n,r=[].concat(i),t=M(t),F(e=function(e,t){if(t&&("object"===C(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,P()?Reflect.construct(t,r||[],M(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!1}),F(e,"handleAnimationEnd",function(){e.setState({isAnimationFinished:!0})}),F(e,"handleAnimationStart",function(){e.setState({isAnimationFinished:!1})}),F(e,"id",(0,y.NF)("recharts-scatter-")),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&L(n,e),t=[{key:"renderSymbolsStatically",value:function(e){var t=this,r=this.props,n=r.shape,i=r.activeShape,s=r.activeIndex,o=(0,m.J9)(this.props,!1);return e.map(function(e,r){var l=s===r,c=N(N({},o),e);return a().createElement(d.W,R({className:"recharts-scatter-symbol",key:"symbol-".concat(null==e?void 0:e.cx,"-").concat(null==e?void 0:e.cy,"-").concat(null==e?void 0:e.size,"-").concat(r)},(0,S.XC)(t.props,e,r),{role:"img"}),a().createElement(O,R({option:l?i:n,isActive:l,key:"symbol-".concat(r)},c)))})}},{key:"renderSymbolsWithAnimation",value:function(){var e=this,t=this.props,r=t.points,n=t.isAnimationActive,s=t.animationBegin,o=t.animationDuration,l=t.animationEasing,c=t.animationId,f=this.state.prevPoints;return a().createElement(i.Ay,{begin:s,duration:o,isActive:n,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(c),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(t){var n=t.t,i=r.map(function(e,t){var r=f&&f[t];if(r){var a=(0,y.Dj)(r.cx,e.cx),i=(0,y.Dj)(r.cy,e.cy),s=(0,y.Dj)(r.size,e.size);return N(N({},e),{},{cx:a(n),cy:i(n),size:s(n)})}var o=(0,y.Dj)(0,e.size);return N(N({},e),{},{size:o(n)})});return a().createElement(d.W,null,e.renderSymbolsStatically(i))})}},{key:"renderSymbols",value:function(){var e=this.props,t=e.points,r=e.isAnimationActive,n=this.state.prevPoints;return r&&t&&t.length&&(!n||!c()(n,t))?this.renderSymbolsWithAnimation():this.renderSymbolsStatically(t)}},{key:"renderErrorBar",value:function(){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var e=this.props,t=e.points,r=e.xAxis,n=e.yAxis,i=e.children,s=(0,m.aS)(i,w.u);return s?s.map(function(e,i){var s=e.props,o=s.direction,l=s.dataKey;return a().cloneElement(e,{key:"".concat(o,"-").concat(l,"-").concat(t[i]),data:t,xAxis:r,yAxis:n,layout:"x"===o?"vertical":"horizontal",dataPointFormatter:function(e,t){return{x:e.cx,y:e.cy,value:"x"===o?+e.node.x:+e.node.y,errorVal:(0,E.kr)(e,t)}}})}):null}},{key:"renderLine",value:function(){var e,t,r=this.props,n=r.points,i=r.line,s=r.lineType,o=r.lineJointType,l=(0,m.J9)(this.props,!1),c=(0,m.J9)(i,!1);if("joint"===s)e=n.map(function(e){return{x:e.cx,y:e.cy}});else if("fitting"===s){var f=(0,y.jG)(n),u=f.xmin,p=f.xmax,g=f.a,v=f.b,w=function(e){return g*e+v};e=[{x:u,y:w(u)},{x:p,y:w(p)}]}var T=N(N(N({},l),{},{fill:"none",stroke:l&&l.fill},c),{},{points:e});return t=a().isValidElement(i)?a().cloneElement(i,T):h()(i)?i(T):a().createElement(b.I,R({},T,{type:o})),a().createElement(d.W,{className:"recharts-scatter-line",key:"recharts-scatter-line"},t)}},{key:"render",value:function(){var e=this.props,t=e.hide,r=e.points,n=e.line,i=e.className,s=e.xAxis,l=e.yAxis,c=e.left,f=e.top,h=e.width,m=e.height,g=e.id,v=e.isAnimationActive;if(t||!r||!r.length)return null;var b=this.state.isAnimationFinished,w=(0,u.A)("recharts-scatter",i),T=s&&s.allowDataOverflow,y=l&&l.allowDataOverflow,E=o()(g)?this.id:g;return a().createElement(d.W,{className:w,clipPath:T||y?"url(#clipPath-".concat(E,")"):null},T||y?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(E)},a().createElement("rect",{x:T?c:c-h/2,y:y?f:f-m/2,width:T?h:2*h,height:y?m:2*m}))):null,n&&this.renderLine(),this.renderErrorBar(),a().createElement(d.W,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!v||b)&&p.Z.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}}],t&&I(n.prototype,t),r&&I(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(n.PureComponent);F(B,"displayName","Scatter"),F(B,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!g.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"}),F(B,"getComposedData",function(e){var t=e.xAxis,r=e.yAxis,n=e.zAxis,a=e.item,i=e.displayedData,s=e.xAxisTicks,l=e.yAxisTicks,c=e.offset,f=a.props.tooltipType,h=(0,m.aS)(a.props.children,T.f),u=o()(t.dataKey)?a.props.dataKey:t.dataKey,d=o()(r.dataKey)?a.props.dataKey:r.dataKey,p=n&&n.dataKey,g=n?n.range:v.K.defaultProps.range,b=g&&g[0],w=t.scale.bandwidth?t.scale.bandwidth():0,y=r.scale.bandwidth?r.scale.bandwidth():0,S=i.map(function(e,i){var c=(0,E.kr)(e,u),m=(0,E.kr)(e,d),g=!o()(p)&&(0,E.kr)(e,p)||"-",v=[{name:o()(t.dataKey)?a.props.name:t.name||t.dataKey,unit:t.unit||"",value:c,payload:e,dataKey:u,type:f},{name:o()(r.dataKey)?a.props.name:r.name||r.dataKey,unit:r.unit||"",value:m,payload:e,dataKey:d,type:f}];"-"!==g&&v.push({name:n.name||n.dataKey,unit:n.unit||"",value:g,payload:e,dataKey:p,type:f});var T=(0,E.nb)({axis:t,ticks:s,bandSize:w,entry:e,index:i,dataKey:u}),S=(0,E.nb)({axis:r,ticks:l,bandSize:y,entry:e,index:i,dataKey:d}),x="-"!==g?n.scale(g):b,A=Math.sqrt(Math.max(x,0)/Math.PI);return N(N({},e),{},{cx:T,cy:S,x:T-A,y:S-A,xAxis:t,yAxis:r,zAxis:n,width:2*A,height:2*A,size:x,node:{x:c,y:m,z:g},tooltipPayload:v,tooltipPosition:{x:T,y:S},payload:e},h&&h[i]&&h[i].props)});return N({points:S},c)})},29094:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},29224:(e,t,r)=>{r.d(t,{k:()=>a});var n=r(10869);function a(e,t){return(0,n.J)(e,-t)}},33103:(e,t,r)=>{let n;r.d(t,{Wp:()=>ss,_h:()=>i6});var a,i,s,o,l={};l.version="0.18.5";var c=1200,f=1252,h=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],u={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},d=function(e){-1!=h.indexOf(e)&&(f=u[0]=e)},p=function(e){c=e,d(e)};function m(){p(1200),d(1252)}function g(e){for(var t=[],r=0,n=e.length;r<n;++r)t[r]=e.charCodeAt(r);return t}function v(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var b=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);if(255==t&&254==r){for(var n=e.slice(2),a=[],i=0;i<n.length>>1;++i)a[i]=String.fromCharCode(n.charCodeAt(2*i)+(n.charCodeAt(2*i+1)<<8));return a.join("")}return 254==t&&255==r?v(e.slice(2)):65279==t?e.slice(1):e},w=function(e){return String.fromCharCode(e)},T=function(e){return String.fromCharCode(e)},y="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function E(e){for(var t="",r=0,n=0,a=0,i=0,s=0,o=0,l=0,c=0;c<e.length;)i=(r=e.charCodeAt(c++))>>2,s=(3&r)<<4|(n=e.charCodeAt(c++))>>4,o=(15&n)<<2|(a=e.charCodeAt(c++))>>6,l=63&a,isNaN(n)?o=l=64:isNaN(a)&&(l=64),t+=y.charAt(i)+y.charAt(s)+y.charAt(o)+y.charAt(l);return t}function S(e){var t="",r=0,n=0,a=0,i=0,s=0,o=0,l=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var c=0;c<e.length;)t+=String.fromCharCode((i=y.indexOf(e.charAt(c++)))<<2|(s=y.indexOf(e.charAt(c++)))>>4),n=(15&s)<<4|(o=y.indexOf(e.charAt(c++)))>>2,64!==o&&(t+=String.fromCharCode(n)),a=(3&o)<<6|(l=y.indexOf(e.charAt(c++))),64!==l&&(t+=String.fromCharCode(a));return t}var x="undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node,A=function(){if("undefined"!=typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function _(e){return x?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}function k(e){return x?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}var O=function(e){return x?A(e,"binary"):e.split("").map(function(e){return 255&e.charCodeAt(0)})};function C(e){if("undefined"==typeof ArrayBuffer)return O(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=255&e.charCodeAt(n);return t}function R(e){if(Array.isArray(e))return e.map(function(e){return String.fromCharCode(e)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}var D=x?function(e){return Buffer.concat(e.map(function(e){return Buffer.isBuffer(e)?e:A(e)}))}:function(e){if("undefined"!=typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else if("string"==typeof e[t])throw"wtf";else n.set(new Uint8Array(e[t]),r);return n}return[].concat.apply([],e.map(function(e){return Array.isArray(e)?e:[].slice.call(e)}))},N=/\u0000/g,I=/[\u0001-\u0006]/g;function P(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function M(e,t){var r=""+e;return r.length>=t?r:eB("0",t-r.length)+r}function L(e,t){var r=""+e;return r.length>=t?r:eB(" ",t-r.length)+r}function F(e,t){var r=""+e;return r.length>=t?r:r+eB(" ",t-r.length)}function U(e,t){var r,n;return e>0x100000000||e<-0x100000000?(r=""+Math.round(e)).length>=t?r:eB("0",t-r.length)+r:(n=""+Math.round(e)).length>=t?n:eB("0",t-n.length)+n}function B(e,t){return t=t||0,e.length>=7+t&&(32|e.charCodeAt(t))==103&&(32|e.charCodeAt(t+1))==101&&(32|e.charCodeAt(t+2))==110&&(32|e.charCodeAt(t+3))==101&&(32|e.charCodeAt(t+4))==114&&(32|e.charCodeAt(t+5))==97&&(32|e.charCodeAt(t+6))==108}var W=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],H=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]],j={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},z={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},G={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function V(e,t,r){for(var n=e<0?-1:1,a=e*n,i=0,s=1,o=0,l=1,c=0,f=0,h=Math.floor(a);c<t&&(o=(h=Math.floor(a))*s+i,f=h*c+l,!(a-h<5e-8));)a=1/(a-h),i=s,s=o,l=c,c=f;if(f>t&&(c>t?(f=l,o=i):(f=c,o=s)),!r)return[0,n*o,f];var u=Math.floor(n*o/f);return[u,n*o-u*f,f]}function K(e,t,r){if(e>2958465||e<0)return null;var n=0|e,a=Math.floor(86400*(e-n)),i=0,s=[],o={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(1e-6>Math.abs(o.u)&&(o.u=0),t&&t.date1904&&(n+=1462),o.u>.9999&&(o.u=0,86400==++a&&(o.T=a=0,++n,++o.D)),60===n)s=r?[1317,10,29]:[1900,2,29],i=3;else if(0===n)s=r?[1317,8,29]:[1900,1,0],i=6;else{n>60&&--n;var l,c,f,h=new Date(1900,0,1);h.setDate(h.getDate()+n-1),s=[h.getFullYear(),h.getMonth()+1,h.getDate()],i=h.getDay(),n<60&&(i=(i+6)%7),r&&(l=h,c=s,c[0]-=581,f=l.getDay(),l<60&&(f=(f+6)%7),i=f)}return o.y=s[0],o.m=s[1],o.d=s[2],o.S=a%60,o.M=(a=Math.floor(a/60))%60,o.H=a=Math.floor(a/60),o.q=i,o}var Y=new Date(1899,11,31,0,0,0),X=Y.getTime(),J=new Date(1900,2,1,0,0,0);function q(e,t){var r=e.getTime();return t?r-=1262304e5:e>=J&&(r+=864e5),(r-(X+(e.getTimezoneOffset()-Y.getTimezoneOffset())*6e4))/864e5}function Z(e){return -1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Q(e){var t,r,n,a,i,s=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return s>=-4&&s<=-1?i=e.toPrecision(10+s):9>=Math.abs(s)?(t=e<0?12:11,i=(r=Z(e.toFixed(12))).length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)):i=10===s?e.toFixed(10).substr(0,12):(n=Z(e.toFixed(11))).length>(e<0?12:11)||"0"===n||"-0"===n?e.toPrecision(6):n,Z(-1==(a=i.toUpperCase()).indexOf("E")?a:a.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2"))}function ee(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):Q(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return em(14,q(e,t&&t.date1904),t)}throw Error("unsupported value in General format: "+e)}function et(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var er=/%/g,en=/# (\?+)( ?)\/( ?)(\d+)/,ea=/^#*0*\.([0#]+)/,ei=/\).*[0#]/,es=/\(###\) ###\\?-####/;function eo(e){for(var t,r="",n=0;n!=e.length;++n)switch(t=e.charCodeAt(n)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function el(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function ec(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function ef(e,t,r){return(0|r)===r?function e(t,r,n){if(40===t.charCodeAt(0)&&!r.match(ei)){var a,i=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return n>=0?e("n",i,n):"("+e("n",i,-n)+")"}if(44===r.charCodeAt(r.length-1)){for(var s=r,o=s.length-1;44===s.charCodeAt(o-1);)--o;return ef(t,s.substr(0,o),n/Math.pow(10,3*(s.length-o)))}if(-1!==r.indexOf("%"))return c=(l=r).replace(er,""),f=l.length-c.length,ef(t,c,n*Math.pow(10,2*f))+eB("%",f);if(-1!==r.indexOf("E"))return function e(t,r){var n,a=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var i=t.indexOf(".");-1===i&&(i=t.indexOf("E"));var s=Math.floor(Math.log(r)*Math.LOG10E)%i;if(s<0&&(s+=i),!(n=(r/Math.pow(10,s)).toPrecision(a+1+(i+s)%i)).match(/[Ee]/)){var o=Math.floor(Math.log(r)*Math.LOG10E);-1===n.indexOf(".")?n=n.charAt(0)+"."+n.substr(1)+"E+"+(o-n.length+s):n+="E+"+(o-s),n=n.replace(/\+-/,"-")}n=n.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,n){return t+r+n.substr(0,(i+s)%i)+"."+n.substr(s)+"E"})}else n=r.toExponential(a);return t.match(/E\+00$/)&&n.match(/e[+-]\d$/)&&(n=n.substr(0,n.length-1)+"0"+n.charAt(n.length-1)),t.match(/E\-/)&&n.match(/e\+/)&&(n=n.replace(/e\+/,"e")),n.replace("e","E")}(r,n);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),n);var l,c,f,h,u,d,p,m=Math.abs(n),g=n<0?"-":"";if(r.match(/^00+$/))return g+M(m,r.length);if(r.match(/^[#?]+$/))return h=""+n,0===n&&(h=""),h.length>r.length?h:eo(r.substr(0,r.length-h.length))+h;if(u=r.match(en))return g+(0===m?"":""+m)+eB(" ",(a=u)[1].length+2+a[4].length);if(r.match(/^#+0+$/))return g+M(m,r.length-r.indexOf("0"));if(u=r.match(ea))return h=(h=(""+n).replace(/^([^\.]+)$/,"$1."+eo(u[1])).replace(/\.$/,"."+eo(u[1]))).replace(/\.(\d*)$/,function(e,t){return"."+t+eB("0",eo(u[1]).length-t.length)}),-1!==r.indexOf("0.")?h:h.replace(/^0\./,".");if(u=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return g+(""+m).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,u[1].length?"0.":".");if(u=r.match(/^#{1,3},##0(\.?)$/))return g+et(""+m);if(u=r.match(/^#,##0\.([#0]*0)$/))return n<0?"-"+e(t,r,-n):et(""+n)+"."+eB("0",u[1].length);if(u=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),n);if(u=r.match(/^([0#]+)(\\?-([0#]+))+$/))return h=P(e(t,r.replace(/[\\-]/g,""),n)),d=0,P(P(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return d<h.length?h.charAt(d++):"0"===e?"0":""}));if(r.match(es))return"("+(h=e(t,"##########",n)).substr(0,3)+") "+h.substr(3,3)+"-"+h.substr(6);var v="";if(u=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return p=V(m,Math.pow(10,d=Math.min(u[4].length,7))-1,!1),h=""+g," "==(v=ef("n",u[1],p[1])).charAt(v.length-1)&&(v=v.substr(0,v.length-1)+"0"),h+=v+u[2]+"/"+u[3],(v=F(p[2],d)).length<u[4].length&&(v=eo(u[4].substr(u[4].length-v.length))+v),h+=v;if(u=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return g+((p=V(m,Math.pow(10,d=Math.min(Math.max(u[1].length,u[4].length),7))-1,!0))[0]||(p[1]?"":"0"))+" "+(p[1]?L(p[1],d)+u[2]+"/"+u[3]+F(p[2],d):eB(" ",2*d+1+u[2].length+u[3].length));if(u=r.match(/^[#0?]+$/))return(h=""+n,r.length<=h.length)?h:eo(r.substr(0,r.length-h.length))+h;if(u=r.match(/^([#0]+)\.([#0]+)$/)){d=(h=""+n.toFixed(Math.min(u[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var b=r.indexOf(".")-d,w=r.length-h.length-b;return eo(r.substr(0,b)+h+r.substr(r.length-w))}if(u=r.match(/^00,000\.([#0]*0)$/))return n<0?"-"+e(t,r,-n):et(""+n).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?M(0,3-e.length):"")+e})+"."+M(0,u[1].length);switch(r){case"###,###":case"##,###":case"#,###":var T=et(""+m);return"0"!==T?g+T:"";default:if(r.match(/\.[0#?]*$/))return e(t,r.slice(0,r.lastIndexOf(".")),n)+eo(r.slice(r.lastIndexOf(".")))}throw Error("unsupported format |"+r+"|")}(e,t,r):function e(t,r,n){if(40===t.charCodeAt(0)&&!r.match(ei)){var a,i,s,o,l,c,f=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return n>=0?e("n",f,n):"("+e("n",f,-n)+")"}if(44===r.charCodeAt(r.length-1)){for(var h=r,u=h.length-1;44===h.charCodeAt(u-1);)--u;return ef(t,h.substr(0,u),n/Math.pow(10,3*(h.length-u)))}if(-1!==r.indexOf("%"))return p=(d=r).replace(er,""),m=d.length-p.length,ef(t,p,n*Math.pow(10,2*m))+eB("%",m);if(-1!==r.indexOf("E"))return function e(t,r){var n,a=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var i=t.indexOf(".");-1===i&&(i=t.indexOf("E"));var s=Math.floor(Math.log(r)*Math.LOG10E)%i;if(s<0&&(s+=i),-1===(n=(r/Math.pow(10,s)).toPrecision(a+1+(i+s)%i)).indexOf("e")){var o=Math.floor(Math.log(r)*Math.LOG10E);for(-1===n.indexOf(".")?n=n.charAt(0)+"."+n.substr(1)+"E+"+(o-n.length+s):n+="E+"+(o-s);"0."===n.substr(0,2);)n=(n=n.charAt(0)+n.substr(2,i)+"."+n.substr(2+i)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");n=n.replace(/\+-/,"-")}n=n.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,n){return t+r+n.substr(0,(i+s)%i)+"."+n.substr(s)+"E"})}else n=r.toExponential(a);return t.match(/E\+00$/)&&n.match(/e[+-]\d$/)&&(n=n.substr(0,n.length-1)+"0"+n.charAt(n.length-1)),t.match(/E\-/)&&n.match(/e\+/)&&(n=n.replace(/e\+/,"e")),n.replace("e","E")}(r,n);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),n);var d,p,m,g,v,b,w,T=Math.abs(n),y=n<0?"-":"";if(r.match(/^00+$/))return y+U(T,r.length);if(r.match(/^[#?]+$/))return"0"===(g=U(n,0))&&(g=""),g.length>r.length?g:eo(r.substr(0,r.length-g.length))+g;if(v=r.match(en))return o=Math.floor((s=Math.round(T*(i=parseInt((a=v)[4],10))))/i),l=s-o*i,y+(0===o?"":""+o)+" "+(0===l?eB(" ",a[1].length+1+a[4].length):L(l,a[1].length)+a[2]+"/"+a[3]+M(i,a[4].length));if(r.match(/^#+0+$/))return y+U(T,r.length-r.indexOf("0"));if(v=r.match(ea))return g=el(n,v[1].length).replace(/^([^\.]+)$/,"$1."+eo(v[1])).replace(/\.$/,"."+eo(v[1])).replace(/\.(\d*)$/,function(e,t){return"."+t+eB("0",eo(v[1]).length-t.length)}),-1!==r.indexOf("0.")?g:g.replace(/^0\./,".");if(v=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return y+el(T,v[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,v[1].length?"0.":".");if(v=r.match(/^#{1,3},##0(\.?)$/))return y+et(U(T,0));if(v=r.match(/^#,##0\.([#0]*0)$/))return n<0?"-"+e(t,r,-n):et(""+(Math.floor(n)+ +((c=v[1].length)<(""+Math.round((n-Math.floor(n))*Math.pow(10,c))).length)))+"."+M(ec(n,v[1].length),v[1].length);if(v=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),n);if(v=r.match(/^([0#]+)(\\?-([0#]+))+$/))return g=P(e(t,r.replace(/[\\-]/g,""),n)),b=0,P(P(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return b<g.length?g.charAt(b++):"0"===e?"0":""}));if(r.match(es))return"("+(g=e(t,"##########",n)).substr(0,3)+") "+g.substr(3,3)+"-"+g.substr(6);var E="";if(v=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return w=V(T,Math.pow(10,b=Math.min(v[4].length,7))-1,!1),g=""+y," "==(E=ef("n",v[1],w[1])).charAt(E.length-1)&&(E=E.substr(0,E.length-1)+"0"),g+=E+v[2]+"/"+v[3],(E=F(w[2],b)).length<v[4].length&&(E=eo(v[4].substr(v[4].length-E.length))+E),g+=E;if(v=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return y+((w=V(T,Math.pow(10,b=Math.min(Math.max(v[1].length,v[4].length),7))-1,!0))[0]||(w[1]?"":"0"))+" "+(w[1]?L(w[1],b)+v[2]+"/"+v[3]+F(w[2],b):eB(" ",2*b+1+v[2].length+v[3].length));if(v=r.match(/^[#0?]+$/))return(g=U(n,0),r.length<=g.length)?g:eo(r.substr(0,r.length-g.length))+g;if(v=r.match(/^([#0?]+)\.([#0]+)$/)){b=(g=""+n.toFixed(Math.min(v[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var S=r.indexOf(".")-b,x=r.length-g.length-S;return eo(r.substr(0,S)+g+r.substr(r.length-x))}if(v=r.match(/^00,000\.([#0]*0)$/))return b=ec(n,v[1].length),n<0?"-"+e(t,r,-n):et(n<0x7fffffff&&n>-0x80000000?""+(n>=0?0|n:n-1|0):""+Math.floor(n)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?M(0,3-e.length):"")+e})+"."+M(b,v[1].length);switch(r){case"###,##0.00":return e(t,"#,##0.00",n);case"###,###":case"##,###":case"#,###":var A=et(U(T,0));return"0"!==A?y+A:"";case"###,###.00":return e(t,"###,##0.00",n).replace(/^0\./,".");case"#,###.00":return e(t,"#,##0.00",n).replace(/^0\./,".")}throw Error("unsupported format |"+r+"|")}(e,t,r)}var eh=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function eu(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":B(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase()||"AM/PM"===e.substr(t,5).toUpperCase()||"上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(n=r;"]"!==e.charAt(t++)&&t<e.length;)n+=e.charAt(t);if(n.match(eh))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(" "==e.charAt(t)||"*"==e.charAt(t))&&++t;break;case"(":case")":case" ":default:++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);}return!1}var ed=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function ep(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function em(e,t,r){null==r&&(r={});var n="";switch(typeof e){case"string":n="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(n=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:j)[e])&&(n=r.table&&r.table[z[e]]||j[z[e]]),null==n&&(n=G[e]||"General")}if(B(n,0))return ee(t,r);t instanceof Date&&(t=q(t,r.date1904));var a=function(e,t){var r=function(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),!0===r)throw Error("Format |"+e+"| unterminated string ");return t}(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var i=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[n,i];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var s=r[0].match(ed),o=r[1].match(ed);return ep(t,s)?[n,r[0]]:ep(t,o)?[n,r[1]]:[n,r[null!=s&&null!=o?2:1]]}return[n,i]}(n,t);if(B(a[1]))return ee(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,n){for(var a,i,s,o=[],l="",c=0,f="",h="t",u="H";c<e.length;)switch(f=e.charAt(c)){case"G":if(!B(e,c))throw Error("unrecognized character "+f+" in "+e);o[o.length]={t:"G",v:"General"},c+=7;break;case'"':for(l="";34!==(s=e.charCodeAt(++c))&&c<e.length;)l+=String.fromCharCode(s);o[o.length]={t:"t",v:l},++c;break;case"\\":var d=e.charAt(++c),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++c;break;case"_":o[o.length]={t:"t",v:" "},c+=2;break;case"@":o[o.length]={t:"T",v:t},++c;break;case"B":case"b":if("1"===e.charAt(c+1)||"2"===e.charAt(c+1)){if(null==a&&null==(a=K(t,r,"2"===e.charAt(c+1))))return"";o[o.length]={t:"X",v:e.substr(c,2)},h=f,c+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||null==a&&null==(a=K(t,r)))return"";for(l=f;++c<e.length&&e.charAt(c).toLowerCase()===f;)l+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),o[o.length]={t:f,v:l},h=f;break;case"A":case"a":case"上":var m={t:f,v:f};if(null==a&&(a=K(t,r)),"A/P"===e.substr(c,3).toUpperCase()?(null!=a&&(m.v=a.H>=12?"P":"A"),m.t="T",u="h",c+=3):"AM/PM"===e.substr(c,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"PM":"AM"),m.t="T",c+=5,u="h"):"上午/下午"===e.substr(c,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"下午":"上午"),m.t="T",c+=5,u="h"):(m.t="t",++c),null==a&&"T"===m.t)return"";o[o.length]=m,h=f;break;case"[":for(l=f;"]"!==e.charAt(c++)&&c<e.length;)l+=e.charAt(c);if("]"!==l.slice(-1))throw'unterminated "[" block: |'+l+"|";if(l.match(eh)){if(null==a&&null==(a=K(t,r)))return"";o[o.length]={t:"Z",v:l.toLowerCase()},h=l.charAt(1)}else l.indexOf("$")>-1&&(l=(l.match(/\$([^-\[\]]*)/)||[])[1]||"$",eu(e)||(o[o.length]={t:"t",v:l}));break;case".":if(null!=a){for(l=f;++c<e.length&&"0"===(f=e.charAt(c));)l+=f;o[o.length]={t:"s",v:l};break}case"0":case"#":for(l=f;++c<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(c))>-1;)l+=f;o[o.length]={t:"n",v:l};break;case"?":for(l=f;e.charAt(++c)===f;)l+=f;o[o.length]={t:f,v:l},h=f;break;case"*":++c,(" "==e.charAt(c)||"*"==e.charAt(c))&&++c;break;case"(":case")":o[o.length]={t:1===n?"t":f,v:f},++c;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(l=f;c<e.length&&"0123456789".indexOf(e.charAt(++c))>-1;)l+=e.charAt(c);o[o.length]={t:"D",v:l};break;case" ":o[o.length]={t:f,v:f},++c;break;case"$":o[o.length]={t:"t",v:"$"},++c;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw Error("unrecognized character "+f+" in "+e);o[o.length]={t:"t",v:f},++c}var g,v=0,b=0;for(c=o.length-1,h="t";c>=0;--c)switch(o[c].t){case"h":case"H":o[c].t=u,h="h",v<1&&(v=1);break;case"s":(g=o[c].v.match(/\.0+$/))&&(b=Math.max(b,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=o[c].t;break;case"m":"s"===h&&(o[c].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[c].v.match(/[Hh]/)&&(v=1),v<2&&o[c].v.match(/[Mm]/)&&(v=2),v<3&&o[c].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M),a.M>=60&&(a.M=0,++a.H);break;case 2:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M)}var w,T="";for(c=0;c<o.length;++c)switch(o[c].t){case"t":case"T":case" ":case"D":break;case"X":o[c].v="",o[c].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[c].v=function(e,t,r,n){var a,i="",s=0,o=0,l=r.y,c=0;switch(e){case 98:l=r.y+543;case 121:switch(t.length){case 1:case 2:a=l%100,c=2;break;default:a=l%1e4,c=4}break;case 109:switch(t.length){case 1:case 2:a=r.m,c=t.length;break;case 3:return H[r.m-1][1];case 5:return H[r.m-1][0];default:return H[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:a=r.d,c=t.length;break;case 3:return W[r.q][0];default:return W[r.q][1]}break;case 104:switch(t.length){case 1:case 2:a=1+(r.H+11)%12,c=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:a=r.H,c=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:a=r.M,c=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;if(0===r.u&&("s"==t||"ss"==t))return M(r.S,t.length);if((s=Math.round((o=n>=2?3===n?1e3:100:1===n?10:1)*(r.S+r.u)))>=60*o&&(s=0),"s"===t)return 0===s?"0":""+s/o;if(i=M(s,2+n),"ss"===t)return i.substr(0,2);return"."+i.substr(2,t.length-1);case 90:switch(t){case"[h]":case"[hh]":a=24*r.D+r.H;break;case"[m]":case"[mm]":a=(24*r.D+r.H)*60+r.M;break;case"[s]":case"[ss]":a=((24*r.D+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}c=3===t.length?1:2;break;case 101:a=l,c=1}return c>0?M(a,c):""}(o[c].t.charCodeAt(0),o[c].v,a,b),o[c].t="t";break;case"n":case"?":for(w=c+1;null!=o[w]&&("?"===(f=o[w].t)||"D"===f||(" "===f||"t"===f)&&null!=o[w+1]&&("?"===o[w+1].t||"t"===o[w+1].t&&"/"===o[w+1].v)||"("===o[c].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===o[w].v||" "===o[w].v&&null!=o[w+1]&&"?"==o[w+1].t));)o[c].v+=o[w].v,o[w]={v:"",t:";"},++w;T+=o[c].v,c=w-1;break;case"G":o[c].t="t",o[c].v=ee(t,r)}var y,E,S="";if(T.length>0){40==T.charCodeAt(0)?(y=t<0&&45===T.charCodeAt(0)?-t:t,E=ef("n",T,y)):(E=ef("n",T,y=t<0&&n>1?-t:t),y<0&&o[0]&&"t"==o[0].t&&(E=E.substr(1),o[0].v="-"+o[0].v)),w=E.length-1;var x=o.length;for(c=0;c<o.length;++c)if(null!=o[c]&&"t"!=o[c].t&&o[c].v.indexOf(".")>-1){x=c;break}var A=o.length;if(x===o.length&&-1===E.indexOf("E")){for(c=o.length-1;c>=0;--c)null!=o[c]&&-1!=="n?".indexOf(o[c].t)&&(w>=o[c].v.length-1?(w-=o[c].v.length,o[c].v=E.substr(w+1,o[c].v.length)):w<0?o[c].v="":(o[c].v=E.substr(0,w+1),w=-1),o[c].t="t",A=c);w>=0&&A<o.length&&(o[A].v=E.substr(0,w+1)+o[A].v)}else if(x!==o.length&&-1===E.indexOf("E")){for(w=E.indexOf(".")-1,c=x;c>=0;--c)if(null!=o[c]&&-1!=="n?".indexOf(o[c].t)){for(i=o[c].v.indexOf(".")>-1&&c===x?o[c].v.indexOf(".")-1:o[c].v.length-1,S=o[c].v.substr(i+1);i>=0;--i)w>=0&&("0"===o[c].v.charAt(i)||"#"===o[c].v.charAt(i))&&(S=E.charAt(w--)+S);o[c].v=S,o[c].t="t",A=c}for(w>=0&&A<o.length&&(o[A].v=E.substr(0,w+1)+o[A].v),w=E.indexOf(".")+1,c=x;c<o.length;++c)if(null!=o[c]&&(-1!=="n?(".indexOf(o[c].t)||c===x)){for(i=o[c].v.indexOf(".")>-1&&c===x?o[c].v.indexOf(".")+1:0,S=o[c].v.substr(0,i);i<o[c].v.length;++i)w<E.length&&(S+=E.charAt(w++));o[c].v=S,o[c].t="t",A=c}}}for(c=0;c<o.length;++c)null!=o[c]&&"n?".indexOf(o[c].t)>-1&&(y=n>1&&t<0&&c>0&&"-"===o[c-1].v?-t:t,o[c].v=ef(o[c].t,o[c].v,y),o[c].t="t");var _="";for(c=0;c!==o.length;++c)null!=o[c]&&(_+=o[c].v);return _}(a[1],t,r,a[0])}function eg(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r){if(void 0==j[r]){t<0&&(t=r);continue}if(j[r]==e){t=r;break}}t<0&&(t=391)}return j[t]=e,t}function ev(e){for(var t=0;392!=t;++t)void 0!==e[t]&&eg(e[t],t)}function eb(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',j=e}var ew=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g,eT=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1,t[r]=e;return"undefined"!=typeof Int32Array?new Int32Array(t):t}(),r=function(e){var t=0,r=0,n=0,a="undefined"!=typeof Int32Array?new Int32Array(4096):Array(4096);for(n=0;256!=n;++n)a[n]=e[n];for(n=0;256!=n;++n)for(r=e[n],t=256+n;t<4096;t+=256)r=a[t]=r>>>8^e[255&r];var i=[];for(n=1;16!=n;++n)i[n-1]="undefined"!=typeof Int32Array?a.subarray(256*n,256*n+256):a.slice(256*n,256*n+256);return i}(t),n=r[0],a=r[1],i=r[2],s=r[3],o=r[4],l=r[5],c=r[6],f=r[7],h=r[8],u=r[9],d=r[10],p=r[11],m=r[12],g=r[13],v=r[14];return e.table=t,e.bstr=function(e,r){for(var n=-1^r,a=0,i=e.length;a<i;)n=n>>>8^t[(n^e.charCodeAt(a++))&255];return~n},e.buf=function(e,r){for(var b=-1^r,w=e.length-15,T=0;T<w;)b=v[e[T++]^255&b]^g[e[T++]^b>>8&255]^m[e[T++]^b>>16&255]^p[e[T++]^b>>>24]^d[e[T++]]^u[e[T++]]^h[e[T++]]^f[e[T++]]^c[e[T++]]^l[e[T++]]^o[e[T++]]^s[e[T++]]^i[e[T++]]^a[e[T++]]^n[e[T++]]^t[e[T++]];for(w+=15;T<w;)b=b>>>8^t[(b^e[T++])&255];return~b},e.str=function(e,r){for(var n=-1^r,a=0,i=e.length,s=0,o=0;a<i;)(s=e.charCodeAt(a++))<128?n=n>>>8^t[(n^s)&255]:s<2048?n=(n=n>>>8^t[(n^(192|s>>6&31))&255])>>>8^t[(n^(128|63&s))&255]:s>=55296&&s<57344?(s=(1023&s)+64,o=1023&e.charCodeAt(a++),n=(n=(n=(n=n>>>8^t[(n^(240|s>>8&7))&255])>>>8^t[(n^(128|s>>2&63))&255])>>>8^t[(n^(128|o>>6&15|(3&s)<<4))&255])>>>8^t[(n^(128|63&o))&255]):n=(n=(n=n>>>8^t[(n^(224|s>>12&15))&255])>>>8^t[(n^(128|s>>6&63))&255])>>>8^t[(n^(128|63&s))&255];return~n},e}(),ey=function(){var e,t,r={};function n(e){if("/"==e.charAt(e.length-1))return -1===e.slice(0,-1).indexOf("/")?e:n(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(0,t+1)}function a(e){if("/"==e.charAt(e.length-1))return a(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(t+1)}function i(e){tZ(e,0);for(var t={},r=0;e.l<=e.length-4;){var n=e.read_shift(2),a=e.read_shift(2),i=e.l+a,s={};21589===n&&(1&(r=e.read_shift(1))&&(s.mtime=e.read_shift(4)),a>5&&(2&r&&(s.atime=e.read_shift(4)),4&r&&(s.ctime=e.read_shift(4))),s.mtime&&(s.mt=new Date(1e3*s.mtime))),e.l=i,t[n]=s}return t}function s(){return e||(e={})}function o(e,t){if(80==e[0]&&75==e[1])return eo(e,t);if((32|e[0])==109&&(32|e[1])==105)return function(e,t){if("mime-version:"!=b(e.slice(0,13)).toLowerCase())throw Error("Unsupported MAD header");var r=t&&t.root||"",n=(x&&Buffer.isBuffer(e)?e.toString("binary"):b(e)).split("\r\n"),a=0,i="";for(a=0;a<n.length;++a)if((i=n[a],/^Content-Location:/i.test(i))&&(i=i.slice(i.indexOf("file")),r||(r=i.slice(0,i.lastIndexOf("/")+1)),i.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),i.slice(0,r.length)!=r););var s=(n[1]||"").match(/boundary="(.*?)"/);if(!s)throw Error("MAD cannot find boundary");var o="--"+(s[1]||""),l={FileIndex:[],FullPaths:[]};c(l);var f,h=0;for(a=0;a<n.length;++a){var u=n[a];(u===o||u===o+"--")&&(h++&&function(e,t,r){for(var n,a="",i="",s="",o=0;o<10;++o){var l=t[o];if(!l||l.match(/^\s*$/))break;var c=l.match(/^(.*?):\s*([^\s].*)$/);if(c)switch(c[1].toLowerCase()){case"content-location":a=c[2].trim();break;case"content-type":s=c[2].trim();break;case"content-transfer-encoding":i=c[2].trim()}}switch(++o,i.toLowerCase()){case"base64":n=O(S(t.slice(o).join("")));break;case"quoted-printable":n=function(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r];r<=e.length&&"="==n.charAt(n.length-1);)n=n.slice(0,n.length-1)+e[++r];t.push(n)}for(var a=0;a<t.length;++a)t[a]=t[a].replace(/[=][0-9A-Fa-f]{2}/g,function(e){return String.fromCharCode(parseInt(e.slice(1),16))});return O(t.join("\r\n"))}(t.slice(o));break;default:throw Error("Unsupported Content-Transfer-Encoding "+i)}var f=ec(e,a.slice(r.length),n,{unsafe:!0});s&&(f.ctype=s)}(l,n.slice(f,a),r),f=a)}return l}(e,t);if(e.length<512)throw Error("CFB file size "+e.length+" < 512");var r=3,n=512,a=0,i=0,s=0,o=0,f=0,h=[],m=e.slice(0,512);tZ(m,0);var g=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(p,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(m);switch(r=g[0]){case 3:n=512;break;case 4:n=4096;break;case 0:if(0==g[1])return eo(e,t);default:throw Error("Major Version: Expected 3 or 4 saw "+r)}512!==n&&tZ(m=e.slice(0,n),28);var v=e.slice(0,n),w=m,T=r,y=9;switch(w.l+=2,y=w.read_shift(2)){case 9:if(3!=T)throw Error("Sector Shift: Expected 9 saw "+y);break;case 12:if(4!=T)throw Error("Sector Shift: Expected 12 saw "+y);break;default:throw Error("Sector Shift: Expected 9 or 12 saw "+y)}w.chk("0600","Mini Sector Shift: "),w.chk("000000000000","Reserved: ");var E=m.read_shift(4,"i");if(3===r&&0!==E)throw Error("# Directory Sectors: Expected 0 saw "+E);m.l+=4,s=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),o=m.read_shift(4,"i"),a=m.read_shift(4,"i"),f=m.read_shift(4,"i"),i=m.read_shift(4,"i");for(var A=-1,_=0;_<109&&!((A=m.read_shift(4,"i"))<0);++_)h[_]=A;var k=function(e,t){for(var r=Math.ceil(e.length/t)-1,n=[],a=1;a<r;++a)n[a-1]=e.slice(a*t,(a+1)*t);return n[r-1]=e.slice(r*t),n}(e,n);!function e(t,r,n,a,i){var s=d;if(t===d){if(0!==r)throw Error("DIFAT chain shorter than expected")}else if(-1!==t){var o=n[t],l=(a>>>2)-1;if(!o)return;for(var c=0;c<l&&(s=tV(o,4*c))!==d;++c)i.push(s);e(tV(o,a-4),r-1,n,a,i)}}(f,i,k,n,h);var C=function(e,t,r,n){var a=e.length,i=[],s=[],o=[],l=[],c=n-1,f=0,h=0,u=0,d=0;for(f=0;f<a;++f)if(o=[],(u=f+t)>=a&&(u-=a),!s[u]){l=[];var p=[];for(h=u;h>=0;){p[h]=!0,s[h]=!0,o[o.length]=h,l.push(e[h]);var m=r[Math.floor(4*h/n)];if(n<4+(d=4*h&c))throw Error("FAT boundary crossed: "+h+" 4 "+n);if(!e[m]||p[h=tV(e[m],d)])break}i[u]={nodes:o,data:ty([l])}}return i}(k,s,h,n);C[s].name="!Directory",a>0&&o!==d&&(C[o].name="!MiniFAT"),C[h[0]].name="!FAT",C.fat_addrs=h,C.ssz=n;var R=[],N=[],I=[];(function(e,t,r,n,a,i,s,o){for(var c,f=0,h=2*!!n.length,p=t[e].data,m=0,g=0;m<p.length;m+=128){var v=p.slice(m,m+128);tZ(v,64),g=v.read_shift(2),c=tS(v,0,g-h),n.push(c);var b={name:c,type:v.read_shift(1),color:v.read_shift(1),L:v.read_shift(4,"i"),R:v.read_shift(4,"i"),C:v.read_shift(4,"i"),clsid:v.read_shift(16),state:v.read_shift(4,"i"),start:0,size:0};0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(b.ct=l(v,v.l-8)),0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(b.mt=l(v,v.l-8)),b.start=v.read_shift(4,"i"),b.size=v.read_shift(4,"i"),b.size<0&&b.start<0&&(b.size=b.type=0,b.start=d,b.name=""),5===b.type?(f=b.start,a>0&&f!==d&&(t[f].name="!StreamData")):b.size>=4096?(b.storage="fat",void 0===t[b.start]&&(t[b.start]=function(e,t,r,n,a){var i=[],s=[];a||(a=[]);var o=n-1,l=0,c=0;for(l=t;l>=0;){a[l]=!0,i[i.length]=l,s.push(e[l]);var f=r[Math.floor(4*l/n)];if(n<4+(c=4*l&o))throw Error("FAT boundary crossed: "+l+" 4 "+n);if(!e[f])break;l=tV(e[f],c)}return{nodes:i,data:ty([s])}}(r,b.start,t.fat_addrs,t.ssz)),t[b.start].name=b.name,b.content=t[b.start].data.slice(0,b.size)):(b.storage="minifat",b.size<0?b.size=0:f!==d&&b.start!==d&&t[f]&&(b.content=function(e,t,r){for(var n=e.start,a=e.size,i=[],s=n;r&&a>0&&s>=0;)i.push(t.slice(s*u,s*u+u)),a-=u,s=tV(r,4*s);return 0===i.length?t1(0):D(i).slice(0,e.size)}(b,t[f].data,(t[o]||{}).data))),b.content&&tZ(b.content,0),i[c]=b,s.push(b)}})(s,C,k,R,a,{},N,o),function(e,t,r){for(var n=0,a=0,i=0,s=0,o=0,l=r.length,c=[],f=[];n<l;++n)c[n]=f[n]=n,t[n]=r[n];for(;o<f.length;++o)a=e[n=f[o]].L,i=e[n].R,s=e[n].C,c[n]===n&&(-1!==a&&c[a]!==a&&(c[n]=c[a]),-1!==i&&c[i]!==i&&(c[n]=c[i])),-1!==s&&(c[s]=n),-1!==a&&n!=c[n]&&(c[a]=c[n],f.lastIndexOf(a)<o&&f.push(a)),-1!==i&&n!=c[n]&&(c[i]=c[n],f.lastIndexOf(i)<o&&f.push(i));for(n=1;n<l;++n)c[n]===n&&(-1!==i&&c[i]!==i?c[n]=c[i]:-1!==a&&c[a]!==a&&(c[n]=c[a]));for(n=1;n<l;++n)if(0!==e[n].type){if((o=n)!=c[o])do o=c[o],t[n]=t[o]+"/"+t[n];while(0!==o&&-1!==c[o]&&o!=c[o]);c[n]=-1}for(t[0]+="/",n=1;n<l;++n)2!==e[n].type&&(t[n]+="/")}(N,I,R),R.shift();var P={FileIndex:N,FullPaths:I};return t&&t.raw&&(P.raw={header:v,sectors:k}),P}function l(e,t){return new Date((tG(e,t+4)/1e7*0x100000000+tG(e,t)/1e7-0x2b6109100)*1e3)}function c(e,t){var r=t||{},n=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=n+"/",e.FileIndex[0]={name:n,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="\x01Sh33tJ5";if(!ey.find(e,"/"+t)){var r=t1(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),f(e)}}(e)}function f(e,t){c(e);for(var r=!1,i=!1,s=e.FullPaths.length-1;s>=0;--s){var o=e.FileIndex[s];switch(o.type){case 0:i?r=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:i=!0,isNaN(o.R*o.L*o.C)&&(r=!0),o.R>-1&&o.L>-1&&o.R==o.L&&(r=!0);break;default:r=!0}}if(r||t){var l=new Date(1987,1,19),f=0,h=Object.create?Object.create(null):{},u=[];for(s=0;s<e.FullPaths.length;++s)h[e.FullPaths[s]]=!0,0!==e.FileIndex[s].type&&u.push([e.FullPaths[s],e.FileIndex[s]]);for(s=0;s<u.length;++s){var d=n(u[s][0]);(i=h[d])||(u.push([d,{name:a(d).replace("/",""),type:1,clsid:g,ct:l,mt:l,content:null}]),h[d]=!0)}for(u.sort(function(e,t){return function(e,t){for(var r=e.split("/"),n=t.split("/"),a=0,i=0,s=Math.min(r.length,n.length);a<s;++a){if(i=r[a].length-n[a].length)return i;if(r[a]!=n[a])return r[a]<n[a]?-1:1}return r.length-n.length}(e[0],t[0])}),e.FullPaths=[],e.FileIndex=[],s=0;s<u.length;++s)e.FullPaths[s]=u[s][0],e.FileIndex[s]=u[s][1];for(s=0;s<u.length;++s){var p=e.FileIndex[s],m=e.FullPaths[s];if(p.name=a(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||g,0===s)p.C=u.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(f=s+1;f<u.length&&n(e.FullPaths[f])!=m;++f);for(p.C=f>=u.length?-1:f,f=s+1;f<u.length&&n(e.FullPaths[f])!=n(m);++f);p.R=f>=u.length?-1:f,p.type=1}else n(e.FullPaths[s+1]||"")==n(m)&&(p.R=s+1),p.type=2}}}function h(e,r){var n=r||{};if("mad"==n.fileType)return function(e,t){for(var r=t||{},n=r.boundary||"SheetJS",a=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(n="------="+n).slice(2)+'"',"","",""],i=e.FullPaths[0],s=i,o=e.FileIndex[0],l=1;l<e.FullPaths.length;++l)if(s=e.FullPaths[l].slice(i.length),(o=e.FileIndex[l]).size&&o.content&&"\x01Sh33tJ5"!=s){s=s.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"});for(var c=o.content,f=x&&Buffer.isBuffer(c)?c.toString("binary"):b(c),h=0,u=Math.min(1024,f.length),d=0,p=0;p<=u;++p)(d=f.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;a.push(n),a.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+s),a.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),a.push("Content-Type: "+function(e,t){if(e.ctype)return e.ctype;var r=e.name||"",n=r.match(/\.([^\.]+)$/);return n&&el[n[1]]||t&&(n=(r=t).match(/[\.\\]([^\.\\])+$/))&&el[n[1]]?el[n[1]]:"application/octet-stream"}(o,s)),a.push(""),a.push(m?function(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)});"\n"==(t=t.replace(/ $/mg,"=20").replace(/\t$/mg,"=09")).charAt(0)&&(t="=0D"+t.slice(1)),t=t.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,"\n=0A").replace(/([^\r\n])\n/mg,"$1=0A");for(var r=[],n=t.split("\r\n"),a=0;a<n.length;++a){var i=n[a];if(0==i.length){r.push("");continue}for(var s=0;s<i.length;){var o=76,l=i.slice(s,s+o);"="==l.charAt(o-1)?o--:"="==l.charAt(o-2)?o-=2:"="==l.charAt(o-3)&&(o-=3),l=i.slice(s,s+o),(s+=o)<i.length&&(l+="="),r.push(l)}}return r.join("\r\n")}(f):function(e){for(var t=E(e),r=[],n=0;n<t.length;n+=76)r.push(t.slice(n,n+76));return r.join("\r\n")+"\r\n"}(f))}return a.push(n+"--\r\n"),a.join("\r\n")}(e,n);if(f(e),"zip"===n.fileType)return function(e,r){var n=[],a=[],i=t1(1),s=8*!!(r||{}).compression,o=0,l=0,c=0,f=0,h=e.FullPaths[0],u=h,d=e.FileIndex[0],p=[],m=0;for(o=1;o<e.FullPaths.length;++o)if(u=e.FullPaths[o].slice(h.length),(d=e.FileIndex[o]).size&&d.content&&"\x01Sh33tJ5"!=u){var g,v=c,b=t1(u.length);for(l=0;l<u.length;++l)b.write_shift(1,127&u.charCodeAt(l));b=b.slice(0,b.l),p[f]=eT.buf(d.content,0);var w=d.content;8==s&&(g=w,w=t?t.deflateRawSync(g):Q(g)),(i=t1(30)).write_shift(4,0x4034b50),i.write_shift(2,20),i.write_shift(2,0),i.write_shift(2,s),d.mt?function(e,t){"string"==typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var n=t.getFullYear()-1980;n=(n=n<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,n)}(i,d.mt):i.write_shift(4,0),i.write_shift(-4,(0,p[f])),i.write_shift(4,(0,w.length)),i.write_shift(4,(0,d.content.length)),i.write_shift(2,b.length),i.write_shift(2,0),c+=i.length,n.push(i),c+=b.length,n.push(b),c+=w.length,n.push(w),(i=t1(46)).write_shift(4,0x2014b50),i.write_shift(2,0),i.write_shift(2,20),i.write_shift(2,0),i.write_shift(2,s),i.write_shift(4,0),i.write_shift(-4,p[f]),i.write_shift(4,w.length),i.write_shift(4,d.content.length),i.write_shift(2,b.length),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(4,0),i.write_shift(4,v),m+=i.l,a.push(i),m+=b.length,a.push(b),++f}return(i=t1(22)).write_shift(4,0x6054b50),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,f),i.write_shift(2,f),i.write_shift(4,m),i.write_shift(4,c),i.write_shift(2,0),D([D(n),D(a),i])}(e,n);var a=function(e){for(var t=0,r=0,n=0;n<e.FileIndex.length;++n){var a=e.FileIndex[n];if(a.content){var i=a.content.length;i>0&&(i<4096?t+=i+63>>6:r+=i+511>>9)}}for(var s=e.FullPaths.length+3>>2,o=t+7>>3,l=t+127>>7,c=o+r+s+l,f=c+127>>7,h=f<=109?0:Math.ceil((f-109)/127);c+f+h+127>>7>f;)h=++f<=109?0:Math.ceil((f-109)/127);var u=[1,h,f,l,s,r,t,0];return e.FileIndex[0].size=t<<6,u[7]=(e.FileIndex[0].start=u[0]+u[1]+u[2]+u[3]+u[4]+u[5])+(u[6]+7>>3),u}(e),i=t1(a[7]<<9),s=0,o=0;for(s=0;s<8;++s)i.write_shift(1,m[s]);for(s=0;s<8;++s)i.write_shift(2,0);for(i.write_shift(2,62),i.write_shift(2,3),i.write_shift(2,65534),i.write_shift(2,9),i.write_shift(2,6),s=0;s<3;++s)i.write_shift(2,0);for(i.write_shift(4,0),i.write_shift(4,a[2]),i.write_shift(4,a[0]+a[1]+a[2]+a[3]-1),i.write_shift(4,0),i.write_shift(4,4096),i.write_shift(4,a[3]?a[0]+a[1]+a[2]-1:d),i.write_shift(4,a[3]),i.write_shift(-4,a[1]?a[0]-1:d),i.write_shift(4,a[1]),s=0;s<109;++s)i.write_shift(-4,s<a[2]?a[1]+s:-1);if(a[1])for(o=0;o<a[1];++o){for(;s<236+127*o;++s)i.write_shift(-4,s<a[2]?a[1]+s:-1);i.write_shift(-4,o===a[1]-1?d:o+1)}var l=function(e){for(o+=e;s<o-1;++s)i.write_shift(-4,s+1);e&&(++s,i.write_shift(-4,d))};for(o=(s=0)+a[1];s<o;++s)i.write_shift(-4,v.DIFSECT);for(o+=a[2];s<o;++s)i.write_shift(-4,v.FATSECT);l(a[3]),l(a[4]);for(var c=0,h=0,u=e.FileIndex[0];c<e.FileIndex.length;++c)(u=e.FileIndex[c]).content&&((h=u.content.length)<4096||(u.start=o,l(h+511>>9)));for(l(a[6]+7>>3);511&i.l;)i.write_shift(-4,v.ENDOFCHAIN);for(c=0,o=s=0;c<e.FileIndex.length;++c)(u=e.FileIndex[c]).content&&(h=u.content.length)&&!(h>=4096)&&(u.start=o,l(h+63>>6));for(;511&i.l;)i.write_shift(-4,v.ENDOFCHAIN);for(s=0;s<a[4]<<2;++s){var p=e.FullPaths[s];if(!p||0===p.length){for(c=0;c<17;++c)i.write_shift(4,0);for(c=0;c<3;++c)i.write_shift(4,-1);for(c=0;c<12;++c)i.write_shift(4,0);continue}u=e.FileIndex[s],0===s&&(u.start=u.size?u.start-1:d);var g=0===s&&n.root||u.name;if(h=2*(g.length+1),i.write_shift(64,g,"utf16le"),i.write_shift(2,h),i.write_shift(1,u.type),i.write_shift(1,u.color),i.write_shift(-4,u.L),i.write_shift(-4,u.R),i.write_shift(-4,u.C),u.clsid)i.write_shift(16,u.clsid,"hex");else for(c=0;c<4;++c)i.write_shift(4,0);i.write_shift(4,u.state||0),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,u.start),i.write_shift(4,u.size),i.write_shift(4,0)}for(s=1;s<e.FileIndex.length;++s)if((u=e.FileIndex[s]).size>=4096)if(i.l=u.start+1<<9,x&&Buffer.isBuffer(u.content))u.content.copy(i,i.l,0,u.size),i.l+=u.size+511&-512;else{for(c=0;c<u.size;++c)i.write_shift(1,u.content[c]);for(;511&c;++c)i.write_shift(1,0)}for(s=1;s<e.FileIndex.length;++s)if((u=e.FileIndex[s]).size>0&&u.size<4096)if(x&&Buffer.isBuffer(u.content))u.content.copy(i,i.l,0,u.size),i.l+=u.size+63&-64;else{for(c=0;c<u.size;++c)i.write_shift(1,u.content[c]);for(;63&c;++c)i.write_shift(1,0)}if(x)i.l=i.length;else for(;i.l<i.length;)i.write_shift(1,0);return i}r.version="1.2.1";var u=64,d=-2,p="d0cf11e0a1b11ae1",m=[208,207,17,224,161,177,26,225],g="00000000000000000000000000000000",v={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:-2,FREESECT:-1,HEADER_SIGNATURE:p,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:g,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function b(e){for(var t=Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}for(var w=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],T=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],y=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],C="undefined"!=typeof Uint8Array,R=C?new Uint8Array(256):[],P=0;P<256;++P)R[P]=function(e){var t=(e<<1|e<<11)&139536|(e<<5|e<<15)&558144;return(t>>16|t>>8|t)&255}(P);function M(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=5?0:e[n+1]<<8))>>>r&7}function L(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=3?0:e[n+1]<<8))>>>r&31}function F(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=1?0:e[n+1]<<8))>>>r&127}function U(e,t,r){var n=7&t,a=t>>>3,i=(1<<r)-1,s=e[a]>>>n;return r<8-n||(s|=e[a+1]<<8-n,r<16-n||(s|=e[a+2]<<16-n,r<24-n))?s&i:(s|=e[a+3]<<24-n)&i}function B(e,t,r){var n=7&t,a=t>>>3;return n<=5?e[a]|=(7&r)<<n:(e[a]|=r<<n&255,e[a+1]=(7&r)>>8-n),t+3}function W(e,t,r){var n=t>>>3;return r<<=7&t,e[n]|=255&r,r>>>=8,e[n+1]=r,t+8}function H(e,t,r){var n=t>>>3;return r<<=7&t,e[n]|=255&r,r>>>=8,e[n+1]=255&r,e[n+2]=r>>>8,t+16}function j(e,t){var r=e.length,n=2*r>t?2*r:t+5,a=0;if(r>=t)return e;if(x){var i=k(n);if(e.copy)e.copy(i);else for(;a<e.length;++a)i[a]=e[a];return i}if(C){var s=new Uint8Array(n);if(s.set)s.set(e);else for(;a<r;++a)s[a]=e[a];return s}return e.length=n,e}function z(e){for(var t=Array(e),r=0;r<e;++r)t[r]=0;return t}function G(e,t,r){var n=1,a=0,i=0,s=0,o=0,l=e.length,c=C?new Uint16Array(32):z(32);for(i=0;i<32;++i)c[i]=0;for(i=l;i<r;++i)e[i]=0;l=e.length;var f=C?new Uint16Array(l):z(l);for(i=0;i<l;++i)c[a=e[i]]++,n<a&&(n=a),f[i]=0;for(i=1,c[0]=0;i<=n;++i)c[i+16]=o=o+c[i-1]<<1;for(i=0;i<l;++i)0!=(o=e[i])&&(f[i]=c[o+16]++);var h=0;for(i=0;i<l;++i)if(0!=(h=e[i]))for(o=function(e,t){var r=R[255&e];return t<=8?r>>>8-t:(r=r<<8|R[e>>8&255],t<=16)?r>>>16-t:(r=r<<8|R[e>>16&255])>>>24-t}(f[i],n)>>n-h,s=(1<<n+4-h)-1;s>=0;--s)t[o|s<<h]=15&h|i<<4;return n}var V=C?new Uint16Array(512):z(512),K=C?new Uint16Array(32):z(32);if(!C){for(var Y=0;Y<512;++Y)V[Y]=0;for(Y=0;Y<32;++Y)K[Y]=0}for(var X=[],J=0;J<32;J++)X.push(5);G(X,K,32);var q=[];for(J=0;J<=143;J++)q.push(8);for(;J<=255;J++)q.push(9);for(;J<=279;J++)q.push(7);for(;J<=287;J++)q.push(8);G(q,V,288);var Z=function(){for(var e=C?new Uint8Array(32768):[],t=0,r=0;t<y.length-1;++t)for(;r<y[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var n=C?new Uint8Array(259):[];for(t=0,r=0;t<T.length-1;++t)for(;r<T[t+1];++r)n[r]=t;return function(t,r){if(t.length<8){for(var a=0;a<t.length;){var i=Math.min(65535,t.length-a),s=a+i==t.length;for(r.write_shift(1,+s),r.write_shift(2,i),r.write_shift(2,65535&~i);i-- >0;)r[r.l++]=t[a++]}return r.l}return function(t,r){for(var a=0,i=0,s=C?new Uint16Array(32768):[];i<t.length;){var o=Math.min(65535,t.length-i);if(o<10){for(7&(a=B(r,a,+(i+o==t.length)))&&(a+=8-(7&a)),r.l=a/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);o-- >0;)r[r.l++]=t[i++];a=8*r.l;continue}a=B(r,a,+(i+o==t.length)+2);for(var l=0;o-- >0;){var c,f,h=t[i],u=-1,d=0;if((u=s[l=(l<<5^h)&32767])&&((u|=-32768&i)>i&&(u-=32768),u<i))for(;t[u+d]==t[i+d]&&d<250;)++d;if(d>2){(h=n[d])<=22?a=W(r,a,R[h+1]>>1)-1:(W(r,a,3),W(r,a+=5,R[h-23]>>5),a+=3);var p=h<8?0:h-4>>2;p>0&&(H(r,a,d-T[h]),a+=p),a=W(r,a,R[h=e[i-u]]>>3)-3;var m=h<4?0:h-2>>1;m>0&&(H(r,a,i-u-y[h]),a+=m);for(var g=0;g<d;++g)s[l]=32767&i,l=(l<<5^t[i])&32767,++i;o-=d-1}else h<=143?h+=48:(f=(1&(f=1))<<(7&(c=a)),r[c>>>3]|=f,a=c+1),a=W(r,a,R[h]),s[l]=32767&i,++i}a=W(r,a,0)-1}return r.l=(a+7)/8|0,r.l}(t,r)}}();function Q(e){var t=t1(50+Math.floor(1.1*e.length)),r=Z(e,t);return t.slice(0,r)}var ee=C?new Uint16Array(32768):z(32768),et=C?new Uint16Array(32768):z(32768),er=C?new Uint16Array(128):z(128),en=1,ea=1;function ei(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[_(t),2];for(var r=0,n=0,a=k(t||262144),i=0,s=a.length>>>0,o=0,l=0;(1&n)==0;){if(n=M(e,r),r+=3,n>>>1==0){7&r&&(r+=8-(7&r));var c=e[r>>>3]|e[(r>>>3)+1]<<8;if(r+=32,c>0)for(!t&&s<i+c&&(s=(a=j(a,i+c)).length);c-- >0;)a[i++]=e[r>>>3],r+=8;continue}for(n>>1==1?(o=9,l=5):(r=function(e,t){var r,n,a,i=L(e,t)+257,s=L(e,t+=5)+1;t+=5;var o=(n=7&(r=t),((e[a=r>>>3]|(n<=4?0:e[a+1]<<8))>>>n&15)+4);t+=4;for(var l=0,c=C?new Uint8Array(19):z(19),f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],h=1,u=C?new Uint8Array(8):z(8),d=C?new Uint8Array(8):z(8),p=c.length,m=0;m<o;++m)c[w[m]]=l=M(e,t),h<l&&(h=l),u[l]++,t+=3;var g=0;for(m=1,u[0]=0;m<=h;++m)d[m]=g=g+u[m-1]<<1;for(m=0;m<p;++m)0!=(g=c[m])&&(f[m]=d[g]++);var v=0;for(m=0;m<p;++m)if(0!=(v=c[m])){g=R[f[m]]>>8-v;for(var b=(1<<7-v)-1;b>=0;--b)er[g|b<<v]=7&v|m<<3}var T=[];for(h=1;T.length<i+s;)switch(g=er[F(e,t)],t+=7&g,g>>>=3){case 16:for(l=3+function(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=6?0:e[n+1]<<8))>>>r&3}(e,t),t+=2,g=T[T.length-1];l-- >0;)T.push(g);break;case 17:for(l=3+M(e,t),t+=3;l-- >0;)T.push(0);break;case 18:for(l=11+F(e,t),t+=7;l-- >0;)T.push(0);break;default:T.push(g),h<g&&(h=g)}var y=T.slice(0,i),E=T.slice(i);for(m=i;m<286;++m)y[m]=0;for(m=s;m<30;++m)E[m]=0;return en=G(y,ee,286),ea=G(E,et,30),t}(e,r),o=en,l=ea);;){!t&&s<i+32767&&(s=(a=j(a,i+32767)).length);var f=U(e,r,o),h=n>>>1==1?V[f]:ee[f];if(r+=15&h,((h>>>=4)>>>8&255)==0)a[i++]=h;else if(256==h)break;else{var u=(h-=257)<8?0:h-4>>2;u>5&&(u=0);var d=i+T[h];u>0&&(d+=U(e,r,u),r+=u),f=U(e,r,l),r+=15&(h=n>>>1==1?K[f]:et[f]);var p=(h>>>=4)<4?0:h-2>>1,m=y[h];for(p>0&&(m+=U(e,r,p),r+=p),!t&&s<d&&(s=(a=j(a,d+100)).length);i<d;)a[i]=a[i-m],++i}}}return t?[a,r+7>>>3]:[a.slice(0,i),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function es(e,t){if(e)"undefined"!=typeof console&&console.error(t);else throw Error(t)}function eo(e,r){tZ(e,0);var n={FileIndex:[],FullPaths:[]};c(n,{root:r.root});for(var a=e.length-4;(80!=e[a]||75!=e[a+1]||5!=e[a+2]||6!=e[a+3])&&a>=0;)--a;e.l=a+4,e.l+=4;var s=e.read_shift(2);e.l+=6;var o=e.read_shift(4);for(a=0,e.l=o;a<s;++a){e.l+=20;var l=e.read_shift(4),f=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d=e.read_shift(2);e.l+=8;var p=e.read_shift(4),m=i(e.slice(e.l+h,e.l+h+u));e.l+=h+u+d;var g=e.l;e.l=p+4,function(e,r,n,a,s){e.l+=2;var o,l,c,f,h,u,d,p=e.read_shift(2),m=e.read_shift(2),g=(o=65535&e.read_shift(2),l=65535&e.read_shift(2),c=new Date,f=31&l,h=15&(l>>>=5),l>>>=4,c.setMilliseconds(0),c.setFullYear(l+1980),c.setMonth(h-1),c.setDate(f),u=31&o,d=63&(o>>>=5),o>>>=6,c.setHours(o),c.setMinutes(d),c.setSeconds(u<<1),c);if(8257&p)throw Error("Unsupported ZIP encryption");for(var v=e.read_shift(4),b=e.read_shift(4),w=e.read_shift(4),T=e.read_shift(2),y=e.read_shift(2),E="",S=0;S<T;++S)E+=String.fromCharCode(e[e.l++]);if(y){var x=i(e.slice(e.l,e.l+y));(x[21589]||{}).mt&&(g=x[21589].mt),((s||{})[21589]||{}).mt&&(g=s[21589].mt)}e.l+=y;var A=e.slice(e.l,e.l+b);switch(m){case 8:A=function(e,r){if(!t)return ei(e,r);var n=new t.InflateRaw,a=n._processChunk(e.slice(e.l),n._finishFlushFlag);return e.l+=n.bytesRead,a}(e,w);break;case 0:break;default:throw Error("Unsupported ZIP Compression method "+m)}var _=!1;8&p&&(0x8074b50==e.read_shift(4)&&(e.read_shift(4),_=!0),b=e.read_shift(4),w=e.read_shift(4)),b!=r&&es(_,"Bad compressed size: "+r+" != "+b),w!=n&&es(_,"Bad uncompressed size: "+n+" != "+w),ec(a,E,A,{unsafe:!0,mt:g})}(e,l,f,n,m),e.l=g}return n}var el={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function ec(e,t,r,n){var i=n&&n.unsafe;i||c(e);var s=!i&&ey.find(e,t);if(!s){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),s={name:a(t),type:2},e.FileIndex.push(s),e.FullPaths.push(o),i||ey.utils.cfb_gc(e)}return s.content=r,s.size=r?r.length:0,n&&(n.CLSID&&(s.clsid=n.CLSID),n.mt&&(s.mt=n.mt),n.ct&&(s.ct=n.ct)),s}return r.find=function(e,t){var r=e.FullPaths.map(function(e){return e.toUpperCase()}),n=r.map(function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]}),a=!1;47===t.charCodeAt(0)?(a=!0,t=r[0].slice(0,-1)+t):a=-1!==t.indexOf("/");var i=t.toUpperCase(),s=!0===a?r.indexOf(i):n.indexOf(i);if(-1!==s)return e.FileIndex[s];var o=!i.match(I);for(i=i.replace(N,""),o&&(i=i.replace(I,"!")),s=0;s<r.length;++s)if((o?r[s].replace(I,"!"):r[s]).replace(N,"")==i||(o?n[s].replace(I,"!"):n[s]).replace(N,"")==i)return e.FileIndex[s];return null},r.read=function(t,r){var n=r&&r.type;switch(!n&&x&&Buffer.isBuffer(t)&&(n="buffer"),n||"base64"){case"file":return s(),o(e.readFileSync(t),r);case"base64":return o(O(S(t)),r);case"binary":return o(O(t),r)}return o(t,r)},r.parse=o,r.write=function(t,r){var n=h(t,r);switch(r&&r.type||"buffer"){case"file":s(),e.writeFileSync(r.filename,n);break;case"binary":return"string"==typeof n?n:b(n);case"base64":return E("string"==typeof n?n:b(n));case"buffer":if(x)return Buffer.isBuffer(n)?n:A(n);case"array":return"string"==typeof n?O(n):n}return n},r.writeFile=function(t,r,n){s();var a=h(t,n);e.writeFileSync(r,a)},r.utils={cfb_new:function(e){var t={};return c(t,e),t},cfb_add:ec,cfb_del:function(e,t){c(e);var r=ey.find(e,t);if(r){for(var n=0;n<e.FileIndex.length;++n)if(e.FileIndex[n]==r)return e.FileIndex.splice(n,1),e.FullPaths.splice(n,1),!0}return!1},cfb_mov:function(e,t,r){c(e);var n=ey.find(e,t);if(n){for(var i=0;i<e.FileIndex.length;++i)if(e.FileIndex[i]==n)return e.FileIndex[i].name=a(r),e.FullPaths[i]=r,!0}return!1},cfb_gc:function(e){f(e,!0)},ReadShift:tK,CheckField:tq,prep_blob:tZ,bconcat:D,use_zlib:function(e){try{var r=new e.InflateRaw;if(r._processChunk(new Uint8Array([3,0]),r._finishFlushFlag),r.bytesRead)t=e;else throw Error("zlib does not expose bytesRead")}catch(e){console.error("cannot use native zlib: "+(e.message||e))}},_deflateRaw:Q,_inflateRaw:ei,consts:v},r}();function eE(e,t,r){if(void 0!==n&&n.writeFileSync)return r?n.writeFileSync(e,t,r):n.writeFileSync(e,t);if("undefined"!=typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=C(t);break;default:throw Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var a="utf8"==r?ti(t):t;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(a,e);if("undefined"!=typeof Blob){var i=new Blob([function(e){if("string"==typeof e)return C(e);if(Array.isArray(e)){if("undefined"==typeof Uint8Array)throw Error("Unsupported");return new Uint8Array(e)}return e}(a)],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(i,e);if("undefined"!=typeof saveAs)return saveAs(i,e);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var s=URL.createObjectURL(i);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(s)},6e4),chrome.downloads.download({url:s,filename:e,saveAs:!0});var o=document.createElement("a");if(null!=o.download)return o.download=e,o.href=s,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(s)},6e4),s}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var l=File(e);return l.open("w"),l.encoding="binary",Array.isArray(t)&&(t=R(t)),l.write(t),l.close(),t}catch(e){if(!e.message||!e.message.match(/onstruct/))throw e}throw Error("cannot save file "+e)}function eS(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function ex(e,t){for(var r=[],n=eS(e),a=0;a!==n.length;++a)null==r[e[n[a]][t]]&&(r[e[n[a]][t]]=n[a]);return r}function eA(e){for(var t=[],r=eS(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function e_(e){for(var t=[],r=eS(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}var ek=new Date(1899,11,30,0,0,0);function eO(e,t){var r=e.getTime();return t&&(r-=1263168e5),(r-(ek.getTime()+(e.getTimezoneOffset()-ek.getTimezoneOffset())*6e4))/864e5}var eC=new Date,eR=ek.getTime()+(eC.getTimezoneOffset()-ek.getTimezoneOffset())*6e4,eD=eC.getTimezoneOffset();function eN(e){var t=new Date;return t.setTime(24*e*36e5+eR),t.getTimezoneOffset()!==eD&&t.setTime(t.getTime()+(t.getTimezoneOffset()-eD)*6e4),t}var eI=new Date("2017-02-19T19:06:09.000Z"),eP=isNaN(eI.getFullYear())?new Date("2/19/17"):eI,eM=2017==eP.getFullYear();function eL(e,t){var r=new Date(e);if(eM)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==eP.getFullYear()&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+a[0],a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-60*i.getTimezoneOffset()*1e3)),i}function eF(e,t){if(x&&Buffer.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return ti(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return ti(v(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return ti(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return ti(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"\x80","‚":"\x82",ƒ:"\x83","„":"\x84","…":"\x85","†":"\x86","‡":"\x87",ˆ:"\x88","‰":"\x89",Š:"\x8a","‹":"\x8b",Œ:"\x8c",Ž:"\x8e","‘":"\x91","’":"\x92","“":"\x93","”":"\x94","•":"\x95","–":"\x96","—":"\x97","˜":"\x98","™":"\x99",š:"\x9a","›":"\x9b",œ:"\x9c",ž:"\x9e",Ÿ:"\x9f"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(e){return r[e]||e})}catch(e){}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function eU(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=eU(e[r]));return t}function eB(e,t){for(var r="";r.length<t;)r+=e;return r}function eW(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return isNaN(t=Number(n))&&isNaN(t=Number(n=n.replace(/[(](.*)[)]/,function(e,t){return r=-r,t})))?t:t/r}var eH=["january","february","march","april","may","june","july","august","september","october","november","december"];function ej(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==eH.indexOf(s))return r}else if(s.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||i>1)&&101!=n?t:e.match(/[^-0-9:,\/\\]/)?r:t}function ez(e){return e?e.content&&e.type?eF(e.content,!0):e.data?b(e.data):e.asNodeBuffer&&x?b(e.asNodeBuffer().toString("binary")):e.asBinary?b(e.asBinary()):e._data&&e._data.getContent?b(eF(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function eG(e,t){for(var r=e.FullPaths||eS(e.files),n=t.toLowerCase().replace(/[\/]/g,"\\"),a=n.replace(/\\/g,"/"),i=0;i<r.length;++i){var s=r[i].replace(/^Root Entry[\/]/,"").toLowerCase();if(n==s||a==s)return e.files?e.files[r[i]]:e.FileIndex[i]}return null}function eV(e,t){var r=eG(e,t);if(null==r)throw Error("Cannot find file "+t+" in zip");return r}function eK(e,t,r){if(!r){var n;return(n=eV(e,t))&&".bin"===n.name.slice(-4)?function(e){if(!e)return null;if(e.data)return g(e.data);if(e.asNodeBuffer&&x)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return"string"==typeof t?g(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}(n):ez(n)}if(!t)return null;try{return eK(e,t)}catch(e){return null}}function eY(e,t,r){if(!r)return ez(eV(e,t));if(!t)return null;try{return eY(e,t)}catch(e){return null}}function eX(e,t,r){if(e.FullPaths){if("string"==typeof r){var n;return n=x?A(r):function(e){for(var t=[],r=0,n=e.length+250,a=_(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)a[r++]=s;else if(s<2048)a[r++]=192|s>>6&31,a[r++]=128|63&s;else if(s>=55296&&s<57344){s=(1023&s)+64;var o=1023&e.charCodeAt(++i);a[r++]=240|s>>8&7,a[r++]=128|s>>2&63,a[r++]=128|o>>6&15|(3&s)<<4,a[r++]=128|63&o}else a[r++]=224|s>>12&15,a[r++]=128|s>>6&63,a[r++]=128|63&s;r>n&&(t.push(a.slice(0,r)),r=0,a=_(65535),n=65530)}return t.push(a.slice(0,r)),D(t)}(r),ey.utils.cfb_add(e,t,n)}ey.utils.cfb_add(e,t,r)}else e.file(t,r)}function e$(){return ey.utils.cfb_new()}var eJ='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',eq=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,eZ=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,eQ=eJ.match(eZ)?eZ:/<[^>]*>/g,e1=/<(\/?)\w+:/;function e0(e,t,r){for(var n={},a=0,i=0;a!==e.length&&32!==(i=e.charCodeAt(a))&&10!==i&&13!==i;++a);if(t||(n[0]=e.slice(0,a)),a===e.length)return n;var s=e.match(eq),o=0,l="",c=0,f="",h="",u=1;if(s)for(c=0;c!=s.length;++c){for(i=0,h=s[c];i!=h.length&&61!==h.charCodeAt(i);++i);for(f=h.slice(0,i).trim();32==h.charCodeAt(i+1);)++i;for(o=0,u=+(34==(a=h.charCodeAt(i+1))||39==a),l=h.slice(i+1+u,h.length-u);o!=f.length&&58!==f.charCodeAt(o);++o);if(o===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),n[f]=l,r||(n[f.toLowerCase()]=l);else{var d=(5===o&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(o+1);if(n[d]&&"ext"==f.slice(o-3,o))continue;n[d]=l,r||(n[d.toLowerCase()]=l)}}return n}var e2=eA({"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"}),e4=/[&<>'"]/g,e3=/[\u0000-\u0008\u000b-\u001f]/g;function e5(e){return(e+"").replace(e4,function(e){return e2[e]}).replace(e3,function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"})}function e6(e){return e5(e).replace(/ /g,"_x0020_")}var e8=/[\u0000-\u001f]/g;function e7(e){return(e+"").replace(e4,function(e){return e2[e]}).replace(/\n/g,"<br/>").replace(e8,function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})}function e9(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function te(e){for(var t="",r=0,n=0,a=0,i=0,s=0,o=0;r<e.length;){if((n=e.charCodeAt(r++))<128){t+=String.fromCharCode(n);continue}if(a=e.charCodeAt(r++),n>191&&n<224){t+=String.fromCharCode((31&n)<<6|63&a);continue}if(i=e.charCodeAt(r++),n<240){t+=String.fromCharCode((15&n)<<12|(63&a)<<6|63&i);continue}t+=String.fromCharCode(55296+((o=((7&n)<<18|(63&a)<<12|(63&i)<<6|63&e.charCodeAt(r++))-65536)>>>10&1023)),t+=String.fromCharCode(56320+(1023&o))}return t}function tt(e){var t,r,n,a=_(2*e.length),i=1,s=0,o=0;for(r=0;r<e.length;r+=i)i=1,(n=e.charCodeAt(r))<128?t=n:n<224?(t=(31&n)*64+(63&e.charCodeAt(r+1)),i=2):n<240?(t=(15&n)*4096+(63&e.charCodeAt(r+1))*64+(63&e.charCodeAt(r+2)),i=3):(i=4,o=55296+((t=(7&n)*262144+(63&e.charCodeAt(r+1))*4096+(63&e.charCodeAt(r+2))*64+(63&e.charCodeAt(r+3))-65536)>>>10&1023),t=56320+(1023&t)),0!==o&&(a[s++]=255&o,a[s++]=o>>>8,o=0),a[s++]=t%256,a[s++]=t>>>8;return a.slice(0,s).toString("ucs2")}function tr(e){return A(e,"binary").toString("utf8")}var tn="foo bar baz\xe2\x98\x83\xf0\x9f\x8d\xa3",ta=x&&(tr(tn)==te(tn)&&tr||tt(tn)==te(tn)&&tt)||te,ti=x?function(e){return A(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,a=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(63&n)));break;case n>=55296&&n<57344:n-=55296,t.push(String.fromCharCode(240+((a=e.charCodeAt(r++)-56320+(n<<10))>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)))}return t.join("")},ts=function(){var e=[["nbsp"," "],["middot","\xb7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(e){return[RegExp("&"+e[0]+";","ig"),e[1]]});return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),n=0;n<e.length;++n)r=r.replace(e[n][0],e[n][1]);return r}}(),to=/<\/?(?:vt:)?variant>/g,tl=/<(?:vt:)([^>]*)>([\s\S]*)</;function tc(e,t){var r=e0(e),n=e.match((null)(r.baseType))||[],a=[];if(n.length!=r.size){if(t.WTF)throw Error("unexpected vector length "+n.length+" != "+r.size);return a}return n.forEach(function(e){var t=e.replace(to,"").match(tl);t&&a.push({v:ta(t[2]),t:t[1]})}),a}var tf=/(^\s|\s$|\n)/;function th(e,t){return"<"+e+(t.match(tf)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function tu(e){return eS(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function td(e,t,r){return"<"+e+(null!=r?tu(r):"")+(null!=t?(t.match(tf)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function tp(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(e){if(t)throw e}return""}function tm(e){if(x&&Buffer.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return ta(R(function e(t){if("undefined"==typeof ArrayBuffer)throw Error("Unsupported");if(t instanceof ArrayBuffer)return e(new Uint8Array(t));for(var r=Array(t.length),n=0;n<t.length;++n)r[n]=t[n];return r}(e)));throw Error("Bad input format: expected Buffer or string")}var tg=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,tv={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},tb=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],tw={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"},tT=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var n=0,a=e[0][r].length;n<a;n+=10240)t.push.apply(t,e[0][r].slice(n,n+10240));return t},ty=x?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(e){return Buffer.isBuffer(e)?e:A(e)})):tT(e)}:tT,tE=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(tj(e,a)));return n.join("").replace(N,"")},tS=x?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(N,""):tE(e,t,r)}:tE,tx=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},tA=x?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):tx(e,t,r)}:tx,t_=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(tH(e,a)));return n.join("")},tk=x?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):t_(e,t,r)}:t_,tO=function(e,t){var r=tG(e,t);return r>0?tk(e,t+4,t+4+r-1):""},tC=tO,tR=function(e,t){var r=tG(e,t);return r>0?tk(e,t+4,t+4+r-1):""},tD=tR,tN=function(e,t){var r=2*tG(e,t);return r>0?tk(e,t+4,t+4+r-1):""},tI=tN,tP=function(e,t){var r=tG(e,t);return r>0?tS(e,t+4,t+4+r):""},tM=tP,tL=function(e,t){var r=tG(e,t);return r>0?tk(e,t+4,t+4+r):""},tF=tL,tU=function(e,t){for(var r=1-2*(e[t+7]>>>7),n=((127&e[t+7])<<4)+(e[t+6]>>>4&15),a=15&e[t+6],i=5;i>=0;--i)a=256*a+e[t+i];return 2047==n?0==a?1/0*r:NaN:(0==n?n=-1022:(n-=1023,a+=0x10000000000000),r*Math.pow(2,n-52)*a)},tB=tU,tW=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};x&&(tC=function(e,t){if(!Buffer.isBuffer(e))return tO(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tD=function(e,t){if(!Buffer.isBuffer(e))return tR(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tI=function(e,t){if(!Buffer.isBuffer(e))return tN(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},tM=function(e,t){if(!Buffer.isBuffer(e))return tP(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},tF=function(e,t){if(!Buffer.isBuffer(e))return tL(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},tB=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):tU(e,t)},tW=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array}),void 0!==a&&(tS=function(e,t,r){return a.utils.decode(1200,e.slice(t,r)).replace(N,"")},tk=function(e,t,r){return a.utils.decode(65001,e.slice(t,r))},tC=function(e,t){var r=tG(e,t);return r>0?a.utils.decode(f,e.slice(t+4,t+4+r-1)):""},tD=function(e,t){var r=tG(e,t);return r>0?a.utils.decode(c,e.slice(t+4,t+4+r-1)):""},tI=function(e,t){var r=2*tG(e,t);return r>0?a.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},tM=function(e,t){var r=tG(e,t);return r>0?a.utils.decode(1200,e.slice(t+4,t+4+r)):""},tF=function(e,t){var r=tG(e,t);return r>0?a.utils.decode(65001,e.slice(t+4,t+4+r)):""});var tH=function(e,t){return e[t]},tj=function(e,t){return 256*e[t+1]+e[t]},tz=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-((65535-r+1)*1)},tG=function(e,t){return 0x1000000*e[t+3]+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},tV=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]};function tK(e,t){var r,n,i,s,o,l,f="",h=[];switch(t){case"dbcs":if(l=this.l,x&&Buffer.isBuffer(this))f=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)f+=String.fromCharCode(tj(this,l)),l+=2;e*=2;break;case"utf8":f=tk(this,this.l,this.l+e);break;case"utf16le":e*=2,f=tS(this,this.l,this.l+e);break;case"wstr":if(void 0===a)return tK.call(this,e,"dbcs");f=a.utils.decode(c,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":f=tC(this,this.l),e=4+tG(this,this.l);break;case"lpstr-cp":f=tD(this,this.l),e=4+tG(this,this.l);break;case"lpwstr":f=tI(this,this.l),e=4+2*tG(this,this.l);break;case"lpp4":e=4+tG(this,this.l),f=tM(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+tG(this,this.l),f=tF(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,f="";0!==(i=tH(this,this.l+e++));)h.push(w(i));f=h.join("");break;case"_wstr":for(e=0,f="";0!==(i=tj(this,this.l+e));)h.push(w(i)),e+=2;e+=2,f=h.join("");break;case"dbcs-cont":for(o=0,f="",l=this.l;o<e;++o){if(this.lens&&-1!==this.lens.indexOf(l))return i=tH(this,l),this.l=l+1,s=tK.call(this,e-o,i?"dbcs-cont":"sbcs-cont"),h.join("")+s;h.push(w(tj(this,l))),l+=2}f=h.join(""),e*=2;break;case"cpstr":if(void 0!==a){f=a.utils.decode(c,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(o=0,f="",l=this.l;o!=e;++o){if(this.lens&&-1!==this.lens.indexOf(l))return i=tH(this,l),this.l=l+1,s=tK.call(this,e-o,i?"dbcs-cont":"sbcs-cont"),h.join("")+s;h.push(w(tH(this,l))),l+=1}f=h.join("");break;default:switch(e){case 1:return r=tH(this,this.l),this.l++,r;case 2:return r=("i"===t?tz:tj)(this,this.l),this.l+=2,r;case 4:case -4:if("i"===t||(128&this[this.l+3])==0)return r=(e>0?tV:function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]})(this,this.l),this.l+=4,r;return n=tG(this,this.l),this.l+=4,n;case 8:case -8:if("f"===t)return n=8==e?tB(this,this.l):tB([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:f=tA(this,this.l,e)}}return this.l+=e,f}var tY=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},tX=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},t$=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function tJ(e,t,r){var n=0,i=0;if("dbcs"===r){for(i=0;i!=t.length;++i)t$(this,t.charCodeAt(i),this.l+2*i);n=2*t.length}else if("sbcs"===r){if(void 0!==a&&874==f)for(i=0;i!=t.length;++i){var s=a.utils.encode(f,t.charAt(i));this[this.l+i]=s[0]}else for(i=0,t=t.replace(/[^\x00-\x7F]/g,"_");i!=t.length;++i)this[this.l+i]=255&t.charCodeAt(i);n=t.length}else if("hex"===r){for(;i<e;++i)this[this.l++]=parseInt(t.slice(2*i,2*i+2),16)||0;return this}else if("utf16le"===r){var o=Math.min(this.l+e,this.length);for(i=0;i<Math.min(t.length,e);++i){var l=t.charCodeAt(i);this[this.l++]=255&l,this[this.l++]=l>>8}for(;this.l<o;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=255&t;break;case 2:n=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:n=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:n=4,tY(this,t,this.l);break;case 8:if(n=8,"f"===r){!function(e,t,r){var n=(t<0||1/t==-1/0)<<7,a=0,i=0,s=n?-t:t;isFinite(s)?0==s?a=i=0:(a=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-a),a<=-1023&&(!isFinite(i)||i<0x10000000000000)?a=-1022:(i-=0x10000000000000,a+=1023)):(a=2047,i=26985*!!isNaN(t));for(var o=0;o<=5;++o,i/=256)e[r+o]=255&i;e[r+6]=(15&a)<<4|15&i,e[r+7]=a>>4|n}(this,t,this.l);break}case 16:break;case -4:n=4,tX(this,t,this.l)}return this.l+=n,this}function tq(e,t){var r=tA(this,this.l,e.length>>1);if(r!==e)throw Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function tZ(e,t){e.l=t,e.read_shift=tK,e.chk=tq,e.write_shift=tJ}function tQ(e,t){e.l+=t}function t1(e){var t=_(e);return tZ(t,0),t}function t0(){var e=[],t=x?256:2048,r=function(e){var t=t1(e);return tZ(t,0),t},n=r(t),a=function(){n&&(n.length>n.l&&((n=n.slice(0,n.l)).l=n.length),n.length>0&&e.push(n),n=null)},i=function(e){return n&&e<n.length-n.l?n:(a(),n=r(Math.max(e+1,t)))};return{next:i,push:function(e){a(),null==(n=e).l&&(n.l=n.length),i(t)},end:function(){return a(),D(e)},_bufs:e}}function t2(e,t,r,n){var a,i=+t;if(!isNaN(i)){n||(n=iy[i].p||(r||[]).length||0),a=1+ +(i>=128)+1,n>=128&&++a,n>=16384&&++a,n>=2097152&&++a;var s=e.next(a);i<=127?s.write_shift(1,i):(s.write_shift(1,(127&i)+128),s.write_shift(1,i>>7));for(var o=0;4!=o;++o)if(n>=128)s.write_shift(1,(127&n)+128),n>>=7;else{s.write_shift(1,n);break}n>0&&tW(r)&&e.push(r)}}function t4(e,t,r){var n=eU(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function t3(e,t,r){var n=eU(e);return n.s=t4(n.s,t.s,r),n.e=t4(n.e,t.s,r),n}function t5(e,t){if(e.cRel&&e.c<0)for(e=eU(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=eU(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=rr(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=r.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")),r}function t6(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?t5(e.s,t.biff)+":"+t5(e.e,t.biff):(e.s.rRel?"":"$")+t7(e.s.r)+":"+(e.e.rRel?"":"$")+t7(e.e.r):(e.s.cRel?"":"$")+re(e.s.c)+":"+(e.e.cRel?"":"$")+re(e.e.c)}function t8(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function t7(e){return""+(e+1)}function t9(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function re(e){if(e<0)throw Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function rt(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function rr(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function rn(e){var t=e.indexOf(":");return -1==t?{s:rt(e),e:rt(e)}:{s:rt(e.slice(0,t)),e:rt(e.slice(t+1))}}function ra(e,t){return void 0===t||"number"==typeof t?ra(e.s,e.e):("string"!=typeof e&&(e=rr(e)),"string"!=typeof t&&(t=rr(t)),e==t?e:e+":"+t)}function ri(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,i=e.length;for(r=0;n<i&&!((a=e.charCodeAt(n)-64)<1)&&!(a>26);++n)r=26*r+a;for(t.s.c=--r,r=0;n<i&&!((a=e.charCodeAt(n)-48)<0)&&!(a>9);++n)r=10*r+a;if(t.s.r=--r,n===i||10!=a)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=i&&!((a=e.charCodeAt(n)-64)<1)&&!(a>26);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=i&&!((a=e.charCodeAt(n)-48)<0)&&!(a>9);++n)r=10*r+a;return t.e.r=--r,t}function rs(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=em(e.z,r?eO(t):t)}catch(e){}try{return e.w=em((e.XF||{}).numFmtId||14*!!r,r?eO(t):t)}catch(e){return""+t}}function ro(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t)?rM[e.v]||e.v:void 0==t?rs(e,e.v):rs(e,t)}function rl(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function rc(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense,i=e||(a?[]:{}),s=0,o=0;if(i&&null!=n.origin){if("number"==typeof n.origin)s=n.origin;else{var l="string"==typeof n.origin?rt(n.origin):n.origin;s=l.r,o=l.c}i["!ref"]||(i["!ref"]="A1:A1")}var c={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var f=ri(i["!ref"]);c.s.c=f.s.c,c.s.r=f.s.r,c.e.c=Math.max(c.e.c,f.e.c),c.e.r=Math.max(c.e.r,f.e.r),-1==s&&(c.e.r=s=f.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if(void 0!==t[h][u]){var d={v:t[h][u]},p=s+h,m=o+u;if(c.s.r>p&&(c.s.r=p),c.s.c>m&&(c.s.c=m),c.e.r<p&&(c.e.r=p),c.e.c<m&&(c.e.c=m),!t[h][u]||"object"!=typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=t[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(n.nullError)d.t="e",d.v=0;else{if(!n.sheetStubs)continue;d.t="z"}else"number"==typeof d.v?d.t="n":"boolean"==typeof d.v?d.t="b":d.v instanceof Date?(d.z=n.dateNF||j[14],n.cellDates?(d.t="d",d.w=em(d.z,eO(d.v))):(d.t="n",d.v=eO(d.v),d.w=em(d.z,d.v))):d.t="s";else d=t[h][u];if(a)i[p]||(i[p]=[]),i[p][m]&&i[p][m].z&&(d.z=i[p][m].z),i[p][m]=d;else{var g=rr({c:m,r:p});i[g]&&i[g].z&&(d.z=i[g].z),i[g]=d}}}return c.s.c<1e7&&(i["!ref"]=ra(c)),i}function rf(e,t){return rc(null,e,t)}function rh(e,t){return t||(t=t1(4)),t.write_shift(4,e),t}function ru(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function rd(e,t){var r=!1;return null==t&&(r=!0,t=t1(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function rp(e,t){var r=e.l,n=e.read_shift(1),a=ru(e),i=[],s={t:a,h:a};if((1&n)!=0){for(var o=e.read_shift(4),l=0;l!=o;++l)i.push({ich:e.read_shift(2),ifnt:e.read_shift(2)});s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function rm(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function rg(e,t){return null==t&&(t=t1(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rv(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function rb(e,t){return null==t&&(t=t1(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rw(e){var t=e.read_shift(4);return 0===t||0xffffffff===t?"":e.read_shift(t,"dbcs")}function rT(e,t){var r=!1;return null==t&&(r=!0,t=t1(127)),t.write_shift(4,e.length>0?e.length:0xffffffff),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function ry(e){var t=e.slice(e.l,e.l+4),r=1&t[0],n=2&t[0];e.l+=4;var a=0===n?tB([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):tV(t,0)>>2;return r?a/100:a}function rE(e,t){null==t&&(t=t1(4));var r=0,n=0,a=100*e;if(e==(0|e)&&e>=-0x20000000&&e<0x20000000?n=1:a==(0|a)&&a>=-0x20000000&&a<0x20000000&&(n=1,r=1),n)t.write_shift(-4,((r?a:e)<<2)+(r+2));else throw Error("unsupported RkNumber "+e)}function rS(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var rx=function(e,t){return t||(t=t1(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function rA(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function r_(e,t){return(t||t1(8)).write_shift(8,e,"f")}function rk(e,t){if(t||(t=t1(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var n=e.rgb||"FFFFFF";"number"==typeof n&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}function rO(e,t){var r=e.read_shift(4);switch(r){case 0:return"";case 0xffffffff:case 0xfffffffe:return({2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"})[e.read_shift(4)]||""}if(r>400)throw Error("Unsupported Clipboard: "+r.toString(16));return e.l-=4,e.read_shift(0,1==t?"lpstr":"lpwstr")}var rC=[80,81],rR={1:{n:"CodePage",t:2},2:{n:"Category",t:80},3:{n:"PresentationFormat",t:80},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:80},15:{n:"Company",t:80},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:80},27:{n:"ContentStatus",t:80},28:{n:"Language",t:80},29:{n:"Version",t:80},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},rD={1:{n:"CodePage",t:2},2:{n:"Title",t:80},3:{n:"Subject",t:80},4:{n:"Author",t:80},5:{n:"Keywords",t:80},6:{n:"Comments",t:80},7:{n:"Template",t:80},8:{n:"LastAuthor",t:80},9:{n:"RevNumber",t:80},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:80},19:{n:"DocSecurity",t:3},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},rN={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},rI=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],rP=eU([0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,8388608,32768,128,8421376,8388736,32896,0xc0c0c0,8421504,0x9999ff,0x993366,0xffffcc,0xccffff,6684774,0xff8080,26316,0xccccff,128,0xff00ff,0xffff00,65535,8388736,8388608,32896,255,52479,0xccffff,0xccffcc,0xffff99,0x99ccff,0xff99cc,0xcc99ff,0xffcc99,3368703,3394764,0x99cc00,0xffcc00,0xff9900,0xff6600,6710937,9868950,13158,3381606,13056,3355392,0x993300,0x993366,3355545,3355443,0xffffff,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].map(function(e){return[e>>16&255,e>>8&255,255&e]})),rM={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},rL={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},rF={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},rU={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function rB(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function rW(e,t){var r,n=function(e){for(var t=[],r=eS(e),n=0;n!==r.length;++n)null==t[e[r[n]]]&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}(rF),a=[];a[a.length]=eJ,a[a.length]=td("Types",null,{xmlns:tv.CT,"xmlns:xsd":tv.xsd,"xmlns:xsi":tv.xsi}),a=a.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(e){return td("Default",null,{Extension:e[0],ContentType:e[1]})}));var i=function(n){e[n]&&e[n].length>0&&(r=e[n][0],a[a.length]=td("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:rU[n][t.bookType]||rU[n].xlsx}))},s=function(r){(e[r]||[]).forEach(function(e){a[a.length]=td("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:rU[r][t.bookType]||rU[r].xlsx})})},o=function(t){(e[t]||[]).forEach(function(e){a[a.length]=td("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:n[t][0]})})};return i("workbooks"),s("sheets"),s("charts"),o("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),s("metadata"),o("people"),a.length>2&&(a[a.length]="</Types>",a[1]=a[1].replace("/>",">")),a.join("")}var rH={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function rj(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function rz(e){var t=[eJ,td("Relationships",null,{xmlns:tv.RELS})];return eS(e["!id"]).forEach(function(r){t[t.length]=td("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function rG(e,t,r,n,a,i){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,i?a.TargetMode=i:[rH.HLINK,rH.XPATH,rH.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function rV(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function rK(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+l.version+"</meta:generator></office:meta></office:document-meta>"}var rY=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function rX(e,t,r,n,a){null==a[e]&&null!=t&&""!==t&&(a[e]=t,t=e5(t),n[n.length]=r?td(e,t,r):th(e,t))}function r$(e,t){var r=t||{},n=[eJ,td("cp:coreProperties",null,{"xmlns:cp":tv.CORE_PROPS,"xmlns:dc":tv.dc,"xmlns:dcterms":tv.dcterms,"xmlns:dcmitype":tv.dcmitype,"xmlns:xsi":tv.xsi})],a={};if(!e&&!r.Props)return n.join("");e&&(null!=e.CreatedDate&&rX("dcterms:created","string"==typeof e.CreatedDate?e.CreatedDate:tp(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),null!=e.ModifiedDate&&rX("dcterms:modified","string"==typeof e.ModifiedDate?e.ModifiedDate:tp(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var i=0;i!=rY.length;++i){var s=rY[i],o=r.Props&&null!=r.Props[s[1]]?r.Props[s[1]]:e?e[s[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&rX(s[0],o,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var rJ=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],rq=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function rZ(e){var t=[];return e||(e={}),e.Application="SheetJS",t[t.length]=eJ,t[t.length]=td("Properties",null,{xmlns:tv.EXT_PROPS,"xmlns:vt":tv.vt}),rJ.forEach(function(r){var n;if(void 0!==e[r[1]]){switch(r[2]){case"string":n=e5(String(e[r[1]]));break;case"bool":n=e[r[1]]?"true":"false"}void 0!==n&&(t[t.length]=td(r[0],n))}}),t[t.length]=td("HeadingPairs",td("vt:vector",td("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+td("vt:variant",td("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=td("TitlesOfParts",td("vt:vector",e.SheetNames.map(function(e){return"<vt:lpstr>"+e5(e)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function rQ(e){var t=[eJ,td("Properties",null,{xmlns:tv.CUST_PROPS,"xmlns:vt":tv.vt})];if(!e)return t.join("");var r=1;return eS(e).forEach(function(n){++r,t[t.length]=td("property",function(e,t){switch(typeof e){case"string":var r=td("vt:lpwstr",e5(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return td((0|e)==e?"vt:i4":"vt:r8",e5(String(e)));case"boolean":return td("vt:bool",e?"true":"false")}if(e instanceof Date)return td("vt:filetime",tp(e));throw Error("Unable to serialize "+e)}(e[n],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:e5(n)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var r1={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function r0(e){var t=e.read_shift(4);return new Date((e.read_shift(4)/1e7*0x100000000+t/1e7-0x2b6109100)*1e3).toISOString().replace(/\.000/,"")}function r2(e,t,r){var n=e.l,a=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-n&3;)++e.l;return a}function r4(e,t,r){var n=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(n.length+1&3)&3),n}function r3(e,t,r){return 31===t?r4(e):r2(e,t,r)}function r5(e,t,r){return r3(e,t,4*(!1!==r))}function r6(e,t){for(var r=e.read_shift(4),n={},a=0;a!=r;++a){var i=e.read_shift(4),s=e.read_shift(4);n[i]=e.read_shift(s,1200===t?"utf16le":"utf8").replace(N,"").replace(I,"!"),1200===t&&s%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),n}function r8(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(3&t)>0&&(e.l+=4-(3&t)&3),r}function r7(e,t,r){var n,a,i=e.read_shift(2),s=r||{};if(e.l+=2,12!==t&&i!==t&&-1===rC.indexOf(t)&&((65534&t)!=4126||(65534&i)!=4126))throw Error("Expected type "+t+" saw "+i);switch(12===t?i:t){case 2:return a=e.read_shift(2,"i"),s.raw||(e.l+=2),a;case 3:return e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return e.read_shift(4);case 30:return r2(e,i,4).replace(N,"");case 31:return r4(e);case 64:return r0(e);case 65:return r8(e);case 71:return(n={}).Size=e.read_shift(4),e.l+=n.Size+3-(n.Size-1)%4,n;case 80:return r5(e,i,!s.raw).replace(N,"");case 81:return(function(e,t){if(!t)throw Error("VtUnalignedString must have positive length");return r3(e,t,0)})(e,i).replace(N,"");case 4108:for(var o=e.read_shift(4),l=[],c=0;c<o/2;++c)l.push(function(e){var t=e.l,r=r7(e,81);return 0==e[e.l]&&0==e[e.l+1]&&e.l-t&2&&(e.l+=2),[r,r7(e,3)]}(e));return l;case 4126:case 4127:return 4127==i?function(e){for(var t=e.read_shift(4),r=[],n=0;n!=t;++n){var a=e.l;r[n]=e.read_shift(0,"lpwstr").replace(N,""),e.l-a&2&&(e.l+=2)}return r}(e):function(e){for(var t=e.read_shift(4),r=[],n=0;n!=t;++n)r[n]=e.read_shift(0,"lpstr-cp").replace(N,"");return r}(e);default:throw Error("TypedPropertyValue unrecognized type "+t+" "+i)}}function r9(e,t){var r,n,a,i,s,o=t1(4),l=t1(4);switch(o.write_shift(4,80==e?31:e),e){case 3:l.write_shift(-4,t);break;case 5:(l=t1(8)).write_shift(8,t,"f");break;case 11:l.write_shift(4,+!!t);break;case 64:n=(r=("string"==typeof t?new Date(Date.parse(t)):t).getTime()/1e3+0x2b6109100)%0x100000000,a=(r-n)/0x100000000*1e7,(i=(n*=1e7)/0x100000000|0)>0&&(n%=0x100000000,a+=i),(s=t1(8)).write_shift(4,n),s.write_shift(4,a),l=s;break;case 31:case 80:for((l=t1(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),l.write_shift(0,t,"dbcs");l.l!=l.length;)l.write_shift(1,0);break;default:throw Error("TypedPropertyValue unrecognized type "+e+" "+t)}return D([o,l])}function ne(e,t){var r=e.l,n=e.read_shift(4),a=e.read_shift(4),i=[],s=0,o=0,l=-1,c={};for(s=0;s!=a;++s){var f=e.read_shift(4),h=e.read_shift(4);i[s]=[f,h+r]}i.sort(function(e,t){return e[1]-t[1]});var u={};for(s=0;s!=a;++s){if(e.l!==i[s][1]){var d=!0;if(s>0&&t)switch(t[i[s-1][0]].t){case 2:e.l+2===i[s][1]&&(e.l+=2,d=!1);break;case 80:case 4108:e.l<=i[s][1]&&(e.l=i[s][1],d=!1)}if((!t||0==s)&&e.l<=i[s][1]&&(d=!1,e.l=i[s][1]),d)throw Error("Read Error: Expected address "+i[s][1]+" at "+e.l+" :"+s)}if(t){var m=t[i[s][0]];if(u[m.n]=r7(e,m.t,{raw:!0}),"version"===m.p&&(u[m.n]=String(u[m.n]>>16)+"."+("0000"+String(65535&u[m.n])).slice(-4)),"CodePage"==m.n)switch(u[m.n]){case 0:u[m.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case -536:case 65001:case -535:p(o=u[m.n]>>>0&65535);break;default:throw Error("Unsupported CodePage: "+u[m.n])}}else if(1===i[s][0]){if(p(o=u.CodePage=r7(e,2)),-1!==l){var g=e.l;e.l=i[l][1],c=r6(e,o),e.l=g}}else if(0===i[s][0]){if(0===o){l=s,e.l=i[s+1][1];continue}c=r6(e,o)}else{var v,b=c[i[s][0]];switch(e[e.l]){case 65:e.l+=4,v=r8(e);break;case 30:case 31:e.l+=4,v=r5(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,v=e.read_shift(4,"i");break;case 19:e.l+=4,v=e.read_shift(4);break;case 5:e.l+=4,v=e.read_shift(8,"f");break;case 11:e.l+=4,v=ns(e,4);break;case 64:e.l+=4,v=eL(r0(e));break;default:throw Error("unparsed value: "+e[e.l])}u[b]=v}}return e.l=r+n,u}var nt=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function nr(e,t,r){var n=t1(8),a=[],i=[],s=8,o=0,l=t1(8),c=t1(8);if(l.write_shift(4,2),l.write_shift(4,1200),c.write_shift(4,1),i.push(l),a.push(c),s+=8+l.length,!t){(c=t1(8)).write_shift(4,0),a.unshift(c);var f=[t1(4)];for(f[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];for((l=t1(8+2*(h.length+1)+(h.length%2?0:2))).write_shift(4,o+2),l.write_shift(4,h.length+1),l.write_shift(0,h,"dbcs");l.l!=l.length;)l.write_shift(1,0);f.push(l)}l=D(f),i.unshift(l),s+=8+l.length}for(o=0;o<e.length;++o)if(!(t&&!t[e[o][0]]||nt.indexOf(e[o][0])>-1||rq.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(t){var p=r[d=+t[e[o][0]]];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(m[0]<<16)+(+m[1]||0)}l=r9(p.t,u)}else{var g=function(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return -1}(u);-1==g&&(g=31,u=String(u)),l=r9(g,u)}i.push(l),(c=t1(8)).write_shift(4,t?d:2+o),a.push(c),s+=8+l.length}var v=8*(i.length+1);for(o=0;o<i.length;++o)a[o].write_shift(4,v),v+=i[o].length;return n.write_shift(4,s),n.write_shift(4,i.length),D([n].concat(a).concat(i))}function nn(e,t,r){var n,a=e.content;if(!a)return{};tZ(a,0);var i,s,o,l,c=0;a.chk("feff","Byte Order: "),a.read_shift(2);var f=a.read_shift(4),h=a.read_shift(16);if(h!==ey.utils.consts.HEADER_CLSID&&h!==r)throw Error("Bad PropertySet CLSID "+h);if(1!==(i=a.read_shift(4))&&2!==i)throw Error("Unrecognized #Sets: "+i);if(s=a.read_shift(16),l=a.read_shift(4),1===i&&l!==a.l)throw Error("Length mismatch: "+l+" !== "+a.l);2===i&&(o=a.read_shift(16),c=a.read_shift(4));var u=ne(a,t),d={SystemIdentifier:f};for(var p in u)d[p]=u[p];if(d.FMTID=s,1===i)return d;if(c-a.l==2&&(a.l+=2),a.l!==c)throw Error("Length mismatch 2: "+a.l+" !== "+c);try{n=ne(a,null)}catch(e){}for(p in n)d[p]=n[p];return d.FMTID=[s,o],d}function na(e,t,r,n,a,i){var s=t1(a?68:48),o=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,0x32363237),s.write_shift(16,ey.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,a?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,a?68:48);var l=nr(e,r,n);if(o.push(l),a){var c=nr(a,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+l.length),o.push(c)}return D(o)}function ni(e,t){return e.read_shift(t),null}function ns(e,t){return 1===e.read_shift(t)}function no(e,t){return t||(t=t1(2)),t.write_shift(2,+!!e),t}function nl(e){return e.read_shift(2,"u")}function nc(e,t){return t||(t=t1(2)),t.write_shift(2,e),t}function nf(e,t){for(var r=[],n=e.l+t;e.l<n;)r.push(nl(e,n-e.l));if(n!==e.l)throw Error("Slurp error");return r}function nh(e,t,r){return r||(r=t1(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,+("e"==t)),r}function nu(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),a="sbcs-cont",i=c;r&&r.biff>=8&&(c=1200),r&&8!=r.biff?12==r.biff&&(a="wstr"):e.read_shift(1)&&(a="dbcs-cont"),r.biff>=2&&r.biff<=5&&(a="cpstr");var s=n?e.read_shift(n,a):"";return c=i,s}function nd(e,t,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function np(e,t,r){var n=e.read_shift(r&&2==r.biff?1:2);return 0===n?(e.l++,""):nd(e,n,r)}function nm(e,t,r){if(r.biff>5)return np(e,t,r);var n=e.read_shift(1);return 0===n?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function ng(e,t,r){return r||(r=t1(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function nv(e){var t=e.read_shift(4);return t>0?e.read_shift(t,"utf16le").replace(N,""):""}function nb(e,t){t||(t=t1(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function nw(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function nT(e,t){var r=nw(e,t);return r[3]=0,r}function ny(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function nE(e,t,r,n){return n||(n=t1(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function nS(e){return[e.read_shift(2),ry(e)]}function nx(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function nA(e,t){return t||(t=t1(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function n_(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(1),r:t},e:{c:e.read_shift(1),r:r}}}function nk(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2);return e.l+=12,[r,t,n]}function nO(e){e.l+=2,e.l+=e.read_shift(2)}var nC={0:nO,4:nO,5:nO,6:nO,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:nO,9:nO,10:nO,11:nO,12:nO,13:function(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t},14:nO,15:nO,16:nO,17:nO,18:nO,19:nO,20:nO,21:nk};function nR(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),(t-=2)>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function nD(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;default:throw Error("unsupported BIFF version")}var i=t1(a);return i.write_shift(2,n),i.write_shift(2,t),a>4&&i.write_shift(2,29282),a>6&&i.write_shift(2,1997),a>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function nN(e,t,r){var n=0;r&&2==r.biff||(n=e.read_shift(2));var a=e.read_shift(2);return r&&2==r.biff&&(n=1-(a>>15),a&=32767),[{Unsynced:1&n,DyZero:(2&n)>>1,ExAsc:(4&n)>>2,ExDsc:(8&n)>>3},a]}function nI(e,t,r){var n=e.l+t,a=8!=r.biff&&r.biff?2:4,i=e.read_shift(a),s=e.read_shift(a),o=e.read_shift(2),l=e.read_shift(2);return e.l=n,{s:{r:i,c:o},e:{r:s,c:l}}}function nP(e,t,r,n){var a=r&&5==r.biff;n||(n=t1(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&a&&(i|=1024),n.write_shift(4,i),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function nM(e,t,r){var n,a=ny(e,6);(2==r.biff||9==t)&&++e.l;var i=(n=e.read_shift(1),1===e.read_shift(1)?n:1===n);return a.val=i,a.t=!0===i||!1===i?"b":"e",a}var nL=function(e,t,r){return 0===t?"":nm(e,t,r)};function nF(e,t,r){var n,a=e.read_shift(2),i={fBuiltIn:1&a,fWantAdvise:a>>>1&1,fWantPict:a>>>2&1,fOle:a>>>3&1,fOleLink:a>>>4&1,cf:a>>>5&1023,fIcon:a>>>15&1};return 14849===r.sbcch&&(n=function(e,t,r){e.l+=4,t-=4;var n=e.l+t,a=nu(e,t,r),i=e.read_shift(2);if(i!==(n-=e.l))throw Error("Malformed AddinUdf: padding = "+n+" != "+i);return e.l+=i,a}(e,t-2,r)),i.body=n||e.read_shift(t-2),"string"==typeof n&&(i.Name=n),i}var nU=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function nB(e,t,r){var n,a,i,s,o,l,c,f=e.l+t,h=e.read_shift(2),u=e.read_shift(1),d=e.read_shift(1),p=e.read_shift(r&&2==r.biff?1:2),m=0;(!r||r.biff>=5)&&(5!=r.biff&&(e.l+=2),m=e.read_shift(2),5==r.biff&&(e.l+=2),e.l+=4);var g=nd(e,d,r);32&h&&(g=nU[g.charCodeAt(0)]);var v=f-e.l;return r&&2==r.biff&&--v,{chKey:u,Name:g,itab:m,rgce:f!=e.l&&0!==p&&v>0?(n=e,a=v,i=r,s=p,l=n.l+a,c=aY(n,s,i),l!==n.l&&(o=aK(n,l-n.l,c,i)),[c,o]):[]}}function nW(e,t,r){if(r.biff<8){var n,a,i,s;return n=e,a=t,i=r,3==n[n.l+1]&&n[n.l]++,3==(s=nu(n,a,i)).charCodeAt(0)?s.slice(1):s}for(var o=[],l=e.l+t,c=e.read_shift(r.biff>8?4:2);0!=c--;)o.push(function(e,t,r){var n=r.biff>8?4:2;return[e.read_shift(n),e.read_shift(n,"i"),e.read_shift(n,"i")]}(e,r.biff,r));if(e.l!=l)throw Error("Bad ExternSheet: "+e.l+" != "+l);return o}function nH(e,t,r){var n=n_(e,6);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[n,function(e,t,r){var n,a,i=e.l+t,s=2==r.biff?1:2,o=e.read_shift(s);if(65535==o)return[[],(n=t-2,void(e.l+=n))];var l=aY(e,o,r);return t!==o+s&&(a=aK(e,t-o-s,l,r)),e.l=i,[l,a]}(e,t,r,n)]}var nj={8:function(e,t){var r=e.l+t;e.l+=10;var n=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var a=e.read_shift(1);return e.l+=a,e.l=r,{fmt:n}}};function nz(e,t,r){if(!r.cellStyles)return void(e.l+=t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),i=e.read_shift(n),s=e.read_shift(n),o=e.read_shift(n),l=e.read_shift(2);2==n&&(e.l+=2);var c={s:a,e:i,w:s,ixfe:o,flags:l};return(r.biff>=5||!r.biff)&&(c.level=l>>8&7),c}var nG=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=eA({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var n=r||{};n.dateNF||(n.dateNF="yyyymmdd");var i=rf(function(t,r){var n=[],i=_(1);switch(r.type){case"base64":i=O(S(t));break;case"binary":i=O(t);break;case"buffer":case"array":i=t}tZ(i,0);var s=i.read_shift(1),o=!!(136&s),l=!1,c=!1;switch(s){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:l=!0,o=!0;break;case 140:c=!0;break;default:throw Error("DBF Unsupported Version: "+s.toString(16))}var f=0,h=521;2==s&&(f=i.read_shift(2)),i.l+=3,2!=s&&(f=i.read_shift(4)),f>1048576&&(f=1e6),2!=s&&(h=i.read_shift(2));var u=i.read_shift(2),d=r.codepage||1252;2!=s&&(i.l+=16,i.read_shift(1),0!==i[i.l]&&(d=e[i[i.l]]),i.l+=1,i.l+=2),c&&(i.l+=36);for(var p=[],m={},g=Math.min(i.length,2==s?521:h-10-264*!!l),v=c?32:11;i.l<g&&13!=i[i.l];)switch((m={}).name=a.utils.decode(d,i.slice(i.l,i.l+v)).replace(/[\u0000\r\n].*$/g,""),i.l+=v,m.type=String.fromCharCode(i.read_shift(1)),2!=s&&!c&&(m.offset=i.read_shift(4)),m.len=i.read_shift(1),2==s&&(m.offset=i.read_shift(2)),m.dec=i.read_shift(1),m.name.length&&p.push(m),2!=s&&(i.l+=c?13:14),m.type){case"B":(!l||8!=m.len)&&r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw Error("Unknown Field Type: "+m.type)}if(13!==i[i.l]&&(i.l=h-1),13!==i.read_shift(1))throw Error("DBF Terminator not found "+i.l+" "+i[i.l]);i.l=h;var b=0,w=0;for(w=0,n[0]=[];w!=p.length;++w)n[0][w]=p[w].name;for(;f-- >0;){if(42===i[i.l]){i.l+=u;continue}for(++i.l,n[++b]=[],w=0,w=0;w!=p.length;++w){var T=i.slice(i.l,i.l+p[w].len);i.l+=p[w].len,tZ(T,0);var y=a.utils.decode(d,T);switch(p[w].type){case"C":y.trim().length&&(n[b][w]=y.replace(/\s+$/,""));break;case"D":8===y.length?n[b][w]=new Date(+y.slice(0,4),y.slice(4,6)-1,+y.slice(6,8)):n[b][w]=y;break;case"F":n[b][w]=parseFloat(y.trim());break;case"+":case"I":n[b][w]=c?0x80000000^T.read_shift(-4,"i"):T.read_shift(4,"i");break;case"L":switch(y.trim().toUpperCase()){case"Y":case"T":n[b][w]=!0;break;case"N":case"F":n[b][w]=!1;break;case"":case"?":break;default:throw Error("DBF Unrecognized L:|"+y+"|")}break;case"M":if(!o)throw Error("DBF Unexpected MEMO for type "+s.toString(16));n[b][w]="##MEMO##"+(c?parseInt(y.trim(),10):T.read_shift(4));break;case"N":(y=y.replace(/\u0000/g,"").trim())&&"."!=y&&(n[b][w]=+y||0);break;case"@":n[b][w]=new Date(T.read_shift(-8,"f")-621356832e5);break;case"T":n[b][w]=new Date((T.read_shift(4)-2440588)*864e5+T.read_shift(4));break;case"Y":n[b][w]=T.read_shift(4,"i")/1e4+T.read_shift(4,"i")/1e4*0x100000000;break;case"O":n[b][w]=-T.read_shift(-8,"f");break;case"B":if(l&&8==p[w].len){n[b][w]=T.read_shift(8,"f");break}case"G":case"P":T.l+=p[w].len;break;case"0":if("_NullFlags"===p[w].name)break;default:throw Error("DBF Unsupported data type "+p[w].type)}}}if(2!=s&&i.l<i.length&&26!=i[i.l++])throw Error("DBF EOF Marker missing "+(i.l-1)+" of "+i.length+" "+i[i.l-1].toString(16));return r&&r.sheetRows&&(n=n.slice(0,r.sheetRows)),r.DBF=p,n}(t,n),n);return i["!cols"]=n.DBF.map(function(e){return{wch:e.len,DBF:e}}),delete n.DBF,i}var n={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return rl(r(e,t),t)}catch(e){if(t&&t.WTF)throw e}return{SheetNames:[],Sheets:{}}},to_sheet:r,from_sheet:function(e,r){var a=r||{};if(+a.codepage>=0&&p(+a.codepage),"string"==a.type)throw Error("Cannot write DBF to JS string");var i=t0(),s=i8(e,{header:1,raw:!0,cellDates:!0}),o=s[0],l=s.slice(1),c=e["!cols"]||[],h=0,u=0,d=0,m=1;for(h=0;h<o.length;++h){if(((c[h]||{}).DBF||{}).name){o[h]=c[h].DBF.name,++d;continue}if(null!=o[h]){if(++d,"number"==typeof o[h]&&(o[h]=o[h].toString(10)),"string"!=typeof o[h])throw Error("DBF Invalid column name "+o[h]+" |"+typeof o[h]+"|");if(o.indexOf(o[h])!==h){for(u=0;u<1024;++u)if(-1==o.indexOf(o[h]+"_"+u)){o[h]+="_"+u;break}}}}var g=ri(e["!ref"]),v=[],b=[],w=[];for(h=0;h<=g.e.c-g.s.c;++h){var T="",y="",E=0,S=[];for(u=0;u<l.length;++u)null!=l[u][h]&&S.push(l[u][h]);if(0==S.length||null==o[h]){v[h]="?";continue}for(u=0;u<S.length;++u){switch(typeof S[u]){case"number":y="B";break;case"string":default:y="C";break;case"boolean":y="L";break;case"object":y=S[u]instanceof Date?"D":"C"}E=Math.max(E,String(S[u]).length),T=T&&T!=y?"C":y}E>250&&(E=250),"C"==(y=((c[h]||{}).DBF||{}).type)&&c[h].DBF.len>E&&(E=c[h].DBF.len),"B"==T&&"N"==y&&(T="N",w[h]=c[h].DBF.dec,E=c[h].DBF.len),b[h]="C"==T||"N"==y?E:n[T]||0,m+=b[h],v[h]=T}var x=i.next(32);for(x.write_shift(4,0x13021130),x.write_shift(4,l.length),x.write_shift(2,296+32*d),x.write_shift(2,m),h=0;h<4;++h)x.write_shift(4,0);for(x.write_shift(4,(+t[f]||3)<<8),h=0,u=0;h<o.length;++h)if(null!=o[h]){var A=i.next(32),_=(o[h].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);A.write_shift(1,_,"sbcs"),A.write_shift(1,"?"==v[h]?"C":v[h],"sbcs"),A.write_shift(4,u),A.write_shift(1,b[h]||n[v[h]]||0),A.write_shift(1,w[h]||0),A.write_shift(1,2),A.write_shift(4,0),A.write_shift(1,0),A.write_shift(4,0),A.write_shift(4,0),u+=b[h]||n[v[h]]||0}var k=i.next(264);for(k.write_shift(4,13),h=0;h<65;++h)k.write_shift(4,0);for(h=0;h<l.length;++h){var O=i.next(m);for(O.write_shift(1,0),u=0;u<o.length;++u)if(null!=o[u])switch(v[u]){case"L":O.write_shift(1,null==l[h][u]?63:l[h][u]?84:70);break;case"B":O.write_shift(8,l[h][u]||0,"f");break;case"N":var C="0";for("number"==typeof l[h][u]&&(C=l[h][u].toFixed(w[u]||0)),d=0;d<b[u]-C.length;++d)O.write_shift(1,32);O.write_shift(1,C,"sbcs");break;case"D":l[h][u]?(O.write_shift(4,("0000"+l[h][u].getFullYear()).slice(-4),"sbcs"),O.write_shift(2,("00"+(l[h][u].getMonth()+1)).slice(-2),"sbcs"),O.write_shift(2,("00"+l[h][u].getDate()).slice(-2),"sbcs")):O.write_shift(8,"00000000","sbcs");break;case"C":var R=String(null!=l[h][u]?l[h][u]:"").slice(0,b[u]);for(O.write_shift(1,R,"sbcs"),d=0;d<b[u]-R.length;++d)O.write_shift(1,32)}}return i.next(1).write_shift(1,26),i.end()}}}(),nV=function(){var e={AA:"\xc0",BA:"\xc1",CA:"\xc2",DA:195,HA:"\xc4",JA:197,AE:"\xc8",BE:"\xc9",CE:"\xca",HE:"\xcb",AI:"\xcc",BI:"\xcd",CI:"\xce",HI:"\xcf",AO:"\xd2",BO:"\xd3",CO:"\xd4",DO:213,HO:"\xd6",AU:"\xd9",BU:"\xda",CU:"\xdb",HU:"\xdc",Aa:"\xe0",Ba:"\xe1",Ca:"\xe2",Da:227,Ha:"\xe4",Ja:229,Ae:"\xe8",Be:"\xe9",Ce:"\xea",He:"\xeb",Ai:"\xec",Bi:"\xed",Ci:"\xee",Hi:"\xef",Ao:"\xf2",Bo:"\xf3",Co:"\xf4",Do:245,Ho:"\xf6",Au:"\xf9",Bu:"\xfa",Cu:"\xfb",Hu:"\xfc",KC:"\xc7",Kc:"\xe7",q:"\xe6",z:"œ",a:"\xc6",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=RegExp("\x1bN("+eS(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var n=e[r];return"number"==typeof n?T(n):n},n=function(e,t,r){var n=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==n?e:T(n)};function i(e,i){var s,o=e.split(/[\n\r]+/),l=-1,c=-1,f=0,h=0,u=[],d=[],m=null,g={},v=[],b=[],w=[],T=0;for(+i.codepage>=0&&p(+i.codepage);f!==o.length;++f){T=0;var y,E=o[f].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),S=E.replace(/;;/g,"\0").split(";").map(function(e){return e.replace(/\u0000/g,";")}),x=S[0];if(E.length>0)switch(x){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==S[1].charAt(0)&&d.push(E.slice(3).replace(/;;/g,";"));break;case"C":var A=!1,_=!1,k=!1,O=!1,C=-1,R=-1;for(h=1;h<S.length;++h)switch(S[h].charAt(0)){case"A":case"G":break;case"X":c=parseInt(S[h].slice(1))-1,_=!0;break;case"Y":for(l=parseInt(S[h].slice(1))-1,_||(c=0),s=u.length;s<=l;++s)u[s]=[];break;case"K":'"'===(y=S[h].slice(1)).charAt(0)?y=y.slice(1,y.length-1):"TRUE"===y?y=!0:"FALSE"===y?y=!1:isNaN(eW(y))?isNaN(ej(y).getDate())||(y=eL(y)):(y=eW(y),null!==m&&eu(m)&&(y=eN(y))),void 0!==a&&"string"==typeof y&&"string"!=(i||{}).type&&(i||{}).codepage&&(y=a.utils.decode(i.codepage,y)),A=!0;break;case"E":O=!0;var D=aO(S[h].slice(1),{r:l,c:c});u[l][c]=[u[l][c],D];break;case"S":k=!0,u[l][c]=[u[l][c],"S5S"];break;case"R":C=parseInt(S[h].slice(1))-1;break;case"C":R=parseInt(S[h].slice(1))-1;break;default:if(i&&i.WTF)throw Error("SYLK bad record "+E)}if(A&&(u[l][c]&&2==u[l][c].length?u[l][c][0]=y:u[l][c]=y,m=null),k){if(O)throw Error("SYLK shared formula cannot have own formula");var N=C>-1&&u[C][R];if(!N||!N[1])throw Error("SYLK shared formula cannot find base");u[l][c][1]=function(e,t){return e.replace(aC,function(e,r,n,a,i,s){return r+("$"==n?n+a:re(t9(a)+t.c))+("$"==i?i+s:t7(t8(s)+t.r))})}(N[1],{r:l-C,c:c-R})}break;case"F":var I=0;for(h=1;h<S.length;++h)switch(S[h].charAt(0)){case"X":c=parseInt(S[h].slice(1))-1,++I;break;case"Y":for(l=parseInt(S[h].slice(1))-1,s=u.length;s<=l;++s)u[s]=[];break;case"M":T=parseInt(S[h].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":m=d[parseInt(S[h].slice(1))];break;case"W":for(s=parseInt((w=S[h].slice(1).split(" "))[0],10);s<=parseInt(w[1],10);++s)T=parseInt(w[2],10),b[s-1]=0===T?{hidden:!0}:{wch:T},ai(b[s-1]);break;case"C":b[c=parseInt(S[h].slice(1))-1]||(b[c]={});break;case"R":v[l=parseInt(S[h].slice(1))-1]||(v[l]={}),T>0?(v[l].hpt=T,v[l].hpx=ao(T)):0===T&&(v[l].hidden=!0);break;default:if(i&&i.WTF)throw Error("SYLK bad record "+E)}I<1&&(m=null);break;default:if(i&&i.WTF)throw Error("SYLK bad record "+E)}}return v.length>0&&(g["!rows"]=v),b.length>0&&(g["!cols"]=b),i&&i.sheetRows&&(u=u.slice(0,i.sheetRows)),[u,g]}function s(e,t){var r=function(e,t){switch(t.type){case"base64":return i(S(e),t);case"binary":return i(e,t);case"buffer":return i(x&&Buffer.isBuffer(e)?e.toString("binary"):R(e),t);case"array":return i(eF(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),n=r[0],a=r[1],s=rf(n,t);return eS(a).forEach(function(e){s[e]=a[e]}),s}return e["|"]=254,{to_workbook:function(e,t){return rl(s(e,t),t)},to_sheet:s,from_sheet:function(e,t){var r,n=["ID;PWXL;N;E"],a=[],i=ri(e["!ref"]),s=Array.isArray(e);n.push("P;PGeneral"),n.push("F;P0;DG0G8;M255"),e["!cols"]&&e["!cols"].forEach(function(e,t){var r="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?r+="0":("number"!=typeof e.width||e.wpx||(e.wpx=at(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=ar(e.wpx)),"number"==typeof e.wch&&(r+=Math.round(e.wch)))," "!=r.charAt(r.length-1)&&n.push(r)}),e["!rows"]&&e["!rows"].forEach(function(e,t){var r="F;";e.hidden?r+="M0;":e.hpt?r+="M"+20*e.hpt+";":e.hpx&&(r+="M"+20*as(e.hpx)+";"),r.length>2&&n.push(r+"R"+(t+1))}),n.push("B;Y"+(i.e.r-i.s.r+1)+";X"+(i.e.c-i.s.c+1)+";D"+[i.s.c,i.s.r,i.e.c,i.e.r].join(" "));for(var o=i.s.r;o<=i.e.r;++o)for(var l=i.s.c;l<=i.e.c;++l){var c=rr({r:o,c:l});(r=s?(e[o]||[])[l]:e[c])&&(null!=r.v||r.f&&!r.F)&&a.push(function(e,t,r,n){var a="C;Y"+(r+1)+";X"+(n+1)+";K";switch(e.t){case"n":a+=e.v||0,e.f&&!e.F&&(a+=";E"+aR(e.f,{r:r,c:n}));break;case"b":a+=e.v?"TRUE":"FALSE";break;case"e":a+=e.w||e.v;break;case"d":a+='"'+(e.w||e.v)+'"';break;case"s":a+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return a}(r,0,o,l,t))}return n.join("\r\n")+"\r\n"+a.join("\r\n")+"\r\nE\r\n"}}}(),nK=function(){var e,t;function r(e,t){for(var r=e.split("\n"),n=-1,a=-1,i=0,s=[];i!==r.length;++i){if("BOT"===r[i].trim()){s[++n]=[],a=0;continue}if(!(n<0)){for(var o=r[i].trim().split(","),l=o[0],c=o[1],f=r[++i]||"";1&(f.match(/["]/g)||[]).length&&i<r.length-1;)f+="\n"+r[++i];switch(f=f.trim(),+l){case -1:if("BOT"===f){s[++n]=[],a=0;continue}if("EOD"!==f)throw Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?s[n][a]=!0:"FALSE"===f?s[n][a]=!1:isNaN(eW(c))?isNaN(ej(c).getDate())?s[n][a]=c:s[n][a]=eL(c):s[n][a]=eW(c),++a;break;case 1:(f=(f=f.slice(1,f.length-1)).replace(/""/g,'"'))&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),s[n][a++]=""!==f?f:null}if("EOD"===f)break}}return t&&t.sheetRows&&(s=s.slice(0,t.sheetRows)),s}function n(e,t){return rf(function(e,t){switch(t.type){case"base64":return r(S(e),t);case"binary":return r(e,t);case"buffer":return r(x&&Buffer.isBuffer(e)?e.toString("binary"):R(e),t);case"array":return r(eF(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),t)}return{to_workbook:function(e,t){return rl(n(e,t),t)},to_sheet:n,from_sheet:(e=function(e,t,r,n,a){e.push(t),e.push(r+","+n),e.push('"'+a.replace(/"/g,'""')+'"')},t=function(e,t,r,n){e.push(t+","+r),e.push(1==t?'"'+n.replace(/"/g,'""')+'"':n)},function(r){var n,a=[],i=ri(r["!ref"]),s=Array.isArray(r);e(a,"TABLE",0,1,"sheetjs"),e(a,"VECTORS",0,i.e.r-i.s.r+1,""),e(a,"TUPLES",0,i.e.c-i.s.c+1,""),e(a,"DATA",0,0,"");for(var o=i.s.r;o<=i.e.r;++o){t(a,-1,0,"BOT");for(var l=i.s.c;l<=i.e.c;++l){var c=rr({r:o,c:l});if(!(n=s?(r[o]||[])[l]:r[c])){t(a,1,0,"");continue}switch(n.t){case"n":var f=n.w;f||null==n.v||(f=n.v),null==f?!n.f||n.F?t(a,1,0,""):t(a,1,0,"="+n.f):t(a,0,f,"V");break;case"b":t(a,0,+!!n.v,n.v?"TRUE":"FALSE");break;case"s":t(a,1,0,isNaN(n.v)?n.v:'="'+n.v+'"');break;case"d":n.w||(n.w=em(n.z||j[14],eO(eL(n.v)))),t(a,0,n.w,"V");break;default:t(a,1,0,"")}}}return t(a,-1,0,"EOD"),a.join("\r\n")})}}(),nY=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return rf(function(e,t){for(var r=e.split("\n"),n=-1,a=-1,i=0,s=[];i!==r.length;++i){var o=r[i].trim().split(":");if("cell"===o[0]){var l=rt(o[1]);if(s.length<=l.r)for(n=s.length;n<=l.r;++n)s[n]||(s[n]=[]);switch(n=l.r,a=l.c,o[2]){case"t":s[n][a]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":s[n][a]=+o[3];break;case"vtf":var c=o[o.length-1];case"vtc":"nl"===o[3]?s[n][a]=!!+o[4]:s[n][a]=+o[4],"vtf"==o[2]&&(s[n][a]=[s[n][a],c])}}}return t&&t.sheetRows&&(s=s.slice(0,t.sheetRows)),s}(e,t),t)}var r="--SocialCalcSpreadsheetControlSave\nContent-type: text/plain; charset=UTF-8\n";return{to_workbook:function(e,r){return rl(t(e,r),r)},to_sheet:t,from_sheet:function(t){return["socialcalc:version:1.5\nMIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave",r,"# SocialCalc Spreadsheet Control Save\npart:sheet",r,function(t){if(!t||!t["!ref"])return"";for(var r,n=[],a=[],i="",s=rn(t["!ref"]),o=Array.isArray(t),l=s.s.r;l<=s.e.r;++l)for(var c=s.s.c;c<=s.e.c;++c)if(i=rr({r:l,c:c}),(r=o?(t[l]||[])[c]:t[i])&&null!=r.v&&"z"!==r.t){switch(a=["cell",i,"t"],r.t){case"s":case"str":a.push(e(r.v));break;case"n":r.f?(a[2]="vtf",a[3]="n",a[4]=r.v,a[5]=e(r.f)):(a[2]="v",a[3]=r.v);break;case"b":a[2]="vt"+(r.f?"f":"c"),a[3]="nl",a[4]=r.v?"1":"0",a[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var f=eO(eL(r.v));a[2]="vtc",a[3]="nd",a[4]=""+f,a[5]=r.w||em(r.z||j[14],f);break;case"e":continue}n.push(a.join(":"))}return n.push("sheet:c:"+(s.e.c-s.s.c+1)+":r:"+(s.e.r-s.s.r+1)+":tvf:1"),n.push("valueformat:1:text-wiki"),n.join("\n")}(t),"--SocialCalcSpreadsheetControlSave--"].join("\n")}}}(),nX=function(){function e(e,t,r,n,a){a.raw?t[r][n]=e:""===e||("TRUE"===e?t[r][n]=!0:"FALSE"===e?t[r][n]=!1:isNaN(eW(e))?isNaN(ej(e).getDate())?t[r][n]=e:t[r][n]=eL(e):t[r][n]=eW(e))}var t={44:",",9:"	",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function n(e){for(var n={},a=!1,i=0,s=0;i<e.length;++i)34==(s=e.charCodeAt(i))?a=!a:!a&&s in t&&(n[s]=(n[s]||0)+1);for(i in s=[],n)Object.prototype.hasOwnProperty.call(n,i)&&s.push([n[i],i]);if(!s.length)for(i in n=r)Object.prototype.hasOwnProperty.call(n,i)&&s.push([n[i],i]);return s.sort(function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]}),t[s.pop()[1]]||44}function i(t,r){var i,s="",o="string"==r.type?[0,0,0,0]:function(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=S(e.slice(0,12));break;case"binary":r=e;break;default:throw Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}(t,r);switch(r.type){case"base64":s=S(t);break;case"binary":case"string":s=t;break;case"buffer":s=65001==r.codepage?t.toString("utf8"):r.codepage&&void 0!==a?a.utils.decode(r.codepage,t):x&&Buffer.isBuffer(t)?t.toString("binary"):R(t);break;case"array":s=eF(t);break;default:throw Error("Unrecognized type "+r.type)}return(239==o[0]&&187==o[1]&&191==o[2]?s=ta(s.slice(3)):"string"!=r.type&&"buffer"!=r.type&&65001==r.codepage?s=ta(s):"binary"==r.type&&void 0!==a&&r.codepage&&(s=a.utils.decode(r.codepage,a.utils.encode(28591,s))),"socialcalc:version:"==s.slice(0,19))?nY.to_sheet("string"==r.type?s:ta(s),r):(i=s,!(r&&r.PRN)||r.FS||"sep="==i.slice(0,4)||i.indexOf("	")>=0||i.indexOf(",")>=0||i.indexOf(";")>=0?function(e,t){var r,a,i=t||{},s="",o=i.dense?[]:{},l={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(s=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(s=e.charAt(4),e=e.slice(6)):s=n(e.slice(0,1024)):s=i&&i.FS?i.FS:n(e.slice(0,1024));var c=0,f=0,h=0,u=0,d=0,p=s.charCodeAt(0),m=!1,g=0,v=e.charCodeAt(0);e=e.replace(/\r\n/mg,"\n");var b=null!=i.dateNF?RegExp("^"+("number"==typeof(r=i.dateNF)?j[r]:r).replace(ew,"(\\d+)")+"$"):null;function w(){var t=e.slice(u,d),r={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)r.t="z";else if(i.raw)r.t="s",r.v=t;else if(0===t.trim().length)r.t="s",r.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(r.t="s",r.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(r.t="n",r.f=t.slice(1)):(r.t="s",r.v=t);else if("TRUE"==t)r.t="b",r.v=!0;else if("FALSE"==t)r.t="b",r.v=!1;else if(isNaN(h=eW(t)))if(!isNaN(ej(t).getDate())||b&&t.match(b)){r.z=i.dateNF||j[14];var n,a,s,m,w,T,y,E,S,x,A=0;b&&t.match(b)&&(n=i.dateNF,a=t.match(b)||[],s=-1,m=-1,w=-1,T=-1,y=-1,E=-1,(n.match(ew)||[]).forEach(function(e,t){var r=parseInt(a[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":s=r;break;case"d":w=r;break;case"h":T=r;break;case"s":E=r;break;case"m":T>=0?y=r:m=r}}),E>=0&&-1==y&&m>=0&&(y=m,m=-1),7==(S=(""+(s>=0?s:new Date().getFullYear())).slice(-4)+"-"+("00"+(m>=1?m:1)).slice(-2)+"-"+("00"+(w>=1?w:1)).slice(-2)).length&&(S="0"+S),8==S.length&&(S="20"+S),x=("00"+(T>=0?T:0)).slice(-2)+":"+("00"+(y>=0?y:0)).slice(-2)+":"+("00"+(E>=0?E:0)).slice(-2),t=-1==T&&-1==y&&-1==E?S:-1==s&&-1==m&&-1==w?x:S+"T"+x,A=1),i.cellDates?(r.t="d",r.v=eL(t,A)):(r.t="n",r.v=eO(eL(t,A))),!1!==i.cellText&&(r.w=em(r.z,r.v instanceof Date?eO(r.v):r.v)),i.cellNF||delete r.z}else r.t="s",r.v=t;else r.t="n",!1!==i.cellText&&(r.w=t),r.v=h;if("z"==r.t||(i.dense?(o[c]||(o[c]=[]),o[c][f]=r):o[rr({c:f,r:c})]=r),u=d+1,v=e.charCodeAt(u),l.e.c<f&&(l.e.c=f),l.e.r<c&&(l.e.r=c),g==p)++f;else if(f=0,++c,i.sheetRows&&i.sheetRows<=c)return!0}e:for(;d<e.length;++d)switch(g=e.charCodeAt(d)){case 34:34===v&&(m=!m);break;case p:case 10:case 13:if(!m&&w())break e}return d-u>0&&w(),o["!ref"]=ra(l),o}(i,r):rf(function(t,r){var n=r||{},a=[];if(!t||0===t.length)return a;for(var i=t.split(/[\r\n]/),s=i.length-1;s>=0&&0===i[s].length;)--s;for(var o=10,l=0,c=0;c<=s;++c)-1==(l=i[c].indexOf(" "))?l=i[c].length:l++,o=Math.max(o,l);for(c=0;c<=s;++c){a[c]=[];var f=0;for(e(i[c].slice(0,o).trim(),a,c,f,n),f=1;f<=(i[c].length-o)/10+1;++f)e(i[c].slice(o+(f-1)*10,o+10*f).trim(),a,c,f,n)}return n.sheetRows&&(a=a.slice(0,n.sheetRows)),a}(i,r),r))}return{to_workbook:function(e,t){return rl(i(e,t),t)},to_sheet:i,from_sheet:function(e){for(var t,r=[],n=ri(e["!ref"]),a=Array.isArray(e),i=n.s.r;i<=n.e.r;++i){for(var s=[],o=n.s.c;o<=n.e.c;++o){var l=rr({r:i,c:o});if(!(t=a?(e[i]||[])[o]:e[l])||null==t.v){s.push("          ");continue}for(var c=(t.w||(ro(t),t.w)||"").slice(0,10);c.length<10;)c+=" ";s.push(c+(0===o?" ":""))}r.push(s.join(""))}return r.join("\n")}}}(),n$=function(){function e(e,t,r){if(e){tZ(e,e.l||0);for(var n=r.Enum||h;e.l<e.length;){var a=e.read_shift(2),i=n[a]||n[65535],s=e.read_shift(2),o=e.l+s,l=i.f&&i.f(e,s,r);if(e.l=o,t(l,i,a))return}}}function t(t,r){if(!t)return t;var n=r||{},a=n.dense?[]:{},i="Sheet1",s="",o=0,l={},c=[],f=[],d={s:{r:0,c:0},e:{r:0,c:0}},p=n.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw Error("Unsupported Works 3 for Mac file");if(2==t[2])n.Enum=h,e(t,function(e,t,r){switch(r){case 0:n.vers=e,e>=4096&&(n.qpro=!0);break;case 6:d=e;break;case 204:e&&(s=e);break;case 222:s=e;break;case 15:case 51:n.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&(112&e[2])==112&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=n.dateNF||j[14],n.cellDates&&(e[1].t="d",e[1].v=eN(e[1].v))),n.qpro&&e[3]>o&&(a["!ref"]=ra(d),l[i]=a,c.push(i),a=n.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],i=s||"Sheet"+(o+1),s="");var f=n.dense?(a[e[0].r]||[])[e[0].c]:a[rr(e[0])];if(f){f.t=e[1].t,f.v=e[1].v,null!=e[1].z&&(f.z=e[1].z),null!=e[1].f&&(f.f=e[1].f);break}n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[rr(e[0])]=e[1]}},n);else if(26==t[2]||14==t[2])n.Enum=u,14==t[2]&&(n.qpro=!0,t.l=0),e(t,function(e,t,r){switch(r){case 204:i=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(a["!ref"]=ra(d),l[i]=a,c.push(i),a=n.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},i="Sheet"+((o=e[3])+1)),p>0&&e[0].r>=p)break;n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[rr(e[0])]=e[1],d.e.c<e[0].c&&(d.e.c=e[0].c),d.e.r<e[0].r&&(d.e.r=e[0].r);break;case 27:e[14e3]&&(f[e[14e3][0]]=e[14e3][1]);break;case 1537:f[e[0]]=e[1],e[0]==o&&(i=e[1])}},n);else throw Error("Unrecognized LOTUS BOF "+t[2]);if(a["!ref"]=ra(d),l[s||i]=a,c.push(s||i),!f.length)return{SheetNames:c,Sheets:l};for(var m={},g=[],v=0;v<f.length;++v)l[c[v]]?(g.push(f[v]||c[v]),m[f[v]]=l[f[v]]||l[c[v]]):(g.push(f[v]),m[f[v]]={"!ref":"A1"});return{SheetNames:g,Sheets:m}}function r(e,t,r){var n=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(n[0].c=e.read_shift(1),n[3]=e.read_shift(1),n[0].r=e.read_shift(2),e.l+=2):(n[2]=e.read_shift(1),n[0].c=e.read_shift(2),n[0].r=e.read_shift(2)),n}function n(e,t,n){var a=e.l+t,i=r(e,t,n);if(i[1].t="s",20768==n.vers){e.l++;var s=e.read_shift(1);return i[1].v=e.read_shift(s,"utf8"),i}return n.qpro&&e.l++,i[1].v=e.read_shift(a-e.l,"cstr"),i}function a(e,t,r){var n=32768&t;return t&=-32769,t=(n?e:0)+(t>=8192?t-16384:t),(n?"":"$")+(r?re(t):t7(t))}var i={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},s=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function o(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function l(e,t){var r=o(e,t),n=e.read_shift(4),a=e.read_shift(4),i=e.read_shift(2);if(65535==i)return 0===n&&0xc0000000===a?(r[1].t="e",r[1].v=15):0===n&&0xd0000000===a?(r[1].t="e",r[1].v=42):r[1].v=0,r;var s=32768&i;return i=(32767&i)-16446,r[1].v=(1-2*s)*(a*Math.pow(2,i+32)+n*Math.pow(2,i)),r}function c(e,t){var r=o(e,t),n=e.read_shift(8,"f");return r[1].v=n,r}function f(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}var h={0:{n:"BOF",f:nl},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var n={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(n.s.c=e.read_shift(1),e.l++,n.s.r=e.read_shift(2),n.e.c=e.read_shift(1),e.l++,n.e.r=e.read_shift(2)):(n.s.c=e.read_shift(2),n.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),n.e.c=e.read_shift(2),n.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==n.s.c&&(n.s.c=n.e.c=n.s.r=n.e.r=0)),n}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,n){var a=r(e,t,n);return a[1].v=e.read_shift(2,"i"),a}},14:{n:"NUMBER",f:function(e,t,n){var a=r(e,t,n);return a[1].v=e.read_shift(8,"f"),a}},15:{n:"LABEL",f:n},16:{n:"FORMULA",f:function(e,t,n){var o=e.l+t,l=r(e,t,n);if(l[1].v=e.read_shift(8,"f"),n.qpro)e.l=o;else{var c=e.read_shift(2);(function(e,t){tZ(e,0);for(var r=[],n=0,o="",l="",c="",f="";e.l<e.length;){var h=e[e.l++];switch(h){case 0:r.push(e.read_shift(8,"f"));break;case 1:l=a(t[0].c,e.read_shift(2),!0),o=a(t[0].r,e.read_shift(2),!1),r.push(l+o);break;case 2:var u=a(t[0].c,e.read_shift(2),!0),d=a(t[0].r,e.read_shift(2),!1);l=a(t[0].c,e.read_shift(2),!0),o=a(t[0].r,e.read_shift(2),!1),r.push(u+d+":"+l+o);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var p="";h=e[e.l++];)p+=String.fromCharCode(h);r.push('"'+p.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:f=r.pop(),c=r.pop(),r.push(["AND","OR"][h-20]+"("+c+","+f+")");break;default:if(h<32&&s[h])f=r.pop(),c=r.pop(),r.push(c+s[h]+f);else if(i[h]){if(69==(n=i[h][1])&&(n=e[e.l++]),n>r.length)return void console.error("WK1 bad formula parse 0x"+h.toString(16)+":|"+r.join("|")+"|");var m=r.slice(-n);r.length-=n,r.push(i[h][0]+"("+m.join(",")+")")}else if(h<=7)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=24)return console.error("WK1 unsupported op "+h.toString(16));else if(h<=30)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=115)return console.error("WK1 unsupported function opcode "+h.toString(16));else return console.error("WK1 unrecognized opcode "+h.toString(16))}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")})(e.slice(e.l,e.l+c),l),e.l+=c}return l}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:n},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:f},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var n="";n.length<r;)n+=String.fromCharCode(e[e.l++]);return n}},65535:{n:""}},u={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=o(e,t);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:l},24:{n:"NUMBER18",f:function(e,t){var r=o(e,t);r[1].v=e.read_shift(2);var n=r[1].v>>1;if(1&r[1].v)switch(7&n){case 0:n=(n>>3)*5e3;break;case 1:n=(n>>3)*500;break;case 2:n=(n>>3)/20;break;case 3:n=(n>>3)/200;break;case 4:n=(n>>3)/2e3;break;case 5:n=(n>>3)/2e4;break;case 6:n=(n>>3)/16;break;case 7:n=(n>>3)/64}return r[1].v=n,r}},25:{n:"FORMULA19",f:function(e,t){var r=l(e,14);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},n=e.l+t;e.l<n;){var a=e.read_shift(2);if(14e3==a){for(r[a]=[0,""],r[a][0]=e.read_shift(2);e[e.l];)r[a][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=o(e,t),n=e.read_shift(4);return r[1].v=n>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:c},40:{n:"FORMULA28",f:function(e,t){var r=c(e,14);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:f},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var n=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[n,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r,n,a,i,s=t||{};if(+s.codepage>=0&&p(+s.codepage),"string"==s.type)throw Error("Cannot write WK1 to JS string");var o=t0(),l=ri(e["!ref"]),c=Array.isArray(e),f=[];iS(o,0,(r=1030,(n=t1(2)).write_shift(2,1030),n)),iS(o,6,(a=l,(i=t1(8)).write_shift(2,a.s.c),i.write_shift(2,a.s.r),i.write_shift(2,a.e.c),i.write_shift(2,a.e.r),i));for(var h=Math.min(l.e.r,8191),u=l.s.r;u<=h;++u)for(var d=t7(u),m=l.s.c;m<=l.e.c;++m){u===l.s.r&&(f[m]=re(m));var g=f[m]+d,v=c?(e[u]||[])[m]:e[g];v&&"z"!=v.t&&("n"==v.t?(0|v.v)==v.v&&v.v>=-32768&&v.v<=32767?iS(o,13,function(e,t,r){var n=t1(7);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(2,r,"i"),n}(u,m,v.v)):iS(o,14,function(e,t,r){var n=t1(13);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(8,r,"f"),n}(u,m,v.v)):iS(o,15,function(e,t,r){var n=t1(7+r.length);n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(1,39);for(var a=0;a<n.length;++a){var i=r.charCodeAt(a);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}(u,m,ro(v).slice(0,239))))}return iS(o,1),o.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&p(+r.codepage),"string"==r.type)throw Error("Cannot write WK3 to JS string");var n=t0();iS(n,0,function(e){var t=t1(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,n=0,a=0,i=0;i<e.SheetNames.length;++i){var s=e.SheetNames[i],o=e.Sheets[s];if(o&&o["!ref"]){++a;var l=rn(o["!ref"]);r<l.e.r&&(r=l.e.r),n<l.e.c&&(n=l.e.c)}}return r>8191&&(r=8191),t.write_shift(2,r),t.write_shift(1,a),t.write_shift(1,n),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var a=0,i=0;a<e.SheetNames.length;++a)(e.Sheets[e.SheetNames[a]]||{})["!ref"]&&iS(n,27,function(e,t){var r=t1(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var n=0;n<e.length;++n){var a=e.charCodeAt(n);r[r.l++]=a>127?95:a}return r[r.l++]=0,r}(e.SheetNames[a],i++));var s=0;for(a=0;a<e.SheetNames.length;++a){var o=e.Sheets[e.SheetNames[a]];if(o&&o["!ref"]){for(var l=ri(o["!ref"]),c=Array.isArray(o),f=[],h=Math.min(l.e.r,8191),u=l.s.r;u<=h;++u)for(var d=t7(u),m=l.s.c;m<=l.e.c;++m){u===l.s.r&&(f[m]=re(m));var g=f[m]+d,v=c?(o[u]||[])[m]:o[g];v&&"z"!=v.t&&("n"==v.t?iS(n,23,function(e,t,r,n){var a=t1(14);if(a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),0==n)return a.write_shift(4,0),a.write_shift(4,0),a.write_shift(2,65535),a;var i=0,s=0,o=0,l=0;return n<0&&(i=1,n=-n),s=0|Math.log2(n),n/=Math.pow(2,s-31),(0x80000000&(l=n>>>0))==0&&(n/=2,++s,l=n>>>0),n-=l,l|=0x80000000,l>>>=0,n*=0x100000000,o=n>>>0,a.write_shift(4,o),a.write_shift(4,l),s+=16383+32768*!!i,a.write_shift(2,s),a}(u,m,s,v.v)):iS(n,22,function(e,t,r,n){var a=t1(6+n.length);a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),a.write_shift(1,39);for(var i=0;i<n.length;++i){var s=n.charCodeAt(i);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}(u,m,s,ro(v).slice(0,239))))}++s}}return iS(n,1),n.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(O(S(e)),r);case"binary":return t(O(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}(),nJ=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,nq=/<(?:\w+:)?r>/,nZ=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g,nQ=/^\s|\s$|[\t\n\r]/;function n1(e,t){if(!t.bookSST)return"";var r=[eJ];r[r.length]=td("sst",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main",count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(null!=e[n]){var a=e[n],i="<si>";a.r?i+=a.r:(i+="<t",a.t||(a.t=""),a.t.match(nQ)&&(i+=' xml:space="preserve"'),i+=">"+e5(a.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}var n0=function(e,t){var r=!1;return null==t&&(r=!0,t=t1(15+4*e.t.length)),t.write_shift(1,0),rd(e.t,t),r?t.slice(0,t.l):t};function n2(e){if(void 0!==a)return a.utils.encode(f,e);for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function n4(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function n3(e){var t,r,n=0,a=n2(e),i=a.length+1;for(r=1,(t=_(i))[0]=a.length;r!=i;++r)t[r]=a[r-1];for(r=i-1;r>=0;--r)n=((16384&n)!=0|n<<1&32767)^t[r];return 52811^n}var n5=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],n=function(e,t){var r;return((r=e^t)/2|128*r)&255},a=function(e){for(var n=t[e.length-1],a=104,i=e.length-1;i>=0;--i)for(var s=e[i],o=0;7!=o;++o)64&s&&(n^=r[a]),s*=2,--a;return n};return function(t){for(var r,i,s,o=n2(t),l=a(o),c=o.length,f=_(16),h=0;16!=h;++h)f[h]=0;for((1&c)==1&&(r=l>>8,f[c]=n(e[0],r),--c,r=255&l,i=o[o.length-1],f[c]=n(i,r));c>0;)--c,r=l>>8,f[c]=n(o[c],r),--c,r=255&l,f[c]=n(o[c],r);for(c=15,s=15-o.length;s>0;)r=l>>8,f[c]=n(e[s],r),--c,--s,r=255&l,f[c]=n(o[c],r),--c,--s;return f}}(),n6=function(e,t,r,n,a){var i,s;for(a||(a=t),n||(n=n5(e)),i=0;i!=t.length;++i)s=((s=t[i]^n[r])>>5|s<<3)&255,a[i]=s,++r;return[a,r,n]},n8=function(e){var t=0,r=n5(e);return function(e){var n=n6("",e,t,r);return t=n[1],n[0]}},n7=function(){function e(e,r){switch(r.type){case"base64":return t(S(e),r);case"binary":return t(e,r);case"buffer":return t(x&&Buffer.isBuffer(e)?e.toString("binary"):R(e),r);case"array":return t(eF(e),r)}throw Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},n=e.match(/\\trowd.*?\\row\b/g);if(!n.length)throw Error("RTF missing table");var a={s:{c:0,r:0},e:{c:0,r:n.length-1}};return n.forEach(function(e,t){Array.isArray(r)&&(r[t]=[]);for(var n,i=/\\\w+\b/g,s=0,o=-1;n=i.exec(e);){if("\\cell"===n[0]){var l=e.slice(s,i.lastIndex-n[0].length);if(" "==l[0]&&(l=l.slice(1)),++o,l.length){var c={v:l,t:"s"};Array.isArray(r)?r[t][o]=c:r[rr({r:t,c:o})]=c}}s=i.lastIndex}o>a.e.c&&(a.e.c=o)}),r["!ref"]=ra(a),r}return{to_workbook:function(t,r){return rl(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],n=ri(e["!ref"]),a=Array.isArray(e),i=n.s.r;i<=n.e.r;++i){r.push("\\trowd\\trautofit1");for(var s=n.s.c;s<=n.e.c;++s)r.push("\\cellx"+(s+1));for(r.push("\\pard\\intbl"),s=n.s.c;s<=n.e.c;++s){var o=rr({r:i,c:s});(t=a?(e[i]||[])[s]:e[o])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(ro(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function n9(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var ae=6;function at(e){return Math.floor((e+Math.round(128/ae)/256)*ae)}function ar(e){return Math.floor((e-5)/ae*100+.5)/100}function an(e){return Math.round((e*ae+5)/ae*256)/256}function aa(e){return an(ar(at(e)))}function ai(e){e.width?(e.wpx=at(e.width),e.wch=ar(e.wpx),e.MDW=ae):e.wpx?(e.wch=ar(e.wpx),e.width=an(e.wch),e.MDW=ae):"number"==typeof e.wch&&(e.width=an(e.wch),e.wpx=at(e.width),e.MDW=ae),e.customWidth&&delete e.customWidth}function as(e){return 96*e/96}function ao(e){return 96*e/96}var al={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function ac(e,t){var r,n,a,i,s,o=[eJ,td("styleSheet",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:vt":tv.vt})];return e.SSF&&null!=(r=e.SSF,n=["<numFmts>"],[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=r[t]&&(n[n.length]=td("numFmt",null,{numFmtId:t,formatCode:e5(r[t])}))}),s=1===n.length?"":(n[n.length]="</numFmts>",n[0]=td("numFmts",null,{count:n.length-2}).replace("/>",">"),n.join("")))&&(o[o.length]=s),o[o.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',o[o.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',o[o.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',o[o.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',a=t.cellXfs,(i=[])[i.length]=td("cellXfs",null),a.forEach(function(e){i[i.length]=td("xf",null,e)}),i[i.length]="</cellXfs>",(s=2===i.length?"":(i[0]=td("cellXfs",null,{count:i.length-2}).replace("/>",">"),i.join("")))&&(o[o.length]=s),o[o.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',o[o.length]='<dxfs count="0"/>',o[o.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',o.length>2&&(o[o.length]="</styleSheet>",o[1]=o[1].replace("/>",">")),o.join("")}var af=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function ah(e,t){t||(t=t1(84)),s||(s=eA(af));var r=s[e.patternType];null==r&&(r=40),t.write_shift(4,r);var n=0;if(40!=r)for(rk({auto:1},t),rk({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function au(e,t,r){return r||(r=t1(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function ad(e,t){return t||(t=t1(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var ap=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function am(e,t,r){t.themeElements.clrScheme=[];var n={};(e[0].match(eQ)||[]).forEach(function(e){var a=e0(e);switch(a[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":n.rgb=a.val;break;case"<a:sysClr":n.rgb=a.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===a[0].charAt(1)?(t.themeElements.clrScheme[ap.indexOf(a[0])]=n,n={}):n.name=a[0].slice(3,a[0].length-1);break;default:if(r&&r.WTF)throw Error("Unrecognized "+a[0]+" in clrScheme")}})}function ag(){}function av(){}var ab=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,aw=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,aT=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/,ay=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function aE(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[eJ];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function aS(){var e=[eJ];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}var ax=1024;function aA(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[td("xml",null,{"xmlns:v":tw.v,"xmlns:o":tw.o,"xmlns:x":tw.x,"xmlns:mv":tw.mv}).replace(/\/>/,">"),td("o:shapelayout",td("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),td("v:shapetype",[td("v:stroke",null,{joinstyle:"miter"}),td("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];ax<1e3*e;)ax+=1e3;return t.forEach(function(e){var t=rt(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var n="gradient"==r.type?td("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,i=td("v:fill",n,r);++ax,a=a.concat(["<v:shape"+tu({id:"_x0000_s"+ax,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",i,td("v:shadow",null,{on:"t",obscured:"t"}),td("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",th("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),th("x:AutoFill","False"),th("x:Row",String(t.r)),th("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),a.push("</xml>"),a.join("")}function a_(e){var t=[eJ,td("comments",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main"})],r=[];return t.push("<authors>"),e.forEach(function(e){e[1].forEach(function(e){var n=e5(e.a);-1==r.indexOf(n)&&(r.push(n),t.push("<author>"+n+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))})}),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(e){var n=0,a=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?n=r.indexOf("tc="+e[1][0].ID):e[1].forEach(function(e){e.a&&(n=r.indexOf(e5(e.a))),a.push(e.t||"")}),t.push('<comment ref="'+e[0]+'" authorId="'+n+'"><text>'),a.length<=1)t.push(th("t",e5(a[0]||"")));else{for(var i="Comment:\n    "+a[0]+"\n",s=1;s<a.length;++s)i+="Reply:\n    "+a[s]+"\n";t.push(th("t",e5(i)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}var ak=["xlsb","xlsm","xlam","biff8","xla"],aO=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,n,a){var i=!1,s=!1;0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1)),0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1));var o=n.length>0?0|parseInt(n,10):0,l=a.length>0?0|parseInt(a,10):0;return i?l+=t.c:--l,s?o+=t.r:--o,r+(i?"":"$")+re(l)+(s?"":"$")+t7(o)}return function(n,a){return t=a,n.replace(e,r)}}(),aC=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,aR=function(e,t){return e.replace(aC,function(e,r,n,a,i,s){var o=t9(a)-(n?0:t.c),l=t8(s)-(i?0:t.r);return r+"R"+(0==l?"":i?l+1:"["+l+"]")+"C"+(0==o?"":n?o+1:"["+o+"]")})};function aD(e){e.l+=1}function aN(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function aI(e,t,r){var n=2;if(r)if(r.biff>=2&&r.biff<=5)return aP(e,t,r);else 12==r.biff&&(n=4);var a=e.read_shift(n),i=e.read_shift(n),s=aN(e,2),o=aN(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:o[0],cRel:o[1],rRel:o[2]}}}function aP(e){var t=aN(e,2),r=aN(e,2),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function aM(e,t,r){if(r&&r.biff>=2&&r.biff<=5){var n,a,i;return a=aN(n=e,2),i=n.read_shift(1),{r:a[0],c:i,cRel:a[1],rRel:a[2]}}var s=e.read_shift(r&&12==r.biff?4:2),o=aN(e,2);return{r:s,c:o[0],cRel:o[1],rRel:o[2]}}function aL(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function aF(e){return[e.read_shift(1),e.read_shift(1)]}function aU(e,t,r){var n;return e.l+=2,[{r:e.read_shift(2),c:255&(n=e.read_shift(2)),fQuoted:!!(16384&n),cRel:n>>15,rRel:n>>15}]}function aB(e){return e.l+=6,[]}function aW(e){return e.l+=2,[nl(e),1&e.read_shift(2)]}var aH=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"],aj={1:{n:"PtgExp",f:function(e,t,r){return(e.l++,r&&12==r.biff)?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:tQ},3:{n:"PtgAdd",f:aD},4:{n:"PtgSub",f:aD},5:{n:"PtgMul",f:aD},6:{n:"PtgDiv",f:aD},7:{n:"PtgPower",f:aD},8:{n:"PtgConcat",f:aD},9:{n:"PtgLt",f:aD},10:{n:"PtgLe",f:aD},11:{n:"PtgEq",f:aD},12:{n:"PtgGe",f:aD},13:{n:"PtgGt",f:aD},14:{n:"PtgNe",f:aD},15:{n:"PtgIsect",f:aD},16:{n:"PtgUnion",f:aD},17:{n:"PtgRange",f:aD},18:{n:"PtgUplus",f:aD},19:{n:"PtgUminus",f:aD},20:{n:"PtgPercent",f:aD},21:{n:"PtgParen",f:aD},22:{n:"PtgMissArg",f:aD},23:{n:"PtgStr",f:function(e,t,r){return e.l++,nu(e,t-1,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,rM[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,rA(e,8)}},32:{n:"PtgArray",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[n]}},33:{n:"PtgFunc",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[a2[a],a0[a],n]}},34:{n:"PtgFuncVar",f:function(e,t,r){var n,a=e[e.l++],i=e.read_shift(1),s=r&&r.biff<=3?[88==a?-1:0,e.read_shift(1)]:[(n=e)[n.l+1]>>7,32767&n.read_shift(2)];return[i,(0===s[0]?a0:a1)[s[1]]]}},35:{n:"PtgName",f:function(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,i=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[n,0,i]}},36:{n:"PtgRef",f:function(e,t,r){var n=(96&e[e.l])>>5;return e.l+=1,[n,aM(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,aI(e,r.biff>=2&&r.biff<=5?6:8,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[n,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:tQ},40:{n:"PtgMemNoMem",f:tQ},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[n]}},43:{n:"PtgAreaErr",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}},44:{n:"PtgRefN",f:function(e,t,r){var n=(96&e[e.l])>>5;return e.l+=1,[n,function(e,t,r){var n,a,i,s,o,l=r&&r.biff?r.biff:8;if(l>=2&&l<=5){return a=(n=e).read_shift(2),i=n.read_shift(1),s=(32768&a)>>15,o=(16384&a)>>14,a&=16383,1==s&&a>=8192&&(a-=16384),1==o&&i>=128&&(i-=256),{r:a,c:i,cRel:o,rRel:s}}var c=e.read_shift(l>=12?4:2),f=e.read_shift(2),h=(16384&f)>>14,u=(32768&f)>>15;if(f&=16383,1==u)for(;c>524287;)c-=1048576;if(1==h)for(;f>8191;)f-=16384;return{r:c,c:f,cRel:h,rRel:u}}(e,0,r)]}},45:{n:"PtgAreaN",f:function(e,t,r){return[(96&e[e.l++])>>5,function(e,t,r){if(r.biff<8)return aP(e,t,r);var n=e.read_shift(12==r.biff?4:2),a=e.read_shift(12==r.biff?4:2),i=aN(e,2),s=aN(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:a,c:s[0],cRel:s[1],rRel:s[2]}}}(e,t-1,r)]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){var n,a,i,s;return 5==r.biff?(a=(n=e).read_shift(1)>>>5&3,i=n.read_shift(2,"i"),n.l+=8,s=n.read_shift(2),n.l+=12,[a,i,s]):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[n,a,aM(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12}return[n,a,aI(e,i,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6}return e.l+=i,[n,a]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12}return e.l+=i,[n,a]}},255:{}},az={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},aG={1:{n:"PtgElfLel",f:aW},2:{n:"PtgElfRw",f:aU},3:{n:"PtgElfCol",f:aU},6:{n:"PtgElfRwV",f:aU},7:{n:"PtgElfColV",f:aU},10:{n:"PtgElfRadical",f:aU},11:{n:"PtgElfRadicalS",f:aB},13:{n:"PtgElfColS",f:aB},15:{n:"PtgElfColSV",f:aB},16:{n:"PtgElfRadicalLel",f:aW},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),i=e.read_shift(2),s=aH[r>>2&31];return{ixti:t,coltype:3&r,rt:s,idx:n,c:a,C:i}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},aV={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[n]}},2:{n:"PtgAttrIf",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var n=e.read_shift(r&&2==r.biff?1:2),a=[],i=0;i<=n;++i)a.push(e.read_shift(r&&2==r.biff?1:2));return a}},8:{n:"PtgAttrGoto",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:aL},33:{n:"PtgAttrBaxcel",f:aL},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),aF(e,2)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),aF(e,2)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function aK(e,t,r,n){if(n.biff<8)return a=t,void(e.l+=a);for(var a,i,s=e.l+t,o=[],l=0;l!==r.length;++l)switch(r[l][0]){case"PtgArray":r[l][1]=function(e,t,r){var n=0,a=0;12==r.biff?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,0==--a&&(a=256));for(var i=0,s=[];i!=n&&(s[i]=[]);++i)for(var o=0;o!=a;++o)s[i][o]=function(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=ns(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=rM[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=rA(e,8);break;case 2:r[1]=nm(e,0,{biff:t>0&&t<8?2:t});break;default:throw Error("Bad SerAr: "+r[0])}return r}(e,r.biff);return s}(e,0,n),o.push(r[l][1]);break;case"PtgMemArea":r[l][2]=function(e,t,r){for(var n=e.read_shift(12==r.biff?4:2),a=[],i=0;i!=n;++i)a.push((12==r.biff?rS:nx)(e,8));return a}(e,r[l][1],n),o.push(r[l][2]);break;case"PtgExp":n&&12==n.biff&&(r[l][1][1]=e.read_shift(4),o.push(r[l][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[l][0]}return 0!=(t=s-e.l)&&o.push((i=t,void(e.l+=i))),o}function aY(e,t,r){for(var n,a,i,s=e.l+t,o=[];s!=e.l;)(t=s-e.l,a=aj[i=e[e.l]]||aj[az[i]],(24===i||25===i)&&(a=(24===i?aG:aV)[e[e.l+1]]),a&&a.f)?o.push([a.n,a.f(e,t,r)]):(n=t,e.l+=n);return o}var aX={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function a$(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=-1==n[1]?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:if(null!=r.SID)return e.SheetNames[r.SID];return"SH33TJSSAME"+e[n[0]][0];default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=-1==n[1]?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(e){return e.Name}).join(";;");default:if(!e[n[0]][0][3])return"SH33TJSERR2";return a=-1==n[1]?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]}}function aJ(e,t,r){var n=a$(e,t,r);return"#REF"==n?n:function(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(n,r)}function aq(e,t,r,n,a){var i,s,o,l,c=a&&a.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,b=e[0].length;v<b;++v){var w=e[0][v];switch(w[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(i=h.pop(),s=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=eB(" ",e[0][m][1][1]);break;case 1:g=eB("\r",e[0][m][1][1]);break;default:if(g="",a.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}s+=g,m=-1}h.push(s+aX[w[0]]+i);break;case"PtgIsect":i=h.pop(),s=h.pop(),h.push(s+" "+i);break;case"PtgUnion":i=h.pop(),s=h.pop(),h.push(s+","+i);break;case"PtgRange":i=h.pop(),s=h.pop(),h.push(s+":"+i);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":o=t4(w[1][1],f,a),h.push(t5(o,c));break;case"PtgRefN":o=r?t4(w[1][1],r,a):w[1][1],h.push(t5(o,c));break;case"PtgRef3d":u=w[1][1],o=t4(w[1][2],f,a),p=aJ(n,u,a),h.push(p+"!"+t5(o,c));break;case"PtgFunc":case"PtgFuncVar":var T=w[1][0],y=w[1][1];T||(T=0);var E=0==(T&=127)?[]:h.slice(-T);h.length-=T,"User"===y&&(y=E.shift()),h.push(y+"("+E.join(",")+")");break;case"PtgBool":h.push(w[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(w[1]);break;case"PtgNum":h.push(String(w[1]));break;case"PtgStr":h.push('"'+w[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":l=t3(w[1][1],r?{s:r}:f,a),h.push(t6(l,a));break;case"PtgArea":l=t3(w[1][1],f,a),h.push(t6(l,a));break;case"PtgArea3d":u=w[1][1],l=w[1][2],p=aJ(n,u,a),h.push(p+"!"+t6(l,a));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=w[1][2];var S=(n.names||[])[d-1]||(n[0]||[])[d],x=S?S.Name:"SH33TJSNAME"+String(d);x&&"_xlfn."==x.slice(0,6)&&!a.xlfn&&(x=x.slice(6)),h.push(x);break;case"PtgNameX":var A,_=w[1][1];if(d=w[1][2],a.biff<=5)_<0&&(_=-_),n[_]&&(A=n[_][d]);else{var k="";if(14849==((n[_]||[])[0]||[])[0]||(1025==((n[_]||[])[0]||[])[0]?n[_][d]&&n[_][d].itab>0&&(k=n.SheetNames[n[_][d].itab-1]+"!"):k=n.SheetNames[d-1]+"!"),n[_]&&n[_][d])k+=n[_][d].Name;else if(n[0]&&n[0][d])k+=n[0][d].Name;else{var O=(a$(n,_,a)||"").split(";;");O[d-1]?k=O[d-1]:k+="SH33TJSERRX"}h.push(k);break}A||(A={Name:"SH33TJSERRY"}),h.push(A.Name);break;case"PtgParen":var C="(",R=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:C=eB(" ",e[0][m][1][1])+C;break;case 3:C=eB("\r",e[0][m][1][1])+C;break;case 4:R=eB(" ",e[0][m][1][1])+R;break;case 5:R=eB("\r",e[0][m][1][1])+R;break;default:if(a.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(C+h.pop()+R);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":o={c:w[1][1],r:w[1][0]};var D={c:r.c,r:r.r};if(n.sharedf[rr(o)]){var N=n.sharedf[rr(o)];h.push(aq(N,f,D,n,a))}else{var I=!1;for(i=0;i!=n.arrayf.length;++i)if((s=n.arrayf[i],!(o.c<s[0].s.c)&&!(o.c>s[0].e.c))&&!(o.r<s[0].s.r)&&!(o.r>s[0].e.r)){h.push(aq(s[1],f,D,n,a)),I=!0;break}I||h.push(w[1])}break;case"PtgArray":h.push("{"+function(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],i=0;i<n.length;++i){var s=n[i];s?2===s[0]?a.push('"'+s[1].replace(/"/g,'""')+'"'):a.push(s[1]):a.push("")}t.push(a.join(","))}return t.join(";")}(w[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+w[1].idx+"[#"+w[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw Error("Unsupported ELFs");default:throw Error("Unrecognized Formula Token: "+String(w))}var P=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=a.biff&&m>=0&&-1==P.indexOf(e[0][v][0])){w=e[0][m];var M=!0;switch(w[1][0]){case 4:M=!1;case 0:g=eB(" ",w[1][1]);break;case 5:M=!1;case 1:g=eB("\r",w[1][1]);break;default:if(g="",a.WTF)throw Error("Unexpected PtgAttrSpaceType "+w[1][0])}h.push((M?g:"")+h.pop()+(M?"":g)),m=-1}}if(h.length>1&&a.WTF)throw Error("bad formula stack");return h[0]}function aZ(e,t,r){var n=e.l+t,a=ny(e,6);2==r.biff&&++e.l;var i=function(e){var t;if(65535!==tj(e,e.l+6))return[rA(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=1===e[e.l+2],e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}(e,8),s=e.read_shift(1);2!=r.biff&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var o=function(e,t,r){var n,a,i=e.l+t,s=2==r.biff?1:2,o=e.read_shift(s);if(65535==o)return[[],(n=t-2,void(e.l+=n))];var l=aY(e,o,r);return t!==o+s&&(a=aK(e,t-o-s,l,r)),e.l=i,[l,a]}(e,n-e.l,r);return{cell:a,val:i[0],formula:o,shared:s>>3&1,tt:i[1]}}function aQ(e,t,r){var n=e.read_shift(4),a=aY(e,n,r),i=e.read_shift(4),s=i>0?aK(e,i,a,r):null;return[a,s]}var a1={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},a0={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},a2={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function a4(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(e,t){return t.replace(/\./g,"")})).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function a3(e){var t=e.split(":");return[t[0].split(".")[0],t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}var a5={},a6="undefined"!=typeof Map;function a8(e,t,r){var n=0,a=e.length;if(r){if(a6?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var i=a6?r.get(t):r[t];n<i.length;++n)if(e[i[n]].t===t)return e.Count++,i[n]}}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t:t},e.Count++,e.Unique++,r&&(a6?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function a7(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(ae=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?n=ar(t.wpx):null!=t.wch&&(n=t.wch),n>-1?(r.width=an(n),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function a9(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function ie(e,t,r){var n=r.revssf[null!=t.z?t.z:"General"],a=60,i=e.length;if(null==n&&r.ssf){for(;a<392;++a)if(null==r.ssf[a]){eg(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}}for(a=0;a!=i;++a)if(e[a].numFmtId===n)return a;return e[i]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}var it=["objects","scenarios","selectLockedCells","selectUnlockedCells"],ir=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function ia(e,t,r,n){var a,i=[eJ,td("worksheet",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:r":tv.r})],s=r.SheetNames[e],o=0,l="",c=r.Sheets[s];null==c&&(c={});var f=c["!ref"]||"A1",h=ri(f);if(h.e.c>16383||h.e.r>1048575){if(t.WTF)throw Error("Range "+f+" exceeds format limit A1:XFD1048576");h.e.c=Math.min(h.e.c,16383),h.e.r=Math.min(h.e.c,1048575),f=ra(h)}n||(n={}),c["!comments"]=[];var u=[];!function(e,t,r,n,a){var i=!1,s={},o=null;if("xlsx"!==n.bookType&&t.vbaraw){var l=t.SheetNames[r];try{t.Workbook&&(l=t.Workbook.Sheets[r].CodeName||l)}catch(e){}i=!0,s.codeName=ti(e5(l))}if(e&&e["!outline"]){var c={summaryBelow:1,summaryRight:1};e["!outline"].above&&(c.summaryBelow=0),e["!outline"].left&&(c.summaryRight=0),o=(o||"")+td("outlinePr",null,c)}(i||o)&&(a[a.length]=td("sheetPr",o,s))}(c,r,e,t,i),i[i.length]=td("dimension",null,{ref:f}),i[i.length]=(d={workbookViewId:"0"},(((r||{}).Workbook||{}).Views||[])[0]&&(d.rightToLeft=r.Workbook.Views[0].RTL?"1":"0"),td("sheetViews",td("sheetView",null,d),{})),t.sheetFormat&&(i[i.length]=td("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=c["!cols"]&&c["!cols"].length>0&&(i[i.length]=function(e,t){for(var r,n=["<cols>"],a=0;a!=t.length;++a)(r=t[a])&&(n[n.length]=td("col",null,a7(a,r)));return n[n.length]="</cols>",n.join("")}(0,c["!cols"])),i[o=i.length]="<sheetData/>",c["!links"]=[],null!=c["!ref"]&&(l=function(e,t,r,n){var a,i,s=[],o=[],l=ri(e["!ref"]),c="",f="",h=[],u=0,d=0,p=e["!rows"],m=Array.isArray(e),g={r:f},v=-1;for(d=l.s.c;d<=l.e.c;++d)h[d]=re(d);for(u=l.s.r;u<=l.e.r;++u){for(o=[],f=t7(u),d=l.s.c;d<=l.e.c;++d){a=h[d]+f;var b=m?(e[u]||[])[d]:e[a];void 0!==b&&null!=(c=function(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!=typeof e.f||"z"===e.t&&!e.f)return"";var a="",i=e.t,s=e.v;if("z"!==e.t)switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=rM[e.v];break;case"d":n&&n.cellDates?a=eL(e.v,-1).toISOString():((e=eU(e)).t="n",a=""+(e.v=eO(eL(e.v)))),void 0===e.z&&(e.z=j[14]);break;default:a=e.v}var o=th("v",e5(a)),l={r:t},c=ie(n.cellXfs,e,n);switch(0!==c&&(l.s=c),e.t){case"n":case"z":break;case"d":l.t="d";break;case"b":l.t="b";break;case"e":l.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){o=th("v",""+a8(n.Strings,e.v,n.revStrings)),l.t="s";break}l.t="str"}if(e.t!=i&&(e.t=i,e.v=s),"string"==typeof e.f&&e.f){var f=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=td("f",e5(e.f),f)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(l.cm=1),td("c",o,l)}(b,a,e,t,r,n))&&o.push(c)}(o.length>0||p&&p[u])&&(g={r:f},p&&p[u]&&((i=p[u]).hidden&&(g.hidden=1),v=-1,i.hpx?v=as(i.hpx):i.hpt&&(v=i.hpt),v>-1&&(g.ht=v,g.customHeight=1),i.level&&(g.outlineLevel=i.level)),s[s.length]=td("row",o.join(""),g))}if(p)for(;u<p.length;++u)p&&p[u]&&(g={r:u+1},(i=p[u]).hidden&&(g.hidden=1),v=-1,i.hpx?v=as(i.hpx):i.hpt&&(v=i.hpt),v>-1&&(g.ht=v,g.customHeight=1),i.level&&(g.outlineLevel=i.level),s[s.length]=td("row","",g));return s.join("")}(c,t,e,r,n)).length>0&&(i[i.length]=l),i.length>o+1&&(i[i.length]="</sheetData>",i[o]=i[o].replace("/>",">")),c["!protect"]&&(i[i.length]=(p=c["!protect"],m={sheet:1},it.forEach(function(e){null!=p[e]&&p[e]&&(m[e]="1")}),ir.forEach(function(e){null==p[e]||p[e]||(m[e]="0")}),p.password&&(m.password=n3(p.password).toString(16).toUpperCase()),td("sheetProtection",null,m))),null!=c["!autofilter"]&&(i[i.length]=function(e,t,r,n){var a="string"==typeof e.ref?e.ref:ra(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=rn(a);s.s.r==s.e.r&&(s.e.r=rn(t["!ref"]).e.r,a=ra(s));for(var o=0;o<i.length;++o){var l=i[o];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return o==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),td("autoFilter",null,{ref:a})}(c["!autofilter"],c,r,e)),null!=c["!merges"]&&c["!merges"].length>0&&(i[i.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+ra(e[r])+'"/>';return t+"</mergeCells>"}(c["!merges"]));var d,p,m,g,v=-1,b=-1;return c["!links"].length>0&&(i[i.length]="<hyperlinks>",c["!links"].forEach(function(e){e[1].Target&&(g={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(b=rG(n,-1,e5(e[1].Target).replace(/#.*$/,""),rH.HLINK),g["r:id"]="rId"+b),(v=e[1].Target.indexOf("#"))>-1&&(g.location=e5(e[1].Target.slice(v+1))),e[1].Tooltip&&(g.tooltip=e5(e[1].Tooltip)),i[i.length]=td("hyperlink",null,g))}),i[i.length]="</hyperlinks>"),delete c["!links"],null!=c["!margins"]&&(i[i.length]=(a9(a=c["!margins"]),td("pageMargins",null,a))),(!t||t.ignoreEC||void 0==t.ignoreEC)&&(i[i.length]=th("ignoredErrors",td("ignoredError",null,{numberStoredAsText:1,sqref:f}))),u.length>0&&(b=rG(n,-1,"../drawings/drawing"+(e+1)+".xml",rH.DRAW),i[i.length]=td("drawing",null,{"r:id":"rId"+b}),c["!drawing"]=u),c["!comments"].length>0&&(b=rG(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",rH.VML),i[i.length]=td("legacyDrawing",null,{"r:id":"rId"+b}),c["!legacy"]=b),i.length>1&&(i[i.length]="</worksheet>",i[1]=i[1].replace("/>",">")),i.join("")}var ii=["left","right","top","bottom","header","footer"],is=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function io(e,t){for(var r=0;r!=e.length;++r)for(var n=e[r],a=0;a!=t.length;++a){var i=t[a];if(null==n[i[0]])n[i[0]]=i[1];else switch(i[2]){case"bool":"string"==typeof n[i[0]]&&(n[i[0]]=e9(n[i[0]]));break;case"int":"string"==typeof n[i[0]]&&(n[i[0]]=parseInt(n[i[0]],10))}}}function il(e,t){for(var r=0;r!=t.length;++r){var n=t[r];if(null==e[n[0]])e[n[0]]=n[1];else switch(n[2]){case"bool":"string"==typeof e[n[0]]&&(e[n[0]]=e9(e[n[0]]));break;case"int":"string"==typeof e[n[0]]&&(e[n[0]]=parseInt(e[n[0]],10))}}}var ic="][*?/\\".split("");function ih(e,t){if(e.length>31){if(t)return!1;throw Error("Sheet names cannot exceed 31 chars")}var r=!0;return ic.forEach(function(n){if(-1!=e.indexOf(n)){if(!t)throw Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}),r}function iu(e){var t=[eJ];t[t.length]=td("workbook",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:r":tv.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(is.forEach(function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(n[t[0]]=e.Workbook.WBProps[t[0]])}),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=td("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],i=0;if(a&&a[0]&&a[0].Hidden){for(i=0,t[t.length]="<bookViews>";i!=e.SheetNames.length&&a[i]&&a[i].Hidden;++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(i=0,t[t.length]="<sheets>";i!=e.SheetNames.length;++i){var s={name:e5(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),a[i])switch(a[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden"}t[t.length]=td("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=td("definedName",e5(e.Ref),r))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}var id=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,ip=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function im(e,t){var r=e.split(/\s+/),n=[];if(t||(n[0]=r[0]),1===r.length)return n;var a,i,s,o,l=e.match(id);if(l)for(o=0;o!=l.length;++o)-1===(i=(a=l[o].match(ip))[1].indexOf(":"))?n[a[1]]=a[2].slice(1,a[2].length-1):n["xmlns:"===a[1].slice(0,6)?"xmlns"+a[1].slice(6):a[1].slice(i+1)]=a[2].slice(1,a[2].length-1);return n}function ig(e,t){var r,n,s,l=t||{};eb();var c=b(tm(e));("binary"==l.type||"array"==l.type||"base64"==l.type)&&(c=void 0!==a?a.utils.decode(65001,g(c)):ta(c));var f=c.slice(0,1024).toLowerCase(),h=!1;if((1023&(f=f.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&f.indexOf(","),1023&f.indexOf(";"))){var u=eU(l);return u.type="string",nX.to_workbook(c,u)}if(-1==f.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach(function(e){f.indexOf("<"+e)>=0&&(h=!0)}),h){var d=c,p=l,m=d.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!m||0==m.length)throw Error("Invalid HTML: could not find <table>");if(1==m.length)return rl(i_(m[0],p),p);var v=sn();return m.forEach(function(e,t){sa(v,i_(e,p),"Sheet"+(t+1))}),v}o={"General Number":"General","General Date":j[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":j[15],"Short Date":j[14],"Long Time":j[19],"Medium Time":j[18],"Short Time":j[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:j[2],Standard:j[4],Percent:j[10],Scientific:j[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var w,T,y,E=[],S={},x=[],A=l.dense?[]:{},_="",k={},O={},C=im('<Data ss:Type="String">'),R=0,D=0,N=0,I={s:{r:2e6,c:2e6},e:{r:0,c:0}},P={},M={},L="",F=0,U=[],B={},W={},H=0,z=[],G=[],V={},Y=[],X=!1,J=[],q=[],Z={},et=0,er=0,en={Sheets:[],WBProps:{date1904:!1}},ea={};tg.lastIndex=0,c=c.replace(/<!--([\s\S]*?)-->/mg,"");for(var ei="";w=tg.exec(c);)switch(w[3]=(ei=w[3]).toLowerCase()){case"data":if("data"==ei){if("/"===w[1]){if((T=E.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"))}else"/"!==w[0].charAt(w[0].length-2)&&E.push([w[3],!0]);break}if(E[E.length-1][1])break;"/"===w[1]?function(e,t,r,n,a,i,s,l,c,f){var h="General",u=n.StyleID,d={};f=f||{};var p=[],m=0;for(void 0===u&&l&&(u=l.StyleID),void 0===u&&s&&(u=s.StyleID);void 0!==i[u]&&(i[u].nf&&(h=i[u].nf),i[u].Interior&&p.push(i[u].Interior),i[u].Parent);)u=i[u].Parent;switch(r.Type){case"Boolean":n.t="b",n.v=e9(e);break;case"String":n.t="s",n.r=(null)((null)(e)),n.v=e.indexOf("<")>-1?(null)(t||e).replace(/<.*?>/g,""):n.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),n.v=(eL(e)-new Date(Date.UTC(1899,11,30)))/864e5,n.v!=n.v?n.v=(null)(e):n.v<60&&(n.v=n.v-1),h&&"General"!=h||(h="yyyy-mm-dd");case"Number":void 0===n.v&&(n.v=+e),n.t||(n.t="n");break;case"Error":n.t="e",n.v=rL[e],!1!==f.cellText&&(n.w=e);break;default:""==e&&""==t?n.t="z":(n.t="s",n.v=(null)(t||e))}if(!function(e,t,r){if("z"!==e.t){if(!r||!1!==r.cellText)try{if("e"===e.t)e.w=e.w||rM[e.v];else if("General"===t)"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Q(e.v):e.w=ee(e.v);else{var n,a,i;n=t||"General",a=e.v,i=o[n]||(null)(n),e.w="General"===i?ee(a):em(i,a)}}catch(e){if(r.WTF)throw e}try{var s=o[t]||t||"General";if(r.cellNF&&(e.z=s),r.cellDates&&"n"==e.t&&eu(s)){var l=K(e.v);l&&(e.t="d",e.v=new Date(l.y,l.m-1,l.d,l.H,l.M,l.S,l.u))}}catch(e){if(r.WTF)throw e}}}(n,h,f),!1!==f.cellFormula)if(n.Formula){var g=(null)(n.Formula);61==g.charCodeAt(0)&&(g=g.slice(1)),n.f=aO(g,a),delete n.Formula,"RC"==n.ArrayRange?n.F=aO("RC:RC",a):n.ArrayRange&&(n.F=aO(n.ArrayRange,a),c.push([ri(n.F),n.F]))}else for(m=0;m<c.length;++m)a.r>=c[m][0].s.r&&a.r<=c[m][0].e.r&&a.c>=c[m][0].s.c&&a.c<=c[m][0].e.c&&(n.F=c[m][1]);f.cellStyles&&(p.forEach(function(e){!d.patternType&&e.patternType&&(d.patternType=e.patternType)}),n.s=d),void 0!==n.StyleID&&(n.ixfe=n.StyleID)}(c.slice(R,w.index),L,C,"comment"==E[E.length-1][0]?V:k,{c:D,r:N},P,Y[D],O,J,l):(L="",C=im(w[0]),R=w.index+w[0].length);break;case"cell":if("/"===w[1])if(G.length>0&&(k.c=G),(!l.sheetRows||l.sheetRows>N)&&void 0!==k.v&&(l.dense?(A[N]||(A[N]=[]),A[N][D]=k):A[re(D)+t7(N)]=k),k.HRef&&(k.l={Target:(null)(k.HRef)},k.HRefScreenTip&&(k.l.Tooltip=k.HRefScreenTip),delete k.HRef,delete k.HRefScreenTip),(k.MergeAcross||k.MergeDown)&&(et=D+(0|parseInt(k.MergeAcross,10)),er=N+(0|parseInt(k.MergeDown,10)),U.push({s:{c:D,r:N},e:{c:et,r:er}})),l.sheetStubs)if(k.MergeAcross||k.MergeDown){for(var es=D;es<=et;++es)for(var eo=N;eo<=er;++eo)(es>D||eo>N)&&(l.dense?(A[eo]||(A[eo]=[]),A[eo][es]={t:"z"}):A[re(es)+t7(eo)]={t:"z"});D=et+1}else++D;else k.MergeAcross?D=et+1:++D;else(k=function(e){var t=e.split(/\s+/),r={};if(1===t.length)return r;var n,a,i,s,o=e.match(id);if(o)for(s=0;s!=o.length;++s)-1===(a=(n=o[s].match(ip))[1].indexOf(":"))?r[n[1]]=n[2].slice(1,n[2].length-1):r["xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(a+1)]=n[2].slice(1,n[2].length-1);return r}(w[0])).Index&&(D=k.Index-1),D<I.s.c&&(I.s.c=D),D>I.e.c&&(I.e.c=D),"/>"===w[0].slice(-2)&&++D,G=[];break;case"row":"/"===w[1]||"/>"===w[0].slice(-2)?(N<I.s.r&&(I.s.r=N),N>I.e.r&&(I.e.r=N),"/>"===w[0].slice(-2)&&(O=im(w[0])).Index&&(N=O.Index-1),D=0,++N):((O=im(w[0])).Index&&(N=O.Index-1),Z={},("0"==O.AutoFitHeight||O.Height)&&(Z.hpx=parseInt(O.Height,10),Z.hpt=as(Z.hpx),q[N]=Z),"1"==O.Hidden&&(Z.hidden=!0,q[N]=Z));break;case"worksheet":if("/"===w[1]){if((T=E.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"));x.push(_),I.s.r<=I.e.r&&I.s.c<=I.e.c&&(A["!ref"]=ra(I),l.sheetRows&&l.sheetRows<=I.e.r&&(A["!fullref"]=A["!ref"],I.e.r=l.sheetRows-1,A["!ref"]=ra(I))),U.length&&(A["!merges"]=U),Y.length>0&&(A["!cols"]=Y),q.length>0&&(A["!rows"]=q),S[_]=A}else I={s:{r:2e6,c:2e6},e:{r:0,c:0}},N=D=0,E.push([w[3],!1]),_=(null)((T=im(w[0])).Name),A=l.dense?[]:{},U=[],J=[],q=[],ea={name:_,Hidden:0},en.Sheets.push(ea);break;case"table":if("/"===w[1]){if((T=E.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"))}else"/>"==w[0].slice(-2)||(E.push([w[3],!1]),Y=[],X=!1);break;case"style":if("/"===w[1]){var el=M;if(l.cellStyles&&el.Interior){var ec=el.Interior;ec.Pattern&&(ec.patternType=al[ec.Pattern]||ec.Pattern)}P[el.ID]=el}else M=im(w[0]);break;case"numberformat":M.nf=(null)(im(w[0]).Format||"General"),o[M.nf]&&(M.nf=o[M.nf]);for(var ef=0;392!=ef&&j[ef]!=M.nf;++ef);if(392==ef){for(ef=57;392!=ef;++ef)if(null==j[ef]){eg(M.nf,ef);break}}break;case"column":if("table"!==E[E.length-1][0])break;if((y=im(w[0])).Hidden&&(y.hidden=!0,delete y.Hidden),y.Width&&(y.wpx=parseInt(y.Width,10)),!X&&y.wpx>10){X=!0,ae=6;for(var eh=0;eh<Y.length;++eh)Y[eh]&&ai(Y[eh])}X&&ai(y),Y[y.Index-1||Y.length]=y;for(var ed=0;ed<+y.Span;++ed)Y[Y.length]=eU(y);break;case"namedrange":if("/"===w[1])break;en.Names||(en.Names=[]);var ep=e0(w[0]),ev={Name:ep.Name,Ref:aO(ep.RefersTo.slice(1),{r:0,c:0})};en.Sheets.length>0&&(ev.Sheet=en.Sheets.length-1),en.Names.push(ev);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":"/>"===w[0].slice(-2)||("/"===w[1]?L+=c.slice(F,w.index):F=w.index+w[0].length);break;case"interior":if(!l.cellStyles)break;M.Interior=im(w[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":"/>"===w[0].slice(-2)||("/"===w[1]?(r=ei,n=c.slice(H,w.index),i||(i=eA(r1)),B[r=i[r]||r]=n):H=w.index+w[0].length);break;case"styles":case"workbook":if("/"===w[1]){if((T=E.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"))}else E.push([w[3],!1]);break;case"comment":if("/"===w[1]){if((T=E.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"));(s=V).t=s.v||"",s.t=s.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),s.v=s.w=s.ixfe=void 0,G.push(V)}else E.push([w[3],!1]),V={a:(T=im(w[0])).Author};break;case"autofilter":if("/"===w[1]){if((T=E.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"))}else if("/"!==w[0].charAt(w[0].length-2)){var ew=im(w[0]);A["!autofilter"]={ref:aO(ew.Range).replace(/\$/g,"")},E.push([w[3],!0])}break;case"datavalidation":case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===w[1]){if((T=E.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"))}else"/"!==w[0].charAt(w[0].length-2)&&E.push([w[3],!0]);break;default:if(0==E.length&&"document"==w[3]||0==E.length&&"uof"==w[3])return iN(c,l);var eT=!0;switch(E[E.length-1][0]){case"officedocumentsettings":switch(w[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:eT=!1}break;case"componentoptions":switch(w[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:eT=!1}break;case"excelworkbook":switch(w[3]){case"date1904":en.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:eT=!1}break;case"workbookoptions":switch(w[3]){case"owcversion":case"height":case"width":break;default:eT=!1}break;case"worksheetoptions":switch(w[3]){case"visible":if("/>"===w[0].slice(-2));else if("/"===w[1])switch(c.slice(H,w.index)){case"SheetHidden":ea.Hidden=1;break;case"SheetVeryHidden":ea.Hidden=2}else H=w.index+w[0].length;break;case"header":A["!margins"]||a9(A["!margins"]={},"xlml"),isNaN(+e0(w[0]).Margin)||(A["!margins"].header=+e0(w[0]).Margin);break;case"footer":A["!margins"]||a9(A["!margins"]={},"xlml"),isNaN(+e0(w[0]).Margin)||(A["!margins"].footer=+e0(w[0]).Margin);break;case"pagemargins":var ey=e0(w[0]);A["!margins"]||a9(A["!margins"]={},"xlml"),isNaN(+ey.Top)||(A["!margins"].top=+ey.Top),isNaN(+ey.Left)||(A["!margins"].left=+ey.Left),isNaN(+ey.Right)||(A["!margins"].right=+ey.Right),isNaN(+ey.Bottom)||(A["!margins"].bottom=+ey.Bottom);break;case"displayrighttoleft":en.Views||(en.Views=[]),en.Views[0]||(en.Views[0]={}),en.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":A["!outline"]||(A["!outline"]={}),A["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":A["!outline"]||(A["!outline"]={}),A["!outline"].left=!0;break;default:eT=!1}break;case"pivottable":case"pivotcache":switch(w[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:eT=!1}break;case"pagebreaks":switch(w[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:eT=!1}break;case"autofilter":switch(w[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:eT=!1}break;case"querytable":switch(w[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:eT=!1}break;case"datavalidation":switch(w[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:eT=!1}break;case"sorting":case"conditionalformatting":switch(w[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:eT=!1}break;case"mapinfo":case"schema":case"data":switch(w[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:eT=!1}break;case"smarttags":break;default:eT=!1}if(eT||w[3].match(/!\[CDATA/))break;if(!E[E.length-1][1])throw"Unrecognized tag: "+w[3]+"|"+E.join("|");if("customdocumentproperties"===E[E.length-1][0]){"/>"===w[0].slice(-2)||("/"===w[1]?function(e,t,r,n){var a=n;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":a=e9(n);break;case"i2":case"int":a=parseInt(n,10);break;case"r4":case"float":a=parseFloat(n);break;case"date":case"dateTime.tz":a=eL(n);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw Error("bad custprop:"+r[0])}e[(null)(t)]=a}(W,ei,z,c.slice(H,w.index)):(z=w,H=w.index+w[0].length));break}if(l.WTF)throw"Unrecognized tag: "+w[3]+"|"+E.join("|")}var eE={};return l.bookSheets||l.bookProps||(eE.Sheets=S),eE.SheetNames=x,eE.Workbook=en,eE.SSF=eU(j),eE.Props=B,eE.Custprops=W,eE}function iv(e){return td("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+aR(e.Ref,{r:0,c:0})})}function ib(e,t,r){if("z"!==e.t&&e.XF){var n=0;try{n=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=j[n])}catch(e){if(t.WTF)throw e}if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||rM[e.v]:0===n||"General"==n?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Q(e.v):e.w=ee(e.v):e.w=em(n,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(e){if(t.WTF)throw e}if(t.cellDates&&n&&"n"==e.t&&eu(j[n]||String(n))){var a=K(e.v);a&&(e.t="d",e.v=new Date(a.y,a.m-1,a.d,a.H,a.M,a.S,a.u))}}}function iw(e,t,r){return{v:e,ixfe:t,t:r}}var iT={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"},iy={0:{f:function(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=n,7&i&&(r.level=7&i),16&i&&(r.hidden=!0),32&i&&(r.hpt=a/20),r}},1:{f:function(e){return[rm(e)]}},2:{f:function(e){return[rm(e),ry(e),"n"]}},3:{f:function(e){return[rm(e),e.read_shift(1),"e"]}},4:{f:function(e){return[rm(e),e.read_shift(1),"b"]}},5:{f:function(e){return[rm(e),rA(e),"n"]}},6:{f:function(e){return[rm(e),ru(e),"str"]}},7:{f:function(e){return[rm(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var n=e.l+t,a=rm(e);a.r=r["!row"];var i=[a,ru(e),"str"];if(r.cellFormula){e.l+=2;var s=aQ(e,n-e.l,r);i[3]=aq(s,null,a,r.supbooks,r)}else e.l=n;return i}},9:{f:function(e,t,r){var n=e.l+t,a=rm(e);a.r=r["!row"];var i=[a,rA(e),"n"];if(r.cellFormula){e.l+=2;var s=aQ(e,n-e.l,r);i[3]=aq(s,null,a,r.supbooks,r)}else e.l=n;return i}},10:{f:function(e,t,r){var n=e.l+t,a=rm(e);a.r=r["!row"];var i=[a,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var s=aQ(e,n-e.l,r);i[3]=aq(s,null,a,r.supbooks,r)}else e.l=n;return i}},11:{f:function(e,t,r){var n=e.l+t,a=rm(e);a.r=r["!row"];var i=[a,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var s=aQ(e,n-e.l,r);i[3]=aq(s,null,a,r.supbooks,r)}else e.l=n;return i}},12:{f:function(e){return[rv(e)]}},13:{f:function(e){return[rv(e),ry(e),"n"]}},14:{f:function(e){return[rv(e),e.read_shift(1),"e"]}},15:{f:function(e){return[rv(e),e.read_shift(1),"b"]}},16:{f:function(e){return[rv(e),rA(e),"n"]}},17:{f:function(e){return[rv(e),ru(e),"str"]}},18:{f:function(e){return[rv(e),e.read_shift(4),"s"]}},19:{f:rp},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),i=ru(e),s=aQ(e,0,r),o=rw(e);e.l=n;var l={Name:i,Ptg:s};return a<0xfffffff&&(l.Sheet=a),o&&(l.Comment=o),l}},40:{},42:{},43:{f:function(e,t,r){var n,a={};a.sz=e.read_shift(2)/20;var i=(n=e.read_shift(1),e.l++,{fBold:1&n,fItalic:2&n,fUnderline:4&n,fStrikeout:8&n,fOutline:16&n,fShadow:32&n,fCondense:64&n,fExtend:128&n});switch(i.fItalic&&(a.italic=1),i.fCondense&&(a.condense=1),i.fExtend&&(a.extend=1),i.fShadow&&(a.shadow=1),i.fOutline&&(a.outline=1),i.fStrikeout&&(a.strike=1),700===e.read_shift(2)&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript"}var s=e.read_shift(1);0!=s&&(a.underline=s);var o=e.read_shift(1);o>0&&(a.family=o);var l=e.read_shift(1);switch(l>0&&(a.charset=l),e.l++,a.color=function(e){var t={},r=e.read_shift(1),n=e.read_shift(1),a=e.read_shift(2,"i"),i=e.read_shift(1),s=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r>>>1){case 0:t.auto=1;break;case 1:t.index=n;var l=rP[n];l&&(t.rgb=n9(l));break;case 2:t.rgb=n9([i,s,o]);break;case 3:t.theme=n}return 0!=a&&(t.tint=a>0?a/32767:a/32768),t}(e,8),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor"}return a.name=ru(e,t-21),a}},44:{f:function(e,t){return[e.read_shift(2),ru(e,t-2)]}},45:{f:tQ},46:{f:tQ},47:{f:function(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:nz},62:{f:function(e){return[rm(e),rp(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=rr(r);var n=e.read_shift(1);return 2&n&&(t.l="1"),8&n&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:tQ,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},n=e[e.l];return++e.l,r.above=!(64&n),r.left=!(128&n),e.l+=18,r.name=ru(e,t-19),r}},148:{f:rS,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?ru(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(65536&n),r.backupFile=!!(64&n),r.checkCompatibility=!!(4096&n),r.date1904=!!(1&n),r.filterPrivacy=!!(8&n),r.hidePivotFieldList=!!(1024&n),r.promptedSolutions=!!(16&n),r.publishItems=!!(2048&n),r.refreshAllConnections=!!(262144&n),r.saveExternalLinkValues=!!(128&n),r.showBorderUnselectedTables=!!(4&n),r.showInkAnnotation=!!(32&n),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(32768&n),r.updateLinks=["userSet","never","always"][n>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=rw(e,t-8),r.name=ru(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:rS},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:rS},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:ru(e,t-8)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:rw},357:{},358:{},359:{},360:{T:1},361:{},362:{f:nW},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var n=e.l+t,a=rS(e,16),i=e.read_shift(1),s=[a];if(s[2]=i,r.cellFormula){var o=aQ(e,n-e.l,r);s[1]=o}else e.l=n;return s}},427:{f:function(e,t,r){var n=e.l+t,a=[rS(e,16)];if(r.cellFormula){var i=aQ(e,n-e.l,r);a[1]=i,e.l=n}else e.l=n;return a}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return ii.forEach(function(r){t[r]=rA(e,8)}),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,n=rS(e,16),a=rw(e),i=ru(e),s=ru(e),o=ru(e);e.l=r;var l={rfx:n,relId:a,loc:i,display:o};return s&&(l.Tooltip=s),l}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:rw},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:ru},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=rS(e,16);return t.rfx=r.s,t.ref=rr(r.s),e.l+=16,t}},636:{T:-1},637:{f:rp},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:ru(e,t-10)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},iE={6:{f:aZ},10:{f:ni},12:{f:nl},13:{f:nl},14:{f:ns},15:{f:ns},16:{f:rA},17:{f:ns},18:{f:ns},19:{f:nl},20:{f:nL},21:{f:nL},23:{f:nW},24:{f:nB},25:{f:ns},26:{},27:{},28:{f:function(e,t,r){return function(e,t,r){if(!(r.biff<8)){var n=e.read_shift(2),a=e.read_shift(2),i=e.read_shift(2),s=e.read_shift(2),o=nm(e,0,r);return r.biff<8&&e.read_shift(1),[{r:n,c:a},o,s,i]}}(e,0,r)}},29:{},34:{f:ns},35:{f:nF},38:{f:rA},39:{f:rA},40:{f:rA},41:{f:rA},42:{f:ns},43:{f:ns},47:{f:function(e,t,r){var n,a,i,s,o={Type:r.biff>=8?e.read_shift(2):0};return o.Type?(n=t-2,(a=o||{}).Info=e.read_shift(2),e.l-=2,1===a.Info?a.Data=function(e){var t={},r=t.EncryptionVersionInfo=n4(e,4);if(1!=r.Major||1!=r.Minor)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}(e,n):a.Data=function(e,t){var r,n,a,i,s={},o=s.EncryptionVersionInfo=n4(e,4);if(t-=4,2!=o.Minor)throw Error("unrecognized minor version code: "+o.Minor);if(o.Major>4||o.Major<2)throw Error("unrecognized major version code: "+o.Major);s.Flags=e.read_shift(4),t-=4;var l=e.read_shift(4);return t-=4,s.EncryptionHeader=function(e,t){var r=e.l+t,n={};n.Flags=63&e.read_shift(4),e.l+=4,n.AlgID=e.read_shift(4);var a=!1;switch(n.AlgID){case 26126:case 26127:case 26128:a=36==n.Flags;break;case 26625:a=4==n.Flags;break;case 0:a=16==n.Flags||4==n.Flags||36==n.Flags;break;default:throw"Unrecognized encryption algorithm: "+n.AlgID}if(!a)throw Error("Encryption Flags/AlgID mismatch");return n.AlgIDHash=e.read_shift(4),n.KeySize=e.read_shift(4),n.ProviderType=e.read_shift(4),e.l+=8,n.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,n}(e,l),r=e,n=t-=l,a={},i=r.l+n,r.l+=4,a.Salt=r.slice(r.l,r.l+16),r.l+=16,a.Verifier=r.slice(r.l,r.l+16),r.l+=16,r.read_shift(4),a.VerifierHash=r.slice(r.l,i),r.l=i,s.EncryptionVerifier=a,s}(e,n)):(r.biff,s={key:nl(e),verificationBytes:nl(e)},r.password&&(s.verifier=n3(r.password)),o.valid=s.verificationBytes===s.verifier,o.valid&&(o.insitu=n8(r.password))),o}},49:{f:function(e,t,r){var n={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return n.name=nu(e,0,r),n}},51:{f:nl},60:{},61:{f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{f:ns},65:{f:function(){}},66:{f:nl},77:{},80:{},81:{},82:{},85:{f:nl},89:{},90:{},91:{},92:{f:function(e,t,r){if(r.enc)return e.l+=t,"";var n=e.l,a=nm(e,0,r);return e.read_shift(t+n-e.l),a}},93:{f:function(e,t,r){if(r&&r.biff<8){var n,a,i,s,o,l,c;return n=e,a=t,i=r,n.l+=4,s=n.read_shift(2),o=n.read_shift(2),l=n.read_shift(2),n.l+=2,n.l+=2,n.l+=2,n.l+=2,n.l+=2,n.l+=2,n.l+=2,n.l+=2,n.l+=2,n.l+=6,a-=36,(c=[]).push((nj[s]||tQ)(n,a,i)),{cmo:[o,s,l],ft:c}}var f=nk(e,22),h=function(e,t){for(var r=e.l+t,n=[];e.l<r;){var a=e.read_shift(2);e.l-=2;try{n.push(nC[a](e,r-e.l))}catch(t){return e.l=r,n}}return e.l!=r&&(e.l=r),n}(e,t-22,f[1]);return{cmo:f,ft:h}}},94:{},95:{f:ns},96:{},97:{},99:{f:ns},125:{f:nz},128:{f:function(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(0!==t[0]&&t[0]--,0!==t[1]&&t[1]--,t[0]>7||t[1]>7)throw Error("Bad Gutters: "+t.join("|"));return t}},129:{f:function(e,t,r){var n=r&&8==r.biff||2==t?e.read_shift(2):(e.l+=t,0);return{fDialog:16&n,fBelow:64&n,fRight:128&n}}},130:{f:nl},131:{f:ns},132:{f:ns},133:{f:function(e,t,r){var n=e.read_shift(4),a=3&e.read_shift(1),i=e.read_shift(1);switch(i){case 0:i="Worksheet";break;case 1:i="Macrosheet";break;case 2:i="Chartsheet";break;case 6:i="VBAModule"}var s=nu(e,0,r);return 0===s.length&&(s="Sheet1"),{pos:n,hs:a,dt:i,name:s}}},134:{},140:{f:function(e){var t,r=[0,0];return t=e.read_shift(2),r[0]=rN[t]||t,t=e.read_shift(2),r[1]=rN[t]||t,r}},141:{f:nl},144:{},146:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(nT(e,8));return r}},151:{},152:{},153:{},154:{},155:{},156:{f:nl},157:{},158:{},160:{f:nf},161:{f:function(e,t){var r={};return t<32||(e.l+=16,r.header=rA(e,8),r.footer=rA(e,8),e.l+=2),r}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(e,t){for(var r=e.l+t-2,n=e.read_shift(2),a=e.read_shift(2),i=[];e.l<r;)i.push(nS(e));if(e.l!==r)throw Error("MulRK read error");var s=e.read_shift(2);if(i.length!=s-a+1)throw Error("MulRK length mismatch");return{r:n,c:a,C:s,rkrec:i}}},190:{f:function(e,t){for(var r=e.l+t-2,n=e.read_shift(2),a=e.read_shift(2),i=[];e.l<r;)i.push(e.read_shift(2));if(e.l!==r)throw Error("MulBlank read error");var s=e.read_shift(2);if(i.length!=s-a+1)throw Error("MulBlank length mismatch");return{r:n,c:a,C:s,ixfe:i}}},193:{f:ni},197:{},198:{},199:{},200:{},201:{},202:{f:ns},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:nl},220:{},221:{f:ns},222:{},224:{f:function(e,t,r){var n,a,i,s,o,l,c={};return c.ifnt=e.read_shift(2),c.numFmtId=e.read_shift(2),c.flags=e.read_shift(2),c.fStyle=c.flags>>2&1,t-=6,c.fStyle,a={},i=e.read_shift(4),s=e.read_shift(4),o=e.read_shift(4),l=e.read_shift(2),a.patternType=rI[o>>26],r.cellStyles&&(a.alc=7&i,a.fWrap=i>>3&1,a.alcV=i>>4&7,a.fJustLast=i>>7&1,a.trot=i>>8&255,a.cIndent=i>>16&15,a.fShrinkToFit=i>>20&1,a.iReadOrder=i>>22&2,a.fAtrNum=i>>26&1,a.fAtrFnt=i>>27&1,a.fAtrAlc=i>>28&1,a.fAtrBdr=i>>29&1,a.fAtrPat=i>>30&1,a.fAtrProt=i>>31&1,a.dgLeft=15&s,a.dgRight=s>>4&15,a.dgTop=s>>8&15,a.dgBottom=s>>12&15,a.icvLeft=s>>16&127,a.icvRight=s>>23&127,a.grbitDiag=s>>30&3,a.icvTop=127&o,a.icvBottom=o>>7&127,a.icvDiag=o>>14&127,a.dgDiag=o>>21&15,a.icvFore=127&l,a.icvBack=l>>7&127,a.fsxButton=l>>14&1),c.data=a,c}},225:{f:function(e,t){return 0===t||e.read_shift(2),1200}},226:{f:ni},227:{},229:{f:function(e,t){for(var r=[],n=e.read_shift(2);n--;)r.push(nx(e,t));return r}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(e,t){for(var r=e.l+t,n=e.read_shift(4),a=e.read_shift(4),i=[],s=0;s!=a&&e.l<r;++s)i.push(function(e){var t=c;c=1200;var r,n=e.read_shift(2),a=e.read_shift(1),i=4&a,s=8&a,o=0,l={};s&&(o=e.read_shift(2)),i&&(r=e.read_shift(4));var f=0===n?"":e.read_shift(n,2==1+(1&a)?"dbcs-cont":"sbcs-cont");return s&&(e.l+=4*o),i&&(e.l+=r),l.t=f,s||(l.raw="<t>"+l.t+"</t>",l.r=l.t),c=t,l}(e));return i.Count=n,i.Unique=a,i}},253:{f:function(e){var t=ny(e);return t.isst=e.read_shift(4),t}},255:{f:function(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:nf},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:ns},353:{f:ni},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(e,t,r){var n=e.l+t,a=e.read_shift(2),i=e.read_shift(2);if(r.sbcch=i,1025==i||14849==i)return[i,a];if(i<1||i>255)throw Error("Unexpected SupBook type: "+i);for(var s=nd(e,i),o=[];n>e.l;)o.push(np(e));return[i,a,s,o]}},431:{f:ns},432:{},433:{},434:{},437:{},438:{f:function(e,t,r){var n=e.l,a="";try{e.l+=4;var i,s,o=(r.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(o)?e.l+=6:(e.read_shift(1),e.l++,e.read_shift(2),e.l+=2);var l=e.read_shift(2);e.read_shift(2),nl(e,2);var c=e.read_shift(2);e.l+=c;for(var f=1;f<e.lens.length-1;++f){if(e.l-n!=e.lens[f])throw Error("TxO: bad continue record");var h=e[e.l],u=nd(e,e.lens[f+1]-e.lens[f]-1);if((a+=u).length>=(h?l:2*l))break}if(a.length!==l&&a.length!==2*l)throw Error("cchText: "+l+" != "+a.length);return e.l=n+t,{t:a}}catch(r){return e.l=n+t,{t:a}}}},439:{f:ns},440:{f:function(e,t){var r=nx(e,8);return e.l+=16,[r,function(e,t){var r=e.l+t,n=e.read_shift(4);if(2!==n)throw Error("Unrecognized streamVersion: "+n);var a=e.read_shift(2);e.l+=2;var i,s,o,l,c,f,h="";16&a&&(i=nv(e,r-e.l)),128&a&&(s=nv(e,r-e.l)),(257&a)==257&&(o=nv(e,r-e.l)),(257&a)==1&&(l=function(e,t){var r,n,a,i,s=e.read_shift(16);switch(t-=16,s){case"e0c9ea79f9bace118c8200aa004ba90b":return r=e.read_shift(4),n=e.l,a=!1,r>24&&(e.l+=r-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(a=!0),e.l=n),i=e.read_shift((a?r-24:r)>>1,"utf16le").replace(N,""),a&&(e.l+=24),i;case"0303000000000000c000000000000046":for(var o=e.read_shift(2),l="";o-- >0;)l+="../";var c=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw Error("Bad FileMoniker");if(0===e.read_shift(4))return l+c.replace(/\\/g,"/");var f=e.read_shift(4);if(3!=e.read_shift(2))throw Error("Bad FileMoniker");return l+e.read_shift(f>>1,"utf16le").replace(N,"");default:throw Error("Unsupported Moniker "+s)}}(e,r-e.l)),8&a&&(h=nv(e,r-e.l)),32&a&&(c=e.read_shift(16)),64&a&&(f=r0(e)),e.l=r;var u=s||o||l||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&a&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return c&&(d.guid=c),f&&(d.time=f),i&&(d.Tooltip=i),d}(e,t-24)]}},441:{},442:{f:np},443:{},444:{f:nl},445:{},446:{},448:{f:ni},449:{f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{f:ni},512:{f:nI},513:{f:ny},515:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var n=ny(e,6);return n.val=rA(e,8),n}},516:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var n=e.l+t,a=ny(e,6);return 2==r.biff&&e.l++,a.val=np(e,n-e.l,r),a}},517:{f:nM},519:{f:np},520:{f:function(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var n=e.read_shift(1);return e.l+=3,7&n&&(t.level=7&n),32&n&&(t.hidden=!0),64&n&&(t.hpt=r/20),t}},523:{},545:{f:nH},549:{f:nN},566:{},574:{f:function(e,t,r){return r&&r.biff>=2&&r.biff<5?{}:{RTL:64&e.read_shift(2)}}},638:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),n=nS(e);return{r:t,c:r,ixfe:n[0],rknum:n[1]}}},659:{},1048:{},1054:{f:function(e,t,r){return[e.read_shift(2),nm(e,0,r)]}},1084:{},1212:{f:function(e,t,r){var n=n_(e,6);e.l++;var a=e.read_shift(1);return[function(e,t,r){var n,a,i=e.l+t,s=e.read_shift(2),o=aY(e,s,r);return 65535==s?[[],(n=t-2,void(e.l+=n))]:(t!==s+2&&(a=aK(e,i-s-2,o,r)),[o,a])}(e,t-=8,r),a,n]}},2048:{f:function(e,t){e.read_shift(2);var r=nx(e,8),n=e.read_shift((t-10)/2,"dbcs-cont");return[r,n=n.replace(N,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:nR},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:ni},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t},r:12},2173:{f:function(e,t){var r=e.l+t;e.l+=2;var n=e.read_shift(2);e.l+=2;for(var a=e.read_shift(2),i=[];a-- >0;)i.push(function(e){var t=e.read_shift(2),r=e.read_shift(2)-4,n=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:n[1]=function(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:case 4:e.l+=4;break;case 1:t.xclrValue=function(e,t){e.l+=t}(e,4);break;case 2:t.xclrValue=nw(e,4);break;case 3:t.xclrValue=e.read_shift(4)}return e.l+=8,t}(e,r);break;case 6:n[1]=void(e.l+=r);break;case 14:case 15:n[1]=e.read_shift(1===r?1:2);break;default:throw Error("Unrecognized ExtProp type: "+t+" "+r)}return n}(e,r-e.l));return{ixfe:n,ext:i}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:ns,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(e,t,r){if(r.biff<8){e.l+=t;return}var n=e.read_shift(2),a=e.read_shift(2);return[nd(e,n,r),nd(e,a,r)]},r:12},2197:{},2198:{f:function(e,t,r){var n,a=e.l+t;if(124226!==e.read_shift(4)){if(!r.cellStyles){e.l=a;return}var i=e.slice(e.l);e.l=a;try{n=function(e,t){switch(t.type){case"base64":return ey.read(e,{type:"base64"});case"binary":return ey.read(e,{type:"binary"});case"buffer":case"array":return ey.read(e,{type:"buffer"})}throw Error("Unrecognized type "+t.type)}(i,{type:"array"})}catch(e){return}var s=eY(n,"theme/theme/theme1.xml",!0);if(s){var o=s,l=r;o&&0!==o.length||(o=aE());var c,f,h,u={};if(!(h=o.match(ay)))throw Error("themeElements not found in theme");return c=h[0],u.themeElements={},[["clrScheme",ab,am],["fontScheme",aw,ag],["fmtScheme",aT,av]].forEach(function(e){if(!(f=c.match(e[1])))throw Error(e[0]+" not found in themeElements");e[2](f,u,l)}),u.raw=o,u}}},r:12},2199:{},2200:{},2201:{},2202:{f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{f:ni},2204:{},2205:{},2206:{},2207:{},2211:{f:function(e){var t,r,n=(t=e.read_shift(2),r=e.read_shift(2),e.l+=8,{type:t,flags:r});if(2211!=n.type)throw Error("Invalid Future Record "+n.type);return 0!==e.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:nl},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(e,t,r){var n={area:!1};if(5!=r.biff)return e.l+=t,n;var a=e.read_shift(1);return e.l+=3,16&a&&(n.area=!0),n}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(nT(e,8));return r}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:nI},1:{},2:{f:function(e){var t=ny(e,6);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}},3:{f:function(e){var t=ny(e,6);++e.l;var r=rA(e,8);return t.t="n",t.val=r,t}},4:{f:function(e,t,r){r.biffguess&&5==r.biff&&(r.biff=2);var n=ny(e,6);++e.l;var a=nm(e,t-7,r);return n.t="str",n.val=a,n}},5:{f:nM},7:{f:function(e){var t=e.read_shift(1);return 0===t?(e.l++,""):e.read_shift(t,"sbcs-cont")}},8:{},9:{f:nR},11:{},22:{f:nl},30:{f:nm},31:{},32:{},33:{f:nH},36:{},37:{f:nN},50:{f:function(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}},62:{},52:{},67:{},68:{f:nl},69:{},86:{},126:{},127:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a={fmt:t,env:r,len:n,data:e.slice(e.l,e.l+n)};return e.l+=n,a}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(e,t,r){var n=e.l+t,a=ny(e,6),i=e.read_shift(2),s=nd(e,i,r);return e.l=n,a.t="str",a.val=s,a}},223:{},234:{},354:{},421:{},518:{f:aZ},521:{f:nR},536:{f:nB},547:{f:nF},561:{},579:{},1030:{f:aZ},1033:{f:nR},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function iS(e,t,r,n){if(!isNaN(t)){var a=n||(r||[]).length||0,i=e.next(4);i.write_shift(2,t),i.write_shift(2,a),a>0&&tW(r)&&e.push(r)}}function ix(e,t,r){return e||(e=t1(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function iA(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];n&&n["!ref"]&&rn(n["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}var a=t||{};switch(a.biff||2){case 8:case 5:var i=t||{},s=[];e&&!e.SSF&&(e.SSF=eU(j)),e&&e.SSF&&(eb(),ev(e.SSF),i.revssf=e_(e.SSF),i.revssf[e.SSF[65535]]=0,i.ssf=e.SSF),i.Strings=[],i.Strings.Count=0,i.Strings.Unique=0,i2(i),i.cellXfs=[],ie(i.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var o=0;o<e.SheetNames.length;++o)s[s.length]=function(e,t,r){var n,a,i,s,o,l,c,f,h,u=t0(),d=r.SheetNames[e],p=r.Sheets[d]||{},m=(r||{}).Workbook||{},g=(m.Sheets||[])[e]||{},v=Array.isArray(p),b=8==t.biff,w="",T=[],y=ri(p["!ref"]||"A1"),E=b?65536:16384;if(y.e.c>255||y.e.r>=E){if(t.WTF)throw Error("Range "+(p["!ref"]||"A1")+" exceeds format limit A1:IV16384");y.e.c=Math.min(y.e.c,255),y.e.r=Math.min(y.e.c,E-1)}iS(u,2057,nD(r,16,t)),iS(u,13,nc(1)),iS(u,12,nc(100)),iS(u,15,no(!0)),iS(u,17,no(!1)),iS(u,16,r_(.001)),iS(u,95,no(!0)),iS(u,42,no(!1)),iS(u,43,no(!1)),iS(u,130,nc(1)),iS(u,128,(n=[0,0],(a=t1(8)).write_shift(4,0),a.write_shift(2,n[0]?n[0]+1:0),a.write_shift(2,n[1]?n[1]+1:0),a)),iS(u,131,no(!1)),iS(u,132,no(!1)),b&&function(e,t){if(t){var r=0;t.forEach(function(t,n){var a,i,s;++r<=256&&t&&iS(e,125,(a=a7(n,t),(i=t1(12)).write_shift(2,n),i.write_shift(2,n),i.write_shift(2,256*a.width),i.write_shift(2,0),s=0,a.hidden&&(s|=1),i.write_shift(1,s),s=a.level||0,i.write_shift(1,s),i.write_shift(2,0),i))})}}(u,p["!cols"]),iS(u,512,((s=t1(2*(i=8!=t.biff&&t.biff?2:4)+6)).write_shift(i,y.s.r),s.write_shift(i,y.e.r+1),s.write_shift(2,y.s.c),s.write_shift(2,y.e.c+1),s.write_shift(2,0),s)),b&&(p["!links"]=[]);for(var S=y.s.r;S<=y.e.r;++S){w=t7(S);for(var x=y.s.c;x<=y.e.c;++x){S===y.s.r&&(T[x]=re(x)),h=T[x]+w;var A=v?(p[S]||[])[x]:p[h];A&&(!function(e,t,r,n,a){var i=16+ie(a.cellXfs,t,a);if(null==t.v&&!t.bf)return iS(e,513,nE(r,n,i));if(t.bf)iS(e,6,function(e,t,r,n,a){var i=nE(t,r,a),s=function(e){if(null==e){var t=t1(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return"number"==typeof e?r_(e):r_(0)}(e.v),o=t1(6);o.write_shift(2,33),o.write_shift(4,0);for(var l=t1(e.bf.length),c=0;c<e.bf.length;++c)l[c]=e.bf[c];return D([i,s,o,l])}(t,r,n,0,i));else switch(t.t){case"d":case"n":var s,o="d"==t.t?eO(eL(t.v)):t.v;iS(e,515,(nE(r,n,i,s=t1(14)),r_(o,s),s));break;case"b":case"e":iS(e,517,(l=t.v,c=t.t,nE(r,n,i,f=t1(8)),nh(l,c,f),f));break;case"s":case"str":if(a.bookSST){var l,c,f,h,u,d,p,m=a8(a.Strings,t.v,a.revStrings);iS(e,253,(nE(r,n,i,p=t1(10)),p.write_shift(4,m),p))}else iS(e,516,(h=(t.v||"").slice(0,255),nE(r,n,i,d=t1(8+ +(u=!a||8==a.biff)+(1+u)*h.length)),d.write_shift(2,h.length),u&&d.write_shift(1,1),d.write_shift((1+u)*h.length,h,u?"utf16le":"sbcs"),d));break;default:iS(e,513,nE(r,n,i))}}(u,A,S,x,t),b&&A.l&&p["!links"].push([h,A.l]))}}var _=g.CodeName||g.name||d;return b&&iS(u,574,(o=(m.Views||[])[0],l=t1(18),c=1718,o&&o.RTL&&(c|=64),l.write_shift(2,c),l.write_shift(4,0),l.write_shift(4,64),l.write_shift(4,0),l.write_shift(4,0),l)),b&&(p["!merges"]||[]).length&&iS(u,229,function(e){var t=t1(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)nA(e[r],t);return t}(p["!merges"])),b&&function(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];iS(e,440,function(e){var t=t1(24),r=rt(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return D([t,function(e){var t=t1(512),r=0,n=e.Target;"file://"==n.slice(0,7)&&(n=n.slice(7));var a=n.indexOf("#"),i=a>-1?31:23;switch(n.charAt(0)){case"#":i=28;break;case".":i&=-3}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(28==i)nb(n=n.slice(1),t);else if(2&i){for(r=0,s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" ");r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var o=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&i&&nb(a>-1?n.slice(a+1):"",t)}else{for(r=0,s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" ");r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var l=0;"../"==n.slice(3*l,3*l+3)||"..\\"==n.slice(3*l,3*l+3);)++l;for(t.write_shift(2,l),t.write_shift(4,n.length-3*l+1),r=0;r<n.length-3*l;++r)t.write_shift(1,255&n.charCodeAt(r+3*l));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}(e[1])])}(n)),n[1].Tooltip&&iS(e,2048,function(e){var t=e[1].Tooltip,r=t1(10+2*(t.length+1));r.write_shift(2,2048);var n=rt(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}(n))}delete t["!links"]}(u,p),iS(u,442,ng(_,t)),b&&((f=t1(19)).write_shift(4,2151),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(2,3),f.write_shift(1,1),f.write_shift(4,0),iS(u,2151,f),(f=t1(39)).write_shift(4,2152),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(2,3),f.write_shift(1,0),f.write_shift(4,0),f.write_shift(2,1),f.write_shift(4,4),f.write_shift(2,0),nA(ri(p["!ref"]||"A1"),f),f.write_shift(4,4),iS(u,2152,f)),iS(u,10),u.end()}(o,i,e);return s.unshift(function(e,t,r){var n,a,i,s,o,l,c,f=t0(),h=(e||{}).Workbook||{},u=h.Sheets||[],d=h.WBProps||{},p=8==r.biff,m=5==r.biff;iS(f,2057,nD(e,5,r)),"xla"==r.bookType&&iS(f,135),iS(f,225,p?nc(1200):null),iS(f,193,function(e,t){t||(t=t1(2));for(var r=0;r<2;++r)t.write_shift(1,0);return t}(2)),m&&iS(f,191),m&&iS(f,192),iS(f,226),iS(f,92,function(e,t){var r=!t||8==t.biff,n=t1(r?112:54);for(n.write_shift(8==t.biff?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,0x33336853),n.write_shift(4,5458548|0x20000000*!r);n.l<n.length;)n.write_shift(1,32*!r);return n}(0,r)),iS(f,66,nc(p?1200:1252)),p&&iS(f,353,nc(0)),p&&iS(f,448),iS(f,317,function(e){for(var t=t1(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),p&&e.vbaraw&&iS(f,211),p&&e.vbaraw&&iS(f,442,ng(d.CodeName||"ThisWorkbook",r)),iS(f,156,nc(17)),iS(f,25,no(!1)),iS(f,18,no(!1)),iS(f,19,nc(0)),p&&iS(f,431,no(!1)),p&&iS(f,444,nc(0)),iS(f,61,((n=t1(18)).write_shift(2,0),n.write_shift(2,0),n.write_shift(2,29280),n.write_shift(2,17600),n.write_shift(2,56),n.write_shift(2,0),n.write_shift(2,0),n.write_shift(2,1),n.write_shift(2,500),n)),iS(f,64,no(!1)),iS(f,141,nc(0)),iS(f,34,no("true"==(e.Workbook&&e.Workbook.WBProps&&e9(e.Workbook.WBProps.date1904)?"true":"false"))),iS(f,14,no(!0)),p&&iS(f,439,no(!1)),iS(f,218,nc(0)),iS(f,49,(i=(a={sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"}).name||"Arial",(o=t1((s=r&&5==r.biff)?15+i.length:16+2*i.length)).write_shift(2,20*(a.sz||12)),o.write_shift(4,0),o.write_shift(2,400),o.write_shift(4,0),o.write_shift(2,0),o.write_shift(1,i.length),s||o.write_shift(1,1),o.write_shift((s?1:2)*i.length,i,s?"sbcs":"utf16le"),o)),l=e.SSF,l&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=l[t]&&iS(f,1054,function(e,t,r,n){var a=r&&5==r.biff;n||(n=t1(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var i=n.length>n.l?n.slice(0,n.l):n;return null==i.l&&(i.l=i.length),i}(t,l[t],r))});for(var g=0;g<16;++g)iS(f,224,nP({numFmtId:0,style:!0},0,r));r.cellXfs.forEach(function(e){iS(f,224,nP(e,0,r))}),p&&iS(f,352,no(!1));var v=f.end(),b=t0();p&&iS(b,140,(c||(c=t1(4)),c.write_shift(2,1),c.write_shift(2,1),c)),p&&r.Strings&&function(e,t,r,n){var a=(r||[]).length||0;if(a<=8224)return iS(e,252,r,a);if(!isNaN(252)){for(var i=r.parts||[],s=0,o=0,l=0;l+(i[s]||8224)<=8224;)l+=i[s]||8224,s++;var c=e.next(4);for(c.write_shift(2,t),c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l;o<a;){for((c=e.next(4)).write_shift(2,60),l=0;l+(i[s]||8224)<=8224;)l+=i[s]||8224,s++;c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l}}}(b,252,function(e,t){var r=t1(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],a=0;a<e.length;++a)n[a]=function(e){var t=e.t||"",r=t1(3);r.write_shift(2,t.length),r.write_shift(1,1);var n=t1(2*t.length);return n.write_shift(2*t.length,t,"utf16le"),D([r,n])}(e[a],t);var i=D([r].concat(n));return i.parts=[r.length].concat(n.map(function(e){return e.length})),i}(r.Strings,r)),iS(b,10);var w=b.end(),T=t0(),y=0,E=0;for(E=0;E<e.SheetNames.length;++E)y+=(p?12:11)+(p?2:1)*e.SheetNames[E].length;var S=v.length+y+w.length;for(E=0;E<e.SheetNames.length;++E)iS(T,133,function(e,t){var r=!t||t.biff>=8?2:1,n=t1(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}({pos:S,hs:(u[E]||{}).Hidden||0,dt:0,name:e.SheetNames[E]},r)),S+=t[E].length;var x=T.end();if(y!=x.length)throw Error("BS8 "+y+" != "+x.length);var A=[];return v.length&&A.push(v),x.length&&A.push(x),w.length&&A.push(w),D(A)}(e,s,i)),D(s);case 4:case 3:case 2:for(var l=t||{},c=t0(),f=0,h=0;h<e.SheetNames.length;++h)e.SheetNames[h]==l.sheet&&(f=h);if(0==f&&l.sheet&&e.SheetNames[0]!=l.sheet)throw Error("Sheet not found: "+l.sheet);return iS(c,4==l.biff?1033:3==l.biff?521:9,nD(e,16,l)),!function(e,t,r,n){var a,i=Array.isArray(t),s=ri(t["!ref"]||"A1"),o="",l=[];if(s.e.c>255||s.e.r>16383){if(n.WTF)throw Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");s.e.c=Math.min(s.e.c,255),s.e.r=Math.min(s.e.c,16383),a=ra(s)}for(var c=s.s.r;c<=s.e.r;++c){o=t7(c);for(var f=s.s.c;f<=s.e.c;++f){c===s.s.r&&(l[f]=re(f)),a=l[f]+o;var h=i?(t[c]||[])[f]:t[a];h&&function(e,t,r,n){if(null!=t.v)switch(t.t){case"d":case"n":var a,i,s,o,l,c,f,h="d"==t.t?eO(eL(t.v)):t.v;h==(0|h)&&h>=0&&h<65536?iS(e,2,(ix(c=t1(9),r,n),c.write_shift(2,h),c)):iS(e,3,(ix(f=t1(15),r,n),f.write_shift(8,h,"f"),f));return;case"b":case"e":iS(e,5,(a=t.v,i=t.t,ix(s=t1(9),r,n),nh(a,i||"b",s),s));return;case"s":case"str":iS(e,4,(ix(l=t1(8+2*(o=(t.v||"").slice(0,255)).length),r,n),l.write_shift(1,o.length),l.write_shift(o.length,o,"sbcs"),l.l<l.length?l.slice(0,l.l):l));return}iS(e,1,ix(null,r,n))}(e,h,c,f,n)}}}(c,e.Sheets[e.SheetNames[f]],0,l,e),iS(c,10),c.end()}throw Error("invalid type "+a.bookType+" for BIFF")}function i_(e,t){var r=t||{},n=r.dense?[]:{},a=(e=e.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!a)throw Error("Invalid HTML: could not find <table>");var i=e.match(/<\/table/i),s=a.index,o=i&&i.index||e.length,l=(null)(e.slice(s,o),/(:?<tr[^>]*>)/i,"<tr>"),c=-1,f=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(s=0;s<l.length;++s){var m=l[s].trim(),g=m.slice(0,3).toLowerCase();if("<tr"==g){if(++c,r.sheetRows&&r.sheetRows<=c){--c;break}f=0;continue}if("<td"==g||"<th"==g){var v=m.split(/<\/t[dh]>/i);for(o=0;o<v.length;++o){var b=v[o].trim();if(b.match(/<t[dh]/i)){for(var w=b,T=0;"<"==w.charAt(0)&&(T=w.indexOf(">"))>-1;)w=w.slice(T+1);for(var y=0;y<p.length;++y){var E=p[y];E.s.c==f&&E.s.r<c&&c<=E.e.r&&(f=E.e.c+1,y=-1)}var S=e0(b.slice(0,b.indexOf(">")));u=S.colspan?+S.colspan:1,((h=+S.rowspan)>1||u>1)&&p.push({s:{r:c,c:f},e:{r:c+(h||1)-1,c:f+u-1}});var x=S.t||S["data-t"]||"";if(!w.length||(w=ts(w),d.s.r>c&&(d.s.r=c),d.e.r<c&&(d.e.r=c),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),!w.length)){f+=u;continue}var A={t:"s",v:w};r.raw||!w.trim().length||"s"==x||("TRUE"===w?A={t:"b",v:!0}:"FALSE"===w?A={t:"b",v:!1}:isNaN(eW(w))?isNaN(ej(w).getDate())||(A={t:"d",v:eL(w)},r.cellDates||(A={t:"n",v:eO(A.v)}),A.z=r.dateNF||j[14]):A={t:"n",v:eW(w)}),r.dense?(n[c]||(n[c]=[]),n[c][f]=A):n[rr({r:c,c:f})]=A,f+=u}}}}return n["!ref"]=ra(d),p.length&&(n["!merges"]=p),n}function ik(e,t){var r,n,a,i=t||{},s=null!=i.header?i.header:'<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',o=null!=i.footer?i.footer:"</body></html>",l=[s],c=rn(e["!ref"]);i.dense=Array.isArray(e),l.push((r=0,n=0,"<table"+((a=i)&&a.id?' id="'+a.id+'"':"")+">"));for(var f=c.s.r;f<=c.e.r;++f)l.push(function(e,t,r,n){for(var a=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var o=0,l=0,c=0;c<a.length;++c)if(!(a[c].s.r>r)&&!(a[c].s.c>s)&&!(a[c].e.r<r)&&!(a[c].e.c<s)){if(a[c].s.r<r||a[c].s.c<s){o=-1;break}o=a[c].e.r-a[c].s.r+1,l=a[c].e.c-a[c].s.c+1;break}if(!(o<0)){var f=rr({r:r,c:s}),h=n.dense?(e[r]||[])[s]:e[f],u=h&&null!=h.v&&(h.h||e7(h.w||(ro(h),h.w)||""))||"",d={};o>1&&(d.rowspan=o),l>1&&(d.colspan=l),n.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(n.id||"sjs")+"-"+f,i.push(td("td",u,d))}}return"<tr>"+i.join("")+"</tr>"}(e,c,f,i));return l.push("</table>"+o),l.join("")}function iO(e,t,r){var n=r||{},a=0,i=0;if(null!=n.origin)if("number"==typeof n.origin)a=n.origin;else{var s="string"==typeof n.origin?rt(n.origin):n.origin;a=s.r,i=s.c}var o=t.getElementsByTagName("tr"),l=Math.min(n.sheetRows||1e7,o.length),c={s:{r:0,c:0},e:{r:a,c:i}};if(e["!ref"]){var f=rn(e["!ref"]);c.s.r=Math.min(c.s.r,f.s.r),c.s.c=Math.min(c.s.c,f.s.c),c.e.r=Math.max(c.e.r,f.e.r),c.e.c=Math.max(c.e.c,f.e.c),-1==a&&(c.e.r=a=f.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,g=0,v=0,b=0,w=0;for(e["!cols"]||(e["!cols"]=[]);p<o.length&&m<l;++p){var T=o[p];if(iR(T)){if(n.display)continue;d[m]={hidden:!0}}var y=T.children;for(g=v=0;g<y.length;++g){var E=y[g];if(!(n.display&&iR(E))){var S=E.hasAttribute("data-v")?E.getAttribute("data-v"):E.hasAttribute("v")?E.getAttribute("v"):ts(E.innerHTML),x=E.getAttribute("data-z")||E.getAttribute("z");for(u=0;u<h.length;++u){var A=h[u];A.s.c==v+i&&A.s.r<m+a&&m+a<=A.e.r&&(v=A.e.c+1-i,u=-1)}w=+E.getAttribute("colspan")||1,((b=+E.getAttribute("rowspan")||1)>1||w>1)&&h.push({s:{r:m+a,c:v+i},e:{r:m+a+(b||1)-1,c:v+i+(w||1)-1}});var _={t:"s",v:S},k=E.getAttribute("data-t")||E.getAttribute("t")||"";null!=S&&(0==S.length?_.t=k||"z":n.raw||0==S.trim().length||"s"==k||("TRUE"===S?_={t:"b",v:!0}:"FALSE"===S?_={t:"b",v:!1}:isNaN(eW(S))?isNaN(ej(S).getDate())||(_={t:"d",v:eL(S)},n.cellDates||(_={t:"n",v:eO(_.v)}),_.z=n.dateNF||j[14]):_={t:"n",v:eW(S)})),void 0===_.z&&null!=x&&(_.z=x);var O="",C=E.getElementsByTagName("A");if(C&&C.length)for(var R=0;R<C.length&&(!C[R].hasAttribute("href")||"#"==(O=C[R].getAttribute("href")).charAt(0));++R);O&&"#"!=O.charAt(0)&&(_.l={Target:O}),n.dense?(e[m+a]||(e[m+a]=[]),e[m+a][v+i]=_):e[rr({c:v+i,r:m+a})]=_,c.e.c<v+i&&(c.e.c=v+i),v+=w}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),c.e.r=Math.max(c.e.r,m-1+a),e["!ref"]=ra(c),m>=l&&(e["!fullref"]=ra((c.e.r=o.length-p+m-1+a,c))),e}function iC(e,t){return iO((t||{}).dense?[]:{},e,t)}function iR(e){var t,r="",n=(t=e).ownerDocument.defaultView&&"function"==typeof t.ownerDocument.defaultView.getComputedStyle?t.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null;return n&&(r=n(e).getPropertyValue("display")),r||(r=e.style&&e.style.display),"none"===r}var iD={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function iN(e,t){var r=t||{},n,a,i,s,o,l,c,f=tm(e),h=[],u={name:""},d="",p=0,m={},g=[],v=r.dense?[]:{},b={value:""},w="",T=0,y=[],E=-1,S=-1,x={s:{r:1e6,c:1e7},e:{r:0,c:0}},A=0,_={},k=[],O={},C=0,R=0,D=[],N=1,I=1,P=[],M={Names:[]},L={},F=["",""],U=[],B={},W="",H=0,j=!1,z=!1,G=0;for(tg.lastIndex=0,f=f.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");o=tg.exec(f);)switch(o[3]=o[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===o[1]?(x.e.c>=x.s.c&&x.e.r>=x.s.r?v["!ref"]=ra(x):v["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=x.e.r&&(v["!fullref"]=v["!ref"],x.e.r=r.sheetRows-1,v["!ref"]=ra(x)),k.length&&(v["!merges"]=k),D.length&&(v["!rows"]=D),i.name=i["名称"]||i.name,"undefined"!=typeof JSON&&JSON.stringify(i),g.push(i.name),m[i.name]=v,z=!1):"/"!==o[0].charAt(o[0].length-2)&&(i=e0(o[0],!1),E=S=-1,x.s.r=x.s.c=1e7,x.e.r=x.e.c=0,v=r.dense?[]:{},k=[],D=[],z=!0);break;case"table-row-group":"/"===o[1]?--A:++A;break;case"table-row":case"行":if("/"===o[1]){E+=N,N=1;break}if((s=e0(o[0],!1))["行号"]?E=s["行号"]-1:-1==E&&(E=0),(N=+s["number-rows-repeated"]||1)<10)for(G=0;G<N;++G)A>0&&(D[E+G]={level:A});S=-1;break;case"covered-table-cell":"/"!==o[1]&&++S,r.sheetStubs&&(r.dense?(v[E]||(v[E]=[]),v[E][S]={t:"z"}):v[rr({r:E,c:S})]={t:"z"}),w="",y=[];break;case"table-cell":case"数据":if("/"===o[0].charAt(o[0].length-2))++S,I=parseInt((b=e0(o[0],!1))["number-columns-repeated"]||"1",10),l={t:"z",v:null},b.formula&&!1!=r.cellFormula&&(l.f=a4((null)(b.formula))),"string"==(b["数据类型"]||b["value-type"])&&(l.t="s",l.v=(null)(b["string-value"]||""),r.dense?(v[E]||(v[E]=[]),v[E][S]=l):v[rr({r:E,c:S})]=l),S+=I-1;else if("/"!==o[1]){w="",T=0,y=[],I=1;var V=N?E+N-1:E;if(++S>x.e.c&&(x.e.c=S),S<x.s.c&&(x.s.c=S),E<x.s.r&&(x.s.r=E),V>x.e.r&&(x.e.r=V),b=e0(o[0],!1),U=[],B={},l={t:b["数据类型"]||b["value-type"],v:null},r.cellFormula)if(b.formula&&(b.formula=(null)(b.formula)),b["number-matrix-columns-spanned"]&&b["number-matrix-rows-spanned"]&&(O={s:{r:E,c:S},e:{r:E+(C=parseInt(b["number-matrix-rows-spanned"],10)||0)-1,c:S+(parseInt(b["number-matrix-columns-spanned"],10)||0)-1}},l.F=ra(O),P.push([O,l.F])),b.formula)l.f=a4(b.formula);else for(G=0;G<P.length;++G)E>=P[G][0].s.r&&E<=P[G][0].e.r&&S>=P[G][0].s.c&&S<=P[G][0].e.c&&(l.F=P[G][1]);switch((b["number-columns-spanned"]||b["number-rows-spanned"])&&(O={s:{r:E,c:S},e:{r:E+(C=parseInt(b["number-rows-spanned"],10)||0)-1,c:S+(parseInt(b["number-columns-spanned"],10)||0)-1}},k.push(O)),b["number-columns-repeated"]&&(I=parseInt(b["number-columns-repeated"],10)),l.t){case"boolean":l.t="b",l.v=e9(b["boolean-value"]);break;case"float":case"percentage":case"currency":l.t="n",l.v=parseFloat(b.value);break;case"date":l.t="d",l.v=eL(b["date-value"]),r.cellDates||(l.t="n",l.v=eO(l.v)),l.z="m/d/yy";break;case"time":l.t="n",l.v=function(e){var t=0,r=0,n=!1,a=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!a)throw Error("|"+e+"| is not an ISO8601 Duration");for(var i=1;i!=a.length;++i)if(a[i]){switch(r=1,i>3&&(n=!0),a[i].slice(a[i].length-1)){case"Y":throw Error("Unsupported ISO Duration Field: "+a[i].slice(a[i].length-1));case"D":r*=24;case"H":r*=60;case"M":if(n)r*=60;else throw Error("Unsupported ISO Duration Field: M")}t+=r*parseInt(a[i],10)}return t}(b["time-value"])/86400,r.cellDates&&(l.t="d",l.v=eN(l.v)),l.z="HH:MM:SS";break;case"number":l.t="n",l.v=parseFloat(b["数据数值"]);break;default:if("string"!==l.t&&"text"!==l.t&&l.t)throw Error("Unsupported value type "+l.t);l.t="s",null!=b["string-value"]&&(w=(null)(b["string-value"]),y=[])}}else{if(j=!1,"s"===l.t&&(l.v=w||"",y.length&&(l.R=y),j=0==T),L.Target&&(l.l=L),U.length>0&&(l.c=U,U=[]),w&&!1!==r.cellText&&(l.w=w),j&&(l.t="z",delete l.v),(!j||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=E))for(var K=0;K<N;++K){if(I=parseInt(b["number-columns-repeated"]||"1",10),r.dense)for(v[E+K]||(v[E+K]=[]),v[E+K][S]=0==K?l:eU(l);--I>0;)v[E+K][S+I]=eU(l);else for(v[rr({r:E+K,c:S})]=l;--I>0;)v[rr({r:E+K,c:S+I})]=eU(l);x.e.c<=S&&(x.e.c=S)}S+=(I=parseInt(b["number-columns-repeated"]||"1",10))-1,I=0,l={},w="",y=[]}L={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===o[1]){if((n=h.pop())[0]!==o[3])throw"Bad state: "+n}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!0]);break;case"annotation":if("/"===o[1]){if((n=h.pop())[0]!==o[3])throw"Bad state: "+n;B.t=w,y.length&&(B.R=y),B.a=W,U.push(B)}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!1]);W="",H=0,w="",T=0,y=[];break;case"creator":"/"===o[1]?W=f.slice(H,o.index):H=o.index+o[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===o[1]){if((n=h.pop())[0]!==o[3])throw"Bad state: "+n}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!1]);w="",T=0,y=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===o[1]){if(_[u.name]=d,(n=h.pop())[0]!==o[3])throw"Bad state: "+n}else"/"!==o[0].charAt(o[0].length-2)&&(d="",u=e0(o[0],!1),h.push([o[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(h[h.length-1][0]){case"time-style":case"date-style":a=e0(o[0],!1),d+=iD[o[3]][+("long"===a.style)]}break;case"text":if("/>"===o[0].slice(-2));else if("/"===o[1])switch(h[h.length-1][0]){case"number-style":case"date-style":case"time-style":d+=f.slice(p,o.index)}else p=o.index+o[0].length;break;case"named-range":F=a3((a=e0(o[0],!1))["cell-range-address"]);var Y={Name:a.name,Ref:F[0]+"!"+F[1]};z&&(Y.Sheet=g.length),M.Names.push(Y);break;case"p":case"文本串":if(["master-styles"].indexOf(h[h.length-1][0])>-1)break;if("/"!==o[1]||b&&b["string-value"])e0(o[0],!1),T=o.index+o[0].length;else{var X=[(null)(f.slice(T,o.index).replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(e,t){return Array(parseInt(t,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,"\n").replace(/<[^>]*>/g,""))];w=(w.length>0?w+"\n":"")+X[0]}break;case"database-range":if("/"===o[1])break;try{m[(F=a3(e0(o[0])["target-range-address"]))[0]]["!autofilter"]={ref:F[1]}}catch(e){}break;case"a":if("/"!==o[1]){if(!(L=e0(o[0],!1)).href)break;L.Target=(null)(L.href),delete L.href,"#"==L.Target.charAt(0)&&L.Target.indexOf(".")>-1?(F=a3(L.Target.slice(1)),L.Target="#"+F[0]+"!"+F[1]):L.Target.match(/^\.\.[\\\/]/)&&(L.Target=L.Target.slice(3))}break;default:switch(o[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw Error(o)}}var J={Sheets:m,SheetNames:g,Workbook:M};return r.bookSheets&&delete J.Sheets,J}var iI=function(){var e="<office:document-styles "+tu({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+'><office:master-styles><style:master-page style:name="mp1" style:page-layout-name="mp1"><style:header/><style:header-left style:display="false"/><style:footer/><style:footer-left style:display="false"/></style:master-page></office:master-styles></office:document-styles>';return function(){return eJ+e}}(),iP=function(){var e="          <table:table-cell />\n",t=function(t,r,n){var a=[];a.push('      <table:table table:name="'+e5(r.SheetNames[n])+'" table:style-name="ta1">\n');var i=0,s=0,o=rn(t["!ref"]||"A1"),l=t["!merges"]||[],c=0,f=Array.isArray(t);if(t["!cols"])for(s=0;s<=o.e.c;++s)a.push("        <table:table-column"+(t["!cols"][s]?' table:style-name="co'+t["!cols"][s].ods+'"':"")+"></table:table-column>\n");var h="",u=t["!rows"]||[];for(i=0;i<o.s.r;++i)h=u[i]?' table:style-name="ro'+u[i].ods+'"':"",a.push("        <table:table-row"+h+"></table:table-row>\n");for(;i<=o.e.r;++i){for(h=u[i]?' table:style-name="ro'+u[i].ods+'"':"",a.push("        <table:table-row"+h+">\n"),s=0;s<o.s.c;++s)a.push(e);for(;s<=o.e.c;++s){var d=!1,p={},m="";for(c=0;c!=l.length;++c)if(!(l[c].s.c>s)&&!(l[c].s.r>i)&&!(l[c].e.c<s)&&!(l[c].e.r<i)){(l[c].s.c!=s||l[c].s.r!=i)&&(d=!0),p["table:number-columns-spanned"]=l[c].e.c-l[c].s.c+1,p["table:number-rows-spanned"]=l[c].e.r-l[c].s.r+1;break}if(d){a.push("          <table:covered-table-cell/>\n");continue}var g=rr({r:i,c:s}),v=f?(t[i]||[])[s]:t[g];if(v&&v.f&&(p["table:formula"]=e5(("of:="+v.f.replace(aC,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),v.F&&v.F.slice(0,g.length)==g)){var b=rn(v.F);p["table:number-matrix-columns-spanned"]=b.e.c-b.s.c+1,p["table:number-matrix-rows-spanned"]=b.e.r-b.s.r+1}if(!v){a.push(e);continue}switch(v.t){case"b":m=v.v?"TRUE":"FALSE",p["office:value-type"]="boolean",p["office:boolean-value"]=v.v?"true":"false";break;case"n":m=v.w||String(v.v||0),p["office:value-type"]="float",p["office:value"]=v.v||0;break;case"s":case"str":m=null==v.v?"":v.v,p["office:value-type"]="string";break;case"d":m=v.w||eL(v.v).toISOString(),p["office:value-type"]="date",p["office:date-value"]=eL(v.v).toISOString(),p["table:style-name"]="ce1";break;default:a.push(e);continue}var w=e5(m).replace(/  +/g,function(e){return'<text:s text:c="'+e.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>");if(v.l&&v.l.Target){var T=v.l.Target;"#"==(T="#"==T.charAt(0)?"#"+T.slice(1).replace(/\./,"!"):T).charAt(0)||T.match(/^\w+:/)||(T="../"+T),w=td("text:a",w,{"xlink:href":T.replace(/&/g,"&amp;")})}a.push("          "+td("table:table-cell",td("text:p",w,{}),p)+"\n")}a.push("        </table:table-row>\n")}return a.push("      </table:table>\n"),a.join("")},r=function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map(function(e){return t.Sheets[e]}).forEach(function(t){if(t&&t["!cols"]){for(var n=0;n<t["!cols"].length;++n)if(t["!cols"][n]){var a=t["!cols"][n];if(null==a.width&&null==a.wpx&&null==a.wch)continue;ai(a),a.ods=r;var i=t["!cols"][n].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+i+'"/>\n'),e.push("  </style:style>\n"),++r}}});var n=0;t.SheetNames.map(function(e){return t.Sheets[e]}).forEach(function(t){if(t&&t["!rows"]){for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=n;var a=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+n+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+a+'"/>\n'),e.push("  </style:style>\n"),++n}}}),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")};return function(e,n){var a=[eJ],i=tu({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),s=tu({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==n.bookType?(a.push("<office:document"+i+s+">\n"),a.push(rK().replace(/office:document-meta/g,"office:meta"))):a.push("<office:document-content"+i+">\n"),r(a,e),a.push("  <office:body>\n"),a.push("    <office:spreadsheet>\n");for(var o=0;o!=e.SheetNames.length;++o)a.push(t(e.Sheets[e.SheetNames[o]],e,o,n));return a.push("    </office:spreadsheet>\n"),a.push("  </office:body>\n"),"fods"==n.bookType?a.push("</office:document>"):a.push("</office:document-content>"),a.join("")}}();function iM(e,t){if("fods"==t.bookType)return iP(e,t);var r=e$(),n="",a=[],i=[];return eX(r,n="mimetype","application/vnd.oasis.opendocument.spreadsheet"),eX(r,n="content.xml",iP(e,t)),a.push([n,"text/xml"]),i.push([n,"ContentFile"]),eX(r,n="styles.xml",iI(e,t)),a.push([n,"text/xml"]),i.push([n,"StylesFile"]),eX(r,n="meta.xml",eJ+rK()),a.push([n,"text/xml"]),i.push([n,"MetadataFile"]),eX(r,n="manifest.rdf",function(e){var t=[eJ];t.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var r=0;r!=e.length;++r)t.push(rV(e[r][0],e[r][1])),t.push(['  <rdf:Description rdf:about="">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+e[r][0]+'"/>\n',"  </rdf:Description>\n"].join(""));return t.push(rV("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}(i)),a.push([n,"application/rdf+xml"]),eX(r,n="META-INF/manifest.xml",function(e){var t=[eJ];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(a)),r}function iL(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function iF(e){return"undefined"!=typeof TextDecoder?new TextDecoder().decode(e):ta(R(e))}function iU(e){var t=new Uint8Array(e.reduce(function(e,t){return e+t.length},0)),r=0;return e.forEach(function(e){t.set(e,r),r+=e.length}),t}function iB(e){return e-=e>>1&0x55555555,((e=(0x33333333&e)+(e>>2&0x33333333))+(e>>4)&0xf0f0f0f)*0x1010101>>>24}function iW(e,t){var r=t?t[0]:0,n=127&e[r];t:if(e[r++]>=128&&(n|=(127&e[r])<<7,e[r++]<128||(n|=(127&e[r])<<14,e[r++]<128)||(n|=(127&e[r])<<21,e[r++]<128)||(n+=(127&e[r])*0x10000000,++r,e[r++]<128)||(n+=(127&e[r])*0x800000000,++r,e[r++]<128)||(n+=(127&e[r])*0x40000000000,++r,e[r++]<128)))break t;return t&&(t[0]=r),n}function iH(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;r:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=0xfffffff)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=0x7ffffffff)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=0x3ffffffffff))break r;t[r-1]|=128,t[r]=e/0x1000000>>>21&127,++r}return t.slice(0,r)}function ij(e){var t=0,r=127&e[0];t:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128||(r|=(127&e[t])<<14,e[t++]<128)||(r|=(127&e[t])<<21,e[t++]<128))break t;r|=(127&e[t])<<28}return r}function iz(e){for(var t=[],r=[0];r[0]<e.length;){var n,a=r[0],i=iW(e,r),s=7&i,o=0;if(0==(i=Math.floor(i/8)))break;switch(s){case 0:for(var l=r[0];e[r[0]++]>=128;);n=e.slice(l,r[0]);break;case 5:o=4,n=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,n=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=iW(e,r),n=e.slice(r[0],r[0]+o),r[0]+=o;break;default:throw Error("PB Type ".concat(s," for Field ").concat(i," at offset ").concat(a))}var c={data:n,type:s};null==t[i]?t[i]=[c]:t[i].push(c)}return t}function iG(e){var t=[];return e.forEach(function(e,r){e.forEach(function(e){e.data&&(t.push(iH(8*r+e.type)),2==e.type&&t.push(iH(e.data.length)),t.push(e.data))})}),iU(t)}function iV(e,t){return(null==e?void 0:e.map(function(e){return t(e.data)}))||[]}function iK(e){for(var t,r=[],n=[0];n[0]<e.length;){var a=iW(e,n),i=iz(e.slice(n[0],n[0]+a));n[0]+=a;var s={id:ij(i[1][0].data),messages:[]};i[2].forEach(function(t){var r=iz(t.data),a=ij(r[3][0].data);s.messages.push({meta:r,data:e.slice(n[0],n[0]+a)}),n[0]+=a}),(null==(t=i[3])?void 0:t[0])&&(s.merge=ij(i[3][0].data)>>>0>0),r.push(s)}return r}function iY(e){var t=[];return e.forEach(function(e){var r=[];r[1]=[{data:iH(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:iH(+!!e.merge),type:0}]);var n=[];e.messages.forEach(function(e){n.push(e.data),e.meta[3]=[{type:0,data:iH(e.data.length)}],r[2].push({data:iG(e.meta),type:2})});var a=iG(r);t.push(iH(a.length)),t.push(a),n.forEach(function(e){return t.push(e)})}),iU(t)}function iX(e){for(var t=[],r=0;r<e.length;){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(function(e,t){if(0!=e)throw Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=iW(t,r),a=[];r[0]<t.length;){var i=3&t[r[0]];if(0==i){var s=t[r[0]++]>>2;if(s<60)++s;else{var o=s-59;s=t[r[0]],o>1&&(s|=t[r[0]+1]<<8),o>2&&(s|=t[r[0]+2]<<16),o>3&&(s|=t[r[0]+3]<<24),s>>>=0,s++,r[0]+=o}a.push(t.slice(r[0],r[0]+s)),r[0]+=s;continue}var l=0,c=0;if(1==i?(c=(t[r[0]]>>2&7)+4,l=(224&t[r[0]++])<<3|t[r[0]++]):(c=(t[r[0]++]>>2)+1,2==i?(l=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(l=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[iU(a)],0==l)throw Error("Invalid offset 0");if(l>a[0].length)throw Error("Invalid offset beyond length");if(c>=l)for(a.push(a[0].slice(-l)),c-=l;c>=a[a.length-1].length;)a.push(a[a.length-1]),c-=a[a.length-1].length;a.push(a[0].slice(-l,-l+c))}var f=iU(a);if(f.length!=n)throw Error("Unexpected length: ".concat(f.length," != ").concat(n));return f}(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw Error("data is not a valid framed stream!");return iU(t)}function i$(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,0xfffffff),a=new Uint8Array(4);t.push(a);var i=iH(n),s=i.length;t.push(i),n<=60?(s++,t.push(new Uint8Array([n-1<<2]))):n<=256?(s+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(s+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=0x1000000?(s+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=0x100000000&&(s+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),s+=n,a[0]=0,a[1]=255&s,a[2]=s>>8&255,a[3]=s>>16&255,r+=n}return iU(t)}function iJ(e,t){var r=new Uint8Array(32),n=iL(r),a=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,function(e,t,r){var n=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(127&n)<<1;for(var i=0;a>=1;++i,a/=256)e[t+i]=255&a;e[t+15]|=r>=0?0:128}(r,a,e.v),i|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,+!!e.v,!0),i|=2,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,i,!0),r.slice(0,a)}function iq(e,t){var r=new Uint8Array(32),n=iL(r),a=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),i|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,+!!e.v,!0),i|=32,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,i,!0),r.slice(0,a)}function iZ(e){return iW(iz(e)[1][0].data)}function iQ(e,t){var r=iz(t.data),n=ij(r[1][0].data),a=r[3],i=[];return(a||[]).forEach(function(t){var r=iz(t.data),a=ij(r[1][0].data)>>>0;switch(n){case 1:i[a]=iF(r[3][0].data);break;case 8:var s=iz(e[iZ(r[9][0].data)][0].data),o=e[iZ(s[1][0].data)][0],l=ij(o.meta[1][0].data);if(2001!=l)throw Error("2000 unexpected reference to ".concat(l));var c=iz(o.data);i[a]=c[3].map(function(e){return iF(e.data)}).join("")}}),i}function i1(e){return function(t){for(var r=0;r!=e.length;++r){var n=e[r];void 0===t[n[0]]&&(t[n[0]]=n[1]),"n"===n[2]&&(t[n[0]]=Number(t[n[0]]))}}}function i0(e){i1([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function i2(e){i1([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function i4(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return eE(t.file,ey.write(e,{type:x?"buffer":""}));case"string":throw Error("'string' output type invalid for '"+t.bookType+"' files");default:throw Error("Unrecognized type "+t.type)}return ey.write(e,t)}function i3(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return E(ti(n));case"binary":return ti(n);case"string":return e;case"file":return eE(t.file,n,"utf8");case"buffer":if(x)return A(n,"utf8");if("undefined"!=typeof TextEncoder)return new TextEncoder().encode(n);return i3(n,{type:"binary"}).split("").map(function(e){return e.charCodeAt(0)})}throw Error("Unrecognized type "+t.type)}function i5(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return"base64"==t.type?E(r):"string"==t.type?ta(r):r;case"file":return eE(t.file,e);case"buffer":return e;default:throw Error("Unrecognized type "+t.type)}}function i6(e,t,r){var n=r||{};return n.type="file",n.file=t,function(e){if(!e.bookType){var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();t.match(/^\.[a-z]+$/)&&(e.bookType=t.slice(1)),e.bookType=({xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"})[e.bookType]||e.bookType}}(n),function e(t,r){m(),function(e){if(!e||!e.SheetNames||!e.Sheets)throw Error("Invalid Workbook");if(!e.SheetNames.length)throw Error("Workbook is empty");var t,r,n=e.Workbook&&e.Workbook.Sheets||[];t=e.SheetNames,r=!!e.vbaraw,t.forEach(function(e,a){ih(e);for(var i=0;i<a;++i)if(e==t[i])throw Error("Duplicate Sheet Name: "+e);if(r){var s=n&&n[a]&&n[a].CodeName||e;if(95==s.charCodeAt(0)&&s.length>22)throw Error("Bad Code Name: Worksheet"+s)}});for(var a=0;a<e.SheetNames.length;++a)!function(e,t,r){if(e&&e["!ref"]){var n=ri(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw Error("Bad range ("+r+"): "+e["!ref"])}}(e.Sheets[e.SheetNames[a]],e.SheetNames[a],a)}(t);var n,a,i=eU(r||{});if(i.cellStyles&&(i.cellNF=!0,i.sheetStubs=!0),"array"==i.type){i.type="binary";var s=e(t,i);return i.type="array",C(s)}var o=0;if(i.sheet&&(o="number"==typeof i.sheet?i.sheet:t.SheetNames.indexOf(i.sheet),!t.SheetNames[o]))throw Error("Sheet not found: "+i.sheet+" : "+typeof i.sheet);switch(i.bookType||"xlsb"){case"xml":case"xlml":return i3(function(e,t){t||(t={}),e.SSF||(e.SSF=eU(j)),e.SSF&&(eb(),ev(e.SSF),t.revssf=e_(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],ie(t.cellXfs,{},{revssf:{General:0}}));var r,n,a,i,s,o,l,c,f,h,u=[];u.push((r=t,n=[],e.Props&&n.push((a=e.Props,i=[],eS(r1).map(function(e){for(var t=0;t<rY.length;++t)if(rY[t][1]==e)return rY[t];for(t=0;t<rJ.length;++t)if(rJ[t][1]==e)return rJ[t];throw e}).forEach(function(e){if(null!=a[e[1]]){var t=r&&r.Props&&null!=r.Props[e[1]]?r.Props[e[1]]:a[e[1]];"date"===e[2]&&(t=new Date(t).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof t?t=String(t):!0===t||!1===t?t=t?"1":"0":t instanceof Date&&(t=new Date(t).toISOString().replace(/\.\d*Z/,"")),i.push(th(r1[e[1]]||e[1],t))}}),td("DocumentProperties",i.join(""),{xmlns:tw.o}))),e.Custprops&&n.push((s=e.Props,o=e.Custprops,l=["Worksheets","SheetNames"],c="CustomDocumentProperties",f=[],s&&eS(s).forEach(function(e){if(Object.prototype.hasOwnProperty.call(s,e)){for(var t=0;t<rY.length;++t)if(e==rY[t][1])return;for(t=0;t<rJ.length;++t)if(e==rJ[t][1])return;for(t=0;t<l.length;++t)if(e==l[t])return;var r=s[e],n="string";"number"==typeof r?(n="float",r=String(r)):!0===r||!1===r?(n="boolean",r=r?"1":"0"):r=String(r),f.push(td(e6(e),r,{"dt:dt":n}))}}),o&&eS(o).forEach(function(e){if(Object.prototype.hasOwnProperty.call(o,e)&&!(s&&Object.prototype.hasOwnProperty.call(s,e))){var t=o[e],r="string";"number"==typeof t?(r="float",t=String(t)):!0===t||!1===t?(r="boolean",t=t?"1":"0"):t instanceof Date?(r="dateTime.tz",t=t.toISOString()):t=String(t),f.push(td(e6(e),t,{"dt:dt":r}))}}),"<"+c+' xmlns="'+tw.o+'">'+f.join("")+"</"+c+">")),n.join(""))),u.push(""),u.push(""),u.push("");for(var d=0;d<e.SheetNames.length;++d)u.push(td("Worksheet",function(e,t,r){var n=[],a=r.SheetNames[e],i=r.Sheets[a],s=i?function(e,t,r,n){if(!e||!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,i=[],s=0;s<a.length;++s){var o=a[s];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||i.push(iv(o)))}return i.join("")}(i,0,e,r):"";return s.length>0&&n.push("<Names>"+s+"</Names>"),(s=i?function(e,t,r,n){if(!e["!ref"])return"";var a=ri(e["!ref"]),i=e["!merges"]||[],s=0,o=[];e["!cols"]&&e["!cols"].forEach(function(e,t){ai(e);var r=!!e.width,n=a7(t,e),a={"ss:Index":t+1};r&&(a["ss:Width"]=at(n.width)),e.hidden&&(a["ss:Hidden"]="1"),o.push(td("Column",null,a))});for(var l=Array.isArray(e),c=a.s.r;c<=a.e.r;++c){for(var f=[function(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=ao(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}(c,(e["!rows"]||[])[c])],h=a.s.c;h<=a.e.c;++h){var u=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>h)&&!(i[s].s.r>c)&&!(i[s].e.c<h)&&!(i[s].e.r<c)){(i[s].s.c!=h||i[s].s.r!=c)&&(u=!0);break}if(!u){var d={r:c,c:h},p=rr(d),m=l?(e[c]||[])[h]:e[p];f.push(function(e,t,r,n,a,i,s){if(!e||void 0==e.v&&void 0==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+e5(aR(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var l=rt(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(l.r==s.r?"":"["+(l.r-s.r)+"]")+"C"+(l.c==s.c?"":"["+(l.c-s.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=e5(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=e5(e.l.Tooltip))),r["!merges"])for(var c=r["!merges"],f=0;f!=c.length;++f)c[f].s.c==s.c&&c[f].s.r==s.r&&(c[f].e.c>c[f].s.c&&(o["ss:MergeAcross"]=c[f].e.c-c[f].s.c),c[f].e.r>c[f].s.r&&(o["ss:MergeDown"]=c[f].e.r-c[f].s.r));var h="",u="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=rM[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||j[14]);break;case"s":h="String",u=((e.v||"")+"").replace(e4,function(e){return e2[e]}).replace(e8,function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"})}var d=ie(n.cellXfs,e,n);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=s.c+1;var p=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+h+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=e.c.map(function(e){var t=td("ss:Data",(e.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"});return td("Comment",t,{"ss:Author":e.a})}).join("")),td("Cell",m,o)}(m,p,e,t,0,0,d))}}f.push("</Row>"),f.length>2&&o.push(f.join(""))}return o.join("")}(i,t,0,0):"").length>0&&n.push("<Table>"+s+"</Table>"),n.push(function(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(td("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(td("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(td("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)a.push(td("Visible",1==n.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&(!n.Workbook.Sheets[i]||n.Workbook.Sheets[i].Hidden);++i);i==r&&a.push("<Selected/>")}return(((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(th("ProtectContents","True")),e["!protect"].objects&&a.push(th("ProtectObjects","True")),e["!protect"].scenarios&&a.push(th("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||a.push(th("EnableSelection","UnlockedCells")):a.push(th("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(t){e["!protect"][t[0]]&&a.push("<"+t[1]+"/>")})),0==a.length)?"":td("WorksheetOptions",a.join(""),{xmlns:tw.x})}(i,0,e,r)),n.join("")}(d,t,e),{"ss:Name":e5(e.SheetNames[d])}));return u[2]=(h=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'],t.cellXfs.forEach(function(e,t){var r=[];r.push(td("NumberFormat",null,{"ss:Format":e5(j[e.numFmtId])})),h.push(td("Style",r.join(""),{"ss:ID":"s"+(21+t)}))}),td("Styles",h.join(""))),u[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];null==a.Sheet&&(a.Name.match(/^_xlfn\./)||r.push(iv(a)))}return td("Names",r.join(""))}(e,t),eJ+td("Workbook",u.join(""),{xmlns:tw.ss,"xmlns:o":tw.o,"xmlns:x":tw.x,"xmlns:ss":tw.ss,"xmlns:dt":tw.dt,"xmlns:html":tw.html})}(t,i),i);case"slk":case"sylk":return i3(nV.from_sheet(t.Sheets[t.SheetNames[o]],i),i);case"htm":case"html":return i3(ik(t.Sheets[t.SheetNames[o]],i),i);case"txt":var c=se(t.Sheets[t.SheetNames[o]],i);switch(i.type){case"base64":return E(c);case"binary":case"string":return c;case"file":return eE(i.file,c,"binary");case"buffer":if(x)return A(c,"binary");return c.split("").map(function(e){return e.charCodeAt(0)})}throw Error("Unrecognized type "+i.type);case"csv":return i3(i9(t.Sheets[t.SheetNames[o]],i),i,"\uFEFF");case"dif":return i3(nK.from_sheet(t.Sheets[t.SheetNames[o]],i),i);case"dbf":return i5(nG.from_sheet(t.Sheets[t.SheetNames[o]],i),i);case"prn":return i3(nX.from_sheet(t.Sheets[t.SheetNames[o]],i),i);case"rtf":return i3(n7.from_sheet(t.Sheets[t.SheetNames[o]],i),i);case"eth":return i3(nY.from_sheet(t.Sheets[t.SheetNames[o]],i),i);case"fods":return i3(iM(t,i),i);case"wk1":return i5(n$.sheet_to_wk1(t.Sheets[t.SheetNames[o]],i),i);case"wk3":return i5(n$.book_to_wk3(t,i),i);case"biff2":i.biff||(i.biff=2);case"biff3":i.biff||(i.biff=3);case"biff4":return i.biff||(i.biff=4),i5(iA(t,i),i);case"biff5":i.biff||(i.biff=5);case"biff8":case"xla":case"xls":return i.biff||(i.biff=8),i4(function(e,t){var r,n=t||{},a=ey.utils.cfb_new({root:"R"}),i="/Workbook";switch(n.bookType||"xls"){case"xls":n.bookType="biff8";case"xla":n.bookType||(n.bookType="xla");case"biff8":i="/Workbook",n.biff=8;break;case"biff5":i="/Book",n.biff=5;break;default:throw Error("invalid type "+n.bookType+" for XLS CFB")}return ey.utils.cfb_add(a,i,iA(e,n)),8==n.biff&&(e.Props||e.Custprops)&&function(e,t){var r,n=[],a=[],i=[],s=0,o=ex(rR,"n"),l=ex(rD,"n");if(e.Props)for(s=0,r=eS(e.Props);s<r.length;++s)(Object.prototype.hasOwnProperty.call(o,r[s])?n:Object.prototype.hasOwnProperty.call(l,r[s])?a:i).push([r[s],e.Props[r[s]]]);if(e.Custprops)for(s=0,r=eS(e.Custprops);s<r.length;++s)Object.prototype.hasOwnProperty.call(e.Props||{},r[s])||(Object.prototype.hasOwnProperty.call(o,r[s])?n:Object.prototype.hasOwnProperty.call(l,r[s])?a:i).push([r[s],e.Custprops[r[s]]]);var c=[];for(s=0;s<i.length;++s)nt.indexOf(i[s][0])>-1||rq.indexOf(i[s][0])>-1||null!=i[s][1]&&c.push(i[s]);a.length&&ey.utils.cfb_add(t,"/\x05SummaryInformation",na(a,iT.SI,l,rD)),(n.length||c.length)&&ey.utils.cfb_add(t,"/\x05DocumentSummaryInformation",na(n,iT.DSI,o,rR,c.length?c:null,iT.UDI))}(e,a),8==n.biff&&e.vbaraw&&(r=ey.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})).FullPaths.forEach(function(e,t){if(0!=t){var n=e.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==n.slice(-1)&&ey.utils.cfb_add(a,n,r.FileIndex[t].content)}}),a}(t,n=i||{}),n);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return function(e,t){var r={},n=x?"nodebuffer":"undefined"!=typeof Uint8Array?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw Error("Unrecognized type "+t.type)}var a=e.FullPaths?ey.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!=typeof Deno&&"string"==typeof a){if("binary"==t.type||"base64"==t.type)return a;a=new Uint8Array(C(a))}return t.password&&"undefined"!=typeof encrypt_agile?i4(encrypt_agile(a,t.password),t):"file"===t.type?eE(t.file,a):"string"==t.type?ta(a):a}("ods"==(a=eU(i||{})).bookType?iM(t,a):"numbers"==a.bookType?function(e,t){if(!t||!t.numbers)throw Error("Must pass a `numbers` option -- check the README");var r,n=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var a=rn(n["!ref"]);a.s.r=a.s.c=0;var i=!1;a.e.c>9&&(i=!0,a.e.c=9),a.e.r>49&&(i=!0,a.e.r=49),i&&console.error("The Numbers writer is currently limited to ".concat(ra(a)));var s=i8(n,{range:a,header:1}),o=["~Sh33tJ5~"];s.forEach(function(e){return e.forEach(function(e){"string"==typeof e&&o.push(e)})});var l={},c=[],f=ey.read(t.numbers,{type:"base64"});f.FileIndex.map(function(e,t){return[e,f.FullPaths[t]]}).forEach(function(e){var t=e[0],r=e[1];2==t.type&&t.name.match(/\.iwa/)&&iK(iX(t.content)).forEach(function(e){c.push(e.id),l[e.id]={deps:[],location:r,type:ij(e.messages[0].meta[1][0].data)}})}),c.sort(function(e,t){return e-t});var h=c.filter(function(e){return e>1}).map(function(e){return[e,iH(e)]});f.FileIndex.map(function(e,t){return[e,f.FullPaths[t]]}).forEach(function(e){var t=e[0];e[1],t.name.match(/\.iwa/)&&iK(iX(t.content)).forEach(function(e){e.messages.forEach(function(t){h.forEach(function(t){e.messages.some(function(e){return 11006!=ij(e.meta[1][0].data)&&function(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}(e.data,t[1])})&&l[t[0]].deps.push(e.id)})})})});for(var u=ey.find(f,l[1].location),d=iK(iX(u.content)),p=0;p<d.length;++p){var m=d[p];1==m.id&&(r=m)}var g=iZ(iz(r.messages[0].data)[1][0].data);for(p=0,d=iK(iX((u=ey.find(f,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);for(p=0,g=iZ(iz(r.messages[0].data)[2][0].data),d=iK(iX((u=ey.find(f,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);for(p=0,g=iZ(iz(r.messages[0].data)[2][0].data),d=iK(iX((u=ey.find(f,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);var v=iz(r.messages[0].data);v[6][0].data=iH(a.e.r+1),v[7][0].data=iH(a.e.c+1);for(var b=iZ(v[46][0].data),w=ey.find(f,l[b].location),T=iK(iX(w.content)),y=0;y<T.length&&T[y].id!=b;++y);if(T[y].id!=b)throw"Bad ColumnRowUIDMapArchive";var E=iz(T[y].messages[0].data);E[1]=[],E[2]=[],E[3]=[];for(var S=0;S<=a.e.c;++S){var x=[];x[1]=x[2]=[{type:0,data:iH(S+420690)}],E[1].push({type:2,data:iG(x)}),E[2].push({type:0,data:iH(S)}),E[3].push({type:0,data:iH(S)})}E[4]=[],E[5]=[],E[6]=[];for(var A=0;A<=a.e.r;++A)(x=[])[1]=x[2]=[{type:0,data:iH(A+726270)}],E[4].push({type:2,data:iG(x)}),E[5].push({type:0,data:iH(A)}),E[6].push({type:0,data:iH(A)});T[y].messages[0].data=iG(E),w.content=i$(iY(T)),w.size=w.content.length,delete v[46];var _=iz(v[4][0].data);_[7][0].data=iH(a.e.r+1);var k=iZ(iz(_[1][0].data)[2][0].data);if((T=iK(iX((w=ey.find(f,l[k].location)).content)))[0].id!=k)throw"Bad HeaderStorageBucket";var C=iz(T[0].messages[0].data);for(A=0;A<s.length;++A){var R=iz(C[2][0].data);R[1][0].data=iH(A),R[4][0].data=iH(s[A].length),C[2][A]={type:C[2][0].type,data:iG(R)}}T[0].messages[0].data=iG(C),w.content=i$(iY(T)),w.size=w.content.length;var D=iZ(_[2][0].data);if((T=iK(iX((w=ey.find(f,l[D].location)).content)))[0].id!=D)throw"Bad HeaderStorageBucket";for(S=0,C=iz(T[0].messages[0].data);S<=a.e.c;++S)(R=iz(C[2][0].data))[1][0].data=iH(S),R[4][0].data=iH(a.e.r+1),C[2][S]={type:C[2][0].type,data:iG(R)};T[0].messages[0].data=iG(C),w.content=i$(iY(T)),w.size=w.content.length;var N=iZ(_[4][0].data);!function(){for(var e,t=ey.find(f,l[N].location),r=iK(iX(t.content)),n=0;n<r.length;++n){var a=r[n];a.id==N&&(e=a)}var i=iz(e.messages[0].data);i[3]=[];var s=[];o.forEach(function(e,t){s[1]=[{type:0,data:iH(t)}],s[2]=[{type:0,data:iH(1)}],s[3]=[{type:2,data:"undefined"!=typeof TextEncoder?new TextEncoder().encode(e):O(ti(e))}],i[3].push({type:2,data:iG(s)})}),e.messages[0].data=iG(i),t.content=i$(iY(r)),t.size=t.content.length}();var I=iz(_[3][0].data),P=I[1][0];delete I[2];var M=iz(P.data),L=iZ(M[2][0].data);!function(){for(var e,t=ey.find(f,l[L].location),r=iK(iX(t.content)),n=0;n<r.length;++n){var i=r[n];i.id==L&&(e=i)}var c=iz(e.messages[0].data);delete c[6],delete I[7];var h=new Uint8Array(c[5][0].data);c[5]=[];for(var u=0,d=0;d<=a.e.r;++d){var p=iz(h);u+=function(e,t,r){if(!(null==(n=e[6])?void 0:n[0])||!(null==(a=e[7])?void 0:a[0]))throw"Mutation only works on post-BNC storages!";if((null==(s=null==(i=e[8])?void 0:i[0])?void 0:s.data)&&ij(e[8][0].data)>0)throw"Math only works with normal offsets";for(var n,a,i,s,o,l,c=0,f=iL(e[7][0].data),h=0,u=[],d=iL(e[4][0].data),p=0,m=[],g=0;g<t.length;++g){if(null==t[g]){f.setUint16(2*g,65535,!0),d.setUint16(2*g,65535);continue}switch(f.setUint16(2*g,h,!0),d.setUint16(2*g,p,!0),typeof t[g]){case"string":o=iJ({t:"s",v:t[g]},r),l=iq({t:"s",v:t[g]},r);break;case"number":o=iJ({t:"n",v:t[g]},r),l=iq({t:"n",v:t[g]},r);break;case"boolean":o=iJ({t:"b",v:t[g]},r),l=iq({t:"b",v:t[g]},r);break;default:throw Error("Unsupported value "+t[g])}u.push(o),h+=o.length,m.push(l),p+=l.length,++c}for(e[2][0].data=iH(c);g<e[7][0].data.length/2;++g)f.setUint16(2*g,65535,!0),d.setUint16(2*g,65535,!0);return e[6][0].data=iU(u),e[3][0].data=iU(m),c}(p,s[d],o),p[1][0].data=iH(d),c[5].push({data:iG(p),type:2})}c[1]=[{type:0,data:iH(a.e.c+1)}],c[2]=[{type:0,data:iH(a.e.r+1)}],c[3]=[{type:0,data:iH(u)}],c[4]=[{type:0,data:iH(a.e.r+1)}],e.messages[0].data=iG(c),t.content=i$(iY(r)),t.size=t.content.length}(),P.data=iG(M),_[3][0].data=iG(I),v[4][0].data=iG(_),r.messages[0].data=iG(v);var F=i$(iY(d));return u.content=F,u.size=u.content.length,f}(t,a):"xlsb"==a.bookType?function(e,t){ax=1024,e&&!e.SSF&&(e.SSF=eU(j)),e&&e.SSF&&(eb(),ev(e.SSF),t.revssf=e_(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,a6?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r,n,a,i,s,o,c,f="xlsb"==t.bookType?"bin":"xml",h=ak.indexOf(t.bookType)>-1,u=rB();i2(t=t||{});var d=e$(),p="",m=0;if(t.cellXfs=[],ie(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),eX(d,p="docProps/core.xml",r$(e.Props,t)),u.coreprops.push(p),rG(t.rels,2,p,rH.CORE_PROPS),p="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var g=[],v=0;v<e.SheetNames.length;++v)2!=(e.Workbook.Sheets[v]||{}).Hidden&&g.push(e.SheetNames[v]);e.Props.SheetNames=g}else e.Props.SheetNames=e.SheetNames;for(e.Props.Worksheets=e.Props.SheetNames.length,eX(d,p,rZ(e.Props,t)),u.extprops.push(p),rG(t.rels,3,p,rH.EXT_PROPS),e.Custprops!==e.Props&&eS(e.Custprops||{}).length>0&&(eX(d,p="docProps/custom.xml",rQ(e.Custprops,t)),u.custprops.push(p),rG(t.rels,4,p,rH.CUST_PROPS)),m=1;m<=e.SheetNames.length;++m){var b={"!id":{}},w=e.Sheets[e.SheetNames[m-1]];if((w||{})["!type"],eX(d,p="xl/worksheets/sheet"+m+"."+f,(T=m-1,y=p,E=t,(".bin"===y.slice(-4)?function(e,t,r,n){var a,i,s,o,l,c,f,h,u,d=t0(),p=r.SheetNames[e],m=r.Sheets[p]||{},g=p;try{r&&r.Workbook&&(g=r.Workbook.Sheets[e].CodeName||g)}catch(e){}var v=ri(m["!ref"]||"A1");if(v.e.c>16383||v.e.r>1048575){if(t.WTF)throw Error("Range "+(m["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");v.e.c=Math.min(v.e.c,16383),v.e.r=Math.min(v.e.c,1048575)}m["!links"]=[],m["!comments"]=[],t2(d,129),(r.vbaraw||m["!outline"])&&t2(d,147,function(e,t,r){null==r&&(r=t1(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return rk({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),rd(e,r),r.slice(0,r.l)}(g,m["!outline"])),t2(d,148,rx(v)),a=r.Workbook,t2(d,133),t2(d,137,(null==i&&(i=t1(30)),s=924,(((a||{}).Views||[])[0]||{}).RTL&&(s|=32),i.write_shift(2,s),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(1,0),i.write_shift(1,0),i.write_shift(2,0),i.write_shift(2,100),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(4,0),i)),t2(d,138),t2(d,134),m&&m["!cols"]&&(t2(d,390),m["!cols"].forEach(function(e,t){var r,n,a;e&&t2(d,60,(null==r&&(r=t1(18)),n=a7(t,e),r.write_shift(-4,t),r.write_shift(-4,t),r.write_shift(4,256*(n.width||10)),r.write_shift(4,0),a=0,e.hidden&&(a|=1),"number"==typeof n.width&&(a|=2),e.level&&(a|=e.level<<8),r.write_shift(2,a),r))}),t2(d,391)),function(e,t,r,n){var a,i=ri(t["!ref"]||"A1"),s="",o=[];t2(e,145);var l=Array.isArray(t),c=i.e.r;t["!rows"]&&(c=Math.max(i.e.r,t["!rows"].length-1));for(var f=i.s.r;f<=c;++f){s=t7(f),function(e,t,r,n){var a=function(e,t,r){var n=t1(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var i=320;a.hpx?i=20*as(a.hpx):a.hpt&&(i=20*a.hpt),n.write_shift(2,i),n.write_shift(1,0);var s=0;a.level&&(s|=a.level),a.hidden&&(s|=16),(a.hpx||a.hpt)&&(s|=32),n.write_shift(1,s),n.write_shift(1,0);var o=0,l=n.l;n.l+=4;for(var c={r:e,c:0},f=0;f<16;++f)if(!(t.s.c>f+1<<10)&&!(t.e.c<f<<10)){for(var h=-1,u=-1,d=f<<10;d<f+1<<10;++d)c.c=d,(Array.isArray(r)?(r[c.r]||[])[c.c]:r[rr(c)])&&(h<0&&(h=d),u=d);h<0||(++o,n.write_shift(4,h),n.write_shift(4,u))}var p=n.l;return n.l=l,n.write_shift(4,o),n.l=p,n.length>n.l?n.slice(0,n.l):n}(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&t2(e,0,a)}(e,t,i,f);var h=!1;if(f<=i.e.r)for(var u=i.s.c;u<=i.e.c;++u){f===i.s.r&&(o[u]=re(u)),a=o[u]+s;var d=l?(t[f]||[])[u]:t[a];if(!d){h=!1;continue}h=function(e,t,r,n,a,i,s){if(void 0===t.v)return!1;var o,l,c,f,h,u,d,p,m,g,v,b,w,T,y,E,S,x,A,_,k,O,C,R,D="";switch(t.t){case"b":D=t.v?"1":"0";break;case"d":(t=eU(t)).z=t.z||j[14],t.v=eO(eL(t.v)),t.t="n";break;case"n":case"e":D=""+t.v;break;default:D=t.v}var N={r:r,c:n};switch(N.s=ie(a.cellXfs,t,a),t.l&&i["!links"].push([rr(N),t.l]),t.c&&i["!comments"].push([rr(N),t.c]),t.t){case"s":case"str":return a.bookSST?(D=a8(a.Strings,t.v,a.revStrings),N.t="s",N.v=D,s)?t2(e,18,(null==o&&(o=t1(8)),rb(N,o),o.write_shift(4,N.v),o)):t2(e,7,(null==l&&(l=t1(12)),rg(N,l),l.write_shift(4,N.v),l)):(N.t="str",s)?t2(e,17,(c=t,null==f&&(f=t1(8+4*c.v.length)),rb(N,f),rd(c.v,f),f.length>f.l?f.slice(0,f.l):f)):t2(e,6,(h=t,null==u&&(u=t1(12+4*h.v.length)),rg(N,u),rd(h.v,u),u.length>u.l?u.slice(0,u.l):u)),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?s?t2(e,13,(d=t,null==p&&(p=t1(8)),rb(N,p),rE(d.v,p),p)):t2(e,2,(m=t,null==g&&(g=t1(12)),rg(N,g),rE(m.v,g),g)):s?t2(e,16,(v=t,null==b&&(b=t1(12)),rb(N,b),r_(v.v,b),b)):t2(e,5,(w=t,null==T&&(T=t1(16)),rg(N,T),r_(w.v,T),T)),!0;case"b":return(N.t="b",s)?t2(e,15,(y=t,null==E&&(E=t1(5)),rb(N,E),E.write_shift(1,+!!y.v),E)):t2(e,4,(S=t,null==x&&(x=t1(9)),rg(N,x),x.write_shift(1,+!!S.v),x)),!0;case"e":return(N.t="e",s)?t2(e,14,(A=t,null==_&&(_=t1(8)),rb(N,_),_.write_shift(1,A.v),_.write_shift(2,0),_.write_shift(1,0),_)):t2(e,3,(k=t,null==O&&(O=t1(9)),rg(N,O),O.write_shift(1,k.v),O)),!0}return s?t2(e,12,(null==C&&(C=t1(4)),rb(N,C))):t2(e,1,(null==R&&(R=t1(8)),rg(N,R))),!0}(e,d,f,u,n,t,h)}}t2(e,146)}(d,m,0,t,r);m["!protect"]&&t2(d,535,(o=m["!protect"],null==l&&(l=t1(66)),l.write_shift(2,o.password?n3(o.password):0),l.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(e){e[1]?l.write_shift(4,+(null!=o[e[0]]&&!o[e[0]])):l.write_shift(4,null!=o[e[0]]&&o[e[0]]?0:1)}),l)),!function(e,t,r,n){if(t["!autofilter"]){var a=t["!autofilter"],i="string"==typeof a.ref?a.ref:ra(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,o=rn(i);o.s.r==o.e.r&&(o.e.r=rn(t["!ref"]).e.r,i=ra(o));for(var l=0;l<s.length;++l){var c=s[l];if("_xlnm._FilterDatabase"==c.Name&&c.Sheet==n){c.Ref="'"+r.SheetNames[n]+"'!"+i;break}}l==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),t2(e,161,rx(ri(i))),t2(e,162)}}(d,m,r,e);m&&m["!merges"]&&(t2(d,177,(c=m["!merges"].length,null==f&&(f=t1(4)),f.write_shift(4,c),f)),m["!merges"].forEach(function(e){t2(d,176,rx(e))}),t2(d,178)),m["!links"].forEach(function(e){if(e[1].Target){var t,r,a=rG(n,-1,e[1].Target.replace(/#.*$/,""),rH.HLINK);t2(d,494,(t=t1(50+4*(e[1].Target.length+(e[1].Tooltip||"").length)),rx({s:rt(e[0]),e:rt(e[0])},t),rT("rId"+a,t),rd((-1==(r=e[1].Target.indexOf("#"))?"":e[1].Target.slice(r+1))||"",t),rd(e[1].Tooltip||"",t),rd("",t),t.slice(0,t.l)))}}),delete m["!links"],m["!margins"]&&t2(d,476,(h=m["!margins"],null==u&&(u=t1(48)),a9(h),ii.forEach(function(e){r_(h[e],u)}),u)),(!t||t.ignoreEC||void 0==t.ignoreEC)&&function(e,t){if(t&&t["!ref"]){var r,n;t2(e,648),t2(e,649,(r=ri(t["!ref"]),(n=t1(24)).write_shift(4,4),n.write_shift(4,1),rx(r,n),n)),t2(e,650)}}(d,m);if(m["!comments"].length>0){var b=rG(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",rH.VML);t2(d,551,rT("rId"+b)),m["!legacy"]=b}return t2(d,130),d.end()}:ia)(T,E,e,b))),u.sheets.push(p),rG(t.wbrels,-1,"worksheets/sheet"+m+"."+f,rH.WS[0]),w){var T,y,E,S,x,A=w["!comments"],_=!1,k="";A&&A.length>0&&(eX(d,k="xl/comments"+m+"."+f,(S=k,x=t,(".bin"===S.slice(-4)?function(e){var t=t0(),r=[];return t2(t,628),t2(t,630),e.forEach(function(e){e[1].forEach(function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),t2(t,632,rd(e.a.slice(0,54))))})}),t2(t,631),t2(t,633),e.forEach(function(e){e[1].forEach(function(n){var a,i,s,o,l,c;n.iauthor=r.indexOf(n.a),t2(t,635,(a=[{s:rt(e[0]),e:rt(e[0])},n],null==i&&(i=t1(36)),i.write_shift(4,a[1].iauthor),rx(a[0],i),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,0),i)),n.t&&n.t.length>0&&t2(t,637,(o=!1,null==s&&(o=!0,s=t1(23+4*n.t.length)),s.write_shift(1,1),rd(n.t,s),s.write_shift(4,1),l={ich:0,ifnt:0},(c=s)||(c=t1(4)),c.write_shift(2,l.ich||0),c.write_shift(2,l.ifnt||0),o?s.slice(0,s.l):s)),t2(t,636),delete n.iauthor})}),t2(t,634),t2(t,629),t.end()}:a_)(A,x))),u.comments.push(k),rG(b,-1,"../comments"+m+"."+f,rH.CMNT),_=!0),w["!legacy"]&&_&&eX(d,"xl/drawings/vmlDrawing"+m+".vml",aA(m,w["!comments"])),delete w["!comments"],delete w["!legacy"]}b["!id"].rId1&&eX(d,rj(p),rz(b))}return null!=t.Strings&&t.Strings.length>0&&(eX(d,p="xl/sharedStrings."+f,(r=t.Strings,n=p,a=t,(".bin"===n.slice(-4)?function(e){var t,r=t0();t2(r,159,(t||(t=t1(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t));for(var n=0;n<e.length;++n)t2(r,19,n0(e[n]));return t2(r,160),r.end()}:n1)(r,a))),u.strs.push(p),rG(t.wbrels,-1,"sharedStrings."+f,rH.SST)),eX(d,p="xl/workbook."+f,(i=p,s=t,(".bin"===i.slice(-4)?function(e,t){var r=t0();t2(r,131),t2(r,128,function(e,t){t||(t=t1(127));for(var r=0;4!=r;++r)t.write_shift(4,0);return rd("SheetJS",t),rd(l.version,t),rd(l.version,t),rd("7262",t),t.length>t.l?t.slice(0,t.l):t}()),t2(r,153,(a=e.Workbook&&e.Workbook.WBProps||null,i||(i=t1(72)),s=0,a&&a.filterPrivacy&&(s|=8),i.write_shift(4,s),i.write_shift(4,0),rd(a&&a.CodeName||"ThisWorkbook",i),i.slice(0,i.l))),function(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,n,a=t.Workbook.Sheets,i=0,s=-1,o=-1;i<a.length;++i)a[i]&&(a[i].Hidden||-1!=s)?1==a[i].Hidden&&-1==o&&(o=i):s=i;o>s||(t2(e,135),t2(e,158,(r=s,n||(n=t1(29)),n.write_shift(-4,0),n.write_shift(-4,460),n.write_shift(4,28800),n.write_shift(4,17600),n.write_shift(4,500),n.write_shift(4,r),n.write_shift(4,r),n.write_shift(1,120),n.length>n.l?n.slice(0,n.l):n)),t2(e,136))}}(r,e,t);t2(r,143);for(var n=0;n!=e.SheetNames.length;++n){var a,i,s,o={Hidden:e.Workbook&&e.Workbook.Sheets&&e.Workbook.Sheets[n]&&e.Workbook.Sheets[n].Hidden||0,iTabID:n+1,strRelID:"rId"+(n+1),name:e.SheetNames[n]},c=void 0;t2(r,156,(c||(c=t1(127)),c.write_shift(4,o.Hidden),c.write_shift(4,o.iTabID),rT(o.strRelID,c),rd(o.name.slice(0,31),c),c.length>c.l?c.slice(0,c.l):c))}return t2(r,144),t2(r,132),r.end()}:iu)(e,s))),u.workbooks.push(p),rG(t.rels,1,p,rH.WB),eX(d,p="xl/theme/theme1.xml",aE(e.Themes,t)),u.themes.push(p),rG(t.wbrels,-1,"theme/theme1.xml",rH.THEME),eX(d,p="xl/styles."+f,(o=p,c=t,(".bin"===o.slice(-4)?function(e,t){var r,n,a,i,s,o,l,c=t0();return t2(c,278),function(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var n=e[0];n<=e[1];++n)null!=t[n]&&++r}),0!=r&&(t2(e,615,rh(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var n=r[0];n<=r[1];++n)null!=t[n]&&t2(e,44,function(e,t,r){r||(r=t1(6+4*t.length)),r.write_shift(2,e),rd(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),n}(n,t[n]))}),t2(e,616))}}(c,e.SSF),function(e){var t,r,n,a,i,s;t2(e,611,rh(1)),t2(e,43,(t={sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"},r||(r=t1(153)),r.write_shift(2,20*t.sz),(n=r)||(n=t1(2)),a=2*!!t.italic|8*!!t.strike|16*!!t.outline|32*!!t.shadow|64*!!t.condense|128*!!t.extend,n.write_shift(1,a),n.write_shift(1,0),r.write_shift(2,t.bold?700:400),i=0,"superscript"==t.vertAlign?i=1:"subscript"==t.vertAlign&&(i=2),r.write_shift(2,i),r.write_shift(1,t.underline||0),r.write_shift(1,t.family||0),r.write_shift(1,t.charset||0),r.write_shift(1,0),rk(t.color,r),s=0,"major"==t.scheme&&(s=1),"minor"==t.scheme&&(s=2),r.write_shift(1,s),rd(t.name,r),r.length>r.l?r.slice(0,r.l):r)),t2(e,612)}(c,e),t2(c,603,rh(2)),t2(c,45,ah({patternType:"none"})),t2(c,45,ah({patternType:"gray125"})),t2(c,604),t2(c,613,rh(1)),t2(c,46,(r||(r=t1(51)),r.write_shift(1,0),ad(null,r),ad(null,r),ad(null,r),ad(null,r),ad(null,r),r.length>r.l?r.slice(0,r.l):r)),t2(c,614),t2(c,626,rh(1)),t2(c,47,au({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),t2(c,627),t2(c,617,rh((n=t.cellXfs).length)),n.forEach(function(e){t2(c,47,au(e,0))}),t2(c,618),t2(c,619,rh(1)),t2(c,48,(a={xfId:0,builtinId:0,name:"Normal"},i||(i=t1(52)),i.write_shift(4,a.xfId),i.write_shift(2,1),i.write_shift(1,+a.builtinId),i.write_shift(1,0),rT(a.name||"",i),i.length>i.l?i.slice(0,i.l):i)),t2(c,620),t2(c,505,rh(0)),t2(c,506),t2(c,508,(s="TableStyleMedium9",o="PivotStyleMedium4",(l=t1(2052)).write_shift(4,0),rT(s,l),rT(o,l),l.length>l.l?l.slice(0,l.l):l)),t2(c,509),t2(c,279),c.end()}:ac)(e,c))),u.styles.push(p),rG(t.wbrels,-1,"styles."+f,rH.STY),e.vbaraw&&h&&(eX(d,p="xl/vbaProject.bin",e.vbaraw),u.vba.push(p),rG(t.wbrels,-1,"vbaProject.bin",rH.VBA)),eX(d,p="xl/metadata."+f,(".bin"===p.slice(-4)?function(){var e,t,r,n,a,i=t0();return t2(i,332),t2(i,334,rh(1)),t2(i,335,((t=t1(12+2*(e={name:"XLDAPR",version:12e4,flags:0xd06ac0b0}).name.length)).write_shift(4,e.flags),t.write_shift(4,e.version),rd(e.name,t),t.slice(0,t.l))),t2(i,336),t2(i,339,((n=t1(8+2*(r="XLDAPR").length)).write_shift(4,1),rd(r,n),n.slice(0,n.l))),t2(i,52),t2(i,35,rh(514)),t2(i,4096,rh(0)),t2(i,4097,nc(1)),t2(i,36),t2(i,53),t2(i,340),t2(i,337,((a=t1(8)).write_shift(4,1),a.write_shift(4,1),a)),t2(i,51,function(e){var t=t1(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}([[1,0]])),t2(i,338),t2(i,333),i.end()}:aS)()),u.metadata.push(p),rG(t.wbrels,-1,"metadata."+f,rH.XLMETA),eX(d,"[Content_Types].xml",rW(u,t)),eX(d,"_rels/.rels",rz(t.rels)),eX(d,"xl/_rels/workbook."+f+".rels",rz(t.wbrels)),delete t.revssf,delete t.ssf,d}(t,a):function(e,t){ax=1024,e&&!e.SSF&&(e.SSF=eU(j)),e&&e.SSF&&(eb(),ev(e.SSF),t.revssf=e_(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,a6?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r,n=ak.indexOf(t.bookType)>-1,a=rB();i2(t=t||{});var i=e$(),s="",o=0;if(t.cellXfs=[],ie(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),eX(i,s="docProps/core.xml",r$(e.Props,t)),a.coreprops.push(s),rG(t.rels,2,s,rH.CORE_PROPS),s="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var l=[],c=0;c<e.SheetNames.length;++c)2!=(e.Workbook.Sheets[c]||{}).Hidden&&l.push(e.SheetNames[c]);e.Props.SheetNames=l}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,eX(i,s,rZ(e.Props,t)),a.extprops.push(s),rG(t.rels,3,s,rH.EXT_PROPS),e.Custprops!==e.Props&&eS(e.Custprops||{}).length>0&&(eX(i,s="docProps/custom.xml",rQ(e.Custprops,t)),a.custprops.push(s),rG(t.rels,4,s,rH.CUST_PROPS));var f=["SheetJ5"];for(o=1,t.tcid=0;o<=e.SheetNames.length;++o){var h={"!id":{}},u=e.Sheets[e.SheetNames[o-1]];if((u||{})["!type"],eX(i,s="xl/worksheets/sheet"+o+".xml",ia(o-1,t,e,h)),a.sheets.push(s),rG(t.wbrels,-1,"worksheets/sheet"+o+".xml",rH.WS[0]),u){var d=u["!comments"],p=!1,m="";if(d&&d.length>0){var g=!1;d.forEach(function(e){e[1].forEach(function(e){!0==e.T&&(g=!0)})}),g&&(eX(i,m="xl/threadedComments/threadedComment"+o+".xml",function(e,t,r){var n=[eJ,td("ThreadedComments",null,{xmlns:tv.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(e){var a="";(e[1]||[]).forEach(function(i,s){if(!i.T)return void delete i.ID;i.a&&-1==t.indexOf(i.a)&&t.push(i.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==s?a=o.id:o.parentId=a,i.ID=o.id,i.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(i.a)).slice(-12)+"}"),n.push(td("threadedComment",th("text",i.t||""),o))})}),n.push("</ThreadedComments>"),n.join("")}(d,f,t)),a.threadedcomments.push(m),rG(h,-1,"../threadedComments/threadedComment"+o+".xml",rH.TCMNT)),eX(i,m="xl/comments"+o+".xml",a_(d,t)),a.comments.push(m),rG(h,-1,"../comments"+o+".xml",rH.CMNT),p=!0}u["!legacy"]&&p&&eX(i,"xl/drawings/vmlDrawing"+o+".vml",aA(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}h["!id"].rId1&&eX(i,rj(s),rz(h))}return null!=t.Strings&&t.Strings.length>0&&(eX(i,s="xl/sharedStrings.xml",n1(t.Strings,t)),a.strs.push(s),rG(t.wbrels,-1,"sharedStrings.xml",rH.SST)),eX(i,s="xl/workbook.xml",iu(e,t)),a.workbooks.push(s),rG(t.rels,1,s,rH.WB),eX(i,s="xl/theme/theme1.xml",aE(e.Themes,t)),a.themes.push(s),rG(t.wbrels,-1,"theme/theme1.xml",rH.THEME),eX(i,s="xl/styles.xml",ac(e,t)),a.styles.push(s),rG(t.wbrels,-1,"styles.xml",rH.STY),e.vbaraw&&n&&(eX(i,s="xl/vbaProject.bin",e.vbaraw),a.vba.push(s),rG(t.wbrels,-1,"vbaProject.bin",rH.VBA)),eX(i,s="xl/metadata.xml",aS()),a.metadata.push(s),rG(t.wbrels,-1,"metadata.xml",rH.XLMETA),f.length>1&&(eX(i,s="xl/persons/person.xml",(r=[eJ,td("personList",null,{xmlns:tv.TCMNT,"xmlns:x":"http://schemas.openxmlformats.org/spreadsheetml/2006/main"}).replace(/[\/]>/,">")],f.forEach(function(e,t){r.push(td("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+t).slice(-12)+"}",userId:e,providerId:"None"}))}),r.push("</personList>"),r.join(""))),a.people.push(s),rG(t.wbrels,-1,"persons/person.xml",rH.PEOPLE)),eX(i,"[Content_Types].xml",rW(a,t)),eX(i,"_rels/.rels",rz(t.rels)),eX(i,"xl/_rels/workbook.xml.rels",rz(t.wbrels)),delete t.revssf,delete t.ssf,i}(t,a),a);default:throw Error("Unrecognized bookType |"+i.bookType+"|")}}(e,n)}function i8(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},n=0,a=1,i=[],s=0,o="",l={s:{r:0,c:0},e:{r:0,c:0}},c=t||{},f=null!=c.range?c.range:e["!ref"];switch(1===c.header?n=1:"A"===c.header?n=2:Array.isArray(c.header)?n=3:null==c.header&&(n=0),typeof f){case"string":l=ri(f);break;case"number":(l=ri(e["!ref"])).s.r=f;break;default:l=f}n>0&&(a=0);var h=t7(l.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=l.s.r,b=0,w={};g&&!e[v]&&(e[v]=[]);var T=c.skipHidden&&e["!cols"]||[],y=c.skipHidden&&e["!rows"]||[];for(b=l.s.c;b<=l.e.c;++b)if(!(T[b]||{}).hidden)switch(u[b]=re(b),r=g?e[v][b]:e[u[b]+h],n){case 1:i[b]=b-l.s.c;break;case 2:i[b]=u[b];break;case 3:i[b]=c.header[b-l.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=s=ro(r,null,c),m=w[s]||0){do o=s+"_"+m++;while(w[o]);w[s]=m,w[o]=1}else w[s]=1;i[b]=o}for(v=l.s.r+a;v<=l.e.r;++v)if(!(y[v]||{}).hidden){var E=function(e,t,r,n,a,i,s,o){var l=t7(r),c=o.defval,f=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),h=!0,u=1===a?[]:{};if(1!==a)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(e){u.__rowNum__=r}else u.__rowNum__=r;if(!s||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=s?e[r][d]:e[n[d]+l];if(void 0===p||void 0===p.t){if(void 0===c)continue;null!=i[d]&&(u[i[d]]=c);continue}var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw Error("unrecognized type "+p.t)}if(null!=i[d]){if(null==m)if("e"==p.t&&null===m)u[i[d]]=null;else if(void 0!==c)u[i[d]]=c;else{if(!f||null!==m)continue;u[i[d]]=null}else u[i[d]]=f&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:ro(p,m,o);null!=m&&(h=!1)}}return{row:u,isempty:h}}(e,l,v,u,n,i,g,c);(!1===E.isempty||(1===n?!1!==c.blankrows:c.blankrows))&&(d[p++]=E.row)}return d.length=p,d}var i7=/"/g;function i9(e,t){var r=[],n=null==t?{}:t;if(null==e||null==e["!ref"])return"";var a=ri(e["!ref"]),i=void 0!==n.FS?n.FS:",",s=i.charCodeAt(0),o=void 0!==n.RS?n.RS:"\n",l=o.charCodeAt(0),c=RegExp(("|"==i?"\\|":i)+"+$"),f="",h=[];n.dense=Array.isArray(e);for(var u=n.skipHidden&&e["!cols"]||[],d=n.skipHidden&&e["!rows"]||[],p=a.s.c;p<=a.e.c;++p)(u[p]||{}).hidden||(h[p]=re(p));for(var m=0,g=a.s.r;g<=a.e.r;++g)!(d[g]||{}).hidden&&null!=(f=function(e,t,r,n,a,i,s,o){for(var l=!0,c=[],f="",h=t7(r),u=t.s.c;u<=t.e.c;++u)if(n[u]){var d=o.dense?(e[r]||[])[u]:e[n[u]+h];if(null==d)f="";else if(null!=d.v){l=!1,f=""+(o.rawNumbers&&"n"==d.t?d.v:ro(d,null,o));for(var p=0,m=0;p!==f.length;++p)if((m=f.charCodeAt(p))===a||m===i||34===m||o.forceQuotes){f='"'+f.replace(i7,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==d.f||d.F?f="":(l=!1,(f="="+d.f).indexOf(",")>=0&&(f='"'+f.replace(i7,'""')+'"'));c.push(f)}return!1===o.blankrows&&l?null:c.join(s)}(e,a,g,h,s,l,i,n))&&(n.strip&&(f=f.replace(c,"")),(f||!1!==n.blankrows)&&r.push((m++?o:"")+f));return delete n.dense,r.join("")}function se(e,t){t||(t={}),t.FS="	",t.RS="\n";var r=i9(e,t);if(void 0===a||"string"==t.type)return r;var n=a.utils.encode(1200,r,"str");return String.fromCharCode(255)+String.fromCharCode(254)+n}function st(e,t,r){var n,a=r||{},i=+!a.skipHeader,s=e||{},o=0,l=0;if(s&&null!=a.origin)if("number"==typeof a.origin)o=a.origin;else{var c="string"==typeof a.origin?rt(a.origin):a.origin;o=c.r,l=c.c}var f={s:{c:0,r:0},e:{c:l,r:o+t.length-1+i}};if(s["!ref"]){var h=ri(s["!ref"]);f.e.c=Math.max(f.e.c,h.e.c),f.e.r=Math.max(f.e.r,h.e.r),-1==o&&(o=h.e.r+1,f.e.r=o+t.length-1+i)}else -1==o&&(o=0,f.e.r=t.length-1+i);var u=a.header||[],d=0;t.forEach(function(e,t){eS(e).forEach(function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var c=e[r],f="z",h="",p=rr({c:l+d,r:o+t+i});n=sr(s,p),!c||"object"!=typeof c||c instanceof Date?("number"==typeof c?f="n":"boolean"==typeof c?f="b":"string"==typeof c?f="s":c instanceof Date?(f="d",a.cellDates||(f="n",c=eO(c)),h=a.dateNF||j[14]):null===c&&a.nullError&&(f="e",c=0),n?(n.t=f,n.v=c,delete n.w,delete n.R,h&&(n.z=h)):s[p]=n={t:f,v:c},h&&(n.z=h)):s[p]=c})}),f.e.c=Math.max(f.e.c,l+u.length-1);var p=t7(o);if(i)for(d=0;d<u.length;++d)s[re(d+l)+p]={t:"s",v:u[d]};return s["!ref"]=ra(f),s}function sr(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var n=rt(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return"number"!=typeof t?sr(e,rr(t)):sr(e,rr({r:t,c:r||0}))}function sn(){return{SheetNames:[],Sheets:{}}}function sa(e,t,r,n){var a=1;if(!r)for(;a<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+a);++a,r=void 0);if(!r||e.SheetNames.length>=65535)throw Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);a=i&&+i[2]||0;var s=i&&i[1]||r;for(++a;a<=65535&&-1!=e.SheetNames.indexOf(r=s+a);++a);}if(ih(r),e.SheetNames.indexOf(r)>=0)throw Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function si(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var ss={encode_col:re,encode_row:t7,encode_cell:rr,encode_range:ra,decode_col:t9,decode_row:t8,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:rt,decode_range:rn,format_cell:ro,sheet_add_aoa:rc,sheet_add_json:st,sheet_add_dom:iO,aoa_to_sheet:rf,json_to_sheet:function(e,t){return st(null,e,t)},table_to_sheet:iC,table_to_book:function(e,t){return rl(iC(e,t),t)},sheet_to_csv:i9,sheet_to_txt:se,sheet_to_json:i8,sheet_to_html:ik,sheet_to_formulae:function(e){var t,r="",n="";if(null==e||null==e["!ref"])return[];var a,i=ri(e["!ref"]),s="",o=[],l=[],c=Array.isArray(e);for(a=i.s.c;a<=i.e.c;++a)o[a]=re(a);for(var f=i.s.r;f<=i.e.r;++f)for(s=t7(f),a=i.s.c;a<=i.e.c;++a)if(r=o[a]+s,t=c?(e[f]||[])[a]:e[r],n="",void 0!==t){if(null!=t.F){if(r=t.F,!t.f)continue;n=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)n=t.f;else if("z"==t.t)continue;else if("n"==t.t&&null!=t.v)n=""+t.v;else if("b"==t.t)n=t.v?"TRUE":"FALSE";else if(void 0!==t.w)n="'"+t.w;else{if(void 0===t.v)continue;n="s"==t.t?"'"+t.v:""+t.v}l[l.length]=r+"="+n}return l},sheet_to_row_object_array:i8,sheet_get_cell:sr,book_new:sn,book_append_sheet:sa,book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw Error("Cannot find sheet name |"+t+"|")}throw Error("Cannot find sheet |"+t+"|")}(e,t);switch(!e.Workbook.Sheets[n]&&(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:si,cell_set_internal_link:function(e,t,r){return si(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,n){for(var a="string"!=typeof t?t:ri(t),i="string"==typeof t?t:ra(t),s=a.s.r;s<=a.e.r;++s)for(var o=a.s.c;o<=a.e.c;++o){var l=sr(e,s,o);l.t="n",l.F=i,delete l.v,s==a.s.r&&o==a.s.c&&(l.f=r,n&&(l.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};l.version},36558:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},43619:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},51358:(e,t,r)=>{r.d(t,{_G:()=>h,gB:()=>p,gl:()=>y});var n=r(43210),a=r.n(n),i=r(57601),s=r(62478);function o(e,t,r){let n=e.slice();return n.splice(r<0?n.length+r:r,0,n.splice(t,1)[0]),n}function l(e){return null!==e&&e>=0}let c=e=>{let{rects:t,activeIndex:r,overIndex:n,index:a}=e,i=o(t,n,r),s=t[a],l=i[a];return l&&s?{x:l.left-s.left,y:l.top-s.top,scaleX:l.width/s.width,scaleY:l.height/s.height}:null},f={scaleX:1,scaleY:1},h=e=>{var t;let{activeIndex:r,activeNodeRect:n,index:a,rects:i,overIndex:s}=e,o=null!=(t=i[r])?t:n;if(!o)return null;if(a===r){let e=i[s];return e?{x:0,y:r<s?e.top+e.height-(o.top+o.height):e.top-o.top,...f}:null}let l=function(e,t,r){let n=e[t],a=e[t-1],i=e[t+1];return n?r<t?a?n.top-(a.top+a.height):i?i.top-(n.top+n.height):0:i?i.top-(n.top+n.height):a?n.top-(a.top+a.height):0:0}(i,a,r);return a>r&&a<=s?{x:0,y:-o.height-l,...f}:a<r&&a>=s?{x:0,y:o.height+l,...f}:{x:0,y:0,...f}},u="Sortable",d=a().createContext({activeIndex:-1,containerId:u,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:c,disabled:{draggable:!1,droppable:!1}});function p(e){let{children:t,id:r,items:o,strategy:l=c,disabled:f=!1}=e,{active:h,dragOverlay:p,droppableRects:m,over:g,measureDroppableContainers:v}=(0,i.fF)(),b=(0,s.YG)(u,r),w=null!==p.rect,T=(0,n.useMemo)(()=>o.map(e=>"object"==typeof e&&"id"in e?e.id:e),[o]),y=null!=h,E=h?T.indexOf(h.id):-1,S=g?T.indexOf(g.id):-1,x=(0,n.useRef)(T),A=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(T,x.current),_=-1!==S&&-1===E||A,k="boolean"==typeof f?{draggable:f,droppable:f}:f;(0,s.Es)(()=>{A&&y&&v(T)},[A,T,y,v]),(0,n.useEffect)(()=>{x.current=T},[T]);let O=(0,n.useMemo)(()=>({activeIndex:E,containerId:b,disabled:k,disableTransforms:_,items:T,overIndex:S,useDragOverlay:w,sortedRects:T.reduce((e,t,r)=>{let n=m.get(t);return n&&(e[r]=n),e},Array(T.length)),strategy:l}),[E,b,k.draggable,k.droppable,_,T,S,m,w,l]);return a().createElement(d.Provider,{value:O},t)}let m=e=>{let{id:t,items:r,activeIndex:n,overIndex:a}=e;return o(r,n,a).indexOf(t)},g=e=>{let{containerId:t,isSorting:r,wasDragging:n,index:a,items:i,newIndex:s,previousItems:o,previousContainerId:l,transition:c}=e;return!!c&&!!n&&(o===i||a!==s)&&(!!r||s!==a&&t===l)},v={duration:200,easing:"ease"},b="transform",w=s.Ks.Transition.toString({property:b,duration:0,easing:"linear"}),T={roleDescription:"sortable"};function y(e){var t,r,a,o;let{animateLayoutChanges:c=g,attributes:f,disabled:h,data:u,getNewIndex:p=m,id:y,strategy:E,resizeObserverConfig:S,transition:x=v}=e,{items:A,containerId:_,activeIndex:k,disabled:O,disableTransforms:C,sortedRects:R,overIndex:D,useDragOverlay:N,strategy:I}=(0,n.useContext)(d),P=(t=h,r=O,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(a=null==t?void 0:t.draggable)?a:r.draggable,droppable:null!=(o=null==t?void 0:t.droppable)?o:r.droppable}),M=A.indexOf(y),L=(0,n.useMemo)(()=>({sortable:{containerId:_,index:M,items:A},...u}),[_,u,M,A]),F=(0,n.useMemo)(()=>A.slice(A.indexOf(y)),[A,y]),{rect:U,node:B,isOver:W,setNodeRef:H}=(0,i.zM)({id:y,data:L,disabled:P.droppable,resizeObserverConfig:{updateMeasurementsFor:F,...S}}),{active:j,activatorEvent:z,activeNodeRect:G,attributes:V,setNodeRef:K,listeners:Y,isDragging:X,over:J,setActivatorNodeRef:q,transform:Z}=(0,i.PM)({id:y,data:L,attributes:{...T,...f},disabled:P.draggable}),Q=(0,s.jn)(H,K),ee=!!j,et=ee&&!C&&l(k)&&l(D),er=!N&&X,en=er&&et?Z:null,ea=et?null!=en?en:(null!=E?E:I)({rects:R,activeNodeRect:G,activeIndex:k,overIndex:D,index:M}):null,ei=l(k)&&l(D)?p({id:y,items:A,activeIndex:k,overIndex:D}):M,es=null==j?void 0:j.id,eo=(0,n.useRef)({activeId:es,items:A,newIndex:ei,containerId:_}),el=A!==eo.current.items,ec=c({active:j,containerId:_,isDragging:X,isSorting:ee,id:y,index:M,items:A,newIndex:eo.current.newIndex,previousItems:eo.current.items,previousContainerId:eo.current.containerId,transition:x,wasDragging:null!=eo.current.activeId}),ef=function(e){let{disabled:t,index:r,node:a,rect:o}=e,[l,c]=(0,n.useState)(null),f=(0,n.useRef)(r);return(0,s.Es)(()=>{if(!t&&r!==f.current&&a.current){let e=o.current;if(e){let t=(0,i.Sj)(a.current,{ignoreTransform:!0}),r={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(r.x||r.y)&&c(r)}}r!==f.current&&(f.current=r)},[t,r,a,o]),(0,n.useEffect)(()=>{l&&c(null)},[l]),l}({disabled:!ec,index:M,node:B,rect:U});return(0,n.useEffect)(()=>{ee&&eo.current.newIndex!==ei&&(eo.current.newIndex=ei),_!==eo.current.containerId&&(eo.current.containerId=_),A!==eo.current.items&&(eo.current.items=A)},[ee,ei,_,A]),(0,n.useEffect)(()=>{if(es===eo.current.activeId)return;if(null!=es&&null==eo.current.activeId){eo.current.activeId=es;return}let e=setTimeout(()=>{eo.current.activeId=es},50);return()=>clearTimeout(e)},[es]),{active:j,activeIndex:k,attributes:V,data:L,rect:U,index:M,newIndex:ei,items:A,isOver:W,isSorting:ee,isDragging:X,listeners:Y,node:B,overIndex:D,over:J,setNodeRef:Q,setActivatorNodeRef:q,setDroppableNodeRef:H,setDraggableNodeRef:K,transform:null!=ef?ef:ea,transition:ef||el&&eo.current.newIndex===M?w:(!er||(0,s.kx)(z))&&x&&(ee||ec)?s.Ks.Transition.toString({...x,property:b}):void 0}}i.vL.Down,i.vL.Right,i.vL.Up,i.vL.Left},54052:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},57207:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},57601:(e,t,r)=>{let n;r.d(t,{Mp:()=>eM,Hd:()=>eJ,vL:()=>o,Sj:()=>I,fF:()=>eB,PM:()=>eU,zM:()=>eH});var a,i,s,o,l,c,f,h,u,d,p=r(43210),m=r.n(p),g=r(51215),v=r(62478);let b={display:"none"};function w(e){let{id:t,value:r}=e;return m().createElement("div",{id:t,style:b},r)}function T(e){let{id:t,announcement:r,ariaLiveType:n="assertive"}=e;return m().createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":n,"aria-atomic":!0},r)}let y=(0,p.createContext)(null),E={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},S={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was moved over droppable area "+r.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was dropped over droppable area "+r.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function x(e){let{announcements:t=S,container:r,hiddenTextDescribedById:n,screenReaderInstructions:a=E}=e,{announce:i,announcement:s}=function(){let[e,t]=(0,p.useState)("");return{announce:(0,p.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),o=(0,v.YG)("DndLiveRegion"),[l,c]=(0,p.useState)(!1);(0,p.useEffect)(()=>{c(!0)},[]);var f=(0,p.useMemo)(()=>({onDragStart(e){let{active:r}=e;i(t.onDragStart({active:r}))},onDragMove(e){let{active:r,over:n}=e;t.onDragMove&&i(t.onDragMove({active:r,over:n}))},onDragOver(e){let{active:r,over:n}=e;i(t.onDragOver({active:r,over:n}))},onDragEnd(e){let{active:r,over:n}=e;i(t.onDragEnd({active:r,over:n}))},onDragCancel(e){let{active:r,over:n}=e;i(t.onDragCancel({active:r,over:n}))}}),[i,t]);let h=(0,p.useContext)(y);if((0,p.useEffect)(()=>{if(!h)throw Error("useDndMonitor must be used within a children of <DndContext>");return h(f)},[f,h]),!l)return null;let u=m().createElement(m().Fragment,null,m().createElement(w,{id:n,value:a.draggable}),m().createElement(T,{id:o,announcement:s}));return r?(0,g.createPortal)(u,r):u}function A(){}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(a||(a={}));let _=Object.freeze({x:0,y:0});function k(e,t){let{data:{value:r}}=e,{data:{value:n}}=t;return n-r}let O=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=[];for(let e of n){let{id:n}=e,i=r.get(n);if(i){let r=function(e,t){let r=Math.max(t.top,e.top),n=Math.max(t.left,e.left),a=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(n<a&&r<i){let s=t.width*t.height,o=e.width*e.height,l=(a-n)*(i-r);return Number((l/(s+o-l)).toFixed(4))}return 0}(i,t);r>0&&a.push({id:n,data:{droppableContainer:e,value:r}})}}return a.sort(k)};function C(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:_}let R=function(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>({...t,top:t.top+e*r.y,bottom:t.bottom+e*r.y,left:t.left+e*r.x,right:t.right+e*r.x}),{...t})}}(1);function D(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}let N={ignoreTransform:!1};function I(e,t){void 0===t&&(t=N);let r=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:n}=(0,v.zk)(e).getComputedStyle(e);t&&(r=function(e,t,r){let n=D(t);if(!n)return e;let{scaleX:a,scaleY:i,x:s,y:o}=n,l=e.left-s-(1-a)*parseFloat(r),c=e.top-o-(1-i)*parseFloat(r.slice(r.indexOf(" ")+1)),f=a?e.width/a:e.width,h=i?e.height/i:e.height;return{width:f,height:h,top:c,right:l+f,bottom:c+h,left:l}}(r,t,n))}let{top:n,left:a,width:i,height:s,bottom:o,right:l}=r;return{top:n,left:a,width:i,height:s,bottom:o,right:l}}function P(e){return I(e,{ignoreTransform:!0})}function M(e,t){let r=[];return e?function n(a){var i;if(null!=t&&r.length>=t||!a)return r;if((0,v.wz)(a)&&null!=a.scrollingElement&&!r.includes(a.scrollingElement))return r.push(a.scrollingElement),r;if(!(0,v.sb)(a)||(0,v.xZ)(a)||r.includes(a))return r;let s=(0,v.zk)(e).getComputedStyle(a);return(a!==e&&function(e,t){void 0===t&&(t=(0,v.zk)(e).getComputedStyle(e));let r=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let n=t[e];return"string"==typeof n&&r.test(n)})}(a,s)&&r.push(a),void 0===(i=s)&&(i=(0,v.zk)(a).getComputedStyle(a)),"fixed"===i.position)?r:n(a.parentNode)}(e):r}function L(e){let[t]=M(e,1);return null!=t?t:null}function F(e){return v.Sw&&e?(0,v.l6)(e)?e:(0,v.Ll)(e)?(0,v.wz)(e)||e===(0,v.TW)(e).scrollingElement?window:(0,v.sb)(e)?e:null:null:null}function U(e){return(0,v.l6)(e)?e.scrollX:e.scrollLeft}function B(e){return(0,v.l6)(e)?e.scrollY:e.scrollTop}function W(e){return{x:U(e),y:B(e)}}function H(e){return!!v.Sw&&!!e&&e===document.scrollingElement}function j(e){let t={x:0,y:0},r=H(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},n={x:e.scrollWidth-r.width,y:e.scrollHeight-r.height},a=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:a,isLeft:i,isBottom:e.scrollTop>=n.y,isRight:e.scrollLeft>=n.x,maxScroll:n,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let z={x:.2,y:.2};function G(e){return e.reduce((e,t)=>(0,v.WQ)(e,W(t)),_)}function V(e,t){if(void 0===t&&(t=I),!e)return;let{top:r,left:n,bottom:a,right:i}=t(e);L(e)&&(a<=0||i<=0||r>=window.innerHeight||n>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}let K=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+U(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+B(t),0)}]];class Y{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let r=M(t),n=G(r);for(let[t,a,i]of(this.rect={...e},this.width=e.width,this.height=e.height,K))for(let e of a)Object.defineProperty(this,e,{get:()=>{let a=i(r),s=n[t]-a;return this.rect[e]+s},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class X{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,r){var n;null==(n=this.target)||n.addEventListener(e,t,r),this.listeners.push([e,t,r])}}function J(e,t){let r=Math.abs(e.x),n=Math.abs(e.y);return"number"==typeof t?Math.sqrt(r**2+n**2)>t:"x"in t&&"y"in t?r>t.x&&n>t.y:"x"in t?r>t.x:"y"in t&&n>t.y}function q(e){e.preventDefault()}function Z(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(s||(s={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(o||(o={}));let Q={start:[o.Space,o.Enter],cancel:[o.Esc],end:[o.Space,o.Enter,o.Tab]},ee=(e,t)=>{let{currentCoordinates:r}=t;switch(e.code){case o.Right:return{...r,x:r.x+25};case o.Left:return{...r,x:r.x-25};case o.Down:return{...r,y:r.y+25};case o.Up:return{...r,y:r.y-25}}};class et{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new X((0,v.TW)(t)),this.windowListeners=new X((0,v.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(s.Resize,this.handleCancel),this.windowListeners.add(s.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(s.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,r=e.node.current;r&&V(r),t(_)}handleKeyDown(e){if((0,v.kx)(e)){let{active:t,context:r,options:n}=this.props,{keyboardCodes:a=Q,coordinateGetter:i=ee,scrollBehavior:s="smooth"}=n,{code:l}=e;if(a.end.includes(l))return void this.handleEnd(e);if(a.cancel.includes(l))return void this.handleCancel(e);let{collisionRect:c}=r.current,f=c?{x:c.left,y:c.top}:_;this.referenceCoordinates||(this.referenceCoordinates=f);let h=i(e,{active:t,context:r.current,currentCoordinates:f});if(h){let t=(0,v.Re)(h,f),n={x:0,y:0},{scrollableAncestors:a}=r.current;for(let r of a){let a=e.code,{isTop:i,isRight:l,isLeft:c,isBottom:f,maxScroll:u,minScroll:d}=j(r),p=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:r,right:n,bottom:a}=e.getBoundingClientRect();return{top:t,left:r,right:n,bottom:a,width:e.clientWidth,height:e.clientHeight}}(r),m={x:Math.min(a===o.Right?p.right-p.width/2:p.right,Math.max(a===o.Right?p.left:p.left+p.width/2,h.x)),y:Math.min(a===o.Down?p.bottom-p.height/2:p.bottom,Math.max(a===o.Down?p.top:p.top+p.height/2,h.y))},g=a===o.Right&&!l||a===o.Left&&!c,v=a===o.Down&&!f||a===o.Up&&!i;if(g&&m.x!==h.x){let e=r.scrollLeft+t.x,i=a===o.Right&&e<=u.x||a===o.Left&&e>=d.x;if(i&&!t.y)return void r.scrollTo({left:e,behavior:s});i?n.x=r.scrollLeft-e:n.x=a===o.Right?r.scrollLeft-u.x:r.scrollLeft-d.x,n.x&&r.scrollBy({left:-n.x,behavior:s});break}if(v&&m.y!==h.y){let e=r.scrollTop+t.y,i=a===o.Down&&e<=u.y||a===o.Up&&e>=d.y;if(i&&!t.x)return void r.scrollTo({top:e,behavior:s});i?n.y=r.scrollTop-e:n.y=a===o.Down?r.scrollTop-u.y:r.scrollTop-d.y,n.y&&r.scrollBy({top:-n.y,behavior:s});break}}this.handleMove(e,(0,v.WQ)((0,v.Re)(h,this.referenceCoordinates),n))}}}handleMove(e,t){let{onMove:r}=this.props;e.preventDefault(),r(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function er(e){return!!(e&&"distance"in e)}function en(e){return!!(e&&"delay"in e)}et.activators=[{eventName:"onKeyDown",handler:(e,t,r)=>{let{keyboardCodes:n=Q,onActivation:a}=t,{active:i}=r,{code:s}=e.nativeEvent;if(n.start.includes(s)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==a||a({event:e.nativeEvent}),!0)}return!1}}];class ea{constructor(e,t,r){var n;void 0===r&&(r=function(e){let{EventTarget:t}=(0,v.zk)(e);return e instanceof t?e:(0,v.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:a}=e,{target:i}=a;this.props=e,this.events=t,this.document=(0,v.TW)(i),this.documentListeners=new X(this.document),this.listeners=new X(r),this.windowListeners=new X((0,v.zk)(i)),this.initialCoordinates=null!=(n=(0,v.e_)(a))?n:_,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:r}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(s.Resize,this.handleCancel),this.windowListeners.add(s.DragStart,q),this.windowListeners.add(s.VisibilityChange,this.handleCancel),this.windowListeners.add(s.ContextMenu,q),this.documentListeners.add(s.Keydown,this.handleKeydown),t){if(null!=r&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(en(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(er(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:r,onPending:n}=this.props;n(r,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(s.Click,Z,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(s.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:r,initialCoordinates:n,props:a}=this,{onMove:i,options:{activationConstraint:s}}=a;if(!n)return;let o=null!=(t=(0,v.e_)(e))?t:_,l=(0,v.Re)(n,o);if(!r&&s){if(er(s)){if(null!=s.tolerance&&J(l,s.tolerance))return this.handleCancel();if(J(l,s.distance))return this.handleStart()}return en(s)&&J(l,s.tolerance)?this.handleCancel():void this.handlePending(s,l)}e.cancelable&&e.preventDefault(),i(o)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===o.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let ei={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class es extends ea{constructor(e){let{event:t}=e;super(e,ei,(0,v.TW)(t.target))}}es.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return!!r.isPrimary&&0===r.button&&(null==n||n({event:r}),!0)}}];let eo={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(l||(l={}));class el extends ea{constructor(e){super(e,eo,(0,v.TW)(e.event.target))}}el.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return r.button!==l.RightClick&&(null==n||n({event:r}),!0)}}];let ec={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class ef extends ea{constructor(e){super(e,ec)}static setup(){return window.addEventListener(ec.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(ec.move.name,e)};function e(){}}}ef.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t,{touches:a}=r;return!(a.length>1)&&(null==n||n({event:r}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(c||(c={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(f||(f={}));let eh={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(h||(h={})),(u||(u={})).Optimized="optimized";let eu=new Map;function ed(e,t){return(0,v.KG)(r=>e?r||("function"==typeof t?t(e):e):null,[t,e])}function ep(e){let{callback:t,disabled:r}=e,n=(0,v._q)(t),a=(0,p.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(n)},[r]);return(0,p.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}function em(e){return new Y(I(e),e)}function eg(e,t,r){void 0===t&&(t=em);let[n,a]=(0,p.useState)(null);function i(){a(n=>{if(!e)return null;if(!1===e.isConnected){var a;return null!=(a=null!=n?n:r)?a:null}let i=t(e);return JSON.stringify(n)===JSON.stringify(i)?n:i})}let s=function(e){let{callback:t,disabled:r}=e,n=(0,v._q)(t),a=(0,p.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(n)},[n,r]);return(0,p.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}({callback(t){if(e)for(let r of t){let{type:t,target:n}=r;if("childList"===t&&n instanceof HTMLElement&&n.contains(e)){i();break}}}}),o=ep({callback:i});return(0,v.Es)(()=>{i(),e?(null==o||o.observe(e),null==s||s.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==s||s.disconnect())},[e]),n}let ev=[];function eb(e,t){void 0===t&&(t=[]);let r=(0,p.useRef)(null);return(0,p.useEffect)(()=>{r.current=null},t),(0,p.useEffect)(()=>{let t=e!==_;t&&!r.current&&(r.current=e),!t&&r.current&&(r.current=null)},[e]),r.current?(0,v.Re)(e,r.current):_}function ew(e){return(0,p.useMemo)(()=>e?function(e){let t=e.innerWidth,r=e.innerHeight;return{top:0,left:0,right:t,bottom:r,width:t,height:r}}(e):null,[e])}let eT=[];function ey(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,v.sb)(t)?t:e}let eE=[{sensor:es,options:{}},{sensor:et,options:{}}],eS={current:{}},ex={draggable:{measure:P},droppable:{measure:P,strategy:h.WhileDragging,frequency:u.Optimized},dragOverlay:{measure:I}};class eA extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,r;return null!=(t=null==(r=this.get(e))?void 0:r.node.current)?t:void 0}}let e_={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eA,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:A},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:ex,measureDroppableContainers:A,windowRect:null,measuringScheduled:!1},ek={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:A,draggableNodes:new Map,over:null,measureDroppableContainers:A},eO=(0,p.createContext)(ek),eC=(0,p.createContext)(e_);function eR(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eA}}}function eD(e,t){switch(t.type){case a.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case a.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case a.DragEnd:case a.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case a.RegisterDroppable:{let{element:r}=t,{id:n}=r,a=new eA(e.droppable.containers);return a.set(n,r),{...e,droppable:{...e.droppable,containers:a}}}case a.SetDroppableDisabled:{let{id:r,key:n,disabled:a}=t,i=e.droppable.containers.get(r);if(!i||n!==i.key)return e;let s=new eA(e.droppable.containers);return s.set(r,{...i,disabled:a}),{...e,droppable:{...e.droppable,containers:s}}}case a.UnregisterDroppable:{let{id:r,key:n}=t,a=e.droppable.containers.get(r);if(!a||n!==a.key)return e;let i=new eA(e.droppable.containers);return i.delete(r),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function eN(e){let{disabled:t}=e,{active:r,activatorEvent:n,draggableNodes:a}=(0,p.useContext)(eO),i=(0,v.ZC)(n),s=(0,v.ZC)(null==r?void 0:r.id);return(0,p.useEffect)(()=>{if(!t&&!n&&i&&null!=s){if(!(0,v.kx)(i)||document.activeElement===i.target)return;let e=a.get(s);if(!e)return;let{activatorNode:t,node:r}=e;(t.current||r.current)&&requestAnimationFrame(()=>{for(let e of[t.current,r.current]){if(!e)continue;let t=(0,v.ag)(e);if(t){t.focus();break}}})}},[n,t,a,s,i]),null}function eI(e,t){let{transform:r,...n}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...n}),r):r}let eP=(0,p.createContext)({..._,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(d||(d={}));let eM=(0,p.memo)(function(e){var t,r,n,s,o,l;let{id:u,accessibility:b,autoScroll:w=!0,children:T,sensors:E=eE,collisionDetection:S=O,measuring:A,modifiers:k,...D}=e,[N,P]=(0,p.useReducer)(eD,void 0,eR),[U,B]=function(){let[e]=(0,p.useState)(()=>new Set),t=(0,p.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,p.useCallback)(t=>{let{type:r,event:n}=t;e.forEach(e=>{var t;return null==(t=e[r])?void 0:t.call(e,n)})},[e]),t]}(),[V,K]=(0,p.useState)(d.Uninitialized),X=V===d.Initialized,{draggable:{active:J,nodes:q,translate:Z},droppable:{containers:Q}}=N,ee=null!=J?q.get(J):null,et=(0,p.useRef)({initial:null,translated:null}),er=(0,p.useMemo)(()=>{var e;return null!=J?{id:J,data:null!=(e=null==ee?void 0:ee.data)?e:eS,rect:et}:null},[J,ee]),en=(0,p.useRef)(null),[ea,ei]=(0,p.useState)(null),[es,eo]=(0,p.useState)(null),el=(0,v.YN)(D,Object.values(D)),ec=(0,v.YG)("DndDescribedBy",u),ef=(0,p.useMemo)(()=>Q.getEnabled(),[Q]),em=(0,p.useMemo)(()=>({draggable:{...ex.draggable,...null==A?void 0:A.draggable},droppable:{...ex.droppable,...null==A?void 0:A.droppable},dragOverlay:{...ex.dragOverlay,...null==A?void 0:A.dragOverlay}}),[null==A?void 0:A.draggable,null==A?void 0:A.droppable,null==A?void 0:A.dragOverlay]),{droppableRects:eA,measureDroppableContainers:e_,measuringScheduled:ek}=function(e,t){let{dragging:r,dependencies:n,config:a}=t,[i,s]=(0,p.useState)(null),{frequency:o,measure:l,strategy:c}=a,f=(0,p.useRef)(e),u=function(){switch(c){case h.Always:return!1;case h.BeforeDragging:return r;default:return!r}}(),d=(0,v.YN)(u),m=(0,p.useCallback)(function(e){void 0===e&&(e=[]),d.current||s(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[d]),g=(0,p.useRef)(null),b=(0,v.KG)(t=>{if(u&&!r)return eu;if(!t||t===eu||f.current!==e||null!=i){let t=new Map;for(let r of e){if(!r)continue;if(i&&i.length>0&&!i.includes(r.id)&&r.rect.current){t.set(r.id,r.rect.current);continue}let e=r.node.current,n=e?new Y(l(e),e):null;r.rect.current=n,n&&t.set(r.id,n)}return t}return t},[e,i,r,u,l]);return(0,p.useEffect)(()=>{f.current=e},[e]),(0,p.useEffect)(()=>{u||m()},[r,u]),(0,p.useEffect)(()=>{i&&i.length>0&&s(null)},[JSON.stringify(i)]),(0,p.useEffect)(()=>{u||"number"!=typeof o||null!==g.current||(g.current=setTimeout(()=>{m(),g.current=null},o))},[o,u,m,...n]),{droppableRects:b,measureDroppableContainers:m,measuringScheduled:null!=i}}(ef,{dragging:X,dependencies:[Z.x,Z.y],config:em.droppable}),eM=function(e,t){let r=null!=t?e.get(t):void 0,n=r?r.node.current:null;return(0,v.KG)(e=>{var r;return null==t?null:null!=(r=null!=n?n:e)?r:null},[n,t])}(q,J),eL=(0,p.useMemo)(()=>es?(0,v.e_)(es):null,[es]),eF=function(){let e=(null==ea?void 0:ea.autoScrollEnabled)===!1,t="object"==typeof w?!1===w.enabled:!1===w,r=X&&!e&&!t;return"object"==typeof w?{...w,enabled:r}:{enabled:r}}(),eU=ed(eM,em.draggable.measure);!function(e){let{activeNode:t,measure:r,initialRect:n,config:a=!0}=e,i=(0,p.useRef)(!1),{x:s,y:o}="boolean"==typeof a?{x:a,y:a}:a;(0,v.Es)(()=>{if(!s&&!o||!t){i.current=!1;return}if(i.current||!n)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let a=C(r(e),n);if(s||(a.x=0),o||(a.y=0),i.current=!0,Math.abs(a.x)>0||Math.abs(a.y)>0){let t=L(e);t&&t.scrollBy({top:a.y,left:a.x})}},[t,s,o,n,r])}({activeNode:null!=J?q.get(J):null,config:eF.layoutShiftCompensation,initialRect:eU,measure:em.draggable.measure});let eB=eg(eM,em.draggable.measure,eU),eW=eg(eM?eM.parentElement:null),eH=(0,p.useRef)({activatorEvent:null,active:null,activeNode:eM,collisionRect:null,collisions:null,droppableRects:eA,draggableNodes:q,draggingNode:null,draggingNodeRect:null,droppableContainers:Q,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ej=Q.getNodeFor(null==(t=eH.current.over)?void 0:t.id),ez=function(e){let{measure:t}=e,[r,n]=(0,p.useState)(null),a=ep({callback:(0,p.useCallback)(e=>{for(let{target:r}of e)if((0,v.sb)(r)){n(e=>{let n=t(r);return e?{...e,width:n.width,height:n.height}:n});break}},[t])}),i=(0,p.useCallback)(e=>{let r=ey(e);null==a||a.disconnect(),r&&(null==a||a.observe(r)),n(r?t(r):null)},[t,a]),[s,o]=(0,v.lk)(i);return(0,p.useMemo)(()=>({nodeRef:s,rect:r,setRef:o}),[r,s,o])}({measure:em.dragOverlay.measure}),eG=null!=(r=ez.nodeRef.current)?r:eM,eV=X?null!=(n=ez.rect)?n:eB:null,eK=!!(ez.nodeRef.current&&ez.rect),eY=function(e){let t=ed(e);return C(e,t)}(eK?null:eB),eX=ew(eG?(0,v.zk)(eG):null),e$=function(e){let t=(0,p.useRef)(e),r=(0,v.KG)(r=>e?r&&r!==ev&&e&&t.current&&e.parentNode===t.current.parentNode?r:M(e):ev,[e]);return(0,p.useEffect)(()=>{t.current=e},[e]),r}(X?null!=ej?ej:eM:null),eJ=function(e,t){void 0===t&&(t=I);let[r]=e,n=ew(r?(0,v.zk)(r):null),[a,i]=(0,p.useState)(eT);function s(){i(()=>e.length?e.map(e=>H(e)?n:new Y(t(e),e)):eT)}let o=ep({callback:s});return(0,v.Es)(()=>{null==o||o.disconnect(),s(),e.forEach(e=>null==o?void 0:o.observe(e))},[e]),a}(e$),eq=eI(k,{transform:{x:Z.x-eY.x,y:Z.y-eY.y,scaleX:1,scaleY:1},activatorEvent:es,active:er,activeNodeRect:eB,containerNodeRect:eW,draggingNodeRect:eV,over:eH.current.over,overlayNodeRect:ez.rect,scrollableAncestors:e$,scrollableAncestorRects:eJ,windowRect:eX}),eZ=eL?(0,v.WQ)(eL,Z):null,eQ=function(e){let[t,r]=(0,p.useState)(null),n=(0,p.useRef)(e),a=(0,p.useCallback)(e=>{let t=F(e.target);t&&r(e=>e?(e.set(t,W(t)),new Map(e)):null)},[]);return(0,p.useEffect)(()=>{let t=n.current;if(e!==t){i(t);let s=e.map(e=>{let t=F(e);return t?(t.addEventListener("scroll",a,{passive:!0}),[t,W(t)]):null}).filter(e=>null!=e);r(s.length?new Map(s):null),n.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=F(e);null==t||t.removeEventListener("scroll",a)})}},[a,e]),(0,p.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,v.WQ)(e,t),_):G(e):_,[e,t])}(e$),e1=eb(eQ),e0=eb(eQ,[eB]),e2=(0,v.WQ)(eq,e1),e4=eV?R(eV,eq):null,e3=er&&e4?S({active:er,collisionRect:e4,droppableRects:eA,droppableContainers:ef,pointerCoordinates:eZ}):null,e5=function(e,t){if(!e||0===e.length)return null;let[r]=e;return r.id}(e3,"id"),[e6,e8]=(0,p.useState)(null),e7=(o=eK?eq:(0,v.WQ)(eq,e0),l=null!=(s=null==e6?void 0:e6.rect)?s:null,{...o,scaleX:l&&eB?l.width/eB.width:1,scaleY:l&&eB?l.height/eB.height:1}),e9=(0,p.useRef)(null),te=(0,p.useCallback)((e,t)=>{let{sensor:r,options:n}=t;if(null==en.current)return;let i=q.get(en.current);if(!i)return;let s=e.nativeEvent,o=new r({active:en.current,activeNode:i,event:s,options:n,context:eH,onAbort(e){if(!q.get(e))return;let{onDragAbort:t}=el.current,r={id:e};null==t||t(r),U({type:"onDragAbort",event:r})},onPending(e,t,r,n){if(!q.get(e))return;let{onDragPending:a}=el.current,i={id:e,constraint:t,initialCoordinates:r,offset:n};null==a||a(i),U({type:"onDragPending",event:i})},onStart(e){let t=en.current;if(null==t)return;let r=q.get(t);if(!r)return;let{onDragStart:n}=el.current,i={activatorEvent:s,active:{id:t,data:r.data,rect:et}};(0,g.unstable_batchedUpdates)(()=>{null==n||n(i),K(d.Initializing),P({type:a.DragStart,initialCoordinates:e,active:t}),U({type:"onDragStart",event:i}),ei(e9.current),eo(s)})},onMove(e){P({type:a.DragMove,coordinates:e})},onEnd:l(a.DragEnd),onCancel:l(a.DragCancel)});function l(e){return async function(){let{active:t,collisions:r,over:n,scrollAdjustedTranslate:i}=eH.current,o=null;if(t&&i){let{cancelDrop:l}=el.current;o={activatorEvent:s,active:t,collisions:r,delta:i,over:n},e===a.DragEnd&&"function"==typeof l&&await Promise.resolve(l(o))&&(e=a.DragCancel)}en.current=null,(0,g.unstable_batchedUpdates)(()=>{P({type:e}),K(d.Uninitialized),e8(null),ei(null),eo(null),e9.current=null;let t=e===a.DragEnd?"onDragEnd":"onDragCancel";if(o){let e=el.current[t];null==e||e(o),U({type:t,event:o})}})}}e9.current=o},[q]),tt=(0,p.useCallback)((e,t)=>(r,n)=>{let a=r.nativeEvent,i=q.get(n);null!==en.current||!i||a.dndKit||a.defaultPrevented||!0===e(r,t.options,{active:i})&&(a.dndKit={capturedBy:t.sensor},en.current=n,te(r,t))},[q,te]),tr=(0,p.useMemo)(()=>E.reduce((e,t)=>{let{sensor:r}=t;return[...e,...r.activators.map(e=>({eventName:e.eventName,handler:tt(e.handler,t)}))]},[]),[E,tt]);(0,p.useEffect)(()=>{if(!v.Sw)return;let e=E.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},E.map(e=>{let{sensor:t}=e;return t})),(0,v.Es)(()=>{eB&&V===d.Initializing&&K(d.Initialized)},[eB,V]),(0,p.useEffect)(()=>{let{onDragMove:e}=el.current,{active:t,activatorEvent:r,collisions:n,over:a}=eH.current;if(!t||!r)return;let i={active:t,activatorEvent:r,collisions:n,delta:{x:e2.x,y:e2.y},over:a};(0,g.unstable_batchedUpdates)(()=>{null==e||e(i),U({type:"onDragMove",event:i})})},[e2.x,e2.y]),(0,p.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:r,droppableContainers:n,scrollAdjustedTranslate:a}=eH.current;if(!e||null==en.current||!t||!a)return;let{onDragOver:i}=el.current,s=n.get(e5),o=s&&s.rect.current?{id:s.id,rect:s.rect.current,data:s.data,disabled:s.disabled}:null,l={active:e,activatorEvent:t,collisions:r,delta:{x:a.x,y:a.y},over:o};(0,g.unstable_batchedUpdates)(()=>{e8(o),null==i||i(l),U({type:"onDragOver",event:l})})},[e5]),(0,v.Es)(()=>{eH.current={activatorEvent:es,active:er,activeNode:eM,collisionRect:e4,collisions:e3,droppableRects:eA,draggableNodes:q,draggingNode:eG,draggingNodeRect:eV,droppableContainers:Q,over:e6,scrollableAncestors:e$,scrollAdjustedTranslate:e2},et.current={initial:eV,translated:e4}},[er,eM,e3,e4,q,eG,eV,eA,Q,e6,e$,e2]),function(e){let{acceleration:t,activator:r=c.Pointer,canScroll:n,draggingRect:a,enabled:s,interval:o=5,order:l=f.TreeOrder,pointerCoordinates:h,scrollableAncestors:u,scrollableAncestorRects:d,delta:m,threshold:g}=e,b=function(e){let{delta:t,disabled:r}=e,n=(0,v.ZC)(t);return(0,v.KG)(e=>{if(r||!n||!e)return eh;let a={x:Math.sign(t.x-n.x),y:Math.sign(t.y-n.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===a.x,[i.Forward]:e.x[i.Forward]||1===a.x},y:{[i.Backward]:e.y[i.Backward]||-1===a.y,[i.Forward]:e.y[i.Forward]||1===a.y}}},[r,t,n])}({delta:m,disabled:!s}),[w,T]=(0,v.$$)(),y=(0,p.useRef)({x:0,y:0}),E=(0,p.useRef)({x:0,y:0}),S=(0,p.useMemo)(()=>{switch(r){case c.Pointer:return h?{top:h.y,bottom:h.y,left:h.x,right:h.x}:null;case c.DraggableRect:return a}},[r,a,h]),x=(0,p.useRef)(null),A=(0,p.useCallback)(()=>{let e=x.current;if(!e)return;let t=y.current.x*E.current.x,r=y.current.y*E.current.y;e.scrollBy(t,r)},[]),_=(0,p.useMemo)(()=>l===f.TreeOrder?[...u].reverse():u,[l,u]);(0,p.useEffect)(()=>{if(!s||!u.length||!S)return void T();for(let e of _){if((null==n?void 0:n(e))===!1)continue;let r=d[u.indexOf(e)];if(!r)continue;let{direction:a,speed:s}=function(e,t,r,n,a){let{top:s,left:o,right:l,bottom:c}=r;void 0===n&&(n=10),void 0===a&&(a=z);let{isTop:f,isBottom:h,isLeft:u,isRight:d}=j(e),p={x:0,y:0},m={x:0,y:0},g={height:t.height*a.y,width:t.width*a.x};return!f&&s<=t.top+g.height?(p.y=i.Backward,m.y=n*Math.abs((t.top+g.height-s)/g.height)):!h&&c>=t.bottom-g.height&&(p.y=i.Forward,m.y=n*Math.abs((t.bottom-g.height-c)/g.height)),!d&&l>=t.right-g.width?(p.x=i.Forward,m.x=n*Math.abs((t.right-g.width-l)/g.width)):!u&&o<=t.left+g.width&&(p.x=i.Backward,m.x=n*Math.abs((t.left+g.width-o)/g.width)),{direction:p,speed:m}}(e,r,S,t,g);for(let e of["x","y"])b[e][a[e]]||(s[e]=0,a[e]=0);if(s.x>0||s.y>0){T(),x.current=e,w(A,o),y.current=s,E.current=a;return}}y.current={x:0,y:0},E.current={x:0,y:0},T()},[t,A,n,T,s,o,JSON.stringify(S),JSON.stringify(b),w,u,_,d,JSON.stringify(g)])}({...eF,delta:Z,draggingRect:e4,pointerCoordinates:eZ,scrollableAncestors:e$,scrollableAncestorRects:eJ});let tn=(0,p.useMemo)(()=>({active:er,activeNode:eM,activeNodeRect:eB,activatorEvent:es,collisions:e3,containerNodeRect:eW,dragOverlay:ez,draggableNodes:q,droppableContainers:Q,droppableRects:eA,over:e6,measureDroppableContainers:e_,scrollableAncestors:e$,scrollableAncestorRects:eJ,measuringConfiguration:em,measuringScheduled:ek,windowRect:eX}),[er,eM,eB,es,e3,eW,ez,q,Q,eA,e6,e_,e$,eJ,em,ek,eX]),ta=(0,p.useMemo)(()=>({activatorEvent:es,activators:tr,active:er,activeNodeRect:eB,ariaDescribedById:{draggable:ec},dispatch:P,draggableNodes:q,over:e6,measureDroppableContainers:e_}),[es,tr,er,eB,P,ec,q,e6,e_]);return m().createElement(y.Provider,{value:B},m().createElement(eO.Provider,{value:ta},m().createElement(eC.Provider,{value:tn},m().createElement(eP.Provider,{value:e7},T)),m().createElement(eN,{disabled:(null==b?void 0:b.restoreFocus)===!1})),m().createElement(x,{...b,hiddenTextDescribedById:ec}))}),eL=(0,p.createContext)(null),eF="button";function eU(e){let{id:t,data:r,disabled:n=!1,attributes:a}=e,i=(0,v.YG)("Draggable"),{activators:s,activatorEvent:o,active:l,activeNodeRect:c,ariaDescribedById:f,draggableNodes:h,over:u}=(0,p.useContext)(eO),{role:d=eF,roleDescription:m="draggable",tabIndex:g=0}=null!=a?a:{},b=(null==l?void 0:l.id)===t,w=(0,p.useContext)(b?eP:eL),[T,y]=(0,v.lk)(),[E,S]=(0,v.lk)(),x=(0,p.useMemo)(()=>s.reduce((e,r)=>{let{eventName:n,handler:a}=r;return e[n]=e=>{a(e,t)},e},{}),[s,t]),A=(0,v.YN)(r);return(0,v.Es)(()=>(h.set(t,{id:t,key:i,node:T,activatorNode:E,data:A}),()=>{let e=h.get(t);e&&e.key===i&&h.delete(t)}),[h,t]),{active:l,activatorEvent:o,activeNodeRect:c,attributes:(0,p.useMemo)(()=>({role:d,tabIndex:g,"aria-disabled":n,"aria-pressed":!!b&&d===eF||void 0,"aria-roledescription":m,"aria-describedby":f.draggable}),[n,d,g,b,m,f.draggable]),isDragging:b,listeners:n?void 0:x,node:T,over:u,setNodeRef:y,setActivatorNodeRef:S,transform:w}}function eB(){return(0,p.useContext)(eC)}let eW={timeout:25};function eH(e){let{data:t,disabled:r=!1,id:n,resizeObserverConfig:i}=e,s=(0,v.YG)("Droppable"),{active:o,dispatch:l,over:c,measureDroppableContainers:f}=(0,p.useContext)(eO),h=(0,p.useRef)({disabled:r}),u=(0,p.useRef)(!1),d=(0,p.useRef)(null),m=(0,p.useRef)(null),{disabled:g,updateMeasurementsFor:b,timeout:w}={...eW,...i},T=(0,v.YN)(null!=b?b:n),y=ep({callback:(0,p.useCallback)(()=>{if(!u.current){u.current=!0;return}null!=m.current&&clearTimeout(m.current),m.current=setTimeout(()=>{f(Array.isArray(T.current)?T.current:[T.current]),m.current=null},w)},[w]),disabled:g||!o}),E=(0,p.useCallback)((e,t)=>{y&&(t&&(y.unobserve(t),u.current=!1),e&&y.observe(e))},[y]),[S,x]=(0,v.lk)(E),A=(0,v.YN)(t);return(0,p.useEffect)(()=>{y&&S.current&&(y.disconnect(),u.current=!1,y.observe(S.current))},[S,y]),(0,p.useEffect)(()=>(l({type:a.RegisterDroppable,element:{id:n,key:s,disabled:r,node:S,rect:d,data:A}}),()=>l({type:a.UnregisterDroppable,key:s,id:n})),[n]),(0,p.useEffect)(()=>{r!==h.current.disabled&&(l({type:a.SetDroppableDisabled,id:n,key:s,disabled:r}),h.current.disabled=r)},[n,s,r,l]),{active:o,rect:d,isOver:(null==c?void 0:c.id)===n,node:S,over:c,setNodeRef:x}}function ej(e){let{animation:t,children:r}=e,[n,a]=(0,p.useState)(null),[i,s]=(0,p.useState)(null),o=(0,v.ZC)(r);return r||n||!o||a(o),(0,v.Es)(()=>{if(!i)return;let e=null==n?void 0:n.key,r=null==n?void 0:n.props.id;if(null==e||null==r)return void a(null);Promise.resolve(t(r,i)).then(()=>{a(null)})},[t,n,i]),m().createElement(m().Fragment,null,r,n?(0,p.cloneElement)(n,{ref:s}):null)}let ez={x:0,y:0,scaleX:1,scaleY:1};function eG(e){let{children:t}=e;return m().createElement(eO.Provider,{value:ek},m().createElement(eP.Provider,{value:ez},t))}let eV={position:"fixed",touchAction:"none"},eK=e=>(0,v.kx)(e)?"transform 250ms ease":void 0,eY=(0,p.forwardRef)((e,t)=>{let{as:r,activatorEvent:n,adjustScale:a,children:i,className:s,rect:o,style:l,transform:c,transition:f=eK}=e;if(!o)return null;let h=a?c:{...c,scaleX:1,scaleY:1},u={...eV,width:o.width,height:o.height,top:o.top,left:o.left,transform:v.Ks.Transform.toString(h),transformOrigin:a&&n?function(e,t){let r=(0,v.e_)(e);if(!r)return"0 0";let n={x:(r.x-t.left)/t.width*100,y:(r.y-t.top)/t.height*100};return n.x+"% "+n.y+"%"}(n,o):void 0,transition:"function"==typeof f?f(n):f,...l};return m().createElement(r,{className:s,style:u,ref:t},i)}),eX={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:r}}=e;return[{transform:v.Ks.Transform.toString(t)},{transform:v.Ks.Transform.toString(r)}]},sideEffects:(n={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:r}=e,a={},{styles:i,className:s}=n;if(null!=i&&i.active)for(let[e,r]of Object.entries(i.active))void 0!==r&&(a[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,r));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&r.node.style.setProperty(e,t);return null!=s&&s.active&&t.node.classList.add(s.active),null!=s&&s.dragOverlay&&r.node.classList.add(s.dragOverlay),function(){for(let[e,r]of Object.entries(a))t.node.style.setProperty(e,r);null!=s&&s.active&&t.node.classList.remove(s.active)}})},e$=0,eJ=m().memo(e=>{let{adjustScale:t=!1,children:r,dropAnimation:n,style:a,transition:i,modifiers:s,wrapperElement:o="div",className:l,zIndex:c=999}=e,{activatorEvent:f,active:h,activeNodeRect:u,containerNodeRect:d,draggableNodes:g,droppableContainers:b,dragOverlay:w,over:T,measuringConfiguration:y,scrollableAncestors:E,scrollableAncestorRects:S,windowRect:x}=eB(),A=(0,p.useContext)(eP),_=function(e){return(0,p.useMemo)(()=>{if(null!=e)return++e$},[e])}(null==h?void 0:h.id),k=eI(s,{activatorEvent:f,active:h,activeNodeRect:u,containerNodeRect:d,draggingNodeRect:w.rect,over:T,overlayNodeRect:w.rect,scrollableAncestors:E,scrollableAncestorRects:S,transform:A,windowRect:x}),O=ed(u),C=function(e){let{config:t,draggableNodes:r,droppableContainers:n,measuringConfiguration:a}=e;return(0,v._q)((e,i)=>{if(null===t)return;let s=r.get(e);if(!s)return;let o=s.node.current;if(!o)return;let l=ey(i);if(!l)return;let{transform:c}=(0,v.zk)(i).getComputedStyle(i),f=D(c);if(!f)return;let h="function"==typeof t?t:function(e){let{duration:t,easing:r,sideEffects:n,keyframes:a}={...eX,...e};return e=>{let{active:i,dragOverlay:s,transform:o,...l}=e;if(!t)return;let c={x:s.rect.left-i.rect.left,y:s.rect.top-i.rect.top},f={scaleX:1!==o.scaleX?i.rect.width*o.scaleX/s.rect.width:1,scaleY:1!==o.scaleY?i.rect.height*o.scaleY/s.rect.height:1},h={x:o.x-c.x,y:o.y-c.y,...f},u=a({...l,active:i,dragOverlay:s,transform:{initial:o,final:h}}),[d]=u,p=u[u.length-1];if(JSON.stringify(d)===JSON.stringify(p))return;let m=null==n?void 0:n({active:i,dragOverlay:s,...l}),g=s.node.animate(u,{duration:t,easing:r,fill:"forwards"});return new Promise(e=>{g.onfinish=()=>{null==m||m(),e()}})}}(t);return V(o,a.draggable.measure),h({active:{id:e,data:s.data,node:o,rect:a.draggable.measure(o)},draggableNodes:r,dragOverlay:{node:i,rect:a.dragOverlay.measure(l)},droppableContainers:n,measuringConfiguration:a,transform:f})})}({config:n,draggableNodes:g,droppableContainers:b,measuringConfiguration:y}),R=O?w.setRef:void 0;return m().createElement(eG,null,m().createElement(ej,{animation:C},h&&_?m().createElement(eY,{key:_,id:h.id,ref:R,as:o,activatorEvent:f,adjustScale:t,className:l,transition:i,rect:O,style:{zIndex:c,...a},transform:k},r):null))})},62478:(e,t,r)=>{r.d(t,{$$:()=>m,Es:()=>d,KG:()=>v,Ks:()=>k,Ll:()=>o,Re:()=>x,Sw:()=>i,TW:()=>u,WQ:()=>S,YG:()=>y,YN:()=>g,ZC:()=>w,_q:()=>p,ag:()=>C,e_:()=>_,jn:()=>a,kx:()=>A,l6:()=>s,lk:()=>b,sb:()=>f,wz:()=>c,xZ:()=>h,zk:()=>l});var n=r(43210);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function s(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function o(e){return"nodeType"in e}function l(e){var t,r;return e?s(e)?e:o(e)&&null!=(t=null==(r=e.ownerDocument)?void 0:r.defaultView)?t:window:window}function c(e){let{Document:t}=l(e);return e instanceof t}function f(e){return!s(e)&&e instanceof l(e).HTMLElement}function h(e){return e instanceof l(e).SVGElement}function u(e){return e?s(e)?e.document:o(e)?c(e)?e:f(e)||h(e)?e.ownerDocument:document:document:document}let d=i?n.useLayoutEffect:n.useEffect;function p(e){let t=(0,n.useRef)(e);return d(()=>{t.current=e}),(0,n.useCallback)(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return null==t.current?void 0:t.current(...r)},[])}function m(){let e=(0,n.useRef)(null);return[(0,n.useCallback)((t,r)=>{e.current=setInterval(t,r)},[]),(0,n.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function g(e,t){void 0===t&&(t=[e]);let r=(0,n.useRef)(e);return d(()=>{r.current!==e&&(r.current=e)},t),r}function v(e,t){let r=(0,n.useRef)();return(0,n.useMemo)(()=>{let t=e(r.current);return r.current=t,t},[...t])}function b(e){let t=p(e),r=(0,n.useRef)(null),a=(0,n.useCallback)(e=>{e!==r.current&&(null==t||t(e,r.current)),r.current=e},[]);return[r,a]}function w(e){let t=(0,n.useRef)();return(0,n.useEffect)(()=>{t.current=e},[e]),t.current}let T={};function y(e,t){return(0,n.useMemo)(()=>{if(t)return t;let r=null==T[e]?0:T[e]+1;return T[e]=r,e+"-"+r},[e,t])}function E(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>{for(let[n,a]of Object.entries(r)){let r=t[n];null!=r&&(t[n]=r+e*a)}return t},{...t})}}let S=E(1),x=E(-1);function A(e){if(!e)return!1;let{KeyboardEvent:t}=l(e.target);return t&&e instanceof t}function _(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=l(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:r}=e.touches[0];return{x:t,y:r}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:r}=e.changedTouches[0];return{x:t,y:r}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let k=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:r}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(r?Math.round(r):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:r}=e;return"scaleX("+t+") scaleY("+r+")"}},Transform:{toString(e){if(e)return[k.Translate.toString(e),k.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:r,easing:n}=e;return t+" "+r+"ms "+n}}}),O="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function C(e){return e.matches(O)?e:e.querySelector(O)}},65456:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},71273:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},72322:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},76288:(e,t,r)=>{r.d(t,{c:()=>i});var n=r(89106),a=r(47138);function i(e,t){let r=(0,a.a)(e),i=(0,a.a)(t),o=s(r,i),l=Math.abs((0,n.m)(r,i));r.setDate(r.getDate()-o*l);let c=Number(s(r,i)===-o),f=o*(l-c);return 0===f?0:f}function s(e,t){let r=e.getFullYear()-t.getFullYear()||e.getMonth()-t.getMonth()||e.getDate()-t.getDate()||e.getHours()-t.getHours()||e.getMinutes()-t.getMinutes()||e.getSeconds()-t.getSeconds()||e.getMilliseconds()-t.getMilliseconds();return r<0?-1:r>0?1:r}},82592:(e,t,r)=>{r.d(t,{A:()=>a});var n=r(35780);function a(e){return(0,n.w)(e,Date.now())}},85168:(e,t,r)=>{r.d(t,{d:()=>R});var n=r(43210),a=r.n(n),i=r(5231),s=r.n(i),o=r(10521),l=r(22989),c=r(54186),f=r(30087),h=r(35261),u=r(34005),d=r(277),p=["x1","y1","x2","y2","key"],m=["offset"];function g(e){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=g(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=g(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==g(t)?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function T(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}var y=function(e){var t=e.fill;if(!t||"none"===t)return null;var r=e.fillOpacity,n=e.x,i=e.y,s=e.width,o=e.height,l=e.ry;return a().createElement("rect",{x:n,y:i,ry:l,width:s,height:o,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function E(e,t){var r;if(a().isValidElement(e))r=a().cloneElement(e,t);else if(s()(e))r=e(t);else{var n=t.x1,i=t.y1,o=t.x2,l=t.y2,f=t.key,h=T(t,p),u=(0,c.J9)(h,!1),d=(u.offset,T(u,m));r=a().createElement("line",w({},d,{x1:n,y1:i,x2:o,y2:l,fill:"none",key:f}))}return r}function S(e){var t=e.x,r=e.width,n=e.horizontal,i=void 0===n||n,s=e.horizontalPoints;if(!i||!s||!s.length)return null;var o=s.map(function(n,a){return E(i,b(b({},e),{},{x1:t,y1:n,x2:t+r,y2:n,key:"line-".concat(a),index:a}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function x(e){var t=e.y,r=e.height,n=e.vertical,i=void 0===n||n,s=e.verticalPoints;if(!i||!s||!s.length)return null;var o=s.map(function(n,a){return E(i,b(b({},e),{},{x1:n,y1:t,x2:n,y2:t+r,key:"line-".concat(a),index:a}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function A(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,s=e.width,o=e.height,l=e.horizontalPoints,c=e.horizontal;if(!(void 0===c||c)||!t||!t.length)return null;var f=l.map(function(e){return Math.round(e+i-i)}).sort(function(e,t){return e-t});i!==f[0]&&f.unshift(0);var h=f.map(function(e,l){var c=f[l+1]?f[l+1]-e:i+o-e;if(c<=0)return null;var h=l%t.length;return a().createElement("rect",{key:"react-".concat(l),y:e,x:n,height:c,width:s,stroke:"none",fill:t[h],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},h)}function _(e){var t=e.vertical,r=e.verticalFill,n=e.fillOpacity,i=e.x,s=e.y,o=e.width,l=e.height,c=e.verticalPoints;if(!(void 0===t||t)||!r||!r.length)return null;var f=c.map(function(e){return Math.round(e+i-i)}).sort(function(e,t){return e-t});i!==f[0]&&f.unshift(0);var h=f.map(function(e,t){var c=f[t+1]?f[t+1]-e:i+o-e;if(c<=0)return null;var h=t%r.length;return a().createElement("rect",{key:"react-".concat(t),x:e,y:s,width:c,height:l,stroke:"none",fill:r[h],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},h)}var k=function(e,t){var r=e.xAxis,n=e.width,a=e.height,i=e.offset;return(0,f.PW)((0,h.f)(b(b(b({},u.u.defaultProps),r),{},{ticks:(0,f.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:a}})),i.left,i.left+i.width,t)},O=function(e,t){var r=e.yAxis,n=e.width,a=e.height,i=e.offset;return(0,f.PW)((0,h.f)(b(b(b({},u.u.defaultProps),r),{},{ticks:(0,f.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:a}})),i.top,i.top+i.height,t)},C={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function R(e){var t,r,n,i,c,f,h=(0,d.yi)(),u=(0,d.rY)(),p=(0,d.hj)(),m=b(b({},e),{},{stroke:null!=(t=e.stroke)?t:C.stroke,fill:null!=(r=e.fill)?r:C.fill,horizontal:null!=(n=e.horizontal)?n:C.horizontal,horizontalFill:null!=(i=e.horizontalFill)?i:C.horizontalFill,vertical:null!=(c=e.vertical)?c:C.vertical,verticalFill:null!=(f=e.verticalFill)?f:C.verticalFill,x:(0,l.Et)(e.x)?e.x:p.left,y:(0,l.Et)(e.y)?e.y:p.top,width:(0,l.Et)(e.width)?e.width:p.width,height:(0,l.Et)(e.height)?e.height:p.height}),v=m.x,T=m.y,E=m.width,R=m.height,D=m.syncWithTicks,N=m.horizontalValues,I=m.verticalValues,P=(0,d.pj)(),M=(0,d.$G)();if(!(0,l.Et)(E)||E<=0||!(0,l.Et)(R)||R<=0||!(0,l.Et)(v)||v!==+v||!(0,l.Et)(T)||T!==+T)return null;var L=m.verticalCoordinatesGenerator||k,F=m.horizontalCoordinatesGenerator||O,U=m.horizontalPoints,B=m.verticalPoints;if((!U||!U.length)&&s()(F)){var W=N&&N.length,H=F({yAxis:M?b(b({},M),{},{ticks:W?N:M.ticks}):void 0,width:h,height:u,offset:p},!!W||D);(0,o.R)(Array.isArray(H),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(g(H),"]")),Array.isArray(H)&&(U=H)}if((!B||!B.length)&&s()(L)){var j=I&&I.length,z=L({xAxis:P?b(b({},P),{},{ticks:j?I:P.ticks}):void 0,width:h,height:u,offset:p},!!j||D);(0,o.R)(Array.isArray(z),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(g(z),"]")),Array.isArray(z)&&(B=z)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(y,{fill:m.fill,fillOpacity:m.fillOpacity,x:m.x,y:m.y,width:m.width,height:m.height,ry:m.ry}),a().createElement(S,w({},m,{offset:p,horizontalPoints:U,xAxis:P,yAxis:M})),a().createElement(x,w({},m,{offset:p,verticalPoints:B,xAxis:P,yAxis:M})),a().createElement(A,w({},m,{horizontalPoints:U})),a().createElement(_,w({},m,{verticalPoints:B})))}R.displayName="CartesianGrid"},86940:(e,t,r)=>{r.d(t,{t:()=>c});var n=r(92491),a=r(28274),i=r(27747),s=r(9920),o=r(14769),l=r(84629),c=(0,n.gu)({chartName:"ScatterChart",GraphicalChild:a.X,defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],axisComponents:[{axisType:"xAxis",AxisComp:i.W},{axisType:"yAxis",AxisComp:s.h},{axisType:"zAxis",AxisComp:o.K}],formatAxisMap:l.pr})},88514:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},90357:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},90586:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},91551:(e,t,r)=>{r.d(t,{Q:()=>a});var n=r(47138);function a(e){let t=(0,n.a)(e),r=t.getFullYear();return t.setFullYear(r+1,0,0),t.setHours(23,59,59,999),t}},92477:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},92865:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("SquareCheckBig",[["path",{d:"M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5",key:"1uzm8b"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},93425:(e,t,r)=>{r.d(t,{E:()=>m});var n=r(43210),a=r(33465),i=r(5563),s=r(35536),o=r(31212);function l(e,t){let r=new Set(t);return e.filter(e=>!r.has(e))}var c=class extends s.Q{#e;#t;#r;#n;#a;#i;#s;#o;#l=[];constructor(e,t,r){super(),this.#e=e,this.#n=r,this.#r=[],this.#a=[],this.#t=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#a.forEach(e=>{e.subscribe(t=>{this.#c(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#a.forEach(e=>{e.destroy()})}setQueries(e,t){this.#r=e,this.#n=t,a.jG.batch(()=>{let e=this.#a,t=this.#f(this.#r);this.#l=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let r=t.map(e=>e.observer),n=r.map(e=>e.getCurrentResult()),a=r.some((t,r)=>t!==e[r]);(e.length!==r.length||a)&&(this.#a=r,this.#t=n,this.hasListeners()&&(l(e,r).forEach(e=>{e.destroy()}),l(r,e).forEach(e=>{e.subscribe(t=>{this.#c(e,t)})}),this.#h()))})}getCurrentResult(){return this.#t}getQueries(){return this.#a.map(e=>e.getCurrentQuery())}getObservers(){return this.#a}getOptimisticResult(e,t){let r=this.#f(e),n=r.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[n,e=>this.#u(e??n,t),()=>this.#d(n,r)]}#d(e,t){return t.map((r,n)=>{let a=e[n];return r.defaultedQueryOptions.notifyOnChangeProps?a:r.observer.trackResult(a,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#u(e,t){return t?(this.#i&&this.#t===this.#o&&t===this.#s||(this.#s=t,this.#o=this.#t,this.#i=(0,o.BH)(this.#i,t(e))),this.#i):e}#f(e){let t=new Map(this.#a.map(e=>[e.options.queryHash,e])),r=[];return e.forEach(e=>{let n=this.#e.defaultQueryOptions(e),a=t.get(n.queryHash);a?r.push({defaultedQueryOptions:n,observer:a}):r.push({defaultedQueryOptions:n,observer:new i.$(this.#e,n)})}),r}#c(e,t){let r=this.#a.indexOf(e);-1!==r&&(this.#t=function(e,t,r){let n=e.slice(0);return n[t]=r,n}(this.#t,r,t),this.#h())}#h(){if(this.hasListeners()){let e=this.#i,t=this.#d(this.#t,this.#l);e!==this.#u(t,this.#n?.combine)&&a.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#t)})})}}},f=r(8693),h=r(24903),u=r(18228),d=r(16142),p=r(76935);function m({queries:e,...t},r){let s=(0,f.jE)(r),l=(0,h.w)(),m=(0,u.h)(),g=n.useMemo(()=>e.map(e=>{let t=s.defaultQueryOptions(e);return t._optimisticResults=l?"isRestoring":"optimistic",t}),[e,s,l]);g.forEach(e=>{(0,p.jv)(e),(0,d.LJ)(e,m)}),(0,d.wZ)(m);let[v]=n.useState(()=>new c(s,g,t)),[b,w,T]=v.getOptimisticResult(g,t.combine),y=!l&&!1!==t.subscribed;n.useSyncExternalStore(n.useCallback(e=>y?v.subscribe(a.jG.batchCalls(e)):o.lQ,[v,y]),()=>v.getCurrentResult(),()=>v.getCurrentResult()),n.useEffect(()=>{v.setQueries(g,t)},[g,t,v]);let E=b.some((e,t)=>(0,p.EU)(g[t],e))?b.flatMap((e,t)=>{let r=g[t];if(r){let t=new i.$(s,r);if((0,p.EU)(r,e))return(0,p.iL)(r,t,m);(0,p.nE)(e,l)&&(0,p.iL)(r,t,m)}return[]}):[];if(E.length>0)throw Promise.all(E);let S=b.find((e,t)=>{let r=g[t];return r&&(0,d.$1)({result:e,errorResetBoundary:m,throwOnError:r.throwOnError,query:s.getQueryCache().get(r.queryHash),suspense:r.suspense})});if(S?.error)throw S.error;return w(T())}},98192:(e,t,r)=>{r.d(t,{a:()=>a});var n=r(96987);function a(e,t){return(0,n.P)(e,-t)}}};