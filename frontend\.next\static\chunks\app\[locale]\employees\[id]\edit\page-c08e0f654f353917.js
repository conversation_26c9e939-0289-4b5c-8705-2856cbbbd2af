(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8400],{1287:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var i=r(95155),a=r(41784),n=r(83343);let o=(0,r(40157).A)("UserCog",[["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m21.7 16.4-.9-.3",key:"12j9ji"}],["path",{d:"m15.2 13.9-.9-.3",key:"1fdjdi"}],["path",{d:"m16.6 18.7.3-.9",key:"heedtr"}],["path",{d:"m19.1 12.2.3-.9",key:"1af3ki"}],["path",{d:"m19.6 18.7-.4-1",key:"1x9vze"}],["path",{d:"m16.8 12.3-.4-1",key:"vqeiwj"}],["path",{d:"m14.3 16.6 1-.4",key:"1qlj63"}],["path",{d:"m20.7 13.8 1-.4",key:"1v5t8k"}]]);var c=r(35695),s=r(12115),l=r(44744),d=r(95647),u=r(68856),p=r(83940),h=r(83761);let m=e=>{switch(e){case"Active":return"Active";case"Inactive":default:return"Inactive";case"On_Leave":return"On_Leave";case"Terminated":return"Terminated"}},g=e=>{switch(e){case"Active":return{status:"Active"};case"Inactive":default:return{status:"Inactive"};case"On_Leave":return{status:"On_Leave"};case"Terminated":return{status:"Terminated"}}};function y(){let e=(0,c.useRouter)(),t=(0,c.useParams)(),r=null==t?void 0:t.id,{data:y,error:f,isLoading:v}=(0,h.uC)(r),E=(0,h.Db)();(0,s.useEffect)(()=>{f&&(console.error("Failed to fetch employee:",f),p.Ok.error("Error Loading Employee",(null==f?void 0:f.message)||"Failed to load employee data."),e.push("/employees"))},[f,e]);let A=async t=>{var r,i,a,n,o,c,s,l,d,u,h,m,f,v,A;if(!y)return void p.Ok.error("Error","Employee data not available for update.");let D=g(t.status),S=Object.fromEntries(Object.entries({availability:null!=(r=t.availability)?r:null,contactInfo:t.contactInfo,contactMobile:null!=(i=t.contactMobile)?i:null,contactPhone:null!=(a=t.contactPhone)?a:null,currentLocation:null!=(n=t.currentLocation)?n:null,department:null!=(o=t.department)?o:null,employeeId:t.employeeId,fullName:null!=(c=t.fullName)?c:null,generalAssignments:null!=(s=t.generalAssignments)?s:[],hireDate:null!=(l=t.hireDate)?l:null,name:t.name,notes:null!=(d=t.notes)?d:null,position:null!=(u=t.position)?u:null,profileImageUrl:null!=(h=t.profileImageUrl)?h:null,role:t.role,shiftSchedule:null!=(m=t.shiftSchedule)?m:null,skills:null!=(f=t.skills)?f:[],status:D.status}).filter(e=>{let[,t]=e;return void 0!==t}));try{let t={name:(await E.mutateAsync({data:S,id:y.id.toString()})).name};p.Ok.entityUpdated(t),e.push("/employees/".concat(y.employeeId))}catch(t){console.error("Failed to update employee:",t);let e=(null==(A=t.response)||null==(v=A.data)?void 0:v.error)||t.message||"An unexpected error occurred.";p.Ok.entityUpdateError(e)}},D=(0,s.useMemo)(()=>{if(y){let e=m(y.status);return y.contactPhone,y.contactMobile,{availability:y.availability||null,contactEmail:y.contactEmail||"",contactInfo:y.contactInfo||"",contactMobile:y.contactMobile||"",contactPhone:y.contactPhone||"",currentLocation:y.currentLocation||"",department:y.department||"",employeeId:y.employeeId,fullName:y.fullName||y.name||"",generalAssignments:y.generalAssignments||[],hireDate:y.hireDate?(0,a.GP)((0,n.H)(y.hireDate),"yyyy-MM-dd"):"",name:y.name||"",notes:y.notes||"",position:y.position||"",profileImageUrl:y.profileImageUrl||"",role:y.role||"other",shiftSchedule:y.shiftSchedule||"",skills:y.skills||[],status:e}}},[y]);return v||r&&!y&&!f?(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)(d.z,{icon:o,title:"Loading Employee Data..."}),(0,i.jsx)(u.E,{className:"h-[700px] w-full rounded-lg bg-card"})]}):y&&D?(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)(d.z,{description:"Modify the details for this employee.",icon:o,title:"Edit Employee: ".concat(y.name)}),(0,i.jsx)(l.N,{initialData:D,isEditing:!0,isLoading:E.isPending,onSubmit:A})]}):(0,i.jsx)("p",{children:"Employee not found or data could not be prepared."})}},40879:(e,t,r)=>{"use strict";r.d(t,{dj:()=>p,oR:()=>u});var i=r(12115);let a=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),d({toastId:e,type:"REMOVE_TOAST"})},1e6);n.set(e,t)},c=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:r}=t;if(r)o(r);else for(let t of e.toasts)o(t.id);return{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)}}},s=[],l={toasts:[]};function d(e){for(let t of(l=c(l,e),s))t(l)}function u(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),i=()=>d({toastId:r,type:"DISMISS_TOAST"});return d({toast:{...t,id:r,onOpenChange:e=>{e||i()},open:!0},type:"ADD_TOAST"}),{dismiss:i,id:r,update:e=>d({toast:{...e,id:r},type:"UPDATE_TOAST"})}}function p(){let[e,t]=i.useState(l);return i.useEffect(()=>(s.push(t),()=>{let e=s.indexOf(t);-1!==e&&s.splice(e,1)}),[e]),{...e,dismiss:e=>d({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:u}}},43840:(e,t,r)=>{"use strict";r.d(t,{cl:()=>i.cl,delegationApiService:()=>i.ac,employeeApiService:()=>i.aV,reliabilityApiService:()=>i.e_,taskApiService:()=>i.Hg,vehicleApiService:()=>i.oL});var i=r(36973);r(72248)},68856:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var i=r(95155),a=r(54036);function n(e){let{className:t,...r}=e;return(0,i.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",t),...r})}},74869:(e,t,r)=>{Promise.resolve().then(r.bind(r,1287))},83940:(e,t,r)=>{"use strict";r.d(t,{G7:()=>p,Gb:()=>s,JP:()=>l,Ok:()=>d,Qu:()=>u,iw:()=>c,oz:()=>m,z0:()=>h});var i=r(40879);class a{show(e){return(0,i.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class n extends a{entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}constructor(e){super(),this.config=e}}class o extends a{serviceRecordCreated(e,t){return this.success("Service Record Added","".concat(t,' service for "').concat(e,'" has been successfully logged.'))}serviceRecordUpdated(e,t){return this.success("Service Record Updated","".concat(t,' service for "').concat(e,'" has been updated.'))}serviceRecordDeleted(e,t){return this.success("Service Record Deleted","".concat(t,' service record for "').concat(e,'" has been permanently removed.'))}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function c(e){return new n(e)}function s(e,t){return new n({entityName:e,getDisplayName:t,messages:{created:{title:"".concat(e," Created"),description:t=>"The ".concat(e.toLowerCase(),' "').concat(t,'" has been successfully created.')},updated:{title:"".concat(e," Updated Successfully"),description:e=>"".concat(e," has been updated.")},deleted:{title:"".concat(e," Deleted Successfully"),description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create ".concat(e),description:t=>t||"An unexpected error occurred while creating the ".concat(e.toLowerCase(),".")},updateError:{title:"Update Failed",description:t=>t||"An unexpected error occurred while updating the ".concat(e.toLowerCase(),".")},deletionError:{title:"Failed to Delete ".concat(e),description:t=>t||"An unexpected error occurred while deleting the ".concat(e.toLowerCase(),".")}}})}let l=new a,d=new n({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>'The employee "'.concat(e,'" has been successfully created.')},updated:{title:"Employee Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Employee Deleted Successfully",description:e=>"".concat(e," has been permanently removed from the system.")},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new n({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>'The delegation "'.concat(e,'" has been successfully created.')},updated:{title:"Delegation Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Delegation Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),p=new n({entityName:"Vehicle",getDisplayName:e=>"".concat(e.make," ").concat(e.model),messages:{created:{title:"Vehicle Added",description:e=>'The vehicle "'.concat(e,'" has been successfully created.')},updated:{title:"Vehicle Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Vehicle Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),h=new n({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>'The task "'.concat(e,'" has been successfully created.')},updated:{title:"Task Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Task Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),m=new o}},e=>{var t=t=>e(e.s=t);e.O(0,[6476,7047,6897,3860,9664,375,7876,1859,5669,4629,4036,4767,303,7515,970,8441,1684,7358],()=>t(74869)),_N_E=e.O()}]);