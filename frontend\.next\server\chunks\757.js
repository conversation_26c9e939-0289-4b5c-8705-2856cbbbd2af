"use strict";exports.id=757,exports.ids=[757],exports.modules={80757:(t,e,a)=>{a.d(e,{aN:()=>d,adminService:()=>h});var i=a(77312),r=a(75176);let s={fromApi:t=>({action:t.action||"",details:t.details||"",id:t.id||"",timestamp:new Date(t.created_at||t.timestamp||new Date),userId:t.user_id||t.userId||t.auth_user_id||"",auth_user_id:t.auth_user_id||"",auth_user:t.auth_user||null}),toApi:t=>t};class n extends i.v{constructor(t,e){super(t,{cacheDuration:12e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...e}),this.endpoint="/admin/audit-logs",this.transformer=s}async getByAction(t,e){return(await this.getAll({...e,action:t})).data}async getByDateRange(t,e,a){let i={endDate:e.toISOString(),startDate:t.toISOString(),...a};return this.getAll(i)}async getByUserId(t,e){return(await this.getAll({...e,userId:t})).data}}let c={fromApi:t=>{let e=t.users?.[0]||{};return{created_at:t.created_at||"",email:e.email||t.email||"",email_confirmed_at:e.email_confirmed_at||t.email_confirmed_at||null,employee_id:t.employee_id||null,full_name:t.full_name||t.name||"",id:t.id,isActive:t.is_active??!0,last_sign_in_at:t.last_sign_in_at||null,phone:t.phone||null,phone_confirmed_at:t.phone_confirmed_at||null,role:t.role||"USER",updated_at:t.updated_at||"",users:t.users}},toApi:t=>t};class l extends i.v{constructor(t,e){super(t,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...e}),this.endpoint="/admin/users",this.transformer=c}async getAll(t={}){let e=new URLSearchParams(t).toString(),a=`${this.endpoint}?${e}`,i=`${this.endpoint}:getAll:${JSON.stringify(t)}`;return this.executeWithInfrastructure(i,async()=>{let e=await this.apiClient.get(a),i=Array.isArray(e.data)?e.data:e.data?.data||[],r=e.pagination||e.data?.pagination||{hasNext:!1,hasPrevious:!1,limit:t.limit||10,page:1,total:0,totalPages:0};return{data:i.map(t=>this.transformer.fromApi?this.transformer.fromApi(t):t),pagination:r}})}async getUsersByRole(t,e={}){return(await this.getAll({...e,role:t})).data}async toggleActivation(t,e){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.patch(`${this.endpoint}/${t}/toggle-activation`,{isActive:e});return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${t}`),a})}}let u={fromApi:t=>t,toApi:t=>t};class o extends i.v{get cacheUtils(){return{clearAll:()=>this.clearCache(),forceRefreshHealth:async()=>(this.cache.invalidate("admin:health"),this.getSystemHealthStatus()),forceRefreshPerformance:async()=>(this.cache.invalidate("admin:performance"),this.getPerformanceMetrics()),getStats:()=>this.cache.getStats(),invalidateAll:()=>this.cache.invalidatePattern(/^admin:/)}}constructor(t,e){let a=t||r.cl.getApiClient();super(a,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...e}),this.endpoint="/admin",this.transformer=u,this.auditService=new n(a),this.userService=new l(a)}async createAuditLog(t){return this.executeWithInfrastructure(null,async()=>{let e=await this.apiClient.post("/admin/audit-logs",t);return this.cache.invalidatePattern(/^admin:audit:/),e})}async createUser(t){return this.executeWithInfrastructure(null,async()=>{let e=await this.apiClient.post("/admin/users",t);return this.cache.invalidatePattern(/^admin:users:/),e})}async deleteUser(t){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete(`/admin/users/${t}`),this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate(`admin:user:${t}`)})}async getAllUsers(t={}){return this.userService.getAll(t)}async getAuditLogs(t={}){return this.auditService.getAll(t)}getMockHealthStatus(){return{services:{api:{responseTime:23,status:"healthy"},cache:{responseTime:12,status:"healthy"},database:{responseTime:45,status:"healthy"}},status:"healthy",timestamp:new Date().toISOString(),uptime:3600}}getMockPerformanceMetrics(){return{cpu:{cores:4,usage:100*Math.random()},errors:{rate:5*Math.random(),total:Math.floor(500*Math.random())},memory:{percentage:100*Math.random(),total:8e3,used:8e3*Math.random()},requests:{averageResponseTime:1e3*Math.random(),perSecond:100*Math.random(),total:Math.floor(1e4*Math.random())},timestamp:new Date().toISOString()}}getMockRecentErrors(){let t=[{details:{component:"database",errorType:"timeout"},id:"1",level:"ERROR",message:"Database connection timeout",requestId:"req-456",source:"database.service.ts",stack:"Error: Connection timeout\n    at Database.connect...",timestamp:new Date().toISOString(),userId:"user123"},{details:{component:"system",metric:"memory"},id:"2",level:"WARNING",message:"High memory usage detected",requestId:"req-789",source:"monitoring.service.ts",timestamp:new Date(Date.now()-3e5).toISOString()}];return{data:t,pagination:{hasNext:!1,hasPrevious:!1,limit:10,page:1,total:t.length,totalPages:Math.ceil(t.length/10)}}}async getPerformanceMetrics(){return this.executeWithInfrastructure("admin:performance",async()=>this.apiClient.get("/admin/performance"))}async getRecentErrors(t=1,e=10,a){let i=new URLSearchParams;i.append("page",t.toString()),i.append("limit",e.toString()),a&&i.append("level",a);let r=`/admin/logs/errors?${i.toString()}`,s=`admin:errors:${t}:${e}:${a||"all"}`;return this.executeWithInfrastructure(s,async()=>this.apiClient.get(r))}async getSystemHealthStatus(){return this.executeWithInfrastructure("admin:health",async()=>this.apiClient.get("/admin/health"))}async toggleUserActivation(t,e){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.patch(`/admin/users/${t}/toggle-activation`,{isActive:e});return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate(`admin:user:${t}`),a})}async updateUser(t,e){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.put(`/admin/users/${t}`,e);return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate(`admin:user:${t}`),a})}}let h=new o,d=h.cacheUtils}};