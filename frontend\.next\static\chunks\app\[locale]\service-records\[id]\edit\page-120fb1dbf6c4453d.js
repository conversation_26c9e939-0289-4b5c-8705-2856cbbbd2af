(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9677],{1190:(e,r,s)=>{"use strict";s.d(r,{fe:()=>d,mm:()=>l,oS:()=>n});var t=s(71153),i=s(21876);let a={date:t.Yj().min(1,"Date is required"),servicePerformed:t.YO(t.Yj().min(1,"Service cannot be empty")).min(1,"At least one service must be performed").max(10,"Maximum 10 services allowed"),odometer:t.au.number({required_error:"Odometer reading is required"}).min(0,"Odometer cannot be negative").max(9999999,"Odometer reading seems unrealistic"),cost:t.au.number({required_error:"Cost is required"}).min(0,"Cost cannot be negative").max(999999,"Cost seems unrealistic"),notes:t.Yj().max(1e3,"Notes cannot exceed 1000 characters").optional(),employeeId:t.au.number().nullable().optional()},l=t.Ik(a),n=t.Ik({...a,vehicleId:t.ai().positive("Vehicle ID is required")});t.Ik(a).partial();let d={validateFormData:e=>l.safeParse(e),transformToApiPayload:(e,r)=>{let s={date:e.date,servicePerformed:e.servicePerformed,odometer:e.odometer,vehicleId:r};return"number"==typeof e.cost&&e.cost>=0&&(s.cost=e.cost),e.notes&&e.notes.trim().length>0&&(s.notes=e.notes.trim()),void 0!==e.employeeId&&(s.employeeId=e.employeeId),s},transformToUpdatePayload:e=>{let r={};return e.date&&(r.date=(0,i.B7)(e.date,{primaryFormat:"US",fallbackToBothFormats:!0})),void 0!==e.servicePerformed&&(r.servicePerformed=e.servicePerformed),void 0!==e.odometer&&(r.odometer=e.odometer),"number"==typeof e.cost&&e.cost>0?r.cost=e.cost:0===e.cost&&(r.cost=void 0),void 0!==e.notes&&e.notes&&e.notes.trim().length>0&&(r.notes=e.notes.trim()),void 0!==e.employeeId&&(r.employeeId=e.employeeId),r},validateServicePerformed:e=>e.length>0&&e.length<=10&&e.every(e=>e.trim().length>0),formatServicePerformed:e=>e.join(", "),parseServicePerformed:e=>e.split(",").map(e=>e.trim()).filter(e=>e.length>0)}},17759:(e,r,s)=>{"use strict";s.d(r,{C5:()=>j,MJ:()=>f,Rr:()=>p,eI:()=>h,lR:()=>v,lV:()=>c,zB:()=>m});var t=s(95155),i=s(12115),a=s(99708),l=s(62177),n=s(54036),d=s(85057);let c=l.Op,o=i.createContext({}),m=e=>{let{...r}=e;return(0,t.jsx)(o.Provider,{value:{name:r.name},children:(0,t.jsx)(l.xI,{...r})})},u=()=>{let e=i.useContext(o),r=i.useContext(x),{getFieldState:s,formState:t}=(0,l.xW)(),a=s(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=r;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...a}},x=i.createContext({}),h=i.forwardRef((e,r)=>{let{className:s,...a}=e,l=i.useId();return(0,t.jsx)(x.Provider,{value:{id:l},children:(0,t.jsx)("div",{ref:r,className:(0,n.cn)("space-y-2",s),...a})})});h.displayName="FormItem";let v=i.forwardRef((e,r)=>{let{className:s,...i}=e,{error:a,formItemId:l}=u();return(0,t.jsx)(d.J,{ref:r,className:(0,n.cn)(a&&"text-destructive",s),htmlFor:l,...i})});v.displayName="FormLabel";let f=i.forwardRef((e,r)=>{let{...s}=e,{error:i,formItemId:l,formDescriptionId:n,formMessageId:d}=u();return(0,t.jsx)(a.DX,{ref:r,id:l,"aria-describedby":i?"".concat(n," ").concat(d):"".concat(n),"aria-invalid":!!i,...s})});f.displayName="FormControl";let p=i.forwardRef((e,r)=>{let{className:s,...i}=e,{formDescriptionId:a}=u();return(0,t.jsx)("p",{ref:r,id:a,className:(0,n.cn)("text-sm text-muted-foreground",s),...i})});p.displayName="FormDescription";let j=i.forwardRef((e,r)=>{var s;let{className:i,children:a,...l}=e,{error:d,formMessageId:c}=u(),o=d?String(null!=(s=null==d?void 0:d.message)?s:""):a;return o?(0,t.jsx)("p",{ref:r,id:c,className:(0,n.cn)("text-sm font-medium text-destructive",i),...l,children:o}):null});j.displayName="FormMessage"},41392:(e,r,s)=>{Promise.resolve().then(s.bind(s,91815))},59119:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(40157).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},62523:(e,r,s)=>{"use strict";s.d(r,{p:()=>l});var t=s(95155),i=s(12115),a=s(54036);let l=i.forwardRef((e,r)=>{let{className:s,type:i,...l}=e;return(0,t.jsx)("input",{className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:r,type:i,...l})});l.displayName="Input"},85057:(e,r,s)=>{"use strict";s.d(r,{J:()=>c});var t=s(95155),i=s(12115),a=s(40968),l=s(74466),n=s(54036);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef((e,r)=>{let{className:s,...i}=e;return(0,t.jsx)(a.b,{ref:r,className:(0,n.cn)(d(),s),...i})});c.displayName=a.b.displayName},88539:(e,r,s)=>{"use strict";s.d(r,{T:()=>l});var t=s(95155),i=s(12115),a=s(54036);let l=i.forwardRef((e,r)=>{let{className:s,...i}=e;return(0,t.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:r,...i})});l.displayName="Textarea"},91815:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>z});var t=s(95155),i=s(90221),a=s(12543),l=s(77223),n=s(31949),d=s(58127),c=s(59119),o=s(6874),m=s.n(o),u=s(35695),x=s(12115),h=s(62177),v=s(28328),f=s(68856),p=s(5484);function j(e){let{vehicleInfo:r}=e;return(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Vehicle:"}),(0,t.jsxs)("span",{className:"font-semibold text-foreground",children:[r.make," ",r.model," (",r.year,")"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium",children:"License Plate:"}),(0,t.jsx)("span",{className:"font-mono font-semibold text-foreground",children:r.licensePlate})]})]})}function g(){return(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Vehicle:"}),(0,t.jsx)(f.E,{className:"h-4 w-32"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium",children:"License Plate:"}),(0,t.jsx)(f.E,{className:"h-4 w-20"})]})]})}function N(){return(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Vehicle information unavailable"})]})}function b(e){let{vehicleId:r,className:s}=e,{vehicleInfo:i,isLoading:a,error:l}=(0,p.g)(r);return(0,t.jsxs)("div",{className:s,children:[a&&(0,t.jsx)(g,{}),l&&(0,t.jsx)(N,{}),i&&(0,t.jsx)(j,{vehicleInfo:i})]})}var y=s(55365),I=s(90010),w=s(30285),C=s(66695),P=s(17759),S=s(62523),R=s(22346),k=s(88539),E=s(40879),A=s(1190),F=s(98691);function z(){let e=(0,u.useParams)(),r=(0,u.useRouter)(),{toast:s}=(0,E.dj)(),o=(null==e?void 0:e.id)||"",v=(0,F.Ln)(),p=(0,F.xT)(),{data:j,error:g,isLoading:N}=(0,F.WV)(o,{enabled:!!o&&"string"==typeof(null==e?void 0:e.id)}),z=(0,h.mN)({defaultValues:{cost:0,date:"",employeeId:null,notes:"",odometer:0,servicePerformed:[]},mode:"onChange",resolver:(0,i.u)(A.mm)});if((0,x.useEffect)(()=>{if(j){var e,r;z.reset({cost:j.cost||0,date:null!=(e=j.date.split("T")[0])?e:"",employeeId:j.employeeId,notes:null!=(r=j.notes)?r:"",odometer:j.odometer||0,servicePerformed:j.servicePerformed||[]})}},[j,z]),!e||"string"!=typeof e.id)return(0,t.jsxs)("div",{className:"container mx-auto py-8 text-center text-red-500",children:[(0,t.jsx)("p",{children:"Error: Invalid or missing Service Record ID."}),(0,t.jsx)(w.$,{asChild:!0,className:"mt-4",children:(0,t.jsxs)(m(),{href:"/service-history",children:[(0,t.jsx)(a.A,{className:"size-4"})," Back to Service History"]})})]});let D=async e=>{if(!j)return void s({description:"Service record data is not available. Please refresh the page.",title:"Error",variant:"destructive"});if(!j.vehicleId){s({description:"Vehicle ID is missing. Cannot update service record.",title:"Error",variant:"destructive"}),console.error("Service record missing vehicleId:",j);return}let t=A.fe.transformToUpdatePayload(e);try{await v.mutateAsync({data:t,id:o,vehicleId:j.vehicleId}),s({description:"Service record updated successfully! \uD83C\uDF89",title:"Success!",variant:"default"}),r.push("/service-records/".concat(o))}catch(e){console.error("Failed to update service record:",e),s({description:"Failed to update service record. Please try again.",title:"Error",variant:"destructive"})}},V=async()=>{if(!j)return void s({description:"Service record data is not available.",title:"Error",variant:"destructive"});if(!j.vehicleId){s({description:"Vehicle ID is missing. Cannot delete service record.",title:"Error",variant:"destructive"}),console.error("Service record missing vehicleId:",j);return}try{await p.mutateAsync({id:o,vehicleId:j.vehicleId}),s({description:"Service record deleted successfully.",title:"Deleted!",variant:"default"}),r.push("/service-history")}catch(e){console.error("Failed to delete service record:",e),s({description:"Failed to delete service record. Please try again.",title:"Error",variant:"destructive"})}},B=e=>{let r=A.fe.parseServicePerformed(e);z.setValue("servicePerformed",r,{shouldValidate:!0})};return N?(0,t.jsx)("div",{className:"container mx-auto py-8",children:(0,t.jsxs)(C.Zp,{children:[(0,t.jsx)(C.aR,{children:(0,t.jsx)(f.E,{className:"h-8 w-3/4"})}),(0,t.jsxs)(C.Wu,{className:"space-y-4",children:[(0,t.jsx)(f.E,{className:"h-4 w-full"}),(0,t.jsx)(f.E,{className:"h-4 w-full"}),(0,t.jsx)(f.E,{className:"h-4 w-2/3"})]})]})}):g?(0,t.jsxs)("div",{className:"container mx-auto py-8 text-center text-red-500",children:[(0,t.jsxs)("p",{children:["Error loading service record: ",g.message]}),(0,t.jsx)(w.$,{asChild:!0,className:"mt-4",children:(0,t.jsxs)(m(),{href:"/service-history",children:[(0,t.jsx)(a.A,{className:"mr-2 size-4"})," Back to Service History"]})})]}):j?(0,t.jsxs)("div",{className:"container mx-auto space-y-6 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(w.$,{asChild:!0,size:"sm",variant:"outline",children:(0,t.jsxs)(m(),{href:"/service-records/".concat(j.id),children:[(0,t.jsx)(a.A,{className:"mr-2 size-4"})," Back to Details"]})}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsxs)(I.Lt,{children:[(0,t.jsx)(I.tv,{asChild:!0,children:(0,t.jsxs)(w.$,{disabled:p.isPending,size:"sm",variant:"destructive",children:[(0,t.jsx)(l.A,{className:"mr-2 size-4"}),"Delete"]})}),(0,t.jsxs)(I.EO,{children:[(0,t.jsxs)(I.wd,{children:[(0,t.jsxs)(I.r7,{className:"flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"size-5 text-destructive"}),"Delete Service Record"]}),(0,t.jsxs)(I.$v,{children:["Are you sure you want to delete this service record? This action cannot be undone.",(0,t.jsxs)("div",{className:"mt-2 rounded-md bg-muted p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Record Details:"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:[j.servicePerformed.join(", ")," -"," ",new Date(j.date).toLocaleDateString()]})]})]})]}),(0,t.jsxs)(I.ck,{children:[(0,t.jsx)(I.Zr,{children:"Cancel"}),(0,t.jsx)(I.Rx,{className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",onClick:V,children:"Delete Record"})]})]})]})})]}),z.formState.isValid&&z.formState.isDirty&&(0,t.jsxs)(y.Fc,{className:"border-green-200 bg-green-50",children:[(0,t.jsx)(d.A,{className:"size-4 text-green-600"}),(0,t.jsx)(y.TN,{className:"text-green-800",children:"All fields are valid. Ready to save changes."})]}),(0,t.jsxs)(C.Zp,{children:[(0,t.jsx)(C.aR,{children:(0,t.jsxs)(C.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"size-5"}),"Edit Service Record"]})}),(0,t.jsx)(C.Wu,{children:(0,t.jsx)(P.lV,{...z,children:(0,t.jsxs)("form",{className:"space-y-6",onSubmit:z.handleSubmit(D),children:[(0,t.jsx)("div",{className:"rounded-lg bg-muted/50 p-4",children:(0,t.jsx)(b,{vehicleId:j.vehicleId})}),(0,t.jsx)(R.w,{}),(0,t.jsx)(P.zB,{control:z.control,name:"date",render:e=>{let{field:r}=e;return(0,t.jsxs)(P.eI,{children:[(0,t.jsx)(P.lR,{children:"Service Date"}),(0,t.jsx)(P.MJ,{children:(0,t.jsx)(S.p,{type:"date",...r})}),(0,t.jsx)(P.C5,{})]})}}),(0,t.jsx)(P.zB,{control:z.control,name:"servicePerformed",render:e=>{var r;let{field:s}=e;return(0,t.jsxs)(P.eI,{children:[(0,t.jsx)(P.lR,{children:"Services Performed"}),(0,t.jsx)(P.MJ,{children:(0,t.jsx)(S.p,{onChange:e=>B(e.target.value),placeholder:"Oil change, brake inspection, tire rotation...",value:A.fe.formatServicePerformed(s.value||[])})}),(0,t.jsxs)(P.Rr,{children:["Enter services separated by commas."," ",(null==(r=s.value)?void 0:r.length)||0,"/10 services"]}),(0,t.jsx)(P.C5,{})]})}}),(0,t.jsx)(P.zB,{control:z.control,name:"odometer",render:e=>{let{field:r}=e;return(0,t.jsxs)(P.eI,{children:[(0,t.jsx)(P.lR,{children:"Odometer Reading"}),(0,t.jsx)(P.MJ,{children:(0,t.jsx)(S.p,{placeholder:"0",type:"number",...r,onChange:e=>r.onChange(Number(e.target.value)||0)})}),(0,t.jsx)(P.Rr,{children:"Current mileage on the vehicle"}),(0,t.jsx)(P.C5,{})]})}}),(0,t.jsx)(P.zB,{control:z.control,name:"cost",render:e=>{let{field:r}=e;return(0,t.jsxs)(P.eI,{children:[(0,t.jsx)(P.lR,{children:"Total Cost"}),(0,t.jsx)(P.MJ,{children:(0,t.jsx)(S.p,{placeholder:"0.00",step:"0.01",type:"number",...r,onChange:e=>r.onChange(Number(e.target.value)||0)})}),(0,t.jsx)(P.Rr,{children:"Total cost of the service in dollars"}),(0,t.jsx)(P.C5,{})]})}}),(0,t.jsx)(P.zB,{control:z.control,name:"notes",render:e=>{var r,s;let{field:i}=e;return(0,t.jsxs)(P.eI,{children:[(0,t.jsx)(P.lR,{children:"Notes (Optional)"}),(0,t.jsx)(P.MJ,{children:(0,t.jsx)(k.T,{className:"min-h-[100px]",placeholder:"Additional notes about the service...",...i})}),(0,t.jsxs)(P.Rr,{children:[null!=(s=null==(r=i.value)?void 0:r.length)?s:0,"/1000 characters"]}),(0,t.jsx)(P.C5,{})]})}}),(0,t.jsx)(R.w,{}),(0,t.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,t.jsx)(w.$,{asChild:!0,type:"button",variant:"outline",children:(0,t.jsx)(m(),{href:"/service-records/".concat(o),children:"Cancel"})}),(0,t.jsx)(w.$,{className:"min-w-[120px]",disabled:v.isPending||!z.formState.isValid,type:"submit",children:v.isPending?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"mr-2 size-4 animate-spin rounded-full border-2 border-background border-t-transparent"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"mr-2 size-4"}),"Save Changes"]})})]})]})})})]})]}):(0,t.jsxs)("div",{className:"container mx-auto py-8 text-center text-gray-500",children:[(0,t.jsx)("p",{children:"No service record data available for editing."}),(0,t.jsx)(w.$,{asChild:!0,className:"mt-4",children:(0,t.jsxs)(m(),{href:"/service-history",children:[(0,t.jsx)(a.A,{className:"mr-2 size-4"})," Back to Service History"]})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6476,7047,6897,3860,9664,375,6874,5669,4629,5106,4036,4767,303,1701,8441,1684,7358],()=>r(41392)),_N_E=e.O()}]);