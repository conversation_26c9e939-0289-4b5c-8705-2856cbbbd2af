"use strict";exports.id=3211,exports.ids=[3211],exports.modules={3940:(e,t,r)=>{r.d(t,{O_:()=>n,t6:()=>i});var s=r(43210),a=r(49278);function i(){let e=(0,s.useCallback)((e,t)=>a.JP.success(e,t),[]),t=(0,s.useCallback)((e,t)=>a.JP.error(e,t),[]),r=(0,s.useCallback)((e,t)=>a.JP.info(e,t),[]),i=(0,s.useCallback)(t=>e(t?.successTitle||"Success",t?.successDescription||"Operation completed successfully"),[e]),n=(0,s.useCallback)((e,r)=>{let s=e instanceof Error?e.message:e;return t(r?.errorTitle||"Error",r?.errorDescription||s||"An unexpected error occurred")},[t]);return{showSuccess:e,showError:t,showInfo:r,showFormSuccess:i,showFormError:n}}function n(e){let t;switch(e){case"employee":t=r(49278).Ok;break;case"vehicle":t=r(49278).G7;break;case"task":t=r(49278).z0;break;case"delegation":t=r(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,t){let{showFormSuccess:r,showFormError:n}=i(),o=t||(e?(0,a.iw)(e):null),l=(0,s.useCallback)(e=>o?o.entityCreated(e):r({successTitle:"Created",successDescription:"Item has been created successfully"}),[o,r]),d=(0,s.useCallback)(e=>o?o.entityUpdated(e):r({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[o,r]),c=(0,s.useCallback)(e=>o?o.entityDeleted(e):r({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[o,r]),u=(0,s.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityCreationError(t)}return n(e,{errorTitle:"Creation Failed"})},[o,n]);return{showEntityCreated:l,showEntityUpdated:d,showEntityDeleted:c,showEntityCreationError:u,showEntityUpdateError:(0,s.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityUpdateError(t)}return n(e,{errorTitle:"Update Failed"})},[o,n]),showEntityDeletionError:(0,s.useCallback)(e=>{if(o){let t=e instanceof Error?e.message:e;return o.entityDeletionError(t)}return n(e,{errorTitle:"Deletion Failed"})},[o,n]),showFormSuccess:r,showFormError:n}}(void 0,t)}},15079:(e,t,r)=>{r.d(t,{bq:()=>p,eb:()=>y,gC:()=>f,l6:()=>c,yv:()=>u});var s=r(60687),a=r(22670),i=r(61662),n=r(89743),o=r(58450),l=r(43210),d=r(22482);let c=a.bL;a.YJ;let u=a.WT,p=l.forwardRef(({children:e,className:t,...r},n)=>(0,s.jsxs)(a.l9,{className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),ref:n,...r,children:[e,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]}));p.displayName=a.l9.displayName;let m=l.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.PP,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),ref:r,...t,children:(0,s.jsx)(n.A,{className:"size-4"})}));m.displayName=a.PP.displayName;let h=l.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.wn,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),ref:r,...t,children:(0,s.jsx)(i.A,{className:"size-4"})}));h.displayName=a.wn.displayName;let f=l.forwardRef(({children:e,className:t,position:r="popper",...i},n)=>(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,ref:n,...i,children:[(0,s.jsx)(m,{}),(0,s.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),(0,s.jsx)(h,{})]})}));f.displayName=a.UC.displayName,l.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.JU,{className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),ref:r,...t})).displayName=a.JU.displayName;let y=l.memo(l.forwardRef(({children:e,className:t,...r},i)=>{let n=l.useCallback(e=>{"function"==typeof i?i(e):i&&(i.current=e)},[i]);return(0,s.jsxs)(a.q7,{className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),ref:n,...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(o.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:e})]})}));y.displayName=a.q7.displayName,l.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.wv,{className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),ref:r,...t})).displayName=a.wv.displayName},49278:(e,t,r)=>{r.d(t,{G7:()=>p,Gb:()=>l,JP:()=>d,Ok:()=>c,Qu:()=>u,iw:()=>o,oz:()=>h,z0:()=>m});var s=r(3389);class a{show(e){return(0,s.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class i extends a{constructor(e){super(),this.config=e}entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class n extends a{serviceRecordCreated(e,t){return this.success("Service Record Added",`${t} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,t){return this.success("Service Record Updated",`${t} service for "${e}" has been updated.`)}serviceRecordDeleted(e,t){return this.success("Service Record Deleted",`${t} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function o(e){return new i(e)}function l(e,t){return new i({entityName:e,getDisplayName:t,messages:{created:{title:`${e} Created`,description:t=>`The ${e.toLowerCase()} "${t}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:t=>t||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:t=>t||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:t=>t||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let d=new a,c=new i({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new i({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),p=new i({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),m=new i({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),h=new n},54050:(e,t,r)=>{r.d(t,{n:()=>c});var s=r(43210),a=r(65406),i=r(33465),n=r(35536),o=r(31212),l=class extends n.Q{#e;#t=void 0;#r;#s;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#a()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#a(),this.#i(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#a(),this.#i()}mutate(e,t){return this.#s=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#a(){let e=this.#r?.state??(0,a.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#i(e){i.jG.batch(()=>{if(this.#s&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#s.onSuccess?.(e.data,t,r),this.#s.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#s.onError?.(e.error,t,r),this.#s.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},d=r(8693);function c(e,t){let r=(0,d.jE)(t),[a]=s.useState(()=>new l(r,e));s.useEffect(()=>{a.setOptions(e)},[a,e]);let n=s.useSyncExternalStore(s.useCallback(e=>a.subscribe(i.jG.batchCalls(e)),[a]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),c=s.useCallback((e,t)=>{a.mutate(e,t).catch(o.lQ)},[a]);if(n.error&&(0,o.GU)(a.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:c,mutateAsync:n.mutate}}},55925:(e,t,r)=>{r.d(t,{Lt:()=>S,Rx:()=>I,Zr:()=>J,EO:()=>M,$v:()=>P,ck:()=>L,wd:()=>z,r7:()=>B,tv:()=>F});var s=r(60687),a=r(43210),i=r(11273),n=r(98599),o=r(26134),l=r(70569),d=r(8730),c="AlertDialog",[u,p]=(0,i.A)(c,[o.Hs]),m=(0,o.Hs)(),h=e=>{let{__scopeAlertDialog:t,...r}=e,a=m(t);return(0,s.jsx)(o.bL,{...a,...r,modal:!0})};h.displayName=c;var f=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,i=m(r);return(0,s.jsx)(o.l9,{...i,...a,ref:t})});f.displayName="AlertDialogTrigger";var y=e=>{let{__scopeAlertDialog:t,...r}=e,a=m(t);return(0,s.jsx)(o.ZL,{...a,...r})};y.displayName="AlertDialogPortal";var g=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,i=m(r);return(0,s.jsx)(o.hJ,{...i,...a,ref:t})});g.displayName="AlertDialogOverlay";var x="AlertDialogContent",[b,v]=u(x),w=(0,d.Dc)("AlertDialogContent"),N=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:i,...d}=e,c=m(r),u=a.useRef(null),p=(0,n.s)(t,u),h=a.useRef(null);return(0,s.jsx)(o.G$,{contentName:x,titleName:j,docsSlug:"alert-dialog",children:(0,s.jsx)(b,{scope:r,cancelRef:h,children:(0,s.jsxs)(o.UC,{role:"alertdialog",...c,...d,ref:p,onOpenAutoFocus:(0,l.m)(d.onOpenAutoFocus,e=>{e.preventDefault(),h.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(w,{children:i}),(0,s.jsx)($,{contentRef:u})]})})})});N.displayName=x;var j="AlertDialogTitle",E=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,i=m(r);return(0,s.jsx)(o.hE,{...i,...a,ref:t})});E.displayName=j;var D="AlertDialogDescription",R=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,i=m(r);return(0,s.jsx)(o.VY,{...i,...a,ref:t})});R.displayName=D;var A=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,i=m(r);return(0,s.jsx)(o.bm,{...i,...a,ref:t})});A.displayName="AlertDialogAction";var k="AlertDialogCancel",C=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,{cancelRef:i}=v(k,r),l=m(r),d=(0,n.s)(t,i);return(0,s.jsx)(o.bm,{...l,...a,ref:d})});C.displayName=k;var $=({contentRef:e})=>{let t=`\`${x}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${x}\` by passing a \`${D}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${x}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return a.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},U=r(29523),O=r(22482);let S=h,F=f,T=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(g,{className:(0,O.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:r}));T.displayName=g.displayName;let M=a.forwardRef(({className:e,...t},r)=>(0,s.jsxs)(y,{children:[(0,s.jsx)(T,{}),(0,s.jsx)(N,{className:(0,O.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),ref:r,...t})]}));M.displayName=N.displayName;let z=({className:e,...t})=>(0,s.jsx)("div",{className:(0,O.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});z.displayName="AlertDialogHeader";let L=({className:e,...t})=>(0,s.jsx)("div",{className:(0,O.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});L.displayName="AlertDialogFooter";let B=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(E,{className:(0,O.cn)("text-lg font-semibold",e),ref:r,...t}));B.displayName=E.displayName;let P=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(R,{className:(0,O.cn)("text-sm text-muted-foreground",e),ref:r,...t}));P.displayName=R.displayName;let I=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(A,{className:(0,O.cn)((0,U.r)(),e),ref:r,...t}));I.displayName=A.displayName;let J=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(C,{className:(0,O.cn)((0,U.r)({variant:"outline"}),"mt-2 sm:mt-0",e),ref:r,...t}));J.displayName=C.displayName},68752:(e,t,r)=>{r.d(t,{r:()=>d});var s=r(60687),a=r(11516),i=r(43210),n=r.n(i),o=r(29523),l=r(22482);let d=n().forwardRef(({actionType:e="primary",asChild:t=!1,children:r,className:i,disabled:n,icon:d,isLoading:c=!1,loadingText:u,...p},m)=>{let{className:h,variant:f}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[e];return(0,s.jsx)(o.$,{asChild:t,className:(0,l.cn)(h,i),disabled:c||n,ref:m,variant:f,...p,children:c?(0,s.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,s.jsx)(a.A,{className:"mr-2 size-4 animate-spin"}),u||r]}):(0,s.jsxs)("span",{className:"inline-flex items-center",children:[" ",d&&(0,s.jsx)("span",{className:"mr-2",children:d}),r]})})});d.displayName="ActionButton"},70640:(e,t,r)=>{r.d(t,{Breadcrumb:()=>l,BreadcrumbItem:()=>c,BreadcrumbLink:()=>u,BreadcrumbList:()=>d,BreadcrumbPage:()=>p,BreadcrumbSeparator:()=>m});var s=r(60687),a=r(8730),i=r(74158),n=(r(69795),r(43210)),o=r(22482);let l=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("nav",{"aria-label":"breadcrumb",className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),ref:r,...t}));l.displayName="Breadcrumb";let d=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("ol",{className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),ref:r,...t}));d.displayName="BreadcrumbList";let c=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("li",{className:(0,o.cn)("inline-flex items-center gap-1.5",e),ref:r,...t}));c.displayName="BreadcrumbItem";let u=n.forwardRef(({asChild:e,className:t,...r},i)=>{let n=e?a.DX:"a";return(0,s.jsx)(n,{className:(0,o.cn)("transition-colors hover:text-foreground",t),ref:i,...r})});u.displayName="BreadcrumbLink";let p=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,o.cn)("font-normal text-foreground",e),ref:r,role:"link",...t}));p.displayName="BreadcrumbPage";let m=({children:e,className:t,...r})=>(0,s.jsx)("span",{"aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",t),role:"presentation",...r,children:e??(0,s.jsx)(i.A,{className:"size-4"})});m.displayName="BreadcrumbSeparator"},71032:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(82614).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},85726:(e,t,r)=>{r.d(t,{E:()=>i});var s=r(60687),a=r(22482);function i({className:e,...t}){return(0,s.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...t})}}};