{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/transformers/giftTransformer.ts"], "sourcesContent": ["/**\n * @file Data transformer for Gift domain models.\n * @module transformers/giftTransformer\n */\n\nimport type {\n  CreateGiftRequest,\n  GiftApiResponse,\n  UpdateGiftRequest,\n} from '../types/apiContracts';\nimport type { CreateGiftData, Gift, UpdateGiftData } from '../types/domain';\n\nimport { formatDateForApi } from '../utils/dateUtils';\n\n/**\n * Transforms gift data between API response formats and frontend domain models.\n */\nexport const GiftTransformer = {\n  /**\n   * Converts an API Gift response into a frontend Gift domain model.\n   * @param apiData - The data received from the API.\n   * @returns The Gift domain model.\n   */\n  fromApi(apiData: GiftApiResponse): Gift {\n    return {\n      id: apiData.id,\n      itemDescription: apiData.itemDescription,\n      recipientId: apiData.recipientId,\n      dateSent: apiData.dateSent,\n      senderName: apiData.senderName,\n      occasion: apiData.occasion || null,\n      notes: apiData.notes || null,\n      createdAt: apiData.createdAt,\n      updatedAt: apiData.updatedAt,\n      ...(apiData.recipient && {\n        recipient: {\n          id: apiData.recipient.id,\n          name: apiData.recipient.name,\n          email: apiData.recipient.email || null,\n          phone: apiData.recipient.phone || null,\n          address: apiData.recipient.address || null,\n          notes: apiData.recipient.notes || null,\n          createdAt: apiData.recipient.createdAt,\n          updatedAt: apiData.recipient.updatedAt,\n        },\n      }),\n    };\n  },\n\n  /**\n   * Converts frontend CreateGiftData into an API request payload.\n   * @param domainData - The domain data to transform.\n   * @returns The API request payload.\n   */\n  toCreateRequest(domainData: CreateGiftData): CreateGiftRequest {\n    return {\n      itemDescription: domainData.itemDescription,\n      recipientId: domainData.recipientId,\n      dateSent: formatDateForApi(domainData.dateSent),\n      senderName: domainData.senderName,\n      occasion: domainData.occasion ?? null,\n      notes: domainData.notes ?? null,\n    };\n  },\n\n  /**\n   * Converts frontend UpdateGiftData into an API request payload.\n   * @param domainData - The domain data to transform.\n   * @returns The API request payload.\n   */\n  toUpdateRequest(domainData: UpdateGiftData): UpdateGiftRequest {\n    const request: UpdateGiftRequest = {};\n\n    if (domainData.itemDescription !== undefined) {\n      request.itemDescription = domainData.itemDescription;\n    }\n    if (domainData.recipientId !== undefined) {\n      request.recipientId = domainData.recipientId;\n    }\n    if (domainData.dateSent !== undefined) {\n      request.dateSent = formatDateForApi(domainData.dateSent);\n    }\n    if (domainData.senderName !== undefined) {\n      request.senderName = domainData.senderName;\n    }\n    if (domainData.occasion !== undefined) {\n      request.occasion = domainData.occasion;\n    }\n    if (domainData.notes !== undefined) {\n      request.notes = domainData.notes;\n    }\n\n    return request;\n  },\n\n  /**\n   * Transforms an array of API Gift responses into domain models.\n   * @param apiDataArray - Array of API responses.\n   * @returns Array of Gift domain models.\n   */\n  fromApiArray(apiDataArray: GiftApiResponse[]): Gift[] {\n    return apiDataArray.map(this.fromApi);\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AASD;;AAKO,MAAM,kBAAkB;IAC7B;;;;GAIC,GACD,SAAQ,OAAwB;QAC9B,OAAO;YACL,IAAI,QAAQ,EAAE;YACd,iBAAiB,QAAQ,eAAe;YACxC,aAAa,QAAQ,WAAW;YAChC,UAAU,QAAQ,QAAQ;YAC1B,YAAY,QAAQ,UAAU;YAC9B,UAAU,QAAQ,QAAQ,IAAI;YAC9B,OAAO,QAAQ,KAAK,IAAI;YACxB,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;YAC5B,GAAI,QAAQ,SAAS,IAAI;gBACvB,WAAW;oBACT,IAAI,QAAQ,SAAS,CAAC,EAAE;oBACxB,MAAM,QAAQ,SAAS,CAAC,IAAI;oBAC5B,OAAO,QAAQ,SAAS,CAAC,KAAK,IAAI;oBAClC,OAAO,QAAQ,SAAS,CAAC,KAAK,IAAI;oBAClC,SAAS,QAAQ,SAAS,CAAC,OAAO,IAAI;oBACtC,OAAO,QAAQ,SAAS,CAAC,KAAK,IAAI;oBAClC,WAAW,QAAQ,SAAS,CAAC,SAAS;oBACtC,WAAW,QAAQ,SAAS,CAAC,SAAS;gBACxC;YACF,CAAC;QACH;IACF;IAEA;;;;GAIC,GACD,iBAAgB,UAA0B;QACxC,OAAO;YACL,iBAAiB,WAAW,eAAe;YAC3C,aAAa,WAAW,WAAW;YACnC,UAAU,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,QAAQ;YAC9C,YAAY,WAAW,UAAU;YACjC,UAAU,WAAW,QAAQ,IAAI;YACjC,OAAO,WAAW,KAAK,IAAI;QAC7B;IACF;IAEA;;;;GAIC,GACD,iBAAgB,UAA0B;QACxC,MAAM,UAA6B,CAAC;QAEpC,IAAI,WAAW,eAAe,KAAK,WAAW;YAC5C,QAAQ,eAAe,GAAG,WAAW,eAAe;QACtD;QACA,IAAI,WAAW,WAAW,KAAK,WAAW;YACxC,QAAQ,WAAW,GAAG,WAAW,WAAW;QAC9C;QACA,IAAI,WAAW,QAAQ,KAAK,WAAW;YACrC,QAAQ,QAAQ,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,QAAQ;QACzD;QACA,IAAI,WAAW,UAAU,KAAK,WAAW;YACvC,QAAQ,UAAU,GAAG,WAAW,UAAU;QAC5C;QACA,IAAI,WAAW,QAAQ,KAAK,WAAW;YACrC,QAAQ,QAAQ,GAAG,WAAW,QAAQ;QACxC;QACA,IAAI,WAAW,KAAK,KAAK,WAAW;YAClC,QAAQ,KAAK,GAAG,WAAW,KAAK;QAClC;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,cAAa,YAA+B;QAC1C,OAAO,aAAa,GAAG,CAAC,IAAI,CAAC,OAAO;IACtC;AACF", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/api/services/domain/giftApi.ts"], "sourcesContent": ["import type {\n  CreateGiftRequest,\n  GiftApiResponse,\n  GiftStatisticsApiResponse,\n  UpdateGiftRequest,\n} from '../../../types/apiContracts';\nimport type { Gift, GiftStatistics } from '../../../types/domain';\nimport type { ApiClient } from '../../core/apiClient';\n\nimport { GiftTransformer } from '../../../transformers/giftTransformer';\nimport {\n  BaseApiService,\n  type DataTransformer,\n  type ServiceConfig,\n} from '../../core/baseApiService';\n\nconst GiftApiTransformer: DataTransformer<Gift> = {\n  fromApi: (data: GiftApiResponse) => GiftTransformer.fromApi(data),\n  toApi: (data: any) => data,\n};\n\nexport class GiftApiService extends BaseApiService<\n  Gift,\n  CreateGiftRequest,\n  UpdateGiftRequest\n> {\n  protected endpoint = '/gifts';\n  protected transformer: DataTransformer<Gift> = GiftApiTransformer;\n\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\n    super(apiClient, {\n      cacheDuration: 5 * 60 * 1000, // 5 minutes for gifts\n      circuitBreakerThreshold: 5,\n      enableMetrics: true,\n      retryAttempts: 3,\n      ...config,\n    });\n  }\n\n  /**\n   * Get gifts sent within a date range\n   * @param startDate - Start date (ISO string)\n   * @param endDate - End date (ISO string)\n   * @returns Promise resolving to array of gifts\n   */\n  async getByDateRange(startDate: string, endDate: string): Promise<Gift[]> {\n    const result = await this.getAll({\n      endDate,\n      startDate,\n    });\n    return result.data;\n  }\n\n  /**\n   * Get gifts by occasion\n   * @param occasion - The occasion to filter by\n   * @returns Promise resolving to array of gifts\n   */\n  async getByOccasion(occasion: string): Promise<Gift[]> {\n    const result = await this.getAll({ occasion });\n    return result.data;\n  }\n\n  /**\n   * Get gifts by recipient ID\n   * @param recipientId - The recipient ID to filter by\n   * @returns Promise resolving to array of gifts\n   */\n  async getByRecipient(recipientId: string): Promise<Gift[]> {\n    const result = await this.getAll({ recipientId });\n    return result.data;\n  }\n\n  /**\n   * Get gifts by sender name\n   * @param senderName - The sender name to filter by\n   * @returns Promise resolving to array of gifts\n   */\n  async getBySender(senderName: string): Promise<Gift[]> {\n    const result = await this.getAll({ senderName });\n    return result.data;\n  }\n\n  /**\n   * Get recent gifts (last 30 days)\n   * @returns Promise resolving to array of recent gifts\n   */\n  async getRecent(): Promise<Gift[]> {\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n\n    return this.getByDateRange(\n      thirtyDaysAgo.toISOString(),\n      new Date().toISOString()\n    );\n  }\n\n  /**\n   * Get gift statistics\n   * @returns Promise resolving to gift statistics\n   */\n  async getStatistics(): Promise<GiftStatistics> {\n    return this.executeWithInfrastructure('getStatistics', async () => {\n      const response = await this.apiClient.get<GiftStatisticsApiResponse>(\n        `${this.endpoint}/statistics`\n      );\n\n      return {\n        giftsThisMonth: response.giftsThisMonth,\n        giftsThisYear: response.giftsThisYear,\n        popularOccasions: response.popularOccasions,\n        recentActivity: response.recentActivity,\n        topRecipients: response.topRecipients,\n        totalGifts: response.totalGifts,\n      };\n    });\n  }\n\n  /**\n   * Search gifts by item description\n   * @param searchTerm - The search term to filter by\n   * @returns Promise resolving to array of gifts\n   */\n  async searchByDescription(searchTerm: string): Promise<Gift[]> {\n    const result = await this.getAll({ search: searchTerm });\n    return result.data;\n  }\n}\n"], "names": [], "mappings": ";;;AASA;AACA;;;AAMA,MAAM,qBAA4C;IAChD,SAAS,CAAC,OAA0B,gJAAA,CAAA,kBAAe,CAAC,OAAO,CAAC;IAC5D,OAAO,CAAC,OAAc;AACxB;AAEO,MAAM,uBAAuB,8IAAA,CAAA,iBAAc;IAKtC,WAAW,SAAS;IACpB,cAAqC,mBAAmB;IAElE,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,yBAAyB;YACzB,eAAe;YACf,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA;;;;;GAKC,GACD,MAAM,eAAe,SAAiB,EAAE,OAAe,EAAmB;QACxE,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAC/B;YACA;QACF;QACA,OAAO,OAAO,IAAI;IACpB;IAEA;;;;GAIC,GACD,MAAM,cAAc,QAAgB,EAAmB;QACrD,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE;QAAS;QAC5C,OAAO,OAAO,IAAI;IACpB;IAEA;;;;GAIC,GACD,MAAM,eAAe,WAAmB,EAAmB;QACzD,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE;QAAY;QAC/C,OAAO,OAAO,IAAI;IACpB;IAEA;;;;GAIC,GACD,MAAM,YAAY,UAAkB,EAAmB;QACrD,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE;QAAW;QAC9C,OAAO,OAAO,IAAI;IACpB;IAEA;;;GAGC,GACD,MAAM,YAA6B;QACjC,MAAM,gBAAgB,IAAI;QAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK;QAEhD,OAAO,IAAI,CAAC,cAAc,CACxB,cAAc,WAAW,IACzB,IAAI,OAAO,WAAW;IAE1B;IAEA;;;GAGC,GACD,MAAM,gBAAyC;QAC7C,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YAG/B,OAAO;gBACL,gBAAgB,SAAS,cAAc;gBACvC,eAAe,SAAS,aAAa;gBACrC,kBAAkB,SAAS,gBAAgB;gBAC3C,gBAAgB,SAAS,cAAc;gBACvC,eAAe,SAAS,aAAa;gBACrC,YAAY,SAAS,UAAU;YACjC;QACF;IACF;IAEA;;;;GAIC,GACD,MAAM,oBAAoB,UAAkB,EAAmB;QAC7D,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE,QAAQ;QAAW;QACtD,OAAO,OAAO,IAAI;IACpB;AACF", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/transformers/recipientTransformer.ts"], "sourcesContent": ["/**\n * @file Data transformer for Recipient domain models.\n * @module transformers/recipientTransformer\n */\n\nimport type {\n  CreateRecipientRequest,\n  RecipientApiResponse,\n  UpdateRecipientRequest,\n} from '../types/apiContracts';\nimport type {\n  CreateRecipientData,\n  Recipient,\n  UpdateRecipientData,\n} from '../types/domain';\n\n/**\n * Transforms recipient data between API response formats and frontend domain models.\n */\nexport const RecipientTransformer = {\n  /**\n   * Converts an API Recipient response into a frontend Recipient domain model.\n   * @param apiData - The data received from the API.\n   * @returns The Recipient domain model.\n   */\n  fromApi(apiData: RecipientApiResponse): Recipient {\n    return {\n      id: apiData.id,\n      name: apiData.name,\n      role: apiData.role || null,\n      worksite: apiData.worksite || null,\n      email: apiData.email || null,\n      phone: apiData.phone || null,\n      address: apiData.address || null,\n      notes: apiData.notes || null,\n      createdAt: apiData.createdAt,\n      updatedAt: apiData.updatedAt,\n      ...(apiData.gifts && {\n        gifts: apiData.gifts.map(gift => ({\n          id: gift.id,\n          itemDescription: gift.itemDescription,\n          recipientId: gift.recipientId,\n          dateSent: gift.dateSent,\n          senderName: gift.senderName,\n          occasion: gift.occasion || null,\n          notes: gift.notes || null,\n          createdAt: gift.createdAt,\n          updatedAt: gift.updatedAt,\n        })),\n      }),\n    };\n  },\n\n  /**\n   * Converts frontend CreateRecipientData into an API request payload.\n   * @param domainData - The domain data to transform.\n   * @returns The API request payload.\n   */\n  toCreateRequest(domainData: CreateRecipientData): CreateRecipientRequest {\n    return {\n      name: domainData.name,\n      role: domainData.role ?? null,\n      worksite: domainData.worksite ?? null,\n      email: domainData.email ?? null,\n      phone: domainData.phone ?? null,\n      address: domainData.address ?? null,\n      notes: domainData.notes ?? null,\n    };\n  },\n\n  /**\n   * Converts frontend UpdateRecipientData into an API request payload.\n   * @param domainData - The domain data to transform.\n   * @returns The API request payload.\n   */\n  toUpdateRequest(domainData: UpdateRecipientData): UpdateRecipientRequest {\n    const request: UpdateRecipientRequest = {};\n\n    if (domainData.name !== undefined) {\n      request.name = domainData.name;\n    }\n    if (domainData.role !== undefined) {\n      request.role = domainData.role;\n    }\n    if (domainData.worksite !== undefined) {\n      request.worksite = domainData.worksite;\n    }\n    if (domainData.email !== undefined) {\n      request.email = domainData.email;\n    }\n    if (domainData.phone !== undefined) {\n      request.phone = domainData.phone;\n    }\n    if (domainData.address !== undefined) {\n      request.address = domainData.address;\n    }\n    if (domainData.notes !== undefined) {\n      request.notes = domainData.notes;\n    }\n\n    return request;\n  },\n\n  /**\n   * Transforms an array of API Recipient responses into domain models.\n   * @param apiDataArray - Array of API responses.\n   * @returns Array of Recipient domain models.\n   */\n  fromApiArray(apiDataArray: RecipientApiResponse[]): Recipient[] {\n    return apiDataArray.map(this.fromApi);\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAgBM,MAAM,uBAAuB;IAClC;;;;GAIC,GACD,SAAQ,OAA6B;QACnC,OAAO;YACL,IAAI,QAAQ,EAAE;YACd,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,IAAI,IAAI;YACtB,UAAU,QAAQ,QAAQ,IAAI;YAC9B,OAAO,QAAQ,KAAK,IAAI;YACxB,OAAO,QAAQ,KAAK,IAAI;YACxB,SAAS,QAAQ,OAAO,IAAI;YAC5B,OAAO,QAAQ,KAAK,IAAI;YACxB,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;YAC5B,GAAI,QAAQ,KAAK,IAAI;gBACnB,OAAO,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAChC,IAAI,KAAK,EAAE;wBACX,iBAAiB,KAAK,eAAe;wBACrC,aAAa,KAAK,WAAW;wBAC7B,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,UAAU;wBAC3B,UAAU,KAAK,QAAQ,IAAI;wBAC3B,OAAO,KAAK,KAAK,IAAI;wBACrB,WAAW,KAAK,SAAS;wBACzB,WAAW,KAAK,SAAS;oBAC3B,CAAC;YACH,CAAC;QACH;IACF;IAEA;;;;GAIC,GACD,iBAAgB,UAA+B;QAC7C,OAAO;YACL,MAAM,WAAW,IAAI;YACrB,MAAM,WAAW,IAAI,IAAI;YACzB,UAAU,WAAW,QAAQ,IAAI;YACjC,OAAO,WAAW,KAAK,IAAI;YAC3B,OAAO,WAAW,KAAK,IAAI;YAC3B,SAAS,WAAW,OAAO,IAAI;YAC/B,OAAO,WAAW,KAAK,IAAI;QAC7B;IACF;IAEA;;;;GAIC,GACD,iBAAgB,UAA+B;QAC7C,MAAM,UAAkC,CAAC;QAEzC,IAAI,WAAW,IAAI,KAAK,WAAW;YACjC,QAAQ,IAAI,GAAG,WAAW,IAAI;QAChC;QACA,IAAI,WAAW,IAAI,KAAK,WAAW;YACjC,QAAQ,IAAI,GAAG,WAAW,IAAI;QAChC;QACA,IAAI,WAAW,QAAQ,KAAK,WAAW;YACrC,QAAQ,QAAQ,GAAG,WAAW,QAAQ;QACxC;QACA,IAAI,WAAW,KAAK,KAAK,WAAW;YAClC,QAAQ,KAAK,GAAG,WAAW,KAAK;QAClC;QACA,IAAI,WAAW,KAAK,KAAK,WAAW;YAClC,QAAQ,KAAK,GAAG,WAAW,KAAK;QAClC;QACA,IAAI,WAAW,OAAO,KAAK,WAAW;YACpC,QAAQ,OAAO,GAAG,WAAW,OAAO;QACtC;QACA,IAAI,WAAW,KAAK,KAAK,WAAW;YAClC,QAAQ,KAAK,GAAG,WAAW,KAAK;QAClC;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,cAAa,YAAoC;QAC/C,OAAO,aAAa,GAAG,CAAC,IAAI,CAAC,OAAO;IACtC;AACF", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/api/services/domain/recipientApi.ts"], "sourcesContent": ["import type {\n  CreateRecipientRequest,\n  RecipientApiResponse,\n  RecipientStatisticsApiResponse,\n  UpdateRecipientRequest,\n} from '../../../types/apiContracts';\nimport type { Recipient, RecipientStatistics } from '../../../types/domain';\nimport type { ApiClient } from '../../core/apiClient';\nimport { RecipientTransformer } from '../../../transformers/recipientTransformer';\nimport {\n  BaseApiService,\n  type DataTransformer,\n  type ServiceConfig,\n} from '../../core/baseApiService';\n\nconst RecipientApiTransformer: DataTransformer<Recipient> = {\n  fromApi: (data: RecipientApiResponse) => RecipientTransformer.fromApi(data),\n  toApi: (data: any) => data,\n};\n\nexport class RecipientApiService extends BaseApiService<\n  Recipient,\n  CreateRecipientRequest,\n  UpdateRecipientRequest\n> {\n  protected endpoint = '/recipients';\n  protected transformer: DataTransformer<Recipient> = RecipientApiTransformer;\n\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\n    super(apiClient, {\n      cacheDuration: 10 * 60 * 1000, // 10 minutes for recipients (longer cache as they change less frequently)\n      retryAttempts: 3,\n      circuitBreakerThreshold: 5,\n      enableMetrics: true,\n      ...config,\n    });\n  }\n\n  /**\n   * Search recipients by name\n   * @param searchTerm - The search term to filter by\n   * @returns Promise resolving to array of recipients\n   */\n  async searchByName(searchTerm: string): Promise<Recipient[]> {\n    const result = await this.getAll({ search: searchTerm });\n    return result.data;\n  }\n\n  /**\n   * Get recipients with gift counts\n   * @returns Promise resolving to array of recipients with gift counts\n   */\n  async getWithGiftCounts(): Promise<Recipient[]> {\n    return this.executeWithInfrastructure('getWithGiftCounts', async () => {\n      const response = await this.apiClient.get<RecipientApiResponse[]>(\n        `${this.endpoint}/with-gift-counts`\n      );\n\n      return RecipientTransformer.fromApiArray(response);\n    });\n  }\n\n  /**\n   * Get recipient statistics\n   * @returns Promise resolving to recipient statistics\n   */\n  async getStatistics(): Promise<RecipientStatistics> {\n    return this.executeWithInfrastructure('getStatistics', async () => {\n      const response = await this.apiClient.get<RecipientStatisticsApiResponse>(\n        `${this.endpoint}/statistics`\n      );\n\n      return {\n        totalRecipients: response.totalRecipients,\n        recipientsThisMonth: response.recipientsThisMonth,\n        recipientsThisYear: response.recipientsThisYear,\n        mostGifted: response.mostGifted,\n      };\n    });\n  }\n\n  /**\n   * Get recipients by email domain\n   * @param domain - The email domain to filter by (e.g., 'gmail.com')\n   * @returns Promise resolving to array of recipients\n   */\n  async getByEmailDomain(domain: string): Promise<Recipient[]> {\n    const result = await this.getAll({ emailDomain: domain });\n    return result.data;\n  }\n\n  /**\n   * Get recipients who have received gifts\n   * @returns Promise resolving to array of recipients with gifts\n   */\n  async getWithGifts(): Promise<Recipient[]> {\n    const result = await this.getAll({ hasGifts: true });\n    return result.data;\n  }\n\n  /**\n   * Get recipients who haven't received gifts\n   * @returns Promise resolving to array of recipients without gifts\n   */\n  async getWithoutGifts(): Promise<Recipient[]> {\n    const result = await this.getAll({ hasGifts: false });\n    return result.data;\n  }\n\n  /**\n   * Get recipients for autocomplete/dropdown (limited results)\n   * @param searchTerm - The search term to filter by\n   * @returns Promise resolving to array of recipients (limited to 10)\n   */\n  async getForAutocomplete(searchTerm: string): Promise<Recipient[]> {\n    return this.executeWithInfrastructure(\n      `autocomplete:${searchTerm}`,\n      async () => {\n        const response = await this.apiClient.get<RecipientApiResponse[]>(\n          `${this.endpoint}/search?q=${encodeURIComponent(searchTerm)}&limit=10`\n        );\n\n        return RecipientTransformer.fromApiArray(response);\n      }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAQA;AACA;;;AAMA,MAAM,0BAAsD;IAC1D,SAAS,CAAC,OAA+B,qJAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC;IACtE,OAAO,CAAC,OAAc;AACxB;AAEO,MAAM,4BAA4B,8IAAA,CAAA,iBAAc;IAK3C,WAAW,cAAc;IACzB,cAA0C,wBAAwB;IAE5E,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,KAAK,KAAK;YACzB,eAAe;YACf,yBAAyB;YACzB,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA;;;;GAIC,GACD,MAAM,aAAa,UAAkB,EAAwB;QAC3D,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE,QAAQ;QAAW;QACtD,OAAO,OAAO,IAAI;IACpB;IAEA;;;GAGC,GACD,MAAM,oBAA0C;QAC9C,OAAO,IAAI,CAAC,yBAAyB,CAAC,qBAAqB;YACzD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;YAGrC,OAAO,qJAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC;QAC3C;IACF;IAEA;;;GAGC,GACD,MAAM,gBAA8C;QAClD,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YAG/B,OAAO;gBACL,iBAAiB,SAAS,eAAe;gBACzC,qBAAqB,SAAS,mBAAmB;gBACjD,oBAAoB,SAAS,kBAAkB;gBAC/C,YAAY,SAAS,UAAU;YACjC;QACF;IACF;IAEA;;;;GAIC,GACD,MAAM,iBAAiB,MAAc,EAAwB;QAC3D,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE,aAAa;QAAO;QACvD,OAAO,OAAO,IAAI;IACpB;IAEA;;;GAGC,GACD,MAAM,eAAqC;QACzC,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE,UAAU;QAAK;QAClD,OAAO,OAAO,IAAI;IACpB;IAEA;;;GAGC,GACD,MAAM,kBAAwC;QAC5C,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE,UAAU;QAAM;QACnD,OAAO,OAAO,IAAI;IACpB;IAEA;;;;GAIC,GACD,MAAM,mBAAmB,UAAkB,EAAwB;QACjE,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,aAAa,EAAE,YAAY,EAC5B;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,mBAAmB,YAAY,SAAS,CAAC;YAGxE,OAAO,qJAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC;QAC3C;IAEJ;AACF", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/api/services/domain/reliabilityApi.ts"], "sourcesContent": ["import type {\r\n  AlertApiResponse,\r\n  AlertHistoryApiResponse,\r\n  AlertStatisticsApiResponse,\r\n  CircuitBreakerStatusApiResponse,\r\n  DeduplicationMetricsApiResponse,\r\n  DependencyHealthApiResponse,\r\n  DetailedHealthApiResponse,\r\n  HealthCheckApiResponse,\r\n  MetricsApiResponse,\r\n  TestAlertsApiResponse,\r\n} from '../../../types/api';\r\nimport type {\r\n  Alert,\r\n  AlertHistory,\r\n  AlertStatistics,\r\n  CircuitBreakerStatus,\r\n  DeduplicationMetrics,\r\n  DependencyHealth,\r\n  DetailedHealthCheck,\r\n  HealthCheck,\r\n  SystemMetrics,\r\n  TestAlertsResult,\r\n} from '../../../types/domain';\r\nimport type { ApiClient } from '../../core/apiClient';\r\n\r\nimport logger from '../../../utils/logger';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '../../core/baseApiService';\r\n\r\nconst ReliabilityTransformer: DataTransformer<any> = {\r\n  fromApi: (data: any) => data,\r\n  toApi: (data: any) => data,\r\n};\r\n\r\nexport class ReliabilityApiService extends BaseApiService<any, any, any> {\r\n  protected endpoint = '/reliability';\r\n  protected transformer: DataTransformer<any> = ReliabilityTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 1 * 60 * 1000, // 1 minute for reliability data\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      retryAttempts: 3,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  async acknowledgeAlert(\r\n    alertId: string,\r\n    note?: string,\r\n    acknowledgedBy?: string\r\n  ): Promise<Alert> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<AlertApiResponse>(\r\n        `/alerts/${alertId}/acknowledge`,\r\n        {\r\n          acknowledgedBy,\r\n          note,\r\n        }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp('^alerts:'));\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async getActiveAlerts(): Promise<Alert[]> {\r\n    return this.executeWithInfrastructure('alerts:active', async () => {\r\n      try {\r\n        const apiResponse = await this.apiClient.get<any>('/alerts');\r\n\r\n        return apiResponse?.alerts || [];\r\n      } catch (error) {\r\n        console.error('Failed to get active alerts:', error);\r\n        return [];\r\n      }\r\n    });\r\n  }\r\n\r\n  async getAlertHistory(page = 1, limit = 50): Promise<AlertHistory> {\r\n    return this.executeWithInfrastructure(\r\n      `alerts:history:${page}:${limit}`,\r\n      async () => {\r\n        const queryParams = new URLSearchParams({\r\n          limit: limit.toString(),\r\n          page: page.toString(),\r\n        });\r\n        const response = await this.apiClient.get<AlertHistoryApiResponse>(\r\n          `/alerts/history?${queryParams.toString()}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getAlertStatistics(): Promise<AlertStatistics> {\r\n    return this.executeWithInfrastructure('alerts:statistics', async () => {\r\n      try {\r\n        const response =\r\n          await this.apiClient.get<AlertStatisticsApiResponse>(\r\n            '/alerts/statistics'\r\n          );\r\n        return response;\r\n      } catch (error) {\r\n        console.error('Failed to get alert statistics:', error);\r\n        return {\r\n          acknowledged: 0,\r\n          active: 0,\r\n          averageResolutionTime: 0,\r\n          bySeverity: { critical: 0, high: 0, low: 0, medium: 0 },\r\n          recentTrends: { last7Days: 0, last24Hours: 0, last30Days: 0 },\r\n          resolved: 0,\r\n          total: 0,\r\n        };\r\n      }\r\n    });\r\n  }\r\n\r\n  async getCircuitBreakerHistory(\r\n    timeframe: '1h' | '6h' | '7d' | '24h' = '24h',\r\n    breakerName?: string\r\n  ): Promise<any> {\r\n    return this.executeWithInfrastructure(\r\n      `circuit-breakers:history:${timeframe}:${breakerName || 'all'}`,\r\n      async () => {\r\n        const params = new URLSearchParams({ timeframe });\r\n        if (breakerName) {\r\n          params.append('breakerName', breakerName);\r\n        }\r\n        const response = await this.apiClient.get<any>(\r\n          `/monitoring/circuit-breakers/history?${params.toString()}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getCircuitBreakerStatus(): Promise<CircuitBreakerStatus> {\r\n    return this.executeWithInfrastructure(\r\n      'monitoring:circuit-breakers',\r\n      async () => {\r\n        try {\r\n          const apiResponse =\r\n            await this.apiClient.get<any>('/circuit-breakers');\r\n\r\n          const circuitBreakers = apiResponse?.circuitBreakers || [];\r\n\r\n          return {\r\n            circuitBreakers: circuitBreakers || [],\r\n            summary: {\r\n              closed:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'CLOSED')\r\n                  .length || 0,\r\n              halfOpen:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'HALF_OPEN')\r\n                  .length || 0,\r\n              open:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'OPEN')\r\n                  .length || 0,\r\n              total: circuitBreakers?.length || 0,\r\n            },\r\n          };\r\n        } catch (error) {\r\n          console.error('Failed to get circuit breaker status:', error);\r\n          return {\r\n            circuitBreakers: [],\r\n            summary: { closed: 0, halfOpen: 0, open: 0, total: 0 },\r\n          };\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  async getCriticalAlertCount(): Promise<number> {\r\n    try {\r\n      const statistics = await this.getAlertStatistics();\r\n      return statistics.bySeverity.critical;\r\n    } catch {\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  async getDeduplicationMetrics(): Promise<DeduplicationMetrics> {\r\n    return this.executeWithInfrastructure(\r\n      'monitoring:deduplication',\r\n      async () => {\r\n        const response =\r\n          await this.apiClient.get<DeduplicationMetricsApiResponse>(\r\n            '/monitoring/deduplication'\r\n          );\r\n        return response as any;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getDependencyHealth(): Promise<DependencyHealth> {\r\n    return this.executeWithInfrastructure('health:dependencies', async () => {\r\n      const response = await this.apiClient.get<DependencyHealthApiResponse>(\r\n        '/health/dependencies'\r\n      );\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getDetailedHealth(): Promise<DetailedHealthCheck> {\r\n    return this.executeWithInfrastructure('health:detailed', async () => {\r\n      const response =\r\n        await this.apiClient.get<DetailedHealthApiResponse>('/health/detailed');\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getHealthTrends(\r\n    timeframe: '1h' | '6h' | '7d' | '24h' = '24h'\r\n  ): Promise<any> {\r\n    return this.executeWithInfrastructure(\r\n      `health:trends:${timeframe}`,\r\n      async () => {\r\n        const response = await this.apiClient.get<any>(\r\n          `/health/trends?timeframe=${timeframe}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getHttpRequestMetrics(): Promise<any> {\r\n    return this.executeWithInfrastructure('http:metrics', async () => {\r\n      const response = await this.apiClient.get<any>('/http-request-metrics');\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async getMetrics(): Promise<SystemMetrics> {\r\n    return this.executeWithInfrastructure('metrics:system', async () => {\r\n      const response = await this.apiClient.get<MetricsApiResponse>(\r\n        '/metrics',\r\n        {\r\n          headers: { Accept: 'application/json' },\r\n        }\r\n      );\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getReliabilityDashboardData(): Promise<{\r\n    activeAlerts: Alert[];\r\n    alertStatistics: AlertStatistics;\r\n    circuitBreakers: CircuitBreakerStatus;\r\n    detailedHealth: DetailedHealthCheck;\r\n    metrics: SystemMetrics;\r\n    systemHealth: HealthCheck;\r\n  }> {\r\n    const [\r\n      systemHealth,\r\n      detailedHealth,\r\n      circuitBreakers,\r\n      metrics,\r\n      activeAlerts,\r\n      alertStatistics,\r\n    ] = await Promise.all([\r\n      this.getSystemHealth(),\r\n      this.getDetailedHealth(),\r\n      this.getCircuitBreakerStatus(),\r\n      this.getMetrics(),\r\n      this.getActiveAlerts(),\r\n      this.getAlertStatistics(),\r\n    ]);\r\n\r\n    return {\r\n      activeAlerts,\r\n      alertStatistics,\r\n      circuitBreakers,\r\n      detailedHealth,\r\n      metrics,\r\n      systemHealth,\r\n    };\r\n  }\r\n\r\n  async getSystemHealth(): Promise<HealthCheck> {\r\n    return this.executeWithInfrastructure('health:system', async () => {\r\n      const response =\r\n        await this.apiClient.get<HealthCheckApiResponse>('/health');\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async isSystemHealthy(): Promise<boolean> {\r\n    try {\r\n      const health = await this.getSystemHealth();\r\n      return health.status === 'healthy';\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  async resolveAlert(\r\n    alertId: string,\r\n    reason?: string,\r\n    resolvedBy?: string\r\n  ): Promise<Alert> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<AlertApiResponse>(\r\n        `/alerts/${alertId}/resolve`,\r\n        {\r\n          reason,\r\n          resolvedBy,\r\n        }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp('^alerts:'));\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async testAlerts(): Promise<TestAlertsResult> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<any>('/alerts/test');\r\n      return {\r\n        message: response?.message || 'Test alert triggered',\r\n        success: response?.status === 'success',\r\n        testAlertId: response?.data?.id,\r\n      };\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AA2BA;;AAMA,MAAM,yBAA+C;IACnD,SAAS,CAAC,OAAc;IACxB,OAAO,CAAC,OAAc;AACxB;AAEO,MAAM,8BAA8B,8IAAA,CAAA,iBAAc;IAC7C,WAAW,eAAe;IAC1B,cAAoC,uBAAuB;IAErE,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,yBAAyB;YACzB,eAAe;YACf,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA,MAAM,iBACJ,OAAe,EACf,IAAa,EACb,cAAuB,EACP;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,QAAQ,EAAE,QAAQ,YAAY,CAAC,EAChC;gBACE;gBACA;YACF;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO;YAExC,OAAO;QACT;IACF;IAEA,MAAM,kBAAoC;QACxC,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,IAAI;gBACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;gBAElD,OAAO,aAAa,UAAU,EAAE;YAClC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO,EAAE;YACX;QACF;IACF;IAEA,MAAM,gBAAgB,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAyB;QACjE,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE,OAAO,EACjC;YACE,MAAM,cAAc,IAAI,gBAAgB;gBACtC,OAAO,MAAM,QAAQ;gBACrB,MAAM,KAAK,QAAQ;YACrB;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,gBAAgB,EAAE,YAAY,QAAQ,IAAI;YAE7C,OAAO;QACT;IAEJ;IAEA,MAAM,qBAA+C;QACnD,OAAO,IAAI,CAAC,yBAAyB,CAAC,qBAAqB;YACzD,IAAI;gBACF,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACtB;gBAEJ,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,OAAO;oBACL,cAAc;oBACd,QAAQ;oBACR,uBAAuB;oBACvB,YAAY;wBAAE,UAAU;wBAAG,MAAM;wBAAG,KAAK;wBAAG,QAAQ;oBAAE;oBACtD,cAAc;wBAAE,WAAW;wBAAG,aAAa;wBAAG,YAAY;oBAAE;oBAC5D,UAAU;oBACV,OAAO;gBACT;YACF;QACF;IACF;IAEA,MAAM,yBACJ,YAAwC,KAAK,EAC7C,WAAoB,EACN;QACd,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,yBAAyB,EAAE,UAAU,CAAC,EAAE,eAAe,OAAO,EAC/D;YACE,MAAM,SAAS,IAAI,gBAAgB;gBAAE;YAAU;YAC/C,IAAI,aAAa;gBACf,OAAO,MAAM,CAAC,eAAe;YAC/B;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,qCAAqC,EAAE,OAAO,QAAQ,IAAI;YAE7D,OAAO;QACT;IAEJ;IAEA,MAAM,0BAAyD;QAC7D,OAAO,IAAI,CAAC,yBAAyB,CACnC,+BACA;YACE,IAAI;gBACF,MAAM,cACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;gBAEhC,MAAM,kBAAkB,aAAa,mBAAmB,EAAE;gBAE1D,OAAO;oBACL,iBAAiB,mBAAmB,EAAE;oBACtC,SAAS;wBACP,QACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,UAC/C,UAAU;wBACf,UACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,aAC/C,UAAU;wBACf,MACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,QAC/C,UAAU;wBACf,OAAO,iBAAiB,UAAU;oBACpC;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,OAAO;oBACL,iBAAiB,EAAE;oBACnB,SAAS;wBAAE,QAAQ;wBAAG,UAAU;wBAAG,MAAM;wBAAG,OAAO;oBAAE;gBACvD;YACF;QACF;IAEJ;IAEA,MAAM,wBAAyC;QAC7C,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,kBAAkB;YAChD,OAAO,WAAW,UAAU,CAAC,QAAQ;QACvC,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,0BAAyD;QAC7D,OAAO,IAAI,CAAC,yBAAyB,CACnC,4BACA;YACE,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACtB;YAEJ,OAAO;QACT;IAEJ;IAEA,MAAM,sBAAiD;QACrD,OAAO,IAAI,CAAC,yBAAyB,CAAC,uBAAuB;YAC3D,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC;YAEF,OAAO;QACT;IACF;IAEA,MAAM,oBAAkD;QACtD,OAAO,IAAI,CAAC,yBAAyB,CAAC,mBAAmB;YACvD,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAA4B;YACtD,OAAO;QACT;IACF;IAEA,MAAM,gBACJ,YAAwC,KAAK,EAC/B;QACd,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,cAAc,EAAE,WAAW,EAC5B;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,yBAAyB,EAAE,WAAW;YAEzC,OAAO;QACT;IAEJ;IAEA,MAAM,wBAAsC;QAC1C,OAAO,IAAI,CAAC,yBAAyB,CAAC,gBAAgB;YACpD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;YAC/C,OAAO;QACT;IACF;IAEA,MAAM,aAAqC;QACzC,OAAO,IAAI,CAAC,yBAAyB,CAAC,kBAAkB;YACtD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,YACA;gBACE,SAAS;oBAAE,QAAQ;gBAAmB;YACxC;YAEF,OAAO;QACT;IACF;IAEA,MAAM,8BAOH;QACD,MAAM,CACJ,cACA,gBACA,iBACA,SACA,cACA,gBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,uBAAuB;YAC5B,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,kBAAkB;SACxB;QAED,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,kBAAwC;QAC5C,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAyB;YACnD,OAAO;QACT;IACF;IAEA,MAAM,kBAAoC;QACxC,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe;YACzC,OAAO,OAAO,MAAM,KAAK;QAC3B,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,aACJ,OAAe,EACf,MAAe,EACf,UAAmB,EACH;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,EAC5B;gBACE;gBACA;YACF;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO;YAExC,OAAO;QACT;IACF;IAEA,MAAM,aAAwC;QAC5C,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAM;YAChD,OAAO;gBACL,SAAS,UAAU,WAAW;gBAC9B,SAAS,UAAU,WAAW;gBAC9B,aAAa,UAAU,MAAM;YAC/B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/api/services/factory.ts"], "sourcesContent": ["/**\r\n * @file Factory for creating and managing API service instances.\r\n * @module api/services/apiServiceFactory\r\n */\r\n\r\nimport { ApiClient } from '../core/apiClient';\r\nimport { DelegationApiService } from './domain/delegationApi';\r\nimport { EmployeeApiService } from './domain/employeeApi';\r\nimport { GiftApiService } from './domain/giftApi';\r\nimport { RecipientApiService } from './domain/recipientApi';\r\nimport { ReliabilityApiService } from './domain/reliabilityApi';\r\nimport { TaskApiService } from './domain/taskApi';\r\nimport { VehicleApiService } from './domain/vehicleApi';\r\nimport { getEnvironmentConfig } from '../../config/environment';\r\n// Import secure auth token provider\r\nimport { getSecureAuthTokenProvider } from '../index';\r\n\r\n/**\r\n * Get the current auth token from the secure provider\r\n * Uses the single source of truth for authentication tokens\r\n */\r\nfunction getSecureAuthToken(): string | null {\r\n  const provider = getSecureAuthTokenProvider();\r\n  if (!provider) {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.warn('⚠️ Factory: Secure Auth Token Provider not initialized');\r\n    }\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    return provider();\r\n  } catch (error) {\r\n    console.error(\r\n      '❌ Factory: Error getting auth token from secure provider:',\r\n      error\r\n    );\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Legacy compatibility - maintains backward compatibility\r\n * @deprecated Use setSecureAuthTokenProvider from main API module instead\r\n */\r\nexport function setFactoryAuthTokenProvider(\r\n  provider: () => string | null\r\n): void {\r\n  console.warn(\r\n    '⚠️ setFactoryAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider from @/lib/api instead.'\r\n  );\r\n  // This function is now a no-op since we use the secure provider\r\n  // The warning guides developers to use the correct function\r\n}\r\n\r\n/**\r\n * Configuration for the API service factory.\r\n */\r\nexport interface ApiServiceFactoryConfig {\r\n  authToken?: string;\r\n  baseURL: string;\r\n  headers?: Record<string, string>;\r\n  retryAttempts?: number;\r\n  timeout?: number;\r\n}\r\n\r\n/**\r\n * Factory class for creating and managing API service instances.\r\n * Provides a centralized way to configure and access all API services.\r\n */\r\nexport class ApiServiceFactory {\r\n  private readonly apiClient: ApiClient;\r\n  private delegationService?: DelegationApiService;\r\n  private employeeService?: EmployeeApiService;\r\n  private giftService?: GiftApiService;\r\n  private recipientService?: RecipientApiService;\r\n  private reliabilityService?: ReliabilityApiService;\r\n  private taskService?: TaskApiService;\r\n  private vehicleService?: VehicleApiService;\r\n\r\n  /**\r\n   * Creates an instance of ApiServiceFactory.\r\n   * @param config - Configuration for the API services.\r\n   */\r\n  constructor(config: ApiServiceFactoryConfig) {\r\n    this.apiClient = new ApiClient({\r\n      ...config,\r\n      getAuthToken: getSecureAuthToken, // Use consistent secure naming\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gets the underlying ApiClient instance.\r\n   * @returns The ApiClient instance.\r\n   */\r\n  public getApiClient(): ApiClient {\r\n    return this.apiClient;\r\n  }\r\n\r\n  /**\r\n   * Gets the Delegation API service instance.\r\n   * @returns The DelegationApiService instance.\r\n   */\r\n  public getDelegationService(): DelegationApiService {\r\n    if (!this.delegationService) {\r\n      this.delegationService = new DelegationApiService(this.apiClient);\r\n    }\r\n    return this.delegationService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Employee API service instance.\r\n   * @returns The EmployeeApiService instance.\r\n   */\r\n  public getEmployeeService(): EmployeeApiService {\r\n    if (!this.employeeService) {\r\n      this.employeeService = new EmployeeApiService(this.apiClient);\r\n    }\r\n    return this.employeeService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Reliability API service instance.\r\n   * @returns The ReliabilityApiService instance.\r\n   */\r\n  public getReliabilityService(): ReliabilityApiService {\r\n    if (!this.reliabilityService) {\r\n      this.reliabilityService = new ReliabilityApiService(this.apiClient);\r\n    }\r\n    return this.reliabilityService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Task API service instance.\r\n   * @returns The TaskApiService instance.\r\n   */\r\n  public getTaskService(): TaskApiService {\r\n    if (!this.taskService) {\r\n      this.taskService = new TaskApiService(this.apiClient);\r\n    }\r\n    return this.taskService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Vehicle API service instance.\r\n   * @returns The VehicleApiService instance.\r\n   */\r\n  public getVehicleService(): VehicleApiService {\r\n    if (!this.vehicleService) {\r\n      this.vehicleService = new VehicleApiService(this.apiClient);\r\n    }\r\n    return this.vehicleService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Gift API service instance.\r\n   * @returns The GiftApiService instance.\r\n   */\r\n  public getGiftService(): GiftApiService {\r\n    if (!this.giftService) {\r\n      this.giftService = new GiftApiService(this.apiClient);\r\n    }\r\n    return this.giftService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Recipient API service instance.\r\n   * @returns The RecipientApiService instance.\r\n   */\r\n  public getRecipientService(): RecipientApiService {\r\n    if (!this.recipientService) {\r\n      this.recipientService = new RecipientApiService(this.apiClient);\r\n    }\r\n    return this.recipientService;\r\n  }\r\n}\r\n\r\n// Create a default factory instance for the application with environment-aware configuration\r\nconst envConfig = getEnvironmentConfig();\r\nconst defaultConfig: ApiServiceFactoryConfig = {\r\n  baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  retryAttempts: 3,\r\n  timeout: 10_000,\r\n};\r\n\r\nexport const apiServiceFactory = new ApiServiceFactory(defaultConfig);\r\n\r\n// Export individual service instances for convenience\r\nexport const vehicleApiService = apiServiceFactory.getVehicleService();\r\nexport const delegationApiService = apiServiceFactory.getDelegationService();\r\nexport const taskApiService = apiServiceFactory.getTaskService();\r\nexport const employeeApiService = apiServiceFactory.getEmployeeService();\r\nexport const reliabilityApiService = apiServiceFactory.getReliabilityService();\r\nexport const giftApiService = apiServiceFactory.getGiftService();\r\nexport const recipientApiService = apiServiceFactory.getRecipientService();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;AAqBO;AAnBR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AAAA;;;;;;;;;;;AAEA;;;CAGC,GACD,SAAS;IACP,MAAM,WAAW,CAAA,GAAA,6IAAA,CAAA,6BAA0B,AAAD;IAC1C,IAAI,CAAC,UAAU;QACb,wCAA4C;YAC1C,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;IAEA,IAAI;QACF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,6DACA;QAEF,OAAO;IACT;AACF;AAMO,SAAS,4BACd,QAA6B;IAE7B,QAAQ,IAAI,CACV;AAEF,gEAAgE;AAChE,4DAA4D;AAC9D;AAiBO,MAAM;IACM,UAAqB;IAC9B,kBAAyC;IACzC,gBAAqC;IACrC,YAA6B;IAC7B,iBAAuC;IACvC,mBAA2C;IAC3C,YAA6B;IAC7B,eAAmC;IAE3C;;;GAGC,GACD,YAAY,MAA+B,CAAE;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,yIAAA,CAAA,YAAS,CAAC;YAC7B,GAAG,MAAM;YACT,cAAc;QAChB;IACF;IAEA;;;GAGC,GACD,AAAO,eAA0B;QAC/B,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA;;;GAGC,GACD,AAAO,uBAA6C;QAClD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,GAAG,IAAI,2JAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,SAAS;QAClE;QACA,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA;;;GAGC,GACD,AAAO,qBAAyC;QAC9C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,yJAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,SAAS;QAC9D;QACA,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA;;;GAGC,GACD,AAAO,wBAA+C;QACpD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,4JAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,SAAS;QACpE;QACA,OAAO,IAAI,CAAC,kBAAkB;IAChC;IAEA;;;GAGC,GACD,AAAO,iBAAiC;QACtC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,IAAI,qJAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,SAAS;QACtD;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;;GAGC,GACD,AAAO,oBAAuC;QAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,wJAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,SAAS;QAC5D;QACA,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA;;;GAGC,GACD,AAAO,iBAAiC;QACtC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,IAAI,qJAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,SAAS;QACtD;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;;GAGC,GACD,AAAO,sBAA2C;QAChD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,IAAI,CAAC,gBAAgB,GAAG,IAAI,0JAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,SAAS;QAChE;QACA,OAAO,IAAI,CAAC,gBAAgB;IAC9B;AACF;AAEA,6FAA6F;AAC7F,MAAM,YAAY,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD;AACrC,MAAM,gBAAyC;IAC7C,SAAS,UAAU,UAAU;IAC7B,SAAS;QACP,gBAAgB;IAClB;IACA,eAAe;IACf,SAAS;AACX;AAEO,MAAM,oBAAoB,IAAI,kBAAkB;AAGhD,MAAM,oBAAoB,kBAAkB,iBAAiB;AAC7D,MAAM,uBAAuB,kBAAkB,oBAAoB;AACnE,MAAM,iBAAiB,kBAAkB,cAAc;AACvD,MAAM,qBAAqB,kBAAkB,kBAAkB;AAC/D,MAAM,wBAAwB,kBAAkB,qBAAqB;AACrE,MAAM,iBAAiB,kBAAkB,cAAc;AACvD,MAAM,sBAAsB,kBAAkB,mBAAmB", "debugId": null}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/api/services/apiServiceFactory.ts"], "sourcesContent": ["/**\r\n * @file API Service Factory - Backward Compatibility Export\r\n * @module api/services/apiServiceFactory\r\n *\r\n * This file provides backward compatibility for imports that expect\r\n * apiServiceFactory.ts instead of factory.ts\r\n */\r\n\r\n// Re-export everything from the factory module\r\nexport * from './factory';\r\n\r\n// Ensure all the commonly used exports are available\r\nexport {\r\n  ApiServiceFactory,\r\n  apiServiceFactory,\r\n  setFactoryAuthTokenProvider, // Legacy compatibility - deprecated\r\n  vehicleApiService,\r\n  delegationApiService,\r\n  taskApiService,\r\n  employeeApiService,\r\n  reliabilityApiService,\r\n  giftApiService,\r\n  recipientApiService,\r\n} from './factory';\r\n\r\n// Re-export secure auth provider for convenience\r\nexport { setSecureAuthTokenProvider } from '../index';\r\n\r\nexport type { ApiServiceFactoryConfig } from './factory';\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED,+CAA+C;;AAC/C;AAgBA,iDAAiD;AACjD", "debugId": null}}]}