"use strict";exports.id=4827,exports.ids=[4827],exports.modules={19203:(e,t,r)=>{r.d(t,{P:()=>i});var s=r(49278);class i{static showSuccessToast(e,t,r){if(!e.showSuccessToast)return;let{entityType:i,entity:a,successMessage:n}=e;try{switch(i){case"employee":a?s.Ok.entityCreated(a):s.JP.success("Employee Created",n);break;case"vehicle":a?s.G7.entityCreated(a):s.JP.success("Vehicle Added",n);break;case"task":a?s.z0.entityCreated(a):s.JP.success("Task Created",n);break;case"delegation":a?s.Qu.entityCreated(a):s.JP.success("Delegation Created",n);break;case"serviceRecord":a&&r?s.oz.serviceRecordCreated(a.vehicleName||"Vehicle",a.serviceType||"Service"):s.<PERSON>.success("Service Record Added",n);break;default:s.JP.success("Success",n||"Operation completed successfully")}}catch(e){s.JP.success("Success",n||"Operation completed successfully")}}static showErrorToast(e,t,r){if(!e.showErrorToast)return;let{entityType:i,errorMessage:a}=e,n=t.message||a||"An unexpected error occurred";try{switch(i){case"employee":s.Ok.entityCreationError(n);break;case"vehicle":s.G7.entityCreationError(n);break;case"task":s.z0.entityCreationError(n);break;case"delegation":s.Qu.entityCreationError(n);break;case"serviceRecord":s.oz.serviceRecordCreationError(n);break;default:s.JP.error("Error",n)}}catch(e){s.JP.error("Error",n)}}static showUpdateSuccessToast(e,t,r){if(!e.showSuccessToast)return;let{entityType:i,entity:a,successMessage:n}=e;try{switch(i){case"employee":a?s.Ok.entityUpdated(a):s.JP.success("Employee Updated",n);break;case"vehicle":a?s.G7.entityUpdated(a):s.JP.success("Vehicle Updated",n);break;case"task":a?s.z0.entityUpdated(a):s.JP.success("Task Updated",n);break;case"delegation":a?s.Qu.entityUpdated(a):s.JP.success("Delegation Updated",n);break;case"serviceRecord":a&&r?s.oz.serviceRecordUpdated(a.vehicleName||"Vehicle",a.serviceType||"Service"):s.JP.success("Service Record Updated",n);break;default:s.JP.success("Success",n||"Update completed successfully")}}catch(e){s.JP.success("Success",n||"Update completed successfully")}}static showUpdateErrorToast(e,t,r){if(!e.showErrorToast)return;let{entityType:i,errorMessage:a}=e,n=t.message||a||"An unexpected error occurred";try{switch(i){case"employee":s.Ok.entityUpdateError(n);break;case"vehicle":s.G7.entityUpdateError(n);break;case"task":s.z0.entityUpdateError(n);break;case"delegation":s.Qu.entityUpdateError(n);break;case"serviceRecord":s.oz.serviceRecordUpdateError(n);break;default:s.JP.error("Update Failed",n)}}catch(e){s.JP.error("Update Failed",n)}}static createCustomEntityToastService(e,t){return(0,s.Gb)(e,t)}}},28439:(e,t,r)=>{r.d(t,{SY:()=>c});let s={maxAttempts:3,delay:1e3,exponentialBackoff:!0,retryCondition:e=>e.message.includes("network")||e.message.includes("timeout")||e.message.includes("502")||e.message.includes("503")||e.message.includes("504")},i={announceStatus:!0,focusManagement:"first-error",screenReaderAnnouncements:!0},a={debounceMs:300,enableDeduplication:!0,cacheResults:!1,timeoutMs:3e4},n={showSuccessToast:!0,showErrorToast:!0,successMessage:"Operation completed successfully",errorMessage:"An unexpected error occurred",entityType:"generic"};class c{static mergeRetryConfig(e){return{...s,...e}}static mergeAccessibilityConfig(e){return{...i,...e}}static mergePerformanceConfig(e){return{...a,...e}}static mergeToastConfig(e){return{...n,...e}}}},49278:(e,t,r)=>{r.d(t,{G7:()=>m,Gb:()=>o,JP:()=>u,Ok:()=>l,Qu:()=>d,iw:()=>c,oz:()=>h,z0:()=>p});var s=r(3389);class i{show(e){return(0,s.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class a extends i{constructor(e){super(),this.config=e}entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class n extends i{serviceRecordCreated(e,t){return this.success("Service Record Added",`${t} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,t){return this.success("Service Record Updated",`${t} service for "${e}" has been updated.`)}serviceRecordDeleted(e,t){return this.success("Service Record Deleted",`${t} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function c(e){return new a(e)}function o(e,t){return new a({entityName:e,getDisplayName:t,messages:{created:{title:`${e} Created`,description:t=>`The ${e.toLowerCase()} "${t}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:t=>t||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:t=>t||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:t=>t||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let u=new i,l=new a({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),d=new a({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),m=new a({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),p=new a({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),h=new n},54827:(e,t,r)=>{r.d(t,{k:()=>u});var s=r(43210),i=r(28439),a=r(19203);class n{constructor(e){this.config=e}announceStatus(e,t="polite"){if(!this.config.announceStatus||!this.config.screenReaderAnnouncements)return;let r=document.getElementById("form-submission-announcements");r||((r=document.createElement("div")).id="form-submission-announcements",r.setAttribute("aria-live",t),r.setAttribute("aria-atomic","true"),r.className="sr-only absolute left-[-10000px] top-[-10000px] w-[1px] h-[1px] overflow-hidden",document.body.appendChild(r)),r.textContent=e,setTimeout(()=>{r&&r.textContent===e&&(r.textContent="")},1e3)}generateAriaAttributes(e,t,r){return{"aria-busy":e,"aria-invalid":t,"aria-describedby":this.config.errorDescribedBy||(t?"form-error":void 0),"aria-live":"submitting"===r||"validating"===r?"polite":"off"}}manageFocus(e,t){if("none"!==this.config.focusManagement)switch(e){case"error":"first-error"===this.config.focusManagement&&t&&t("first-error");break;case"success":if("success-message"===this.config.focusManagement){let e=document.getElementById("form-success-message");e&&e.focus()}else"next-field"===this.config.focusManagement&&t&&t("next-field");break;case"retry":t&&t("retry-button")}}createErrorMessage(e){let t=document.createElement("div");return t.id=this.config.errorDescribedBy||"form-error",t.setAttribute("role","alert"),t.setAttribute("aria-live","assertive"),t.className="sr-only",t.textContent=e,t}updateErrorMessage(e){let t=this.config.errorDescribedBy||"form-error",r=document.getElementById(t);e?r?r.textContent=e:(r=this.createErrorMessage(e),document.body.appendChild(r)):r&&r.remove()}getStatusMessage(e,t,r){switch(e){case"validating":return"Validating form data...";case"submitting":return"Submitting form...";case"retrying":return`Retrying submission... (Attempt ${t||1}/${r||3})`;case"success":return"Form submitted successfully";case"error":return"Form submission failed";default:return""}}setupKeyboardNavigation(){let e=e=>{if("Escape"===e.key){let e=document.querySelector("[data-form-cancel]");e&&e.click()}if((e.ctrlKey||e.metaKey)&&"Enter"===e.key){let e=document.querySelector('[type="submit"]');e&&!e.disabled&&e.click()}};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}announceProgress(e,t,r){if(!this.config.screenReaderAnnouncements)return;let s=`Step ${e} of ${t}: ${r}`;this.announceStatus(s,"polite")}cleanup(){let e=document.getElementById("form-submission-announcements");e&&e.remove(),this.updateErrorMessage(null)}}class c{constructor(e){this.currentAttempt=0,this.config=e}shouldRetry(e){return this.currentAttempt<this.config.maxAttempts&&(!this.config.retryCondition||this.config.retryCondition(e))}getRetryDelay(){let e=this.config.delay;return this.config.exponentialBackoff?e*Math.pow(2,this.currentAttempt):e}incrementAttempt(){return this.currentAttempt+=1,this.currentAttempt}resetAttempts(){this.currentAttempt=0}getCurrentAttempt(){return this.currentAttempt}getMaxAttempts(){return this.config.maxAttempts}async sleep(e){return new Promise(t=>setTimeout(t,e))}async executeRetry(e){if(!this.shouldRetry(Error("Manual retry")))throw Error("Maximum retry attempts exceeded");let t=this.getRetryDelay();return this.incrementAttempt(),await this.sleep(t),e()}getRetryStatus(){return{currentAttempt:this.currentAttempt,maxAttempts:this.config.maxAttempts,hasRetriesLeft:this.currentAttempt<this.config.maxAttempts,nextDelay:this.getRetryDelay()}}withConfig(e){return new c({...this.config,...e})}}class o{constructor(e){this.submissionStartTime=null,this.debounceTimer=null,this.config=e,this.metrics={totalSubmissions:0,successfulSubmissions:0,failedSubmissions:0,averageDuration:0}}startTiming(){this.submissionStartTime=Date.now()}endTiming(e){if(!this.submissionStartTime)return 0;let t=Date.now()-this.submissionStartTime;return this.updateMetrics(e,t),this.submissionStartTime=null,t}updateMetrics(e,t){let r=this.metrics.totalSubmissions+1,s=e?this.metrics.successfulSubmissions+1:this.metrics.successfulSubmissions,i=e?this.metrics.failedSubmissions:this.metrics.failedSubmissions+1,a=this.metrics.averageDuration*this.metrics.totalSubmissions+t;this.metrics={totalSubmissions:r,successfulSubmissions:s,failedSubmissions:i,averageDuration:a/r}}getMetrics(){return{...this.metrics}}resetMetrics(){this.metrics={totalSubmissions:0,successfulSubmissions:0,failedSubmissions:0,averageDuration:0}}debounce(e,t=this.config.debounceMs){return(...r)=>{this.debounceTimer&&clearTimeout(this.debounceTimer),this.debounceTimer=setTimeout(()=>{e(...r)},t)}}clearDebounce(){this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null)}createTimeoutPromise(e=this.config.timeoutMs){return new Promise((t,r)=>{setTimeout(()=>{r(Error(`Request timeout after ${e}ms`))},e)})}async withTimeout(e,t=this.config.timeoutMs){return Promise.race([e,this.createTimeoutPromise(t)])}getSuccessRate(){return 0===this.metrics.totalSubmissions?0:this.metrics.successfulSubmissions/this.metrics.totalSubmissions*100}getFailureRate(){return 0===this.metrics.totalSubmissions?0:this.metrics.failedSubmissions/this.metrics.totalSubmissions*100}isPerformanceAcceptable(e){let t={maxAverageDuration:5e3,minSuccessRate:95,...e};return this.metrics.averageDuration<=t.maxAverageDuration&&this.getSuccessRate()>=t.minSuccessRate}generateReport(){let e=this.getSuccessRate(),t=this.getFailureRate(),r=this.isPerformanceAcceptable(),s=[];return this.metrics.averageDuration>3e3&&s.push("Consider optimizing form validation or submission logic"),e<90&&s.push("High failure rate detected - review error handling"),this.metrics.totalSubmissions>100&&this.metrics.averageDuration>1e3&&s.push("Consider implementing caching for better performance"),{metrics:this.getMetrics(),successRate:e,failureRate:t,isAcceptable:r,recommendations:s}}cleanup(){this.clearDebounce(),this.submissionStartTime=null}}let u=(e,t={})=>{let r=i.SY.mergeRetryConfig(t.retry),u=i.SY.mergeAccessibilityConfig(t.accessibility),l=i.SY.mergePerformanceConfig(t.performance),d=i.SY.mergeToastConfig(t.toast),m=(0,s.useRef)(new n(u)).current,p=(0,s.useRef)(new c(r)).current,h=(0,s.useRef)(new o(l)).current,[g,f]=(0,s.useState)("idle"),[y,b]=(0,s.useState)(null),[S,v]=(0,s.useState)(null),[w,E]=(0,s.useState)(null),[A,C]=(0,s.useState)(null),[k,T]=(0,s.useState)(null),[D,R]=(0,s.useState)(null),x=(0,s.useRef)(null),$="submitting"===g||"validating"===g,U="success"===g,M="validating"===g,P="retrying"===g,F=p.getCurrentAttempt();(0,s.useEffect)(()=>()=>{x.current&&x.current.abort(),h.cleanup(),m.cleanup()},[h,m]);let J=(0,s.useCallback)(()=>{b(null),v(null),m.updateErrorMessage(null),"error"===g&&f("idle")},[g,m]),V=(0,s.useCallback)(()=>{f("idle"),b(null),v(null),E(null),C(null),T(null),R(null),p.resetAttempts(),h.resetMetrics(),m.updateErrorMessage(null)},[p,h,m]),N=(0,s.useCallback)(()=>{x.current&&x.current.abort(),f("idle"),m.announceStatus("Form submission cancelled")},[m]),z=(0,s.useCallback)(async(s,i=!1)=>{try{h.startTiming(),x.current=new AbortController;let n=i?"retrying":"submitting";f(n);let c=m.getStatusMessage(n,F,r.maxAttempts);if(m.announceStatus(c),t.onSubmitStart&&await t.onSubmitStart(s),t.preSubmitValidation&&(f("validating"),m.announceStatus("Validating form data..."),!await t.preSubmitValidation(s)))throw Error("Validation failed");f(n);let o=s;t.transformData&&(o=await t.transformData(s));let u=e(o),l=await h.withTimeout(u),g=l;if(t.transformResult&&(g=await t.transformResult(l)),t.postSubmitValidation&&!await t.postSubmitValidation(g))throw Error("Post-submission validation failed");let y=h.endTiming(!0);f("success"),T(g),C(Date.now()),E(s),R(y),p.resetAttempts(),a.P.showSuccessToast(d,s,g),m.announceStatus("Form submitted successfully","assertive"),m.manageFocus("success",t.formFocus),t.resetOnSuccess&&t.formReset&&t.formReset(),t.onSuccess&&await t.onSuccess(s,g),t.onSubmitComplete&&await t.onSubmitComplete(s,!0)}catch(o){let e=o instanceof Error?o:Error(String(o)),n=h.endTiming(!1);if(!i&&p.shouldRetry(e)){f("retrying");let e=p.getRetryDelay();return p.incrementAttempt(),m.announceStatus(`Retrying in ${e}ms... (Attempt ${p.getCurrentAttempt()}/${r.maxAttempts})`),await p.sleep(e),z(s,!0)}f("error");let c=e.message||d.errorMessage||"An unexpected error occurred";b(c),v(e),R(n),a.P.showErrorToast(d,e,s),m.updateErrorMessage(c),m.announceStatus(`Error: ${c}`,"assertive"),m.manageFocus("error",t.formFocus),t.onError&&await t.onError(e,s),t.onSubmitComplete&&await t.onSubmitComplete(s,!1)}},[e,t,p,h,m,d,r.maxAttempts,F]),B=(0,s.useCallback)(async(e,t)=>{t&&t.preventDefault(),h.debounce(()=>z(e),l.debounceMs)()},[z,h,l.debounceMs]),O=(0,s.useCallback)(async()=>{w&&(p.resetAttempts(),await z(w))},[w,z,p]),L=m.generateAriaAttributes($,!!y,g);return{isLoading:$,state:g,error:y,errorObject:S,isSuccess:U,isValidating:M,isRetrying:P,lastSubmittedData:w,lastSubmitted:A,lastResult:k,retryAttempt:F,handleSubmit:B,clearError:J,reset:V,retry:O,cancel:N,ariaAttributes:L,submissionDuration:D,metrics:h.getMetrics()}}}};