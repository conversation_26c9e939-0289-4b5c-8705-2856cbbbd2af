"use strict";exports.id=8800,exports.ids=[8800],exports.modules={8800:e=>{e.exports=JSON.parse('{"metadata":{"title":"WorkHub - Comprehensive Management System","description":"Streamline your operations with our comprehensive management platform","keywords":"workforce management, employee tracking, performance monitoring, productivity optimization, HR management, business operations, team management, organizational efficiency"},"NotFoundPage":{"title":"Page Not Found","description":"The page you are looking for does not exist.","backHome":"Back to Home"},"common":{"loading":"Loading...","error":"An error occurred","retry":"Retry","cancel":"Cancel","save":"Save","edit":"Edit","delete":"Delete","add":"Add","search":"Search","filter":"Filter","clear":"Clear","submit":"Submit","back":"Back","next":"Next","previous":"Previous","close":"Close","confirm":"Confirm","yes":"Yes","no":"No","view":"View","actions":"Actions","status":"Status","priority":"Priority","title":"Title","name":"Name","date":"Date","type":"Type","noData":"No data available","noResults":"No results found","tryAdjustingFilters":"Try adjusting your filters","clearFilters":"Clear filters","created":"Created","updated":"Updated","createdAt":"Created At","updatedAt":"Updated At","metadata":"Metadata","more":"more","less":"less","showMore":"Show more","showLess":"Show less","total":"Total","average":"Average","settings":"Settings","comingSoon":"Coming soon...","taskTitle":"Task Title","customer":"Customer","delegates":"Delegates","noDelegates":"No delegates","selectAll":"Select All","export":"Export","import":"Import","refresh":"Refresh","print":"Print","download":"Download","upload":"Upload","help":"Help","profile":"Profile","logout":"Logout","viewDetails":"View Details"},"navigation":{"dashboard":"Dashboard","delegations":"Delegations","employees":"Employees","reports":"Reports","settings":"Settings","profile":"Profile","logout":"Logout","assets":"Assets","fleet":"Fleet","maintenance":"Maintenance","serviceRecords":"Service Records","projects":"Projects","tasks":"Tasks","team":"Team","teamMembers":"Team Members","analytics":"Analytics","reliability":"Reliability","monitoring":"Monitoring","admin":"Admin","administration":"Administration","allSettings":"All Settings","gifts":"Gifts","recipients":"Recipients","sections":{"overview":"Overview","operations":"Operations","workforce":"Workforce","system":"System"},"badges":{"new":"New"},"fontSize":{"small":"Small","medium":"Medium","large":"Large"},"search":{"placeholder":"Search navigation..."}},"auth":{"login":"Login","logout":"Logout","email":"Email","password":"Password","forgotPassword":"Forgot Password?","rememberMe":"Remember Me","loginError":"Invalid email or password","loginSuccess":"Login successful","welcomeBack":"Welcome back","signInToAccount":"Sign in to your WorkHub account","emailAddress":"Email address","signIn":"Sign in","signingIn":"Signing in...","noInternetConnection":"No internet connection","offlineWarning":"You\'re currently offline. Please check your internet connection to sign in.","showPassword":"Show password","hidePassword":"Hide password","validation":{"emailRequired":"Email is required","emailInvalid":"Please enter a valid email address","passwordRequired":"Password is required","passwordTooShort":"Password must be at least 6 characters"},"errors":{"authenticationFailed":"Failed to initialize authentication","invalidCredentials":"Invalid email or password","networkError":"Network error. Please try again.","unknownError":"An unexpected error occurred"}},"dashboard":{"welcomeBack":"Welcome back","welcomeToWorkHub":"Welcome to WorkHub","signInToAccess":"Please sign in to access your dashboard","signIn":"Sign In","operationsToday":"Here\'s what\'s happening with your operations today.","keyMetrics":"Key Metrics","stats":{"totalVehicles":"Total Vehicles","activeFleetAssets":"Active fleet assets","activeProjects":"Active Projects","inProgressDelegations":"In progress delegations","pendingTasks":"Pending Tasks","awaitingCompletion":"Awaiting completion","maintenanceDue":"Maintenance Due","requiresAttention":"Requires attention"},"recentActivity":"Recent Activity","viewAll":"View All","quickActions":"Quick Actions","addVehicle":"Add Vehicle","createDelegation":"Create Delegation","assignTask":"Assign Task","scheduleService":"Schedule Service","fleetPerformance":"Fleet Performance","fleetEfficiencyOverview":"Overview of fleet efficiency and maintenance status","fleetUtilization":"Fleet Utilization","maintenanceUpToDate":"Maintenance Up-to-date","fuelEfficiency":"Fuel Efficiency","taskCompletionRate":"Task Completion Rate","latestUpdates":"Latest updates from your operations","addNewVehicle":"Add New Vehicle","registerNewAsset":"Register a new asset to your fleet","scheduleMaintenance":"Schedule Maintenance","planUpcomingService":"Plan upcoming service appointments","viewAnalytics":"View Analytics","seeDetailedReports":"See detailed reports and insights","createAndDelegate":"Create and delegate new tasks","activity":{"maintenanceCompleted":"maintenance completed","newTaskAssigned":"New task assigned","milestoneCompleted":"milestone completed","oilChangeFinished":"Oil change and tire rotation finished","safetyInspectionDue":"Quarterly safety inspection due next week","deliverablesSubmitted":"Phase 2 deliverables submitted"},"timeAgo":{"hoursAgo":"{hours} hours ago","dayAgo":"{days} day ago","daysAgo":"{days} days ago"}},"reliability":{"dashboard":"Reliability Dashboard","systemMonitoring":"System monitoring","connectionStatus":{"connected":"Connected","connecting":"Connecting","reconnecting":"Reconnecting","disconnected":"Disconnected","error":"Error","polling":"Polling","realTimeActive":"Real-time connection active","establishingConnection":"Establishing connection...","attemptingReconnect":"Attempting to reconnect...","connectionError":"Connection error occurred","pollingFallback":"Using polling fallback (WebSocket requires authentication)","connectionLost":"Real-time connection lost"},"widgets":{"systemHealth":"System Health","systemHealthDesc":"Overall system status and health indicators","healthStatusIndicators":"Health Status Indicators","healthStatusDesc":"Real-time component health status indicators","dependencyStatus":"Dependency Status","dependencyStatusDesc":"External dependency health monitoring","healthTrends":"Health Trends","healthTrendsDesc":"Historical health trend visualization","systemResources":"System Resources","systemResourcesDesc":"CPU, memory, and connection monitoring","performanceOverview":"Performance Overview","performanceOverviewDesc":"Comprehensive performance metrics and scoring","systemMetrics":"System Performance","systemMetricsDesc":"Performance-focused system metrics monitoring","httpMetrics":"HTTP Request Metrics","httpMetricsDesc":"Request performance and throughput analysis","deduplicationMetrics":"Deduplication Metrics","deduplicationMetricsDesc":"Cache efficiency and request deduplication analysis"}},"forms":{"validation":{"required":"This field is required","invalidEmail":"Please enter a valid email address","invalidPhone":"Please enter a valid phone number","invalidDate":"Please enter a valid date","minLength":"Must be at least {min} characters","maxLength":"Must be no more than {max} characters","invalidFormat":"Invalid format"},"placeholders":{"enterEmail":"Enter your email address","enterPassword":"Enter your password","enterName":"Enter name","enterRole":"Enter role or position","enterWorksite":"Enter worksite or location","enterDescription":"Enter description","enterPhone":"Enter phone number","enterAddress":"Enter address","enterNotes":"Enter notes","selectOption":"Select an option","selectRecipient":"Select a recipient","searchPlaceholder":"Search...","searchGifts":"Search gifts...","searchRecipients":"Search recipients...","whoSentThisGift":"Who sent this gift?","selectOccasionOptional":"Select an occasion (optional)","enterGiftNotes":"Any additional notes about this gift..."},"labels":{"name":"Name","role":"Role","worksite":"Worksite","email":"Email","phone":"Phone","address":"Address","notes":"Notes","recipient":"Recipient","dateSent":"Date Sent","itemDescription":"Item Description","occasion":"Occasion","senderName":"Sender Name"},"buttons":{"submit":"Submit","save":"Save","cancel":"Cancel","edit":"Edit","delete":"Delete","add":"Add","update":"Update","create":"Create","goBack":"Go Back","saving":"Saving...","saveGift":"Save Gift","updateGift":"Update Gift"},"titles":{"addRecipient":"Add New Recipient","editRecipient":"Edit Recipient","addGift":"Add New Gift","editGift":"Edit Gift"},"descriptions":{"chooseRecipient":"Choose who received this gift","whenGiftSent":"When was this gift sent?","senderNameDesc":"Name of the person who sent the gift","giftOccasion":"What was the occasion for this gift?","optionalNotes":"Optional notes or additional details"},"occasions":{"religiousHolidays":"Religious Holidays","nationalHolidays":"National Holidays","personalOccasions":"Personal Occasions"}},"gifts":{"pageTitle":"Gift Management","pageDescription":"Track and manage gifts sent to recipients","addGift":"Add Gift","editGift":"Edit Gift","addGiftDescription":"Add a new gift to the tracking system","editGiftDescription":"Update gift information","giftDetails":"Gift Details","giftDetailsDescription":"View detailed gift information","giftInformation":"Gift Information","giftSettings":"Gift Settings","giftSettingsDescription":"Configure gift tracking preferences","recipientInformation":"Recipient Information","giftHistory":"Gift History","totalGifts":"Total Gifts","recentGifts":"Recent Gifts","viewAllGifts":"View All Gifts","noGiftsYet":"No gifts tracked yet","noGiftsMatch":"No gifts match your search","getStartedWithGifts":"Start tracking gifts by adding your first gift record","tryAdjustingFilters":"Try adjusting your search or filters","addFirstGift":"Add Your First Gift","backToGifts":"Back to Gifts","giftNotFound":"Gift Not Found","giftNotFoundDescription":"The gift you\'re looking for doesn\'t exist or has been removed","deleteGiftTitle":"Delete Gift","deleteGiftConfirmation":"Are you sure you want to delete \'{item}\'? This action cannot be undone.","gifts":"Gifts","form":{"senderName":"Sender Name","dateSent":"Date Sent","recipient":"Recipient"},"occasions":{"eidAlFitr":"Eid Al-Fitr","eidAlAdha":"Eid Al-Adha","hijraNewYear":"Hijra New Year","eidAlMawlid":"Eid Al-Mawlid Annabawi","newYear":"New Year","independenceManifesto":"Independence Manifesto","amazighNewYear":"Amazigh New Year","labourDay":"Labour Day","feastOfThrone":"Feast of the Throne","ouedEdDahabRecovery":"Recovery of Oued Ed-Dahab","revolutionKingPeople":"Revolution of the King and the People","youthDay":"Youth Day","greenMarch":"Green March","independenceDay":"Independence Day","birthday":"Birthday","anniversary":"Anniversary","other":"Other"}},"recipients":{"pageTitle":"Recipient Management","pageDescription":"Manage gift recipients and their information","addRecipient":"Add Recipient","editRecipient":"Edit Recipient","addRecipientDescription":"Add a new recipient to the system","editRecipientDescription":"Update recipient information","recipientDetails":"Recipient Details","recipientDetailsDescription":"View detailed recipient information","recipientInformation":"Recipient Information","recipientSettings":"Recipient Settings","recipientSettingsDescription":"Configure recipient management preferences","noRecipientsYet":"No recipients added yet","noRecipientsMatch":"No recipients match your search","getStartedWithRecipients":"Start by adding your first recipient","tryAdjustingFilters":"Try adjusting your search or filters","addFirstRecipient":"Add Your First Recipient","backToRecipients":"Back to Recipients","recipientNotFound":"Recipient Not Found","recipientNotFoundDescription":"The recipient you\'re looking for doesn\'t exist or has been removed","deleteRecipientTitle":"Delete Recipient","deleteRecipientConfirmation":"Are you sure you want to delete \'{name}\'? This action cannot be undone.","recipients":"Recipients"},"status":{"active":"Active","inactive":"Inactive","pending":"Pending","completed":"Completed","inProgress":"In Progress","cancelled":"Cancelled","planned":"Planned","confirmed":"Confirmed","assigned":"Assigned","overdue":"Overdue","onLeave":"On Leave","terminated":"Terminated","noDetails":"No Details"},"priority":{"low":"Low","medium":"Medium","high":"High","urgent":"Urgent","critical":"Critical"},"tables":{"headers":{"title":"Title","status":"Status","priority":"Priority","assignedTo":"Assigned To","dueDate":"Due Date","createdAt":"Created","updatedAt":"Updated","actions":"Actions","name":"Name","email":"Email","phone":"Phone","role":"Role","worksite":"Worksite","location":"Location","description":"Description","vehicle":"Vehicle","customer":"Customer","delegationId":"Delegation ID","estimatedHours":"Est. Hours","actualHours":"Actual Hours","progress":"Progress","category":"Category","amount":"Amount","id":"ID","address":"Address","department":"Department","position":"Position","hireDate":"Hire Date","tags":"Tags","notes":"Notes"},"actions":{"view":"View","edit":"Edit","delete":"Delete","viewDetails":"View Details","editItem":"Edit Item","deleteItem":"Delete Item","sendMessage":"Send Message","archive":"Archive","restore":"Restore","assign":"Assign","unassign":"Unassign","approve":"Approve","reject":"Reject","schedule":"Schedule","copyId":"Copy ID"},"emptyStates":{"noData":"No data to display","noResults":"No results match your search","noItemsYet":"No items added yet","startByAdding":"Start by adding your first item","tryAdjusting":"Try adjusting your search or filters"},"pagination":{"showing":"Showing","to":"to","of":"of","results":"results","itemsPerPage":"Items per page","rowsPerPage":"Rows per page","page":"Page","goToPage":"Go to page","previousPage":"Previous page","nextPage":"Next page","first":"First","last":"Last"}}}')}};