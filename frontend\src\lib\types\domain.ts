/**
 * @file Defines the core domain models for the WorkHub frontend application.
 * These types represent the structured data used within the application,
 * after being transformed from raw API responses.
 * @module types/domain
 */

/**
 * Alert information
 */
export interface Alert {
  acknowledgedAt?: string | undefined;
  acknowledgedBy?: string | undefined;
  details?: Record<string, any> | undefined;
  id: string;
  message: string;
  resolvedAt?: string | undefined;
  resolvedBy?: string | undefined;
  severity: AlertSeverity;
  source: string;
  status: AlertStatus;
  timestamp: string;
  type: string;
}

/**
 * Paginated alert history
 */
export interface AlertHistory {
  alerts: Alert[];
  pagination: {
    hasNext: boolean;
    hasPrevious: boolean;
    limit: number;
    page: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Alert severity levels
 */
export type AlertSeverity = 'critical' | 'high' | 'low' | 'medium';
/**
 * Alert statistics and analytics
 */
export interface AlertStatistics {
  acknowledged: number;
  active: number;
  averageResolutionTime: number;
  bySeverity: {
    critical: number;
    high: number;
    low: number;
    medium: number;
  };
  recentTrends: {
    last7Days: number;
    last24Hours: number;
    last30Days: number;
  };
  resolved: number;
  total: number;
}
/**
 * Alert status states
 */
export type AlertStatus = 'acknowledged' | 'active' | 'resolved';

export interface AuditLog {
  action: string;
  created_at: string;
  details: string;
  id: string;
  ip_address?: null | string;
  user_agent?: null | string;
  user_id: string;
}
/**
 * Circuit breaker states
 */
export type CircuitBreakerState = 'CLOSED' | 'HALF_OPEN' | 'OPEN';

/**
 * Circuit breaker status information
 */
export interface CircuitBreakerStatus {
  circuitBreakers: {
    failureCount: number;
    lastFailureTime?: string;
    name: string;
    nextAttempt?: string;
    state: CircuitBreakerState;
    successCount?: number;
    timeout?: number;
  }[];
  summary: {
    closed: number;
    halfOpen: number;
    open: number;
    total: number;
  };
}

/**
 * Individual component health information
 */
export interface ComponentHealth {
  responseTime?: number;
  status: HealthStatus;
}

export interface CreateDelegationData {
  delegates?:
    | undefined
    | { name: string; notes?: string | undefined; title: string }[]; // Matches CreateDelegateRequest
  drivers?: undefined | { employeeId: number; notes?: string | undefined }[]; // Matches CreateDriverRequest
  durationFrom: string;
  durationTo: string;
  escorts?: undefined | { employeeId: number; notes?: string | undefined }[]; // Matches CreateEscortRequest
  eventName: string;
  flightArrivalDetails?: null | Omit<FlightDetails, 'id'> | undefined; // Allow null for creation
  flightArrivalId?: null | string | undefined; // Added FK field
  flightDepartureDetails?: null | Omit<FlightDetails, 'id'> | undefined; // Allow null for creation
  flightDepartureId?: null | string | undefined; // Added FK field
  imageUrl?: string | undefined;
  invitationFrom?: string | undefined;
  invitationTo?: string | undefined;
  location: string;
  notes?: string;
  status: DelegationStatusPrisma;
  vehicles?: {
    assignedDate: string;
    notes?: null | string;
    returnDate?: null | string;
    vehicleId: number;
  }[]; // Matches CreateVehicleAssignmentRequest
}

export interface CreateEmployeeData {
  availability?: DriverAvailabilityPrisma | null | undefined;
  contactEmail?: null | string | undefined; // OPTIONAL
  contactInfo: string; // REQUIRED
  contactMobile?: null | string | undefined; // OPTIONAL
  contactPhone?: null | string | undefined; // OPTIONAL
  currentLocation?: null | string | undefined;
  department?: null | string | undefined; // OPTIONAL
  employeeId: string;
  fullName?: null | string | undefined; // OPTIONAL
  generalAssignments: string[]; // REQUIRED - Fixed from optional
  hireDate?: null | string | undefined; // OPTIONAL
  name: string; // REQUIRED
  notes?: null | string | undefined;
  position?: null | string | undefined; // OPTIONAL
  profileImageUrl?: null | string | undefined;
  role: EmployeeRolePrisma; // REQUIRED
  shiftSchedule?: null | string | undefined;
  skills: string[]; // REQUIRED - Fixed from optional
  status?: EmployeeStatusPrisma | null | undefined; // OPTIONAL
  workingHours?: null | string | undefined; // Added to match Prisma schema
}

export interface CreateSubtaskData {
  completed?: boolean;
  taskId: string; // REQUIRED
  title: string;
}

export interface CreateTaskData {
  dateTime: string;
  deadline?: string | undefined;
  description: string;
  driverEmployeeId?: number | undefined;
  estimatedDuration: number;
  location: string;
  notes?: string | undefined;
  priority: TaskPriorityPrisma;
  requiredSkills: string[]; // REQUIRED - Fixed from optional
  staffEmployeeId: number;
  status: TaskStatusPrisma;
  subtasks?: CreateSubtaskData[] | undefined;
  vehicleId?: number | undefined;
}

export interface CreateVehicleData {
  // Basic Information
  make: string;
  model: string;
  year: number;
  color?: string | undefined;

  // Identification
  licensePlate: string;
  vin?: string | undefined;

  // Additional Information
  mileage?: number | undefined;
  status?: 'active' | 'maintenance' | 'inactive' | undefined; // Optional with default
  notes?: string | undefined;

  // Legacy fields for backward compatibility
  ownerContact?: string | undefined;
  ownerName?: string | undefined;
  imageUrl?: string | undefined;
  initialOdometer?: number | undefined;
}

/**
 * Request deduplication metrics
 */
export interface DeduplicationMetrics {
  cacheHits: number;
  cacheMisses: number;
  errors: number;
  hitRate: number;
  lastReset: string;
  totalRequests: number;
}

export interface Delegate {
  // Define standalone Delegate interface
  id?: string;
  name: string;
  notes?: null | string;
  title?: string;
}

export interface Delegation {
  arrivalFlight?: FlightDetails | null;
  createdAt: string;
  // Delegates type adjusted to match schema.prisma and API response
  delegates?: Delegate[];
  departureFlight?: FlightDetails | null;
  drivers?: DelegationDriver[];
  durationFrom: string;
  durationTo: string;
  escorts?: DelegationEscort[];
  eventName: string;
  flightArrivalId?: null | string; // Added FK field from Prisma schema
  flightDepartureId?: null | string; // Added FK field from Prisma schema
  id: string; // Reverted to string based on schema.prisma
  imageUrl?: null | string;
  invitationFrom?: null | string;
  invitationTo?: null | string;
  location: string;
  notes?: null | string;
  status: DelegationStatusPrisma;
  statusHistory?: DelegationStatusEntry[];
  updatedAt: string;
  vehicles?: DelegationVehicleAssignment[];
}

export interface DelegationDriver {
  createdAt: string;
  createdBy?: null | string;
  delegationId: string;
  employee?: Employee; // Optional relation
  employeeId: number; // Changed from string to number
  id: string;
  updatedAt: string;
}

export interface DelegationEscort {
  createdAt: string;
  createdBy?: null | string;
  delegationId: string;
  employee?: Employee; // Optional relation
  employeeId: number; // Changed from string to number
  id: string;
  updatedAt: string;
}

export interface DelegationStatusEntry {
  changedAt: string;
  delegationId: string;
  id: string;
  reason?: null | string;
  status: DelegationStatusPrisma;
}

export type DelegationStatusPrisma =
  | 'Cancelled'
  | 'Completed'
  | 'Confirmed'
  | 'In_Progress'
  | 'No_details'
  | 'Planned';

export interface DelegationVehicleAssignment {
  createdAt: string;
  createdBy?: null | string;
  delegationId: string;
  id: string;
  updatedAt: string;
  vehicle?: Vehicle; // Optional relation
  vehicleId: number;
}

/**
 * External dependency health information
 */
export interface DependencyHealth {
  dependencies: {
    error?: string;
    lastChecked: string;
    name: string;
    responseTime?: number;
    status: HealthStatus;
  }[];
  summary: {
    degraded: number;
    healthy: number;
    total: number;
    unhealthy: number;
  };
}

/**
 * Detailed health check with component-level status
 */
export interface DetailedHealthCheck {
  checks: {
    businessLogic: ComponentHealth;
    cache: ComponentHealth & {
      details?: {
        redis?: {
          status: string;
        };
      };
    };
    circuitBreakers: ComponentHealth & {
      details?: {
        openBreakers: number;
      };
    };
    database: ComponentHealth;
    supabase: ComponentHealth;
    systemResources: ComponentHealth & {
      details?: {
        memory?: {
          usagePercent: number;
        };
      };
    };
  };
  environment: string;
  status: HealthStatus;
  summary: {
    degradedChecks: number;
    healthyChecks: number;
    totalChecks: number;
    unhealthyChecks: number;
  };
  timestamp: string;
  uptime: number;
  version: string;
}

export type DriverAvailabilityPrisma =
  | 'Busy'
  | 'Off_Shift'
  | 'On_Break'
  | 'On_Shift';

export interface Employee {
  availability?: DriverAvailabilityPrisma | null;
  contactEmail?: null | string; // OPTIONAL
  contactInfo: string; // REQUIRED - general contact
  contactMobile?: null | string; // OPTIONAL
  contactPhone?: null | string; // OPTIONAL
  createdAt: string;
  currentLocation?: null | string;
  department?: null | string; // OPTIONAL
  employeeId: string;
  fullName?: null | string; // OPTIONAL
  generalAssignments: string[]; // REQUIRED - Fixed from optional
  hireDate?: null | string; // OPTIONAL
  id: number;
  name: string; // REQUIRED - single name field
  notes?: null | string;
  position?: null | string; // OPTIONAL
  profileImageUrl?: null | string;
  role: EmployeeRolePrisma; // REQUIRED
  shiftSchedule?: null | string;
  skills: string[]; // REQUIRED - Fixed from optional
  status?: EmployeeStatusPrisma | null; // OPTIONAL
  updatedAt: string;
  workingHours?: null | string; // Added to match Prisma schema
}

export type EmployeeRolePrisma =
  | 'administrator'
  | 'driver'
  | 'manager'
  | 'mechanic'
  | 'office_staff'
  | 'other'
  | 'service_advisor'
  | 'technician';

export interface EmployeeStatusEntry {
  changedAt: string;
  employeeId: number;
  id: string;
  reason?: null | string;
  status: EmployeeStatusPrisma;
}

export type EmployeeStatusPrisma =
  | 'Active'
  | 'Inactive'
  | 'On_Leave'
  | 'Terminated';

export interface EnrichedServiceRecord extends ServiceRecord {
  licensePlate?: null | string; // Keep this as licensePlate for consistency with Vehicle
  vehicleMake: string;
  vehicleModel: string;
  vehicleYear: number;
}

// =============================================================================
// RELIABILITY DOMAIN TYPES
// =============================================================================

/**
 * Error log entry data for domain use
 */
export interface ErrorLogEntry {
  details?: Record<string, any>; // Direct property for details
  id: string;
  level: 'DEBUG' | 'ERROR' | 'INFO' | 'TRACE' | 'WARNING'; // Standardized levels
  message: string;
  metadata?: Record<string, any>; // For additional unstructured data
  requestId?: string;
  source?: string; // Direct property for source
  stack?: string;
  timestamp: string;
  userId?: string;
}

// Updated FlightDetails to match actual database schema
export interface FlightDetails {
  airport: string;
  dateTime: string;
  flightNumber: string;
  id?: string | undefined; // Made optional
  notes?: null | string | undefined;
  terminal?: null | string | undefined;
}

/**
 * Basic health check information
 */
export interface HealthCheck {
  environment: string;
  status: HealthStatus;
  timestamp: string;
  uptime: number;
  version: string;
}

/**
 * Health status enumeration for system components
 */
export type HealthStatus = 'degraded' | 'healthy' | 'unhealthy';

export type LogLevel = ErrorLogEntry['level']; // Define LogLevel based on ErrorLogEntry

export interface ServiceRecord {
  cost?: null | number; // Changed to optional and nullable
  createdAt: string;
  date: string;
  employeeId?: null | number;
  id: string;
  notes?: null | string;
  odometer: number;
  servicePerformed: string[]; // Added to match form data
  updatedAt: string;
  vehicleId: number; // This should align with Vehicle.id type (number) if it's a direct FK. Prisma has Int.
}

export interface Subtask {
  completed: boolean;
  id: string;
  taskId: string; // REQUIRED - FK to Task
  title: string;
}

/**
 * System performance metrics
 */
export interface SystemMetrics {
  deduplicationMetrics: DeduplicationMetrics;
  httpRequestMetrics: {
    help: string;
    name: string;
    type: string;
    values: {
      labels: {
        method: string;
        route: string;
        statusCode: string;
        userRole: string;
      };
      value: number;
    }[];
  };
  systemMetrics: {
    connections: {
      active: number;
      total: number;
    };
    cpu: {
      loadAverage: number[];
      usage: number;
    };
    memory: {
      free: number;
      total: number;
      usagePercent: number;
      used: number;
    };
  };
}

export interface Task {
  createdAt: string;
  dateTime: string; // REQUIRED - matches Prisma schema
  deadline?: null | string | undefined;
  description: string; // REQUIRED - matches Prisma schema
  driverEmployee?: Employee | null | undefined; // Driver employee details
  driverEmployeeId?: null | number | undefined;
  estimatedDuration: number; // REQUIRED - matches Prisma schema
  id: string; // REQUIRED - matches Prisma schema
  location: string; // REQUIRED - matches Prisma schema
  notes?: null | string | undefined;
  priority: TaskPriorityPrisma; // REQUIRED - matches Prisma schema
  requiredSkills: string[]; // REQUIRED - matches Prisma schema
  // Enriched relation objects (populated by enrichment transformer)
  staffEmployee?: Employee | null; // Staff employee details
  staffEmployeeId: number; // REQUIRED - matches Prisma schema
  status: TaskStatusPrisma; // REQUIRED - matches Prisma schema
  subtasks?: Subtask[];

  updatedAt: string;
  vehicle?: null | Vehicle; // Vehicle details
  vehicleId?: null | number;
}

export type TaskPriorityPrisma = 'High' | 'Low' | 'Medium';

export interface TaskStatusEntry {
  changedAt: string;
  id: string;
  reason?: null | string;
  status: TaskStatusPrisma;
  taskId: string;
}

export type TaskStatusPrisma =
  | 'Assigned'
  | 'Cancelled'
  | 'Completed'
  | 'In_Progress'
  | 'Pending';

/**
 * Test alerts operation result
 */
export interface TestAlertsResult {
  message: string;
  success: boolean;
  testAlertId?: string;
}

export interface UserProfile {
  created_at: string;
  employee_id?: null | number;
  id: string;
  is_active: boolean;
  role: string;
  updated_at: string;
  users?: {
    email: string;
    email_confirmed_at: null | string;
  }[];
}

export interface Vehicle {
  color?: null | string;
  createdAt: string;
  id: number; // Changed from string to number
  imageUrl?: null | string;
  initialOdometer?: null | number;
  licensePlate: string;
  make: string;
  model: string;
  ownerContact: string;
  ownerName: string;
  serviceHistory?: ServiceRecord[];
  updatedAt: string;
  vin: string;
  year: number;
}

// =============================================================================
// GIFT TRACKING DOMAIN TYPES
// =============================================================================

/**
 * Gift information for tracking sent gifts
 */
export interface Gift {
  /** Unique identifier for the gift */
  id: string;
  /** Description of the gift item */
  itemDescription: string;
  /** ID of the recipient who received the gift */
  recipientId: string;
  /** Date when the gift was sent */
  dateSent: string;
  /** Name of the person who sent the gift */
  senderName: string;
  /** Occasion for which the gift was sent (e.g., birthday, anniversary) */
  occasion?: string | null;
  /** Additional notes about the gift */
  notes?: string | null;
  /** Timestamp when the gift record was created */
  createdAt: string;
  /** Timestamp when the gift record was last updated */
  updatedAt: string;
  /** Optional recipient details when populated */
  recipient?: Recipient;
}

/**
 * Recipient information for gift tracking
 */
export interface Recipient {
  /** Unique identifier for the recipient */
  id: string;
  /** Full name of the recipient */
  name: string;
  /** Job role or position of the recipient */
  role?: string | null;
  /** Worksite or location where the recipient works */
  worksite?: string | null;
  /** Email address of the recipient */
  email?: string | null;
  /** Phone number of the recipient */
  phone?: string | null;
  /** Physical address of the recipient */
  address?: string | null;
  /** Additional notes about the recipient */
  notes?: string | null;
  /** Timestamp when the recipient record was created */
  createdAt: string;
  /** Timestamp when the recipient record was last updated */
  updatedAt: string;
  /** Optional list of gifts sent to this recipient */
  gifts?: Gift[];
}

/**
 * Data structure for creating a new gift
 */
export interface CreateGiftData {
  /** Description of the gift item */
  itemDescription: string;
  /** ID of the recipient who will receive the gift */
  recipientId: string;
  /** Date when the gift was sent */
  dateSent: string;
  /** Name of the person who sent the gift */
  senderName: string;
  /** Occasion for which the gift was sent */
  occasion?: string | null;
  /** Additional notes about the gift */
  notes?: string | null;
}

/**
 * Data structure for updating an existing gift
 */
export interface UpdateGiftData {
  /** Description of the gift item */
  itemDescription?: string;
  /** ID of the recipient who received the gift */
  recipientId?: string;
  /** Date when the gift was sent */
  dateSent?: string;
  /** Name of the person who sent the gift */
  senderName?: string;
  /** Occasion for which the gift was sent */
  occasion?: string | null;
  /** Additional notes about the gift */
  notes?: string | null;
}

/**
 * Data structure for creating a new recipient
 */
export interface CreateRecipientData {
  /** Full name of the recipient */
  name: string;
  /** Job role or position of the recipient */
  role?: string | null;
  /** Worksite or location where the recipient works */
  worksite?: string | null;
  /** Email address of the recipient */
  email?: string | null;
  /** Phone number of the recipient */
  phone?: string | null;
  /** Physical address of the recipient */
  address?: string | null;
  /** Additional notes about the recipient */
  notes?: string | null;
}

/**
 * Data structure for updating an existing recipient
 */
export interface UpdateRecipientData {
  /** Full name of the recipient */
  name?: string;
  /** Job role or position of the recipient */
  role?: string | null;
  /** Worksite or location where the recipient works */
  worksite?: string | null;
  /** Email address of the recipient */
  email?: string | null;
  /** Phone number of the recipient */
  phone?: string | null;
  /** Physical address of the recipient */
  address?: string | null;
  /** Additional notes about the recipient */
  notes?: string | null;
}

/**
 * Gift statistics and analytics
 */
export interface GiftStatistics {
  /** Total number of gifts sent */
  totalGifts: number;
  /** Number of gifts sent this month */
  giftsThisMonth: number;
  /** Number of gifts sent this year */
  giftsThisYear: number;
  /** Most popular occasions for gift giving */
  popularOccasions: Array<{
    occasion: string;
    count: number;
  }>;
  /** Recent gift activity */
  recentActivity: Array<{
    date: string;
    count: number;
  }>;
  /** Top recipients by gift count */
  topRecipients: Array<{
    recipientId: string;
    recipientName: string;
    giftCount: number;
  }>;
}

/**
 * Recipient statistics and analytics
 */
export interface RecipientStatistics {
  /** Total number of recipients */
  totalRecipients: number;
  /** Number of recipients added this month */
  recipientsThisMonth: number;
  /** Number of recipients added this year */
  recipientsThisYear: number;
  /** Recipients with the most gifts received */
  mostGifted: Array<{
    recipientId: string;
    recipientName: string;
    giftCount: number;
  }>;
}
