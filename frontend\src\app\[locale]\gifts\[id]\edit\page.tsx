'use client';

import { Gift } from 'lucide-react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import type { UpdateGiftData } from '@/lib/types/domain';

import { GiftForm } from '@/components/features/gifts';
import { AppBreadcrumb } from '@/components/ui/app-breadcrumb';
import { DataLoader, SkeletonLoader } from '@/components/ui/loading';
import { PageHeader } from '@/components/ui/PageHeader';
import { useGift, useUpdateGift } from '@/lib/stores/queries/useGifts';
import { useRecipientOptions } from '@/lib/stores/queries/useRecipients';

export default function EditGiftPage() {
  const params = useParams();
  const router = useRouter();
  const t = useTranslations('gifts');
  const tCommon = useTranslations('common');
  const tNavigation = useTranslations('navigation');

  const giftId = params.id as string;

  const { data: gift, isLoading, error } = useGift(giftId);
  const { data: recipientOptions = [] } = useRecipientOptions();
  const {
    mutateAsync: updateGift,
    isPending,
    error: updateError,
  } = useUpdateGift();

  const handleSubmit = async (data: UpdateGiftData) => {
    try {
      await updateGift({ id: giftId, data });
      router.push(`/gifts/${giftId}`);
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Error updating gift:', error);
    }
  };

  return (
    <div className="space-y-6">
      <AppBreadcrumb homeHref="/" homeLabel={tNavigation('dashboard')} />

      <DataLoader
        data={gift}
        error={error?.message || null}
        isLoading={isLoading}
        loadingComponent={<SkeletonLoader count={1} variant="card" />}
        emptyComponent={
          <div className="rounded-lg bg-card py-12 text-center shadow-md">
            <Gift className="mx-auto mb-6 size-16 text-muted-foreground" />
            <h3 className="mb-2 text-2xl font-semibold text-foreground">
              {t('giftNotFound')}
            </h3>
            <p className="mx-auto mb-6 mt-2 max-w-md text-muted-foreground">
              {t('giftNotFoundDescription')}
            </p>
          </div>
        }
      >
        {giftData => (
          <>
            <PageHeader
              description={t('editGiftDescription')}
              icon={Gift}
              title={t('editGift')}
            />

            {updateError && (
              <div className="rounded-md bg-red-100 p-3 text-red-500">
                {tCommon('error')}: {updateError.message}
              </div>
            )}

            <GiftForm
              initialData={giftData}
              isEditing={true}
              isLoading={isPending}
              onSubmit={handleSubmit}
              recipients={recipientOptions}
            />
          </>
        )}
      </DataLoader>
    </div>
  );
}
