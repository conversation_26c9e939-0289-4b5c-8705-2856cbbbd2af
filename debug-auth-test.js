#!/usr/bin/env node

/**
 * Debug Script: Test Authentication Flow
 * Purpose: Identify why /api/vehicles returns 401 Unauthorized
 */

const https = require('https');
const http = require('http');

const BASE_URL = 'http://**************:3001';

console.log('🔍 Testing Authentication Flow for /api/vehicles endpoint\n');

// Test credentials (replace with actual test user)
const TEST_CREDENTIALS = {
	email: '<EMAIL>', // Replace with your test user
	password: 'password123', // Replace with actual password
};

/**
 * Make HTTP request with proper error handling
 */
function makeRequest(options, data = null) {
	return new Promise((resolve, reject) => {
		const client = options.protocol === 'https:' ? https : http;

		const req = client.request(options, (res) => {
			let body = '';

			res.on('data', (chunk) => {
				body += chunk;
			});

			res.on('end', () => {
				resolve({
					statusCode: res.statusCode,
					headers: res.headers,
					body: body,
				});
			});
		});

		req.on('error', reject);

		if (data) {
			req.write(JSON.stringify(data));
		}

		req.end();
	});
}

/**
 * Extract cookies from Set-Cookie headers
 */
function extractCookies(headers) {
	const setCookieHeaders = headers['set-cookie'] || [];
	const cookies = {};

	setCookieHeaders.forEach((header) => {
		const parts = header.split(';')[0].split('=');
		if (parts.length === 2) {
			cookies[parts[0].trim()] = parts[1].trim();
		}
	});

	return cookies;
}

/**
 * Format cookies for Cookie header
 */
function formatCookies(cookies) {
	return Object.entries(cookies)
		.map(([key, value]) => `${key}=${value}`)
		.join('; ');
}

async function testAuthFlow() {
	try {
		console.log('1️⃣ Testing Backend Health Check');
		console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

		// Step 1: Health check
		const healthCheck = await makeRequest({
			hostname: '**************',
			port: 3001,
			path: '/api/health',
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
			},
		});

		console.log(`Status: ${healthCheck.statusCode}`);
		console.log(`Response: ${healthCheck.body}`);

		if (healthCheck.statusCode !== 200) {
			console.log('❌ Backend is not responding correctly');
			return;
		}

		console.log('\n2️⃣ Testing Unauthenticated /api/vehicles Request');
		console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

		// Step 2: Test unauthenticated request
		const unauthRequest = await makeRequest({
			hostname: '**************',
			port: 3001,
			path: '/api/vehicles',
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
			},
		});

		console.log(`Status: ${unauthRequest.statusCode}`);
		console.log(`Response: ${unauthRequest.body}`);

		if (unauthRequest.statusCode === 401) {
			console.log('✅ Expected 401 for unauthenticated request');
		}

		console.log('\n3️⃣ Testing Login Flow');
		console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

		// Step 3: Login
		const loginResponse = await makeRequest(
			{
				hostname: '**************',
				port: 3001,
				path: '/api/auth/login',
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
			},
			TEST_CREDENTIALS
		);

		console.log(`Login Status: ${loginResponse.statusCode}`);
		console.log(`Login Response: ${loginResponse.body}`);

		if (loginResponse.statusCode !== 200) {
			console.log('❌ Login failed - check credentials or backend');
			console.log(
				'💡 Update TEST_CREDENTIALS in this script with valid user credentials'
			);
			return;
		}

		// Extract cookies from login response
		const cookies = extractCookies(loginResponse.headers);
		console.log('🍪 Cookies received:', Object.keys(cookies));

		if (!cookies['sb-access-token']) {
			console.log('❌ No sb-access-token cookie received');
			return;
		}

		console.log(
			'\n4️⃣ Testing Authenticated /api/vehicles Request with Cookies'
		);
		console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

		// Step 4: Test authenticated request with cookies
		const authenticatedRequest = await makeRequest({
			hostname: '**************',
			port: 3001,
			path: '/api/vehicles',
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Cookie: formatCookies(cookies),
			},
		});

		console.log(`Authenticated Status: ${authenticatedRequest.statusCode}`);
		console.log(
			`Response: ${authenticatedRequest.body.substring(0, 500)}${
				authenticatedRequest.body.length > 500 ? '...' : ''
			}`
		);

		if (authenticatedRequest.statusCode === 200) {
			console.log('✅ Authentication with cookies working correctly!');
		} else if (authenticatedRequest.statusCode === 401) {
			console.log(
				'❌ Still getting 401 with cookies - authentication middleware issue'
			);
		}

		// Step 5: Test with Authorization header if we have token data
		let loginData;
		try {
			loginData = JSON.parse(loginResponse.body);
		} catch (e) {
			console.log('Could not parse login response');
		}

		if (
			loginData &&
			(loginData.session?.access_token || loginData.access_token)
		) {
			console.log('\n5️⃣ Testing with Authorization Header');
			console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

			const token = loginData.session?.access_token || loginData.access_token;

			const headerAuthRequest = await makeRequest({
				hostname: '**************',
				port: 3001,
				path: '/api/vehicles',
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${token}`,
				},
			});

			console.log(`Header Auth Status: ${headerAuthRequest.statusCode}`);
			console.log(
				`Response: ${headerAuthRequest.body.substring(0, 500)}${
					headerAuthRequest.body.length > 500 ? '...' : ''
				}`
			);

			if (headerAuthRequest.statusCode === 200) {
				console.log('✅ Authentication with Authorization header working!');
			} else {
				console.log('❌ Authorization header authentication also failing');
			}
		}

		console.log('\n📋 DIAGNOSIS SUMMARY');
		console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

		if (healthCheck.statusCode === 200) {
			console.log('✅ Backend is running and accessible');
		} else {
			console.log('❌ Backend health check failed');
		}

		if (loginResponse.statusCode === 200) {
			console.log('✅ Login endpoint working');
		} else {
			console.log('❌ Login endpoint not working - check credentials');
		}

		if (cookies['sb-access-token']) {
			console.log('✅ HttpOnly cookies being set correctly');
		} else {
			console.log('❌ HttpOnly cookies not being set');
		}

		console.log('\n💡 NEXT STEPS:');
		console.log('1. Ensure you have a valid test user account');
		console.log('2. Update TEST_CREDENTIALS in this script');
		console.log('3. Make sure backend JWT middleware is working');
		console.log('4. Check that the user has appropriate permissions');
	} catch (error) {
		console.error('❌ Test failed with error:', error.message);
	}
}

// Run the test
testAuthFlow();
