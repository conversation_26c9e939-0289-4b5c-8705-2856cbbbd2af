(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8450],{22346:(e,s,t)=>{"use strict";t.d(s,{w:()=>n});var a=t(95155),r=t(87489),l=t(12115),i=t(54036);let n=l.forwardRef((e,s)=>{let{className:t,decorative:l=!0,orientation:n="horizontal",...c}=e;return(0,a.jsx)(r.b,{className:(0,i.cn)("shrink-0 bg-border","horizontal"===n?"h-[1px] w-full":"h-full w-[1px]",t),decorative:l,orientation:n,ref:s,...c})});n.displayName=r.b.displayName},27659:(e,s,t)=>{"use strict";t.d(s,{OV:()=>a.<PERSON>Rout<PERSON>}),t(40283),t(92999);var a=t(45876)},30043:(e,s,t)=>{"use strict";t.r(s),t.d(s,{useModal:()=>l});var a=t(12115),r=t(26119);let l=()=>{let e=(0,r.n)(e=>e.isModalOpen),s=(0,r.n)(e=>e.modalContent),t=(0,r.n)(e=>e.openModal),l=(0,r.n)(e=>e.closeModal),i=(0,a.useCallback)(()=>{t("login")},[t]),n=(0,a.useCallback)(()=>{t("signup")},[t]),c=(0,a.useCallback)(()=>{t("settings")},[t]),o=(0,a.useCallback)(()=>{t("delegation-form")},[t]),d=(0,a.useCallback)(()=>{t("vehicle-details")},[t]),m=(0,a.useCallback)(()=>{t("task-assignment")},[t]),u=(0,a.useCallback)(()=>{t("employee-profile")},[t]),h=(0,a.useCallback)(t=>e&&s===t,[e,s]),x=(0,a.useCallback)(()=>({backdrop:"modal-backdrop",container:e?"modal-container-visible":"modal-container-hidden",content:"modal-content modal-content-".concat(s||"default"),overlay:e?"modal-overlay-visible":"modal-overlay-hidden"}),[e,s]),f=(0,a.useCallback)(()=>({"aria-describedby":s?"".concat(s,"-modal-description"):void 0,"aria-hidden":!e,"aria-labelledby":s?"".concat(s,"-modal-title"):void 0,"aria-modal":e,role:"dialog"}),[e,s]),p=(0,a.useCallback)(s=>{"Escape"===s.key&&e&&l()},[e,l]),g=(0,a.useCallback)(s=>{s.target===s.currentTarget&&e&&l()},[e,l]);return{closeModal:l,getModalAriaAttributes:f,getModalClasses:x,getModalTitle:(0,a.useCallback)(()=>{switch(s){case"delegation-form":return"Create Delegation";case"employee-profile":return"Employee Profile";case"login":return"Sign In";case"settings":return"Settings";case"signup":return"Create Account";case"task-assignment":return"Assign Task";case"vehicle-details":return"Vehicle Details";default:return"Modal"}},[s]),handleBackdropClick:g,handleEscapeKey:p,isModalOfTypeOpen:h,isModalOpen:e,isWorkHubModal:(0,a.useCallback)(()=>["delegation-form","employee-profile","task-assignment","vehicle-details"].includes(s||""),[s])(),modalContent:s,openDelegationFormModal:o,openEmployeeProfileModal:u,openLoginModal:i,openModal:t,openSettingsModal:c,openSignupModal:n,openTaskAssignmentModal:m,openVehicleDetailsModal:d}}},30257:(e,s,t)=>{"use strict";t.d(s,{RTLProvider:()=>n});var a=t(95155),r=t(46453),l=t(12115),i=t(56284);let n=e=>{let{children:s}=e,t=(0,r.Ym)(),n=(0,i.Jx)(t)?"rtl":"ltr";return(0,l.useEffect)(()=>{document.documentElement.dir=n,document.documentElement.lang=t,"rtl"===n?document.documentElement.classList.add("rtl"):document.documentElement.classList.remove("rtl"),document.documentElement.style.setProperty("--text-direction",n)},[n,t]),(0,a.jsx)(a.Fragment,{children:s})}},40879:(e,s,t)=>{"use strict";t.d(s,{dj:()=>u,oR:()=>m});var a=t(12115);let r=0,l=new Map,i=e=>{if(l.has(e))return;let s=setTimeout(()=>{l.delete(e),d({toastId:e,type:"REMOVE_TOAST"})},1e6);l.set(e,s)},n=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:t}=s;if(t)i(t);else for(let s of e.toasts)i(s.id);return{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)}}},c=[],o={toasts:[]};function d(e){for(let s of(o=n(o,e),c))s(o)}function m(e){let{...s}=e,t=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>d({toastId:t,type:"DISMISS_TOAST"});return d({toast:{...s,id:t,onOpenChange:e=>{e||a()},open:!0},type:"ADD_TOAST"}),{dismiss:a,id:t,update:e=>d({toast:{...e,id:t},type:"UPDATE_TOAST"})}}function u(){let[e,s]=a.useState(o);return a.useEffect(()=>(c.push(s),()=>{let e=c.indexOf(s);-1!==e&&c.splice(e,1)}),[e]),{...e,dismiss:e=>d({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:m}}},54165:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>u,Es:()=>x,L3:()=>f,c7:()=>h,lG:()=>c,rr:()=>p,zM:()=>o});var a=t(95155),r=t(15452),l=t(25318),i=t(12115),n=t(54036);let c=r.bL,o=r.l9,d=r.ZL;r.bm;let m=i.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.hJ,{className:(0,n.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),ref:s,...l})});m.displayName=r.hJ.displayName;let u=i.forwardRef((e,s)=>{let{children:t,className:i,...c}=e;return(0,a.jsxs)(d,{children:[(0,a.jsx)(m,{}),(0,a.jsxs)(r.UC,{className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",i),ref:s,...c,children:[t,(0,a.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(l.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=r.UC.displayName;let h=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...t})};h.displayName="DialogHeader";let x=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};x.displayName="DialogFooter";let f=i.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.hE,{className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",t),ref:s,...l})});f.displayName=r.hE.displayName;let p=i.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.VY,{className:(0,n.cn)("text-sm text-muted-foreground",t),ref:s,...l})});p.displayName=r.VY.displayName},56284:(e,s,t)=>{"use strict";t.d(s,{IB:()=>a,Jx:()=>l,L$:()=>i});let a=["en-US","ar-IQ"],r=["ar-IQ"];function l(e){return r.includes(e)}let i={"en-US":"English","ar-IQ":"العربية"}},69856:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,68413,23)),Promise.resolve().then(t.bind(t,46096)),Promise.resolve().then(t.bind(t,85692)),Promise.resolve().then(t.bind(t,30257)),Promise.resolve().then(t.bind(t,74512))},70647:(e,s,t)=>{"use strict";t.d(s,{DC:()=>o,qQ:()=>c});var a=t(27461),r=t(2602),l=t(55513),i=t(3695);let n=[],c=new a.E({defaultOptions:{mutations:{retry:0,retryDelay:1e3},queries:{gcTime:6e5,refetchInterval:!1,refetchOnMount:!0,refetchOnReconnect:!0,refetchOnWindowFocus:!0,retry:(e,s)=>!(s instanceof i.v3)&&!(s instanceof i.Dr)&&(!(s instanceof i.hD)||400!==s.status&&401!==s.status&&404!==s.status)&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:3e5}},mutationCache:new r.q({onError:e=>{e instanceof i.v3?console.error("Mutation Authentication Error:",e.message):e instanceof i.Dr?console.error("Mutation Network Error:",e.message):e instanceof i.hD?console.error("Mutation API Error (".concat(e.status,"):"),e.message,e.details):console.error("An unexpected mutation error occurred:",e)}}),queryCache:new l.$({onError:e=>{e instanceof i.v3?console.error("Authentication Error:",e.message):e instanceof i.Dr?console.error("Network Error:",e.message):e instanceof i.hD?console.error("API Error (".concat(e.status,"):"),e.message,e.details):console.error("An unexpected error occurred:",e)},onSuccess:(e,s)=>{let t=JSON.stringify(s.queryKey),a=Date.now()-(s.state.dataUpdatedAt||Date.now()),r="fetching"!==s.state.fetchStatus&&void 0!==s.state.data;n.push({cacheHit:r,duration:a,queryKey:t,timestamp:Date.now()}),n.length>1e3&&n.splice(0,n.length-1e3)}})}),o={prefetchDashboardData:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!e)return void console.warn("Authentication not ready, deferring dashboard data prefetch.");let{getGlobalAuthTokenProvider:s}=await Promise.resolve().then(t.bind(t,72248)),a=s();if(!a||!a())return void console.warn("No auth token available, skipping dashboard data prefetch.");let{delegationApiService:r,employeeApiService:l,taskApiService:i,vehicleApiService:n}=await t.e(3840).then(t.bind(t,43840)),o=[c.prefetchQuery({queryFn:()=>n.getAll(),queryKey:["vehicles"],staleTime:6e5}),c.prefetchQuery({queryFn:()=>i.getAll(),queryKey:["tasks"],staleTime:3e5}),c.prefetchQuery({queryFn:()=>l.getAll(),queryKey:["employees"],staleTime:6e5}),c.prefetchQuery({queryFn:()=>r.getAll(),queryKey:["delegations"],staleTime:3e5})];await Promise.allSettled(o)},prefetchTaskManagementData:async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(!e)return void console.warn("Authentication not ready, deferring task management data prefetch.");let{employeeApiService:s,taskApiService:a,vehicleApiService:r}=await t.e(3840).then(t.bind(t,43840)),l=[c.prefetchQuery({queryFn:()=>a.getAll(),queryKey:["tasks"],staleTime:3e5}),c.prefetchQuery({queryFn:()=>s.getAll(),queryKey:["employees"],staleTime:6e5}),c.prefetchQuery({queryFn:()=>r.getAll(),queryKey:["vehicles"],staleTime:6e5})];await Promise.allSettled(l)},prefetchVehicleDetails:async function(e){let s=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(!s)return void console.warn("Authentication not ready, deferring vehicle details prefetch.");let{vehicleApiService:a}=await t.e(3840).then(t.bind(t,43840));await c.prefetchQuery({queryFn:()=>a.getById(e),queryKey:["vehicles",e],staleTime:6e5})}}},74512:(e,s,t)=>{"use strict";t.d(s,{CSPProvider:()=>i,s5:()=>d,oD:()=>o,dD:()=>c});var a=t(95155),r=t(12115);let l=(0,r.createContext)({nonce:null,isStrictCSP:!1,violationCount:0,isNonceValid:!1,reportViolation:()=>{},getSecureNonce:()=>null,resetViolationCount:()=>{}});function i(e){let{children:s,nonce:t}=e,[i,n]=(0,r.useState)(0),c=!!t&&function(e){let s=e.length>=16,t=/^[A-Za-z0-9+/]+=*$/.test(e),a=!/(.)\1{3,}/.test(e);return s&&t&&a}(t),o=c&&!"production".includes("development");return(0,a.jsx)(l.Provider,{value:{nonce:t,isStrictCSP:o,violationCount:i,isNonceValid:c,reportViolation:e=>{n(e=>e+1),fetch("/api/csp-report",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({"csp-report":{"document-uri":e.documentURI,"violated-directive":e.violatedDirective,"blocked-uri":e.blockedURI,"effective-directive":e.effectiveDirective,"original-policy":e.originalPolicy,"source-file":e.sourceFile||"","line-number":e.lineNumber||0,"column-number":e.columnNumber||0,referrer:document.referrer,disposition:"enforce","status-code":200,"script-sample":"",timestamp:new Date().toISOString(),"user-agent":navigator.userAgent,"violation-count":i+1,"nonce-valid":c,"strict-csp":o}})}).catch(e=>{console.error("Failed to report CSP violation:",e)})},getSecureNonce:()=>c?t:null,resetViolationCount:()=>{n(0)}},children:s})}function n(){let e=(0,r.useContext)(l);if(!e)throw Error("useCSP must be used within a CSPProvider");return e}function c(){let{nonce:e}=n();return e}function o(){let{reportViolation:e}=n();return e}function d(e){document.addEventListener("securitypolicyviolation",s=>{e({violatedDirective:s.violatedDirective,blockedURI:s.blockedURI,documentURI:s.documentURI,effectiveDirective:s.effectiveDirective,originalPolicy:s.originalPolicy,sourceFile:s.sourceFile,lineNumber:s.lineNumber,columnNumber:s.columnNumber})}),console.log("\uD83D\uDD12 CSP violation reporting initialized")}},80333:(e,s,t)=>{"use strict";t.d(s,{d:()=>n});var a=t(95155),r=t(4884),l=t(12115),i=t(54036);let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.bL,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...l,ref:s,children:(0,a.jsx)(r.zi,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});n.displayName=r.bL.displayName},85692:(e,s,t)=>{"use strict";t.d(s,{default:()=>eq});var a=t(95155),r=t(26715),l=t(35695),i=t(40283),n=t(27659),c=t(28341),o=t(25318),d=t(12115),m=t(18186),u=t(5263),h=t(28328),x=t(3638),f=t(3235),p=t(2160),g=t(50286),b=t(57082),j=t(35079),v=t(95120),y=t(18271),N=t(83173),w=t(75074),k=t(79556),A=t(73158),C=t(17652),z=t(6874),S=t.n(z),D=t(30285),T=t(62523),E=t(54036);let P=(e,s)=>"/"===e?"/".concat(s):"/".concat(s).concat(e),O=(e,s)=>[{defaultOpen:!0,items:[{href:P("/",s),icon:m.A,label:e("dashboard")},{href:P("/reports",s),icon:u.A,isNew:!0,label:e("analytics")}],title:e("sections.overview")},{defaultOpen:!0,items:[{href:P("/vehicles",s),icon:h.A,label:e("fleet")},{href:P("/service-history",s),icon:x.A,label:e("maintenance")},{href:P("/service-records",s),icon:f.A,label:e("serviceRecords")},{href:P("/gifts",s),icon:p.A,label:e("gifts")},{href:P("/recipients",s),icon:g.A,label:e("recipients")}],title:e("sections.operations")},{defaultOpen:!1,items:[{href:P("/employees",s),icon:g.A,label:e("teamMembers")},{href:P("/delegations",s),icon:b.A,label:e("projects")},{href:P("/tasks",s),icon:j.A,label:e("tasks")}],title:e("sections.workforce")},{defaultOpen:!1,items:[{href:P("/reliability",s),icon:v.A,label:e("monitoring")},{href:P("/admin",s),icon:y.A,label:e("administration")}],title:e("sections.system")}],R=e=>{let{className:s,collapsed:t=!1}=e,r=(0,l.usePathname)(),i=(0,l.useParams)(),[n,c]=(0,d.useState)(""),o=(0,C.c3)("navigation"),m=i.locale||"en-US",u=O(o,m),[h,x]=(0,d.useState)(new Set(u.filter(e=>e.defaultOpen).map(e=>e.title))),f=e=>{let s=new Set(h);s.has(e)?s.delete(e):s.add(e),x(s)},p=u.map(e=>({...e,items:e.items.filter(e=>e.label.toLowerCase().includes(n.toLowerCase()))})).filter(e=>e.items.length>0),g=e=>"/"===e?"/"===r:null==r?void 0:r.startsWith(e);return t?(0,a.jsxs)("aside",{className:(0,E.cn)("flex w-16 flex-col border-r bg-background",s),children:[(0,a.jsx)("div",{className:"flex h-16 items-center justify-center border-b",children:(0,a.jsx)(S(),{className:"flex items-center",href:P("/",m),children:(0,a.jsx)(N.A,{className:"size-6 text-primary"})})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsx)("div",{className:"space-y-2 p-2",children:u.flatMap(e=>e.items).map(e=>(0,a.jsx)(D.$,{asChild:!0,className:"size-12",size:"icon",variant:g(e.href)?"secondary":"ghost",children:(0,a.jsx)(S(),{href:e.href,title:e.label,children:(0,a.jsx)(e.icon,{className:"size-5"})})},e.href))})})]}):(0,a.jsxs)("aside",{className:(0,E.cn)("flex w-64 flex-col border-r bg-background",s),children:[(0,a.jsx)("div",{className:"flex h-16 items-center border-b px-6",children:(0,a.jsxs)(S(),{className:"flex items-center space-x-2 text-lg font-semibold transition-opacity hover:opacity-80",href:P("/",m),children:[(0,a.jsx)(N.A,{className:"size-6 text-primary"}),(0,a.jsx)("span",{children:"WorkHub"})]})}),(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(w.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(T.p,{className:"pl-9",onChange:e=>c(e.target.value),placeholder:o("search.placeholder"),value:n})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsx)("div",{className:"space-y-2 p-4",children:p.map(e=>{let s=h.has(e.title);return e.items.length>0?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)(D.$,{className:"w-full justify-between px-2 py-1.5 text-xs font-medium uppercase text-muted-foreground hover:text-foreground",onClick:()=>f(e.title),variant:"ghost",children:[(0,a.jsx)("span",{children:e.title}),s?(0,a.jsx)(k.A,{className:"size-3"}):(0,a.jsx)(A.A,{className:"size-3"})]}),s&&(0,a.jsx)("div",{className:"ml-2 space-y-1",children:e.items.map(e=>(0,a.jsx)(D.$,{asChild:!0,className:"w-full justify-start px-3 py-2",variant:g(e.href)?"secondary":"ghost",children:(0,a.jsxs)(S(),{className:"flex items-center space-x-3",href:e.href,children:[(0,a.jsx)(e.icon,{className:"size-4"}),(0,a.jsx)("span",{className:"flex-1",children:e.label}),e.isNew&&(0,a.jsx)("span",{className:"rounded bg-primary px-1.5 py-0.5 text-xs text-primary-foreground",children:o("badges.new")}),e.badge&&(0,a.jsx)("span",{className:"rounded bg-muted px-1.5 py-0.5 text-xs",children:e.badge})]})},e.href))})]},e.title):null})})}),(0,a.jsx)("div",{className:"border-t p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground",children:[(0,a.jsx)("span",{children:"v2.1.0"}),(0,a.jsx)(S(),{className:"hover:text-foreground",href:P("/settings",m),children:"Settings"})]})})]})};var I=t(29360),M=t(36936),q=t(68027),F=t(51362),L=t(44838),U=t(85187);function $(){let{setTheme:e,systemTheme:s,theme:t}=(0,F.D)(),{currentTheme:r,setTheme:l}=(0,U.useTheme)(),[i,n]=d.useState(!1);d.useEffect(()=>{n(!0)},[]);let c=t=>{e(t),"system"===t?l(s||"light"):l(t)},o=t||r,m="dark"===("system"===o?s:o);return i?(0,a.jsxs)(L.rI,{children:[(0,a.jsx)(L.ty,{asChild:!0,children:(0,a.jsxs)(D.$,{"aria-label":"Current theme: ".concat(o,". Click to change theme"),className:"text-foreground hover:bg-accent hover:text-accent-foreground",size:"icon",variant:"ghost",children:[(0,a.jsx)(M.A,{className:"size-[1.2rem] transition-all duration-300 ".concat(m?"rotate-90 scale-0":"rotate-0 scale-100")}),(0,a.jsx)(q.A,{className:"absolute size-[1.2rem] transition-all duration-300 ".concat(m?"rotate-0 scale-100":"rotate-90 scale-0")}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,a.jsxs)(L.SQ,{align:"end",className:"min-w-32",children:[(0,a.jsxs)(L._2,{className:"light"===o?"bg-accent text-accent-foreground":"",onClick:()=>c("light"),children:[(0,a.jsx)(M.A,{className:"mr-2 size-4"}),"Light"]}),(0,a.jsxs)(L._2,{className:"dark"===o?"bg-accent text-accent-foreground":"",onClick:()=>c("dark"),children:[(0,a.jsx)(q.A,{className:"mr-2 size-4"}),"Dark"]}),(0,a.jsxs)(L._2,{className:"system"===o?"bg-accent text-accent-foreground":"",onClick:()=>c("system"),children:[(0,a.jsx)(v.A,{className:"mr-2 size-4"}),"System"]})]})]}):(0,a.jsx)(D.$,{className:"text-foreground",size:"icon",variant:"ghost",children:(0,a.jsx)("div",{className:"size-[1.2rem]"})})}var _=t(57679),V=t(15873);let Q=[{code:"en-US",name:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"ar-IQ",name:"العربية",flag:"\uD83C\uDDEE\uD83C\uDDF6"}];function W(e){let{variant:s="ghost",size:t="default",iconOnly:r=!1,className:i}=e,n=(0,l.useRouter)(),c=(0,l.usePathname)(),o=(0,l.useParams)(),d=(0,C.c3)("common"),m=o.locale||"en-US",u=Q.find(e=>e.code===m)||Q[0],h=e=>{if(e===m)return;let s=c.replace("/".concat(m),"")||"/",t="/".concat(e).concat(s);n.push(t)},x=r?(0,a.jsx)(_.A,{className:"h-4 w-4"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(V.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline-block",children:u.name}),(0,a.jsx)("span",{className:"sm:hidden",children:u.flag})]});return(0,a.jsxs)(L.rI,{children:[(0,a.jsx)(L.ty,{asChild:!0,children:(0,a.jsx)(D.$,{variant:s,size:t,className:i,"aria-label":d("settings"),children:x})}),(0,a.jsx)(L.SQ,{align:"end",className:"w-48",children:Q.map(e=>(0,a.jsxs)(L._2,{onClick:()=>h(e.code),className:"flex items-center gap-2 ".concat(m===e.code?"bg-accent":""),children:[(0,a.jsx)("span",{className:"text-lg",children:e.flag}),(0,a.jsx)("span",{children:e.name}),m===e.code&&(0,a.jsx)("span",{className:"ml-auto text-xs text-muted-foreground",children:"✓"})]},e.code))})]})}var K=t(36846);let Z=e=>{let{children:s,className:t}=e,[r,l]=(0,d.useState)(!1),[i,n]=(0,d.useState)(!1),{fontSize:m}=(0,K.useUiPreferences)();return(0,a.jsxs)("div",{className:(0,E.cn)("flex h-screen bg-background","small"===m&&"text-sm","large"===m&&"text-lg",t),children:[i&&(0,a.jsx)("div",{className:"fixed inset-0 z-50 bg-black/50 lg:hidden",onClick:()=>n(!1)}),(0,a.jsx)("div",{className:(0,E.cn)("fixed inset-y-0 left-0 z-50 lg:static lg:z-auto",i?"translate-x-0":"-translate-x-full lg:translate-x-0","transition-transform duration-200 ease-in-out"),children:(0,a.jsx)(R,{collapsed:r,className:"h-full"})}),(0,a.jsxs)("div",{className:"flex flex-1 flex-col overflow-hidden",children:[(0,a.jsxs)("header",{className:"flex h-16 items-center justify-between border-b bg-background px-4 lg:px-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(D.$,{variant:"ghost",size:"icon",className:"lg:hidden",onClick:()=>{n(!i)},children:[(0,a.jsx)(c.A,{className:"size-5"}),(0,a.jsx)("span",{className:"sr-only",children:"Open navigation menu"})]}),(0,a.jsxs)(D.$,{variant:"ghost",size:"icon",className:"hidden lg:flex",onClick:()=>{l(!r)},children:[r?(0,a.jsx)(c.A,{className:"size-5"}):(0,a.jsx)(o.A,{className:"size-5"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle sidebar"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(W,{}),(0,a.jsx)($,{}),(0,a.jsx)(I.F,{variant:"dropdown"})]})]}),(0,a.jsx)("main",{className:"flex-1 overflow-auto",children:(0,a.jsx)("div",{className:"h-full p-6",children:s})})]})]})};var B=t(82733),H=t(73350),Y=t(36931),J=t(58543),G=t(8531),X=t(26126),ee=t(66695),es=t(54165),et=t(22346),ea=t(80333),er=t(30043),el=t(27945),ei=t(66866);let en=()=>{let{closeModal:e,isModalOpen:s,modalContent:t}=(0,er.useModal)(),{dashboardLayout:r,fontSize:l,getAllPreferences:i,notificationsEnabled:n,resetPreferences:c,setDashboardLayout:o,setFontSize:d,setTableDensity:m,tableDensity:u,toggleNotifications:h}=(0,K.useUiPreferences)(),{currentTheme:x,setTheme:f}=(0,U.useTheme)(),{setTheme:p,systemTheme:g,theme:b}=(0,F.D)(),j=e=>{p(e),"system"===e?f(g||"light"):f(e)};return(0,a.jsx)(es.lG,{onOpenChange:s=>!s&&e(),open:s&&"settings"===t,children:(0,a.jsxs)(es.Cf,{className:"max-h-[90vh] max-w-4xl overflow-y-auto",children:[(0,a.jsxs)(es.c7,{children:[(0,a.jsxs)(es.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"size-5"}),"Application Settings"]}),(0,a.jsx)(es.rr,{children:"Customize your WorkHub experience with these preferences"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"mb-4 flex items-center gap-2 text-lg font-semibold",children:[(0,a.jsx)(B.A,{className:"size-5"}),"Theme Preferences"]}),(0,a.jsx)(ei.LG,{})]}),(0,a.jsx)(et.w,{}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"mb-4 flex items-center gap-2 text-lg font-semibold",children:[(0,a.jsx)(H.A,{className:"size-5"}),"Display Preferences"]}),(0,a.jsx)(el.uq,{})]}),(0,a.jsx)(et.w,{}),(0,a.jsxs)(ee.Zp,{children:[(0,a.jsxs)(ee.aR,{children:[(0,a.jsxs)(ee.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(Y.A,{className:"size-5"}),"Notifications"]}),(0,a.jsx)(ee.BT,{children:"Control how you receive notifications and alerts"})]}),(0,a.jsx)(ee.Wu,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Enable Notifications"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive system notifications and updates"})]}),(0,a.jsx)(ea.d,{checked:n,onCheckedChange:h})]})})]}),(0,a.jsxs)(ee.Zp,{children:[(0,a.jsxs)(ee.aR,{children:[(0,a.jsxs)(ee.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(J.A,{className:"size-5"}),"Layout Preferences"]}),(0,a.jsx)(ee.BT,{children:"Customize the layout and density of interface elements"})]}),(0,a.jsxs)(ee.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"mb-3 font-medium",children:"Table Density"}),(0,a.jsx)("div",{className:"flex gap-2",children:["compact","comfortable","spacious"].map(e=>(0,a.jsx)(D.$,{className:"capitalize",onClick:()=>m(e),size:"sm",variant:u===e?"default":"outline",children:e},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"mb-3 font-medium",children:"Dashboard Layout"}),(0,a.jsx)("div",{className:"flex gap-2",children:["grid","list","cards"].map(e=>(0,a.jsx)(D.$,{className:"capitalize",onClick:()=>o(e),size:"sm",variant:r===e?"default":"outline",children:e},e))})]})]})]}),(0,a.jsxs)(ee.Zp,{children:[(0,a.jsxs)(ee.aR,{children:[(0,a.jsx)(ee.ZB,{children:"Current Settings Summary"}),(0,a.jsx)(ee.BT,{children:"Overview of your current preferences"})]}),(0,a.jsx)(ee.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Theme"}),(0,a.jsx)(X.E,{className:"capitalize",variant:"secondary",children:b||"system"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Font Size"}),(0,a.jsx)(X.E,{className:"capitalize",variant:"secondary",children:l})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Notifications"}),(0,a.jsx)(X.E,{variant:n?"default":"secondary",children:n?"Enabled":"Disabled"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Table Density"}),(0,a.jsx)(X.E,{className:"capitalize",variant:"secondary",children:u})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Dashboard Layout"}),(0,a.jsx)(X.E,{className:"capitalize",variant:"secondary",children:r})]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between border-t pt-4",children:[(0,a.jsxs)(D.$,{className:"flex items-center gap-2",onClick:()=>{c(),j("system")},variant:"outline",children:[(0,a.jsx)(G.A,{className:"size-4"}),"Reset to Defaults"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(D.$,{onClick:e,variant:"outline",children:"Cancel"}),(0,a.jsx)(D.$,{onClick:e,children:"Save Changes"})]})]})]})]})})};var ec=t(96016);function eo(e){let{children:s,...t}=e;return(0,a.jsxs)(F.N,{...t,children:[(0,a.jsx)(ed,{}),s]})}function ed(){let{setTheme:e,systemTheme:s,theme:t}=(0,F.D)(),{currentTheme:a,setTheme:r}=(0,ec.C)(),[l,i]=d.useState(!1);return(0,d.useEffect)(()=>{if(!l&&t){i(!0);let e="system"===t?s:t;e&&"system"!==e&&e!==a&&r(e)}},[t,s,a,r,l]),(0,d.useEffect)(()=>{l&&a&&a!==t&&"system"!==t&&a!==("system"===t?s:t)&&e(a)},[a,t,s,e,l]),null}var em=t(11133),eu=t(8376),eh=t(31949),ex=t(50594);let ef=e=>{switch(e){case"delegation-update":return(0,a.jsx)(b.A,{className:"size-4"});case"employee-update":return(0,a.jsx)(g.A,{className:"size-4"});case"error":return(0,a.jsx)(em.A,{className:"size-4"});case"success":case"task-assigned":return(0,a.jsx)(eu.A,{className:"size-4"});case"vehicle-maintenance":return(0,a.jsx)(h.A,{className:"size-4"});case"warning":return(0,a.jsx)(eh.A,{className:"size-4"});default:return(0,a.jsx)(ex.A,{className:"size-4"})}},ep=e=>{switch(e){case"delegation-update":return"border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-200";case"employee-update":return"border-teal-200 bg-teal-50 text-teal-800 dark:border-teal-800 dark:bg-teal-950 dark:text-teal-200";case"error":return"border-red-200 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-950 dark:text-red-200";case"success":return"border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200";case"task-assigned":return"border-indigo-200 bg-indigo-50 text-indigo-800 dark:border-indigo-800 dark:bg-indigo-950 dark:text-indigo-200";case"vehicle-maintenance":return"border-purple-200 bg-purple-50 text-purple-800 dark:border-purple-800 dark:bg-purple-950 dark:text-purple-200";case"warning":return"border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-200";default:return"border-gray-200 bg-gray-50 text-gray-800 dark:border-gray-800 dark:bg-gray-950 dark:text-gray-200"}},eg=e=>{switch(e){case"error":return"destructive";case"success":return"default";case"warning":return"secondary";default:return"outline"}},eb=()=>{let{clearAllNotifications:e,markNotificationAsRead:s,notifications:t,removeNotification:r,unreadNotificationCount:l}=(0,ec.C)();return 0===t.length?null:(0,a.jsxs)("div",{className:"fixed right-4 top-4 z-50 w-96 max-w-sm space-y-2",children:[t.length>1&&(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Notifications"}),l()>0&&(0,a.jsx)(X.E,{className:"text-xs",variant:"destructive",children:l()})]}),(0,a.jsx)(D.$,{className:"text-xs",onClick:e,size:"sm",variant:"ghost",children:"Clear All"})]}),(0,a.jsx)("div",{className:"max-h-96 space-y-2 overflow-y-auto",children:t.slice(-5).map(e=>(0,a.jsx)(ee.Zp,{className:(0,E.cn)("relative transition-all duration-300 hover:shadow-md",ep(e.type),!e.read&&"ring-2 ring-offset-2 ring-offset-background"),children:(0,a.jsx)(ee.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"mt-0.5 shrink-0",children:ef(e.type)}),(0,a.jsx)("div",{className:"min-w-0 flex-1",children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(X.E,{className:"mb-1 text-xs",variant:eg(e.type),children:e.type.replace("-"," ")}),(0,a.jsx)("p",{className:"text-sm font-medium leading-tight",children:e.message}),(0,a.jsx)("p",{className:"mt-1 text-xs opacity-75",children:new Date(e.timestamp).toLocaleString()}),(e.category||e.actionUrl)&&(0,a.jsxs)("div",{className:"mt-2 flex items-center gap-2",children:[e.category&&(0,a.jsx)(X.E,{className:"text-xs",variant:"outline",children:e.category}),e.actionUrl&&(0,a.jsx)(D.$,{className:"h-auto p-0 text-xs",onClick:()=>window.open(e.actionUrl,"_blank"),size:"sm",variant:"link",children:"View Details"})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,a.jsx)(D.$,{className:"size-6 p-0",onClick:()=>r(e.id),size:"sm",variant:"ghost",children:(0,a.jsx)(o.A,{className:"size-3"})}),!e.read&&(0,a.jsx)(D.$,{className:"size-6 p-0",onClick:()=>s(e.id),size:"sm",title:"Mark as read",variant:"ghost",children:(0,a.jsx)(eu.A,{className:"size-3"})})]})]})})]})})},e.id))})]})};var ej=t(26621),ev=t(74466);let ey=ej.Kq,eN=d.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(ej.LM,{className:(0,E.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",t),ref:s,...r})});eN.displayName=ej.LM.displayName;let ew=(0,ev.F)("group grid items-center gap-1 rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-10 data-[state=open]:fade-in-10 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[swipe=end]:slide-out-to-right-full data-[swipe=start]:slide-out-to-left-full",{defaultVariants:{variant:"default"},variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}}}),ek=d.forwardRef((e,s)=>{let{className:t,variant:r,...l}=e;return(0,a.jsx)(ej.bL,{className:(0,E.cn)(ew({variant:r}),t),ref:s,...l})});ek.displayName=ej.bL.displayName,d.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(ej.rc,{className:(0,E.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",t),ref:s,...r})}).displayName=ej.rc.displayName;let eA=d.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(ej.bm,{className:(0,E.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",t),ref:s,"toast-close":"",...r,children:(0,a.jsx)(o.A,{className:"size-4"})})});eA.displayName=ej.bm.displayName;let eC=d.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(ej.hE,{className:(0,E.cn)("text-sm font-semibold",t),ref:s,...r})});eC.displayName=ej.hE.displayName;let ez=d.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(ej.VY,{className:(0,E.cn)("text-sm opacity-90",t),ref:s,...r})});ez.displayName=ej.VY.displayName;var eS=t(40879);function eD(){let{toasts:e}=(0,eS.dj)();return(0,a.jsxs)(ey,{children:[e.map(function(e){let{action:s,description:t,id:r,title:l,...i}=e;return(0,a.jsxs)(ek,{...i,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[l&&(0,a.jsx)(eC,{children:l}),t&&(0,a.jsx)(ez,{children:t})]}),s&&d.isValidElement(s)?s:null,(0,a.jsx)(eA,{})]},r)}),(0,a.jsx)(eN,{})]})}var eT=t(80659),eE=t(15300),eP=t(77381);let eO=e=>{let{className:s=""}=e,[t,r]=(0,d.useState)(!1),i=(0,l.usePathname)();if(null==i?void 0:i.startsWith("/reports"))return null;let n=[{href:"/reports",icon:u.A,label:"Dashboard",description:"Main Analytics",color:"bg-blue-600 hover:bg-blue-700"},{href:"/reports/analytics",icon:eT.A,label:"Analytics",description:"Advanced Insights",color:"bg-purple-600 hover:bg-purple-700"},{href:"/reports/data",icon:eE.A,label:"Data Tables",description:"Raw Data View",color:"bg-green-600 hover:bg-green-700"}];return(0,a.jsxs)("div",{className:(0,E.cn)("fixed bottom-6 right-6 z-50",s),children:[t&&(0,a.jsx)("div",{className:"mb-4 space-y-3",children:n.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-3 animate-in slide-in-from-bottom-2 duration-200",style:{animationDelay:"".concat(50*s,"ms")},children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border px-3 py-2 text-sm",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 dark:text-gray-100",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.description})]}),(0,a.jsx)(D.$,{asChild:!0,size:"lg",className:(0,E.cn)("h-12 w-12 rounded-full shadow-lg border-0 text-white",e.color),children:(0,a.jsxs)(S(),{href:e.href,children:[(0,a.jsx)(e.icon,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"sr-only",children:e.label})]})})]},e.href))}),(0,a.jsxs)(D.$,{onClick:()=>r(!t),size:"lg",className:(0,E.cn)("h-14 w-14 rounded-full shadow-lg transition-all duration-200",t?"bg-red-600 hover:bg-red-700 rotate-45":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"),children:[t?(0,a.jsx)(o.A,{className:"h-6 w-6 text-white"}):(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-white"}),(0,a.jsx)(eP.A,{className:"h-3 w-3 text-white -mt-1"})]}),(0,a.jsx)("span",{className:"sr-only",children:t?"Close quick access menu":"Open reporting quick access"})]}),!t&&(0,a.jsx)(X.E,{variant:"destructive",className:"absolute -top-2 -right-2 text-xs px-1.5 py-0.5 animate-pulse",children:"NEW"})]})};var eR=t(70647),eI=t(52747),eM=t(74512);function eq(e){let{children:s}=e;(0,eM.dD)();let t=(0,eM.oD)();return(0,d.useEffect)(()=>{(0,eM.s5)(t)},[t]),(0,a.jsx)(eo,{attribute:"class",defaultTheme:"system",disableTransitionOnChange:!0,enableSystem:!0,children:(0,a.jsxs)(r.Ht,{client:eR.qQ,children:[(0,a.jsx)(i.AuthProvider,{children:(0,a.jsx)(eI.sf,{children:(0,a.jsx)(eF,{children:s})})}),(0,a.jsx)(eb,{}),(0,a.jsx)(en,{}),(0,a.jsx)(eO,{})]})})}function eF(e){let{children:s}=e,t=(0,l.usePathname)(),{isInitialized:r,loading:c,user:o}=(0,i.useAuthContext)(),{getFontSizeClass:m}=(0,K.useUiPreferences)(),u=["/auth-test","/supabase-diagnostics","/login"].some(e=>null==t?void 0:t.startsWith(e)),h=["/login"].some(e=>null==t?void 0:t.startsWith(e)),x=(0,d.useMemo)(()=>r&&!c&&!!o,[r,c,null==o?void 0:o.id]);return((0,d.useEffect)(()=>{x&&"/"===t&&(console.log("Authentication ready, triggering dashboard data prefetch."),eR.DC.prefetchDashboardData(x).catch(e=>{console.warn("Failed to prefetch dashboard data:",e)}))},[x,t]),h)?(0,a.jsxs)(a.Fragment,{children:[s,(0,a.jsx)(eD,{})]}):u?(0,a.jsxs)("div",{className:"app-layout ".concat(m()),children:[(0,a.jsx)(Z,{children:s}),(0,a.jsx)(eD,{}),(0,a.jsx)("footer",{className:"no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground",children:(0,a.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," WorkHub. All rights reserved."]})})]}):(0,a.jsx)("div",{className:"app-layout ".concat(m()),children:(0,a.jsxs)(n.OV,{requireEmailVerification:!0,children:[(0,a.jsx)(Z,{children:s}),(0,a.jsx)(eD,{}),(0,a.jsx)("footer",{className:"no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground",children:(0,a.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," WorkHub. All rights reserved."]})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[9484,9268,6476,7047,6897,3860,375,7876,6874,5247,6453,1727,6769,5487,87,5051,4036,4767,2999,6750,6979,1978,8441,1684,7358],()=>s(69856)),_N_E=e.O()}]);