"use strict";exports.id=742,exports.ids=[742],exports.modules={6211:(e,s,a)=>{a.d(s,{A0:()=>n,BF:()=>c,Hj:()=>d,XI:()=>i,nA:()=>m,nd:()=>o});var l=a(60687),t=a(43210),r=a(22482);let i=t.forwardRef(({className:e,...s},a)=>(0,l.jsx)("div",{className:"relative w-full overflow-auto",children:(0,l.jsx)("table",{className:(0,r.cn)("w-full caption-bottom text-sm",e),ref:a,...s})}));i.displayName="Table";let n=t.forwardRef(({className:e,...s},a)=>(0,l.jsx)("thead",{className:(0,r.cn)("[&_tr]:border-b",e),ref:a,...s}));n.displayName="TableHeader";let c=t.forwardRef(({className:e,...s},a)=>(0,l.jsx)("tbody",{className:(0,r.cn)("[&_tr:last-child]:border-0",e),ref:a,...s}));c.displayName="TableBody",t.forwardRef(({className:e,...s},a)=>(0,l.jsx)("tfoot",{className:(0,r.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),ref:a,...s})).displayName="TableFooter";let d=t.forwardRef(({className:e,...s},a)=>(0,l.jsx)("tr",{className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),ref:a,...s}));d.displayName="TableRow";let o=t.forwardRef(({className:e,...s},a)=>(0,l.jsx)("th",{className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),ref:a,...s}));o.displayName="TableHead";let m=t.forwardRef(({className:e,...s},a)=>(0,l.jsx)("td",{className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),ref:a,...s}));m.displayName="TableCell",t.forwardRef(({className:e,...s},a)=>(0,l.jsx)("caption",{className:(0,r.cn)("mt-4 text-sm text-muted-foreground",e),ref:a,...s})).displayName="TableCaption"},15079:(e,s,a)=>{a.d(s,{bq:()=>x,eb:()=>u,gC:()=>j,l6:()=>o,yv:()=>m});var l=a(60687),t=a(22670),r=a(61662),i=a(89743),n=a(58450),c=a(43210),d=a(22482);let o=t.bL;t.YJ;let m=t.WT,x=c.forwardRef(({children:e,className:s,...a},i)=>(0,l.jsxs)(t.l9,{className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),ref:i,...a,children:[e,(0,l.jsx)(t.In,{asChild:!0,children:(0,l.jsx)(r.A,{className:"size-4 opacity-50"})})]}));x.displayName=t.l9.displayName;let h=c.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.PP,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),ref:a,...s,children:(0,l.jsx)(i.A,{className:"size-4"})}));h.displayName=t.PP.displayName;let f=c.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.wn,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),ref:a,...s,children:(0,l.jsx)(r.A,{className:"size-4"})}));f.displayName=t.wn.displayName;let j=c.forwardRef(({children:e,className:s,position:a="popper",...r},i)=>(0,l.jsx)(t.ZL,{children:(0,l.jsxs)(t.UC,{className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:a,ref:i,...r,children:[(0,l.jsx)(h,{}),(0,l.jsx)(t.LM,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),(0,l.jsx)(f,{})]})}));j.displayName=t.UC.displayName,c.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.JU,{className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),ref:a,...s})).displayName=t.JU.displayName;let u=c.memo(c.forwardRef(({children:e,className:s,...a},r)=>{let i=c.useCallback(e=>{"function"==typeof r?r(e):r&&(r.current=e)},[r]);return(0,l.jsxs)(t.q7,{className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),ref:i,...a,children:[(0,l.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,l.jsx)(t.VF,{children:(0,l.jsx)(n.A,{className:"size-4"})})}),(0,l.jsx)(t.p4,{children:e})]})}));u.displayName=t.q7.displayName,c.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.wv,{className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),ref:a,...s})).displayName=t.wv.displayName},19484:(e,s,a)=>{a.d(s,{BZ:()=>C,K:()=>w,Wy:()=>S,YB:()=>y,YW:()=>N,ZI:()=>b,e7:()=>k,nh:()=>z,vk:()=>v,yX:()=>A});var l=a(60687),t=a(75699),r=a(69795),i=a(76311),n=a(35137),c=a(5068),d=a(90357),o=a(65456),m=a(72322),x=a(85814),h=a.n(x),f=a(96834),j=a(29523),u=a(56896),p=a(21342),g=a(22482);let N=(e,s,a)=>({createActionsColumn:a=>({cell:({row:e})=>{let t=e.original;return(0,l.jsxs)(p.rI,{children:[(0,l.jsx)(p.ty,{asChild:!0,children:(0,l.jsxs)(j.$,{className:"size-8 p-0",variant:"ghost",children:[(0,l.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,l.jsx)(r.A,{className:"size-4"})]})}),(0,l.jsxs)(p.SQ,{align:"end",children:[(0,l.jsx)(p.lp,{children:s("actions")}),(a.onView||a.viewHref)&&(0,l.jsx)(l.Fragment,{children:a.viewHref?(0,l.jsx)(p._2,{asChild:!0,children:(0,l.jsxs)(h(),{href:a.viewHref(t),children:[(0,l.jsx)(i.A,{className:"mr-2 size-4"}),s("viewDetails")]})}):(0,l.jsxs)(p._2,{onClick:()=>a.onView?.(t),children:[(0,l.jsx)(i.A,{className:"mr-2 size-4"}),s("viewDetails")]})}),(a.onEdit||a.editHref)&&(0,l.jsx)(l.Fragment,{children:a.editHref?(0,l.jsx)(p._2,{asChild:!0,children:(0,l.jsxs)(h(),{href:a.editHref(t),children:[(0,l.jsx)(n.A,{className:"mr-2 size-4"}),s("edit")]})}):(0,l.jsxs)(p._2,{onClick:()=>a.onEdit?.(t),children:[(0,l.jsx)(n.A,{className:"mr-2 size-4"}),s("edit")]})}),a.customActions?.map((e,a)=>(0,l.jsxs)(p._2,{className:"destructive"===e.variant?"text-destructive":"",onClick:()=>e.onClick(t),children:[e.icon&&(0,l.jsx)(e.icon,{className:"mr-2 size-4"}),s(e.labelKey)]},a)),a.showCopyId&&(0,l.jsx)(p._2,{onClick:()=>navigator.clipboard.writeText(String(t.id)),children:s("copyId")}),a.onDelete&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(p.mB,{}),(0,l.jsxs)(p._2,{className:"text-destructive",onClick:()=>a.onDelete?.(t),children:[(0,l.jsx)(c.A,{className:"mr-2 size-4"}),s("delete")]})]})]})]})},header:e("actions"),id:"actions"}),createDateColumn:(s,a,r="MMM dd, yyyy")=>({accessorKey:s,cell:({getValue:e})=>{let s=e();if(!s)return"-";try{let e="string"==typeof s?new Date(s):s;return(0,t.GP)(e,r)}catch{return"-"}},header:({column:s})=>{let t=s.getIsSorted();return(0,l.jsxs)(j.$,{className:"h-auto p-0 font-semibold hover:bg-transparent",onClick:()=>s.toggleSorting("asc"===s.getIsSorted()),variant:"ghost",children:[e(a),"asc"===t?(0,l.jsx)(d.A,{className:"ml-2 size-4"}):"desc"===t?(0,l.jsx)(o.A,{className:"ml-2 size-4"}):(0,l.jsx)(m.A,{className:"ml-2 size-4 opacity-50"})]})}}),createSortableHeader:s=>({column:a})=>{let t=a.getIsSorted();return(0,l.jsxs)(j.$,{className:"h-auto p-0 font-semibold hover:bg-transparent",onClick:()=>a.toggleSorting("asc"===a.getIsSorted()),variant:"ghost",children:[e(s),"asc"===t?(0,l.jsx)(d.A,{className:"ml-2 size-4"}):"desc"===t?(0,l.jsx)(o.A,{className:"ml-2 size-4"}):(0,l.jsx)(m.A,{className:"ml-2 size-4 opacity-50"})]})},createStatusColumn:(s,t,r)=>({accessorKey:s,cell:({getValue:e})=>{let s=e();if(!s)return"-";let t=r?.[s]||{variant:"secondary"},i=t.label||a(s.toLowerCase());return(0,l.jsx)(f.E,{variant:t.variant,children:i})},header:({column:s})=>{let a=s.getIsSorted();return(0,l.jsxs)(j.$,{className:"h-auto p-0 font-semibold hover:bg-transparent",onClick:()=>s.toggleSorting("asc"===s.getIsSorted()),variant:"ghost",children:[e(t),"asc"===a?(0,l.jsx)(d.A,{className:"ml-2 size-4"}):"desc"===a?(0,l.jsx)(o.A,{className:"ml-2 size-4"}):(0,l.jsx)(m.A,{className:"ml-2 size-4 opacity-50"})]})}}),createTextColumn:(s,a,t)=>({accessorKey:s,cell:({getValue:e})=>{let s=e();if(!s)return"-";let a=t?.maxLength&&s.length>t.maxLength?`${s.slice(0,t.maxLength)}...`:s;return(0,l.jsx)("span",{className:(0,g.cn)("line-clamp-2",t?.className),title:s,children:a})},header:({column:s})=>{let t=s.getIsSorted();return(0,l.jsxs)(j.$,{className:"h-auto p-0 font-semibold hover:bg-transparent",onClick:()=>s.toggleSorting("asc"===s.getIsSorted()),variant:"ghost",children:[e(a),"asc"===t?(0,l.jsx)(d.A,{className:"ml-2 size-4"}):"desc"===t?(0,l.jsx)(o.A,{className:"ml-2 size-4"}):(0,l.jsx)(m.A,{className:"ml-2 size-4 opacity-50"})]})}})}),y=e=>({column:s})=>{let a=s.getIsSorted();return(0,l.jsxs)(j.$,{className:"h-auto p-0 font-semibold hover:bg-transparent",onClick:()=>s.toggleSorting("asc"===s.getIsSorted()),variant:"ghost",children:[e,"asc"===a?(0,l.jsx)(d.A,{className:"ml-2 size-4"}):"desc"===a?(0,l.jsx)(o.A,{className:"ml-2 size-4"}):(0,l.jsx)(m.A,{className:"ml-2 size-4 opacity-50"})]})},v=(e,s,a="MMM dd, yyyy")=>({accessorKey:e,cell:({getValue:e})=>{let s=e();if(!s)return"-";try{let e="string"==typeof s?new Date(s):s;return(0,t.GP)(e,a)}catch{return"-"}},header:y(s)}),b=(e,s="Status",a)=>({accessorKey:e,cell:({getValue:e})=>{let s=e();if(!s)return"-";let t=a?.[s]||{variant:"secondary"},r=t.label||s;return(0,l.jsx)(f.E,{variant:t.variant,children:r})},header:y(s)}),w=(e,s,a)=>({accessorKey:e,cell:({getValue:e})=>{let s=e();if(!s)return"-";let t=a?.maxLength&&s.length>a.maxLength?`${s.slice(0,Math.max(0,a.maxLength))}...`:s;return(0,l.jsx)("span",{className:a?.className,title:s,children:t})},header:y(s)}),A=(e,s,a)=>({accessorKey:e,cell:({getValue:e})=>{let s=e(),t="string"==typeof s?Number.parseFloat(s):s;if(null==t||isNaN(t))return"-";let r=t.toString();return a?.format==="currency"?r=new Intl.NumberFormat("en-US",{currency:"USD",minimumFractionDigits:a.decimals??2,style:"currency"}).format(t):a?.format==="percentage"?r=`${(100*t).toFixed(a.decimals??1)}%`:a?.decimals!==void 0&&(r=t.toFixed(a.decimals)),a?.prefix&&(r=a.prefix+r),a?.suffix&&(r+=a.suffix),(0,l.jsx)("span",{className:"font-mono",children:r})},header:y(s)}),C=()=>({cell:({row:e})=>(0,l.jsx)(u.S,{"aria-label":"Select row",checked:e.getIsSelected(),onCheckedChange:s=>e.toggleSelected(!!s)}),enableHiding:!1,enableSorting:!1,header:({table:e})=>(0,l.jsx)(u.S,{"aria-label":"Select all",checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:s=>e.toggleAllPageRowsSelected(!!s)}),id:"select"}),z=(e,s,a,t)=>({accessorKey:e,cell:({row:s})=>{let r=s.getValue(e),i=t?.formatter?t.formatter(r):r;return(0,l.jsxs)("div",{className:(0,g.cn)("flex items-center justify-center gap-1 text-sm",t?.className),children:[(0,l.jsx)(a,{className:"size-3 text-muted-foreground"}),String(i)]})},header:y(s)}),S=(e,s)=>({cell:({row:a})=>{let t=a.original;return(0,l.jsxs)(p.rI,{children:[(0,l.jsx)(p.ty,{asChild:!0,children:(0,l.jsxs)(j.$,{className:"size-8 p-0",variant:"ghost",children:[(0,l.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,l.jsx)(r.A,{className:"size-4"})]})}),(0,l.jsxs)(p.SQ,{align:"end",children:[(0,l.jsx)(p.lp,{children:s?.actions||"Actions"}),(e.onView||e.viewHref)&&(0,l.jsx)(l.Fragment,{children:e.viewHref?(0,l.jsx)(p._2,{asChild:!0,children:(0,l.jsxs)(h(),{href:e.viewHref(t),children:[(0,l.jsx)(i.A,{className:"mr-2 size-4"}),s?.viewDetails||"View Details"]})}):(0,l.jsxs)(p._2,{onClick:()=>e.onView?.(t),children:[(0,l.jsx)(i.A,{className:"mr-2 size-4"}),s?.viewDetails||"View Details"]})}),(e.onEdit||e.editHref)&&(0,l.jsx)(l.Fragment,{children:e.editHref?(0,l.jsx)(p._2,{asChild:!0,children:(0,l.jsxs)(h(),{href:e.editHref(t),children:[(0,l.jsx)(n.A,{className:"mr-2 size-4"}),s?.edit||"Edit"]})}):(0,l.jsxs)(p._2,{onClick:()=>e.onEdit?.(t),children:[(0,l.jsx)(n.A,{className:"mr-2 size-4"}),s?.edit||"Edit"]})}),e.customActions?.map((e,s)=>(0,l.jsxs)(p._2,{className:"destructive"===e.variant?"text-destructive":"",onClick:()=>e.onClick(t),children:[e.icon&&(0,l.jsx)(e.icon,{className:"mr-2 size-4"}),e.label]},s)),e.showCopyId&&(0,l.jsx)(p._2,{onClick:()=>navigator.clipboard.writeText(String(t.id)),children:"Copy ID"}),e.onDelete&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(p.mB,{}),(0,l.jsxs)(p._2,{className:"text-destructive",onClick:()=>e.onDelete?.(t),children:[(0,l.jsx)(c.A,{className:"mr-2 size-4"}),s?.delete||"Delete"]})]})]})]})},header:s?.actions||"Actions",id:"actions"}),k=e=>({delegation:{Cancelled:{label:e("cancelled"),variant:"destructive"},Completed:{label:e("completed"),variant:"success"},"In Progress":{label:e("inProgress"),variant:"default"},Planned:{label:e("planned"),variant:"secondary"}},employee:{Active:{label:e("active"),variant:"success"},Inactive:{label:e("inactive"),variant:"secondary"},"On Leave":{label:e("onLeave"),variant:"warning"}},task:{Assigned:{label:e("assigned"),variant:"default"},Cancelled:{label:e("cancelled"),variant:"destructive"},Completed:{label:e("completed"),variant:"success"},In_Progress:{label:e("inProgress"),variant:"default"},Overdue:{label:e("overdue"),variant:"destructive"},Pending:{label:e("pending"),variant:"secondary"}}})},52027:(e,s,a)=>{a.d(s,{gO:()=>x,jt:()=>u,pp:()=>h});var l=a(60687),t=a(72963),r=a(11516);a(43210);var i=a(68752),n=a(91821),c=a(85726),d=a(22482);let o={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function x({children:e,className:s,data:a,emptyComponent:t,error:r,errorComponent:i,isLoading:n,loadingComponent:c,onRetry:o}){return n?c||(0,l.jsx)(j,{...s&&{className:s},text:"Loading..."}):r?i||(0,l.jsx)(f,{...s&&{className:s},message:r,...o&&{onRetry:o}}):!a||Array.isArray(a)&&0===a.length?t||(0,l.jsx)("div",{className:(0,d.cn)("text-center py-8",s),children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,l.jsx)("div",{className:s,children:e(a)})}function h({className:e,description:s,icon:a,primaryAction:t,secondaryAction:r,title:n}){return(0,l.jsxs)("div",{className:(0,d.cn)("space-y-6 text-center py-12",e),children:[a&&(0,l.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,l.jsx)(a,{className:"h-10 w-10 text-muted-foreground"})}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:n}),s&&(0,l.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:s})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[t&&(0,l.jsx)(i.r,{actionType:"primary",asChild:!!t.href,icon:t.icon,onClick:t.onClick,children:t.href?(0,l.jsx)("a",{href:t.href,children:t.label}):t.label}),r&&(0,l.jsx)(i.r,{actionType:"tertiary",asChild:!!r.href,icon:r.icon,onClick:r.onClick,children:r.href?(0,l.jsx)("a",{href:r.href,children:r.label}):r.label})]})]})}function f({className:e,message:s,onRetry:a}){return(0,l.jsxs)(n.Fc,{className:(0,d.cn)("my-4",e),variant:"destructive",children:[(0,l.jsx)(t.A,{className:"size-4"}),(0,l.jsx)(n.XL,{children:"Error"}),(0,l.jsx)(n.TN,{children:(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:s}),a&&(0,l.jsx)(i.r,{actionType:"tertiary",icon:(0,l.jsx)(r.A,{className:"size-4"}),onClick:a,size:"sm",children:"Try Again"})]})})]})}function j({className:e,fullPage:s=!1,size:a="md",text:t}){return(0,l.jsx)("div",{className:(0,d.cn)("flex items-center justify-center",s&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",e),children:(0,l.jsxs)("div",{className:"flex flex-col items-center",children:[(0,l.jsx)(r.A,{className:(0,d.cn)("animate-spin text-primary",o[a])}),t&&(0,l.jsx)("span",{className:(0,d.cn)("mt-2 text-muted-foreground",m[a]),children:t})]})})}function u({className:e,count:s=1,testId:a="loading-skeleton",variant:t="default"}){return"card"===t?(0,l.jsx)("div",{className:(0,d.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",e),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,l.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,l.jsx)(c.E,{className:"aspect-[16/10] w-full"}),(0,l.jsxs)("div",{className:"p-5",children:[(0,l.jsx)(c.E,{className:"mb-1 h-7 w-3/4"}),(0,l.jsx)(c.E,{className:"mb-3 h-4 w-1/2"}),(0,l.jsx)(c.E,{className:"my-3 h-px w-full"}),(0,l.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(c.E,{className:"mr-2.5 size-5 rounded-full"}),(0,l.jsx)(c.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===t?(0,l.jsxs)("div",{className:(0,d.cn)("space-y-3",e),"data-testid":a,children:[(0,l.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,l.jsx)(c.E,{className:"h-8 flex-1"},s))}),Array(s).fill(0).map((e,s)=>(0,l.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,l.jsx)(c.E,{className:"h-6 flex-1"},s))},s))]}):"list"===t?(0,l.jsx)("div",{className:(0,d.cn)("space-y-3",e),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)(c.E,{className:"size-12 rounded-full"}),(0,l.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,l.jsx)(c.E,{className:"h-4 w-1/3"}),(0,l.jsx)(c.E,{className:"h-4 w-full"})]})]},s))}):"stats"===t?(0,l.jsx)("div",{className:(0,d.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",e),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,l.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)(c.E,{className:"h-5 w-1/3"}),(0,l.jsx)(c.E,{className:"size-5 rounded-full"})]}),(0,l.jsx)(c.E,{className:"mt-3 h-8 w-1/2"}),(0,l.jsx)(c.E,{className:"mt-2 h-4 w-2/3"})]},s))}):(0,l.jsx)("div",{className:(0,d.cn)("space-y-2",e),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,l.jsx)(c.E,{className:"h-5 w-full"},s))})}},68752:(e,s,a)=>{a.d(s,{r:()=>d});var l=a(60687),t=a(11516),r=a(43210),i=a.n(r),n=a(29523),c=a(22482);let d=i().forwardRef(({actionType:e="primary",asChild:s=!1,children:a,className:r,disabled:i,icon:d,isLoading:o=!1,loadingText:m,...x},h)=>{let{className:f,variant:j}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[e];return(0,l.jsx)(n.$,{asChild:s,className:(0,c.cn)(f,r),disabled:o||i,ref:h,variant:j,...x,children:o?(0,l.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,l.jsx)(t.A,{className:"mr-2 size-4 animate-spin"}),m||a]}):(0,l.jsxs)("span",{className:"inline-flex items-center",children:[" ",d&&(0,l.jsx)("span",{className:"mr-2",children:d}),a]})})});d.displayName="ActionButton"},85726:(e,s,a)=>{a.d(s,{E:()=>r});var l=a(60687),t=a(22482);function r({className:e,...s}){return(0,l.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-md bg-muted",e),...s})}}};