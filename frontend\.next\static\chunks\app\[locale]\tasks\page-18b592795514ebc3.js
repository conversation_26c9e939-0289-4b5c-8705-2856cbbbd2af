(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2196],{3567:(e,s,a)=>{"use strict";a.d(s,{R:()=>r});var t=a(35476);function r(e){return+(0,t.a)(e)<Date.now()}},14636:(e,s,a)=>{"use strict";a.d(s,{AM:()=>n,Wv:()=>d,hl:()=>c});var t=a(95155),r=a(20547),l=a(12115),i=a(54036);let n=r.bL,d=r.l9;r.bm;let c=l.forwardRef((e,s)=>{let{align:a="center",className:l,sideOffset:n=4,...d}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsx)(r.<PERSON>,{align:a,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",l),ref:s,sideOffset:n,...d})})});c.displayName=r.UC.displayName},19968:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},20360:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eu});var t=a(95155);let r=(0,a(40157).A)("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);var l=a(34301),i=a(18271),n=a(6874),d=a.n(n),c=a(12115),o=a(17652),m=a(88240),u=a(38342),x=a(97697);let h={entityType:"task",title:"Task Dashboard",description:"Oversee all tasks, assignments, and progress.",viewModes:["cards","table","list"],defaultViewMode:"cards",enableBulkActions:!0,enableExport:!0,refreshInterval:3e4},g=e=>{let{className:s=""}=e,{layout:a,monitoring:r,setViewMode:l,setGridColumns:i,toggleCompactMode:n,setMonitoringEnabled:d,setRefreshInterval:c,toggleAutoRefresh:o,resetSettings:m}=(0,x.fX)("task")();return(0,t.jsx)(u.s,{config:h,entityType:"task",layout:a,monitoring:r,setViewMode:l,setGridColumns:i,toggleCompactMode:n,setMonitoringEnabled:d,setRefreshInterval:c,toggleAutoRefresh:o,resetSettings:m,className:s})};var p=a(37648),f=a(91721),v=a(11133),b=a(8376),j=a(24371),y=a(27300),N=a(50286),k=a(98328),w=a(75074),C=a(9572),A=a(25318),S=a(26126),z=a(30285),M=a(62523),R=a(85057),E=a(22346),I=a(38382),D=a(14636),T=a(47262),L=a(85511),F=a(54036),P=a(41784);let _=[{value:"Pending",label:"Pending",icon:p.A,color:"text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-900/20 dark:border-amber-800"},{value:"Assigned",label:"Assigned",icon:f.A,color:"text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:border-blue-800"},{value:"In_Progress",label:"In Progress",icon:v.A,color:"text-purple-600 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-900/20 dark:border-purple-800"},{value:"Completed",label:"Completed",icon:b.A,color:"text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800"},{value:"Cancelled",label:"Cancelled",icon:j.A,color:"text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800"}],V=[{value:"Low",label:"Low Priority",color:"text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800"},{value:"Medium",label:"Medium Priority",color:"text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-900/20 dark:border-amber-800"},{value:"High",label:"High Priority",color:"text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800"}],B=e=>{let{onFiltersChange:s,className:a,initialFilters:r={},employeesList:l=[]}=e,[i,n]=(0,c.useState)({search:"",status:[],priority:[],assignee:[],dateRange:{},...r}),[d,o]=(0,c.useState)(!1),m=e=>{let a={...i,...e};n(a),null==s||s(a)},u=()=>{let e={search:"",status:[],priority:[],assignee:[],dateRange:{}};n(e),null==s||s(e)},x=e=>{m({status:i.status.includes(e)?i.status.filter(s=>s!==e):[...i.status,e]})},h=e=>{m({priority:i.priority.includes(e)?i.priority.filter(s=>s!==e):[...i.priority,e]})},g=e=>{m({assignee:i.assignee.includes(e)?i.assignee.filter(s=>s!==e):[...i.assignee,e]})},p=e=>{var s,a;m({dateRange:{from:null!=(s=null==e?void 0:e.from)?s:void 0,to:null!=(a=null==e?void 0:e.to)?a:void 0}})},v=+!!i.search+i.status.length+i.priority.length+i.assignee.length+(i.dateRange.from||i.dateRange.to?1:0);return(0,t.jsxs)("div",{className:(0,F.cn)("flex flex-col gap-4",a),children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(w.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,t.jsx)(M.p,{placeholder:"Search tasks...",value:i.search,onChange:e=>m({search:e.target.value}),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,t.jsxs)("div",{className:"hidden md:flex items-center gap-2",children:[(0,t.jsx)(()=>(0,t.jsxs)(D.AM,{children:[(0,t.jsx)(D.Wv,{asChild:!0,children:(0,t.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(b.A,{className:"size-4"}),"Status",i.status.length>0&&(0,t.jsx)(S.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:i.status.length})]})}),(0,t.jsx)(D.hl,{className:"w-56 p-3",align:"start",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Task Status"}),(0,t.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>m({status:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)(E.w,{}),(0,t.jsx)("div",{className:"space-y-2",children:_.map(e=>{let s=e.icon;return(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(T.S,{id:"status-".concat(e.value),checked:i.status.includes(e.value),onCheckedChange:()=>x(e.value)}),(0,t.jsxs)(R.J,{htmlFor:"status-".concat(e.value),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,t.jsx)(s,{className:"size-3"}),(0,t.jsx)(S.E,{variant:"outline",className:(0,F.cn)("text-xs border",e.color),children:e.label})]})]},e.value)})})]})})]}),{}),(0,t.jsx)(()=>(0,t.jsxs)(D.AM,{children:[(0,t.jsx)(D.Wv,{asChild:!0,children:(0,t.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(y.A,{className:"size-4"}),"Priority",i.priority.length>0&&(0,t.jsx)(S.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:i.priority.length})]})}),(0,t.jsx)(D.hl,{className:"w-48 p-3",align:"start",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Priority Level"}),(0,t.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>m({priority:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)(E.w,{}),(0,t.jsx)("div",{className:"space-y-2",children:V.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(T.S,{id:"priority-".concat(e.value),checked:i.priority.includes(e.value),onCheckedChange:()=>h(e.value)}),(0,t.jsx)(R.J,{htmlFor:"priority-".concat(e.value),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:(0,t.jsx)(S.E,{variant:"outline",className:(0,F.cn)("text-xs border",e.color),children:e.label})})]},e.value))})]})})]}),{}),l&&l.length>0&&(0,t.jsx)(()=>(0,t.jsxs)(D.AM,{children:[(0,t.jsx)(D.Wv,{asChild:!0,children:(0,t.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(N.A,{className:"size-4"}),"Assignee",i.assignee.length>0&&(0,t.jsx)(S.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:i.assignee.length})]})}),(0,t.jsx)(D.hl,{className:"w-64 p-3",align:"start",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Assigned Employee"}),(0,t.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>m({assignee:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)(E.w,{}),(0,t.jsxs)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(T.S,{id:"assignee-unassigned",checked:i.assignee.includes("unassigned"),onCheckedChange:()=>g("unassigned")}),(0,t.jsxs)(R.J,{htmlFor:"assignee-unassigned",className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,t.jsx)(f.A,{className:"size-3 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"Unassigned"})]})]}),null==l?void 0:l.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(T.S,{id:"assignee-".concat(e.id),checked:i.assignee.includes(e.id),onCheckedChange:()=>g(e.id)}),(0,t.jsxs)(R.J,{htmlFor:"assignee-".concat(e.id),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,t.jsx)(f.A,{className:"size-3"}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{children:e.name}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:e.role})]})]})]},e.id))]})]})})]}),{}),(0,t.jsx)(()=>(0,t.jsxs)(D.AM,{children:[(0,t.jsx)(D.Wv,{asChild:!0,children:(0,t.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(k.A,{className:"size-4"}),"Date Range",(i.dateRange.from||i.dateRange.to)&&(0,t.jsx)(S.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:"1"})]})}),(0,t.jsx)(D.hl,{className:"w-auto p-0",align:"start",children:(0,t.jsxs)("div",{className:"p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Task Date Range"}),(0,t.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>m({dateRange:{}}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)(L.V,{mode:"range",selected:{from:i.dateRange.from,to:i.dateRange.to},onSelect:p,numberOfMonths:2,className:"rounded-md border-0"}),(0,t.jsx)("div",{className:"mt-3 text-xs text-muted-foreground text-center",children:i.dateRange.from&&!i.dateRange.to?"Select end date to complete range":"Click start date, then end date"})]})})]}),{})]}),(0,t.jsx)("div",{className:"md:hidden",children:(0,t.jsxs)(I.cj,{open:d,onOpenChange:o,children:[(0,t.jsx)(I.CG,{asChild:!0,children:(0,t.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(C.A,{className:"size-4"}),"Filters",v>0&&(0,t.jsx)(S.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:v})]})}),(0,t.jsxs)(I.h,{side:"bottom",className:"h-[80vh]",children:[(0,t.jsxs)(I.Fm,{children:[(0,t.jsx)(I.qp,{children:"Filter Tasks"}),(0,t.jsx)(I.Qs,{children:"Refine your task list with advanced filters"})]}),(0,t.jsxs)("div",{className:"grid gap-6 py-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(R.J,{className:"text-sm font-medium",children:"Status"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-2",children:_.map(e=>{let s=e.icon;return(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,t.jsx)(T.S,{id:"mobile-status-".concat(e.value),checked:i.status.includes(e.value),onCheckedChange:()=>x(e.value)}),(0,t.jsxs)(R.J,{htmlFor:"mobile-status-".concat(e.value),className:"flex items-center gap-1 cursor-pointer text-xs flex-1",children:[(0,t.jsx)(s,{className:"size-3"}),e.label]})]},e.value)})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(R.J,{className:"text-sm font-medium",children:"Priority"}),(0,t.jsx)("div",{className:"grid gap-2",children:V.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,t.jsx)(T.S,{id:"mobile-priority-".concat(e.value),checked:i.priority.includes(e.value),onCheckedChange:()=>h(e.value)}),(0,t.jsx)(R.J,{htmlFor:"mobile-priority-".concat(e.value),className:"cursor-pointer text-sm flex-1",children:e.label})]},e.value))})]}),l&&l.length>0&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(R.J,{className:"text-sm font-medium",children:"Assignee"}),(0,t.jsxs)("div",{className:"grid gap-2 max-h-48 overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,t.jsx)(T.S,{id:"mobile-assignee-unassigned",checked:i.assignee.includes("unassigned"),onCheckedChange:()=>g("unassigned")}),(0,t.jsx)(R.J,{htmlFor:"mobile-assignee-unassigned",className:"cursor-pointer text-sm flex-1",children:"Unassigned"})]}),l.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,t.jsx)(T.S,{id:"mobile-assignee-".concat(e.id),checked:i.assignee.includes(e.id),onCheckedChange:()=>g(e.id)}),(0,t.jsx)(R.J,{htmlFor:"mobile-assignee-".concat(e.id),className:"cursor-pointer text-sm flex-1",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{children:e.name}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:e.role})]})})]},e.id))]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(R.J,{className:"text-sm font-medium",children:"Date Range"}),(0,t.jsx)("div",{className:"border rounded-md p-3",children:(0,t.jsx)(L.V,{mode:"range",selected:{from:i.dateRange.from,to:i.dateRange.to},onSelect:p,numberOfMonths:1,className:"rounded-md border-0"})})]}),(0,t.jsx)(z.$,{variant:"outline",onClick:u,className:"w-full",children:"Clear All Filters"})]})]})]})}),v>0&&(0,t.jsxs)(z.$,{variant:"ghost",size:"sm",onClick:u,className:"gap-1 text-muted-foreground hover:text-foreground hidden md:flex",children:[(0,t.jsx)(A.A,{className:"size-3"}),"Clear (",v,")"]})]}),v>0&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[i.search&&(0,t.jsxs)(S.E,{variant:"secondary",className:"gap-1",children:['Search: "',i.search,'"',(0,t.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>m({search:""}),children:(0,t.jsx)(A.A,{className:"size-3"})})]}),i.status.map(e=>{let s=_.find(s=>s.value===e);return s?(0,t.jsxs)(S.E,{variant:"secondary",className:"gap-1",children:[s.label,(0,t.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>x(e),children:(0,t.jsx)(A.A,{className:"size-3"})})]},e):null}),i.priority.map(e=>{let s=V.find(s=>s.value===e);return s?(0,t.jsxs)(S.E,{variant:"secondary",className:"gap-1",children:[s.label,(0,t.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>h(e),children:(0,t.jsx)(A.A,{className:"size-3"})})]},e):null}),i.assignee.map(e=>{let s=null==l?void 0:l.find(s=>s.id===e),a="unassigned"===e?"Unassigned":(null==s?void 0:s.name)||"Unknown";return(0,t.jsxs)(S.E,{variant:"secondary",className:"gap-1",children:["Assignee: ",a,(0,t.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>g(e),children:(0,t.jsx)(A.A,{className:"size-3"})})]},e)}),(i.dateRange.from||i.dateRange.to)&&(0,t.jsxs)(S.E,{variant:"secondary",className:"gap-1",children:["Date:"," ",i.dateRange.from?(0,P.GP)(i.dateRange.from,"MMM d"):"?"," ","-"," ",i.dateRange.to?(0,P.GP)(i.dateRange.to,"MMM d, yyyy"):"?",(0,t.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>m({dateRange:{}}),children:(0,t.jsx)(A.A,{className:"size-3"})})]})]})]})};var $=a(83343),J=a(83662),U=a(28328),q=a(31949),G=a(19968),K=a(6560),O=a(66695),H=a(99673);let Z=e=>{switch(e){case"Assigned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Completed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-indigo-500/20 text-indigo-700 border-indigo-500/30 dark:text-indigo-400 dark:bg-indigo-500/10 dark:border-indigo-500/20";case"Pending":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},W=e=>{switch(e){case"High":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Low":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"Medium":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},X=e=>{if(!e)return"N/A";try{return(0,P.GP)((0,$.H)(e),"MMM d, yyyy HH:mm")}catch(e){return"Invalid Date"}};function Q(e){let{task:s}=e,a=!!s.staffEmployeeId,r=!!s.driverEmployeeId,l=!!s.vehicleId;return(0,t.jsxs)(O.Zp,{className:"flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md",children:[(0,t.jsxs)(O.aR,{className:"p-5",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,t.jsx)(O.ZB,{className:"line-clamp-2 text-lg font-semibold text-primary",title:s.description,children:s.description}),(0,t.jsxs)("div",{className:"flex shrink-0 flex-col items-end gap-1",children:[(0,t.jsx)(S.E,{className:(0,F.cn)("text-xs py-1 px-2 font-semibold",Z(s.status)),children:s.status}),(0,t.jsxs)(S.E,{className:(0,F.cn)("text-xs py-1 px-2 font-semibold",W(s.priority)),children:[s.priority," Priority"]})]})]}),(0,t.jsxs)(O.BT,{className:"flex items-center pt-1 text-sm text-muted-foreground",children:[(0,t.jsx)(J.A,{className:"mr-1.5 size-4 shrink-0 text-accent"}),s.location]})]}),(0,t.jsxs)(O.Wu,{className:"flex grow flex-col p-5",children:[(0,t.jsx)(E.w,{className:"my-3 bg-border/50"}),(0,t.jsxs)("div",{className:"grow space-y-2.5 text-sm text-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(k.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Start: "}),(0,t.jsx)("strong",{className:"font-semibold",children:X(s.dateTime)})]})]}),s.deadline&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Deadline: "}),(0,t.jsx)("strong",{className:"font-semibold",children:X(s.deadline)})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Duration: "}),(0,t.jsxs)("strong",{className:"font-semibold",children:[s.estimatedDuration," mins"]})]})]}),a&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Staff: "}),(0,t.jsx)("strong",{className:"font-semibold",children:s.staffEmployee?(0,H.DV)(s.staffEmployee):"ID: ".concat(s.staffEmployeeId)})]})]}),r&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Driver: "}),(0,t.jsx)("strong",{className:"font-semibold",children:s.driverEmployee?(0,H.DV)(s.driverEmployee):"ID: ".concat(s.driverEmployeeId)})]})]}),l&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(U.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Vehicle: "}),(0,t.jsx)("strong",{className:"font-semibold",children:s.vehicle?"".concat(s.vehicle.make," ").concat(s.vehicle.model," (").concat(s.vehicle.licensePlate||"ID: ".concat(s.vehicle.id),")"):"ID: ".concat(s.vehicleId)})]})]}),!a&&"Completed"!==s.status&&"Cancelled"!==s.status&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(q.A,{className:"mr-2.5 size-4 shrink-0 text-destructive"}),(0,t.jsx)("strong",{className:"font-semibold text-destructive",children:"No Staff Assigned"})]})]}),s.notes&&(0,t.jsx)("p",{className:"mt-3 line-clamp-2 border-t border-dashed border-border/50 pt-2 text-xs text-muted-foreground",title:s.notes,children:s.notes})]}),(0,t.jsx)(O.wL,{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,t.jsx)(K.r,{actionType:"tertiary",asChild:!0,className:"w-full",icon:(0,t.jsx)(G.A,{className:"size-4"}),children:(0,t.jsx)(d(),{href:"/tasks/".concat(s.id),children:"View Details"})})})]})}var Y=a(56819);let ee=e=>{let{className:s="",compactMode:a,tasks:r,gridColumns:l=3,viewMode:i}=e;switch(i){case"list":return(0,t.jsx)("div",{className:(0,F.cn)("flex flex-col",a?"gap-2":"gap-4",s),children:r.map(e=>(0,t.jsx)(Q,{task:e},e.id))});case"table":return(0,t.jsx)(Y.z,{className:s,tasks:r});default:return(0,t.jsx)("div",{className:(0,F.cn)("grid grid-cols-1 gap-6","md:grid-cols-2 lg:grid-cols-".concat(l),a&&"gap-3",s),children:r.map(e=>(0,t.jsx)(Q,{task:e},e.id))})}};var es=a(24865),ea=a(89440),et=a(54165),er=a(77023),el=a(95647),ei=a(83761),en=a(61051),ed=a(80937),ec=a(21354);function eo(){return(0,t.jsxs)("div",{className:"flex h-full flex-col overflow-hidden rounded-lg border-border/60 bg-card shadow-md",children:[(0,t.jsxs)("div",{className:"flex grow flex-col p-5",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsx)(er.jt,{className:"mb-1 h-7 w-3/5 bg-muted/50",count:1,variant:"default"}),(0,t.jsx)(er.jt,{className:"mb-1 h-5 w-1/4 rounded-full bg-muted/50",count:1,variant:"default"})]}),(0,t.jsx)(er.jt,{className:"mb-3 h-4 w-1/2 bg-muted/50",count:1,variant:"default"}),(0,t.jsx)(er.jt,{className:"my-3 h-px w-full bg-border/50",count:1,variant:"default"}),(0,t.jsx)("div",{className:"grow space-y-2.5",children:Array.from({length:3}).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(er.jt,{className:"mr-2.5 size-5 rounded-full bg-muted/50",count:1,variant:"default"}),(0,t.jsx)(er.jt,{className:"h-5 w-2/3 bg-muted/50",count:1,variant:"default"})]},s))})]}),(0,t.jsx)("div",{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,t.jsx)(er.jt,{className:"h-10 w-full bg-muted/50",count:1,variant:"default"})})]})}let em=()=>{var e,s,a;(0,o.c3)("common");let n=(0,o.c3)("navigation"),{layout:m}=(0,x.fX)("task")(),{data:u=[],error:h,isLoading:p,refetch:f}=(0,en.si)(),{data:v=[],error:b,isLoading:j,refetch:y}=(0,ei.nR)(),{data:N=[],error:k,isLoading:w,refetch:C}=(0,ed.T$)(),[A,S]=(0,c.useState)(""),[z,M]=(0,c.useState)("all"),[R,E]=(0,c.useState)("all"),[I,D]=(0,c.useState)("all"),[T,L]=(0,c.useState)({}),F=(0,c.useMemo)(()=>v.map(e=>{var s;return{id:String(e.id),name:null!=(s=e.fullName)?s:e.name,role:e.role}}),[v]),P=(0,c.useMemo)(()=>{let e=[...u.map(e=>(0,ec.R)(e,v,N))],s=A.toLowerCase();return"all"!==z&&(e=e.filter(e=>e.status===z)),"all"!==R&&(e=e.filter(e=>e.priority===R)),"all"!==I&&(e=e.filter(e=>{var s;return null!=(s=e.staffEmployeeId&&String(e.staffEmployeeId)===I||e.driverEmployeeId&&String(e.driverEmployeeId)===I)?s:"unassigned"===I&&!e.staffEmployeeId&&!e.driverEmployeeId})),s&&(e=e.filter(e=>{var a,t,r;let l=e.staffEmployeeId?F.find(s=>s.id===String(e.staffEmployeeId)):null,i=e.driverEmployeeId?F.find(s=>s.id===String(e.driverEmployeeId)):null;return null!=(r=null!=(t=e.description.toLowerCase().includes(s)||e.location.toLowerCase().includes(s)||(null==(a=e.notes)?void 0:a.toLowerCase().includes(s)))?t:null==l?void 0:l.name.toLowerCase().includes(s))?r:null==i?void 0:i.name.toLowerCase().includes(s)})),(T.from||T.to)&&(e=e.filter(e=>{let s=new Date(e.createdAt);return T.from&&T.to?s>=T.from&&s<=T.to:T.from?s>=T.from:!T.to||s<=T.to})),e},[A,u,v,N,z,R,I,F,T.from,T.to]),_=(0,c.useCallback)(async()=>{await Promise.all([f(),y(),C()])},[f,y,C]),V=A||"all"!==z||"all"!==R||"all"!==I;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(ea.AppBreadcrumb,{homeHref:"/",homeLabel:n("dashboard")}),(0,t.jsx)(el.z,{description:"Oversee all tasks, assignments, and progress.",icon:r,title:"Manage Tasks",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(K.r,{actionType:"primary",asChild:!0,icon:(0,t.jsx)(l.A,{className:"size-4"}),children:(0,t.jsx)(d(),{href:"/tasks/add",children:"Add New Task"})}),(0,t.jsx)(es.M,{getReportUrl:()=>{let e=new URLSearchParams({employee:I,priority:R,searchTerm:A,status:z}).toString();return"/tasks/report?".concat(e)},isList:!0}),(0,t.jsxs)(et.lG,{children:[(0,t.jsx)(et.zM,{asChild:!0,children:(0,t.jsx)(K.r,{actionType:"secondary",icon:(0,t.jsx)(i.A,{className:"size-4"}),children:"Settings"})}),(0,t.jsxs)(et.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsx)(et.L3,{children:"Dashboard Settings"}),(0,t.jsx)(et.rr,{children:"Customize how tasks are displayed and managed."}),(0,t.jsx)(g,{})]})]})]})}),(0,t.jsx)(B,{employeesList:F,initialFilters:{assignee:"all"===I?[]:[I],dateRange:T,priority:"all"===R?[]:[R],search:A,status:"all"===z?[]:[z]},onFiltersChange:e=>{S(e.search),M(e.status.length>0?e.status[0]:"all"),E(e.priority.length>0?e.priority[0]:"all"),D(e.assignee.length>0?e.assignee[0]:"all"),L(e.dateRange)}}),(0,t.jsx)(er.gO,{data:P,emptyComponent:(0,t.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,t.jsx)(r,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,t.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:V?"No Tasks Match Your Filters":"No Tasks Created Yet"}),(0,t.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:V?"Try adjusting your search or filter criteria.":"It looks like you haven't created any tasks yet. Get started by adding one."}),!V&&(0,t.jsx)(K.r,{actionType:"primary",asChild:!0,icon:(0,t.jsx)(l.A,{className:"size-4"}),size:"lg",children:(0,t.jsx)(d(),{href:"/tasks/add",children:"Create Your First Task"})})]}),error:null!=(a=null!=(s=null!=(e=null==h?void 0:h.message)?e:null==b?void 0:b.message)?s:null==k?void 0:k.message)?a:null,isLoading:p||j||w,loadingComponent:(0,t.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:Array.from({length:3}).map((e,s)=>(0,t.jsx)(eo,{},s))}),onRetry:_,children:e=>(0,t.jsx)(ee,{compactMode:m.compactMode,gridColumns:m.gridColumns,tasks:e,viewMode:"calendar"===m.viewMode?"cards":m.viewMode})})]})};function eu(){return(0,t.jsx)(m.A,{children:(0,t.jsx)(em,{})})}},24371:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},30356:(e,s,a)=>{"use strict";a.d(s,{C:()=>c,z:()=>d});var t=a(95155),r=a(54059),l=a(70154),i=a(12115),n=a(54036);let d=i.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.bL,{className:(0,n.cn)("grid gap-2",a),...l,ref:s})});d.displayName=r.bL.displayName;let c=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(r.q7,{className:(0,n.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...i,children:(0,t.jsx)(r.C1,{className:"flex items-center justify-center",children:(0,t.jsx)(l.A,{className:"size-2.5 fill-current text-current"})})})});c.displayName=r.q7.displayName},38342:(e,s,a)=>{"use strict";a.d(s,{s:()=>b});var t=a(95155),r=a(66655),l=a(71978),i=a(34214),n=a(51920),d=a(29471),c=a(18271),o=a(89829),m=a(67554);a(12115);var u=a(30285),x=a(85057),h=a(30356),g=a(76202),p=a(80333),f=a(17313);let v={cards:{icon:r.A,label:"Cards"},table:{icon:l.A,label:"Table"},list:{icon:i.A,label:"List"},calendar:{icon:n.A,label:"Calendar"},grid:{icon:d.A,label:"Grid"}},b=e=>{let{config:s,entityType:a,layout:l,monitoring:i,setViewMode:n,setGridColumns:d,toggleCompactMode:b,setMonitoringEnabled:j,setRefreshInterval:y,toggleAutoRefresh:N,resetSettings:k,className:w=""}=e,C=s.viewModes||["cards","table","list"];return(0,t.jsxs)("div",{className:"space-y-6 p-4 ".concat(w),children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"size-6"}),s.title," Settings"]}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Customize your ",a," dashboard experience"]})]}),(0,t.jsxs)(f.tU,{className:"w-full",defaultValue:"layout",children:[(0,t.jsxs)(f.j7,{className:"grid w-full grid-cols-3",children:[(0,t.jsx)(f.Xi,{value:"layout",children:"Layout"}),(0,t.jsx)(f.Xi,{value:"display",children:"Display"}),(0,t.jsx)(f.Xi,{value:"refresh",children:"Refresh"})]}),(0,t.jsx)(f.av,{className:"mt-4 space-y-6",value:"layout",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{className:"text-lg font-semibold",children:"View Mode"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Choose how ",a,"s are displayed"]}),(0,t.jsx)(h.z,{className:"grid grid-cols-2 gap-4 pt-2",onValueChange:e=>n(e),value:l.viewMode,children:C.map(e=>{var s,a;let l=(null==(s=v[e])?void 0:s.icon)||r.A,i=(null==(a=v[e])?void 0:a.label)||e;return(0,t.jsxs)(x.J,{className:"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary cursor-pointer",htmlFor:"layout-".concat(e),children:[(0,t.jsx)(h.C,{className:"sr-only",id:"layout-".concat(e),value:e}),(0,t.jsx)(l,{className:"mb-3 size-6"}),i]},e)})})]}),("cards"===l.viewMode||"grid"===l.viewMode)&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(x.J,{className:"text-lg font-semibold",children:["Grid Columns: ",l.gridColumns]}),(0,t.jsx)(g.A,{defaultValue:[l.gridColumns],max:6,min:1,onValueChange:e=>{let[s]=e;return void 0!==s&&d(s)},step:1}),(0,t.jsxs)("div",{className:"flex justify-between text-sm text-muted-foreground",children:[(0,t.jsx)("span",{children:"1 column"}),(0,t.jsx)("span",{children:"6 columns"})]})]})]})}),(0,t.jsx)(f.av,{className:"mt-4 space-y-6",value:"display",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(x.J,{className:"text-lg font-semibold",children:"Compact Mode"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Show more ",a,"s in less space"]})]}),(0,t.jsx)(p.d,{checked:l.compactMode,onCheckedChange:b})]}),s.enableBulkActions&&(0,t.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(x.J,{className:"text-lg font-semibold",children:"Bulk Actions"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable selection and bulk operations"})]}),(0,t.jsx)(p.d,{checked:!0,disabled:!0})]})]})}),(0,t.jsx)(f.av,{className:"mt-4 space-y-6",value:"refresh",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsxs)(x.J,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"size-4"}),"Auto Refresh"]}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Automatically refresh ",a," data"]})]}),(0,t.jsx)(p.d,{checked:i.autoRefresh,onCheckedChange:N})]}),i.autoRefresh&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(x.J,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"size-4"}),"Refresh Interval"]}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{label:"5 seconds",value:5e3},{label:"10 seconds",value:1e4},{label:"30 seconds",value:3e4},{label:"1 minute",value:6e4},{label:"5 minutes",value:3e5}].map(e=>(0,t.jsx)(u.$,{variant:i.refreshInterval===e.value?"default":"outline",size:"sm",onClick:()=>y(e.value),children:e.label},e.value))})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(x.J,{className:"text-lg font-semibold",children:"Real-time Updates"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable live data updates"})]}),(0,t.jsx)(p.d,{checked:i.enabled,onCheckedChange:j})]})]})})]}),(0,t.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,t.jsx)(u.$,{onClick:k,variant:"outline",children:"Reset to Defaults"})})]})}},43840:(e,s,a)=>{"use strict";a.d(s,{cl:()=>t.cl,delegationApiService:()=>t.ac,employeeApiService:()=>t.aV,reliabilityApiService:()=>t.e_,taskApiService:()=>t.Hg,vehicleApiService:()=>t.oL});var t=a(36973);a(72248)},56819:(e,s,a)=>{"use strict";a.d(s,{z:()=>m});var t=a(95155),r=a(41784),l=a(3567),i=a(31949),n=a(44956),d=a(65064);a(12115);var c=a(77931),o=a(99673);function m(e){let{className:s="",tasks:a,onDelete:m,onBulkDelete:u,onBulkArchive:x}=e,h=e=>{try{return(0,r.GP)(new Date(e),"MMM d, yyyy h:mm a")}catch(e){return"Invalid date"}},g=e=>e.deadline&&(0,l.R)(new Date(e.deadline))&&"Completed"!==e.status&&"Cancelled"!==e.status,p=e=>e.staffEmployeeId?e.staffEmployee?(0,o.DV)(e.staffEmployee):"Staff ID: ".concat(e.staffEmployeeId):"Unassigned",f=[(0,c.BZ)(),(0,c.K)("description","Description",{maxLength:50,className:"max-w-xs"}),(0,c.ZI)("status","Status",{Assigned:{variant:"default",label:"Assigned"},Cancelled:{variant:"secondary",label:"Cancelled"},Completed:{variant:"success",label:"Completed"},In_Progress:{variant:"default",label:"In Progress"},Pending:{variant:"warning",label:"Pending"}}),(0,c.ZI)("priority","Priority",{High:{variant:"destructive",label:"High"},Medium:{variant:"warning",label:"Medium"},Low:{variant:"secondary",label:"Low"}}),{accessorKey:"staffEmployeeId",header:"Assignee",cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsx)("span",{className:a.staffEmployeeId?"":"text-muted-foreground",children:p(a)})}},{accessorKey:"dateTime",header:"Start Time",cell:e=>{let{row:s}=e;return h(s.getValue("dateTime"))}},{accessorKey:"deadline",header:"Deadline",cell:e=>{let{row:s}=e,a=s.original,r=a.deadline;if(!r)return(0,t.jsx)("span",{className:"text-muted-foreground",children:"No deadline"});let l=g(a);return(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("span",{className:l?"text-red-600 font-medium":"",children:h(r)}),l&&(0,t.jsx)(i.A,{"aria-label":"Overdue",className:"size-4 text-red-600"})]})}},(0,c.K)("location","Location",{maxLength:30,className:"max-w-xs"}),(0,c.Wy)({viewHref:e=>"/tasks/".concat(e.id),editHref:e=>"/tasks/".concat(e.id,"/edit"),...m&&{onDelete:e=>{m(e)}},showCopyId:!0})],v=[...u?[{label:"Delete Selected",icon:e=>{let{className:s}=e;return(0,t.jsx)(n.A,{className:s})},onClick:async e=>{await u(e)},variant:"destructive"}]:[],...x?[{label:"Archive Selected",icon:e=>{let{className:s}=e;return(0,t.jsx)(d.A,{className:s})},onClick:async e=>{await x(e)}}]:[]];return(0,t.jsx)(c.bQ,{data:a,columns:f,className:s,searchPlaceholder:"Search tasks by description or location...",searchColumn:"description",emptyMessage:"No tasks found. Create your first task to get started.",pageSize:20,enableRowSelection:!0,enableBulkActions:v.length>0,bulkActions:v,enableColumnVisibility:!0,tableClassName:"shadow-lg",headerClassName:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",rowClassName:"hover:bg-blue-50/50 dark:hover:bg-blue-900/10"})}},76202:(e,s,a)=>{"use strict";a.d(s,{A:()=>n});var t=a(95155),r=a(54073),l=a(12115),i=a(54036);let n=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsxs)(r.bL,{className:(0,i.cn)("relative flex w-full touch-none select-none items-center",a),ref:s,...l,children:[(0,t.jsx)(r.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,t.jsx)(r.Q6,{className:"absolute h-full bg-primary"})}),(0,t.jsx)(r.zi,{className:"block size-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});n.displayName=r.bL.displayName},77931:(e,s,a)=>{"use strict";a.d(s,{BZ:()=>t.BZ,K:()=>t.K,Wy:()=>t.Wy,YB:()=>t.YB,ZI:()=>t.ZI,bQ:()=>r.b,nh:()=>t.nh,vk:()=>t.vk,yX:()=>t.yX});var t=a(83506),r=a(84411);a(85127)},80333:(e,s,a)=>{"use strict";a.d(s,{d:()=>n});var t=a(95155),r=a(4884),l=a(12115),i=a(54036);let n=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.bL,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...l,ref:s,children:(0,t.jsx)(r.zi,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});n.displayName=r.bL.displayName},80937:(e,s,a)=>{"use strict";a.d(s,{NS:()=>h,T$:()=>o,W_:()=>m,Y1:()=>u,lR:()=>x});var t=a(26715),r=a(5041),l=a(90111),i=a(42366),n=a(99605),d=a(43840);let c={all:["vehicles"],detail:e=>["vehicles",e]},o=e=>(0,l.GK)([...c.all],async()=>(await d.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),m=(e,s)=>{var a;return(0,l.GK)([...c.detail(e)],()=>d.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(null==(a=null==s?void 0:s.enabled)||a),staleTime:3e5,...s})},u=()=>{let e=(0,t.jE)(),{showError:s,showSuccess:a}=(0,i.useNotifications)();return(0,r.n)({mutationFn:e=>{let s=n.M.toCreateRequest(e);return d.vehicleApiService.create(s)},onError:e=>{s("Failed to create vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:s=>{e.invalidateQueries({queryKey:c.all}),a('Vehicle "'.concat(s.licensePlate,'" has been created successfully!'))}})},x=()=>{let e=(0,t.jE)(),{showError:s,showSuccess:a}=(0,i.useNotifications)();return(0,r.n)({mutationFn:e=>{let{data:s,id:a}=e,t=n.M.toUpdateRequest(s);return d.vehicleApiService.update(a,t)},onError:e=>{s("Failed to update vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:s=>{e.invalidateQueries({queryKey:c.all}),e.invalidateQueries({queryKey:c.detail(s.id)}),a('Vehicle "'.concat(s.licensePlate,'" has been updated successfully!'))}})},h=()=>{let e=(0,t.jE)(),{showError:s,showSuccess:a}=(0,i.useNotifications)();return(0,r.n)({mutationFn:e=>d.vehicleApiService.delete(e),onError:e=>{s("Failed to delete vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:(s,t)=>{e.invalidateQueries({queryKey:c.all}),e.removeQueries({queryKey:c.detail(t)}),a("Vehicle has been deleted successfully!")}})}},85511:(e,s,a)=>{"use strict";a.d(s,{V:()=>c});var t=a(95155),r=a(965),l=a(73158);a(12115);var i=a(33683),n=a(30285),d=a(54036);function c(e){let{className:s,classNames:a,showOutsideDays:c=!0,...o}=e;return(0,t.jsx)(i.hv,{className:(0,d.cn)("p-3",s),classNames:{caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,d.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_range_end:"day-range-end",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",head_row:"flex",month:"space-y-4",months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",nav:"space-x-1 flex items-center",nav_button:(0,d.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_next:"absolute right-1",nav_button_previous:"absolute left-1",row:"flex w-full mt-2",table:"w-full border-collapse space-y-1",...a},components:{IconLeft:e=>{let{className:s,...a}=e;return(0,t.jsx)(r.A,{className:(0,d.cn)("h-4 w-4",s),...a})},IconRight:e=>{let{className:s,...a}=e;return(0,t.jsx)(l.A,{className:(0,d.cn)("h-4 w-4",s),...a})}},showOutsideDays:c,...o})}c.displayName="Calendar"},88240:(e,s,a)=>{"use strict";a.d(s,{A:()=>o});var t=a(95155),r=a(31949),l=a(67554),i=a(12115),n=a(55365),d=a(30285);class c extends i.Component{static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,s){this.setState({errorInfo:s}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",s.componentStack),this.props.onError&&this.props.onError(e,s)}render(){let{description:e="An unexpected error occurred.",resetLabel:s="Try Again",title:a="Something went wrong"}=this.props;if(this.state.hasError){var i;return this.props.fallback?this.props.fallback:(0,t.jsxs)(n.Fc,{className:"my-4",variant:"destructive",children:[(0,t.jsx)(r.A,{className:"mr-2 size-4"}),(0,t.jsx)(n.XL,{className:"text-lg font-semibold",children:a}),(0,t.jsxs)(n.TN,{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-2",children:(null==(i=this.state.error)?void 0:i.message)||e}),!1,(0,t.jsxs)(d.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,t.jsx)(l.A,{className:"mr-2 size-4"}),s]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}}let o=c},95947:(e,s,a)=>{Promise.resolve().then(a.bind(a,20360))},97697:(e,s,a)=>{"use strict";a.d(s,{fX:()=>n});var t=a(12115),r=a(65453),l=a(46786);let i=new Map;function n(e){return(0,t.useMemo)(()=>(i.has(e)||i.set(e,(0,r.v)()((0,l.lt)((0,l.Zr)((e,s)=>({activeTab:"all",layout:{viewMode:"cards",gridColumns:3,compactMode:!1,showFilters:!0,showSettings:!1},monitoring:{enabled:!1,refreshInterval:3e4,autoRefresh:!0,pausedDataTypes:new Set},filters:{},sortBy:"createdAt",sortDirection:"desc",selectedItems:new Set,searchTerm:"",setActiveTab:s=>e({activeTab:s},!1,"setActiveTab"),setViewMode:s=>e(e=>({layout:{...e.layout,viewMode:s}}),!1,"setViewMode"),setGridColumns:s=>e(e=>({layout:{...e.layout,gridColumns:s}}),!1,"setGridColumns"),toggleCompactMode:()=>e(e=>({layout:{...e.layout,compactMode:!e.layout.compactMode}}),!1,"toggleCompactMode"),toggleFilters:()=>e(e=>({layout:{...e.layout,showFilters:!e.layout.showFilters}}),!1,"toggleFilters"),toggleSettings:()=>e(e=>({layout:{...e.layout,showSettings:!e.layout.showSettings}}),!1,"toggleSettings"),updateFilter:(s,a)=>e(e=>({filters:{...e.filters,[s]:a}}),!1,"updateFilter"),clearFilters:()=>e({filters:{}},!1,"clearFilters"),setSorting:(s,a)=>e({sortBy:s,sortDirection:a},!1,"setSorting"),setSearchTerm:s=>e({searchTerm:s},!1,"setSearchTerm"),toggleItemSelection:s=>e(e=>{let a=new Set(e.selectedItems);return a.has(s)?a.delete(s):a.add(s),{selectedItems:a}},!1,"toggleItemSelection"),clearSelection:()=>e({selectedItems:new Set},!1,"clearSelection"),selectAll:s=>e({selectedItems:new Set(s)},!1,"selectAll"),setMonitoringEnabled:s=>e(e=>({monitoring:{...e.monitoring,enabled:s}}),!1,"setMonitoringEnabled"),setRefreshInterval:s=>e(e=>({monitoring:{...e.monitoring,refreshInterval:s}}),!1,"setRefreshInterval"),toggleAutoRefresh:()=>e(e=>({monitoring:{...e.monitoring,autoRefresh:!e.monitoring.autoRefresh}}),!1,"toggleAutoRefresh"),pauseDataType:s=>e(e=>({monitoring:{...e.monitoring,pausedDataTypes:new Set([...e.monitoring.pausedDataTypes,s])}}),!1,"pauseDataType"),resumeDataType:s=>e(e=>{let a=new Set(e.monitoring.pausedDataTypes);return a.delete(s),{monitoring:{...e.monitoring,pausedDataTypes:a}}},!1,"resumeDataType"),resetSettings:()=>e({layout:{viewMode:"cards",gridColumns:3,compactMode:!1,showFilters:!0,showSettings:!1},monitoring:{enabled:!1,refreshInterval:3e4,autoRefresh:!0,pausedDataTypes:new Set},filters:{},sortBy:"createdAt",sortDirection:"desc",selectedItems:new Set,searchTerm:""},!1,"resetSettings"),getFilteredData:(e,a)=>{let t=s(),r=[...e];if(t.searchTerm){let e=t.searchTerm.toLowerCase();r=r.filter(s=>Object.values(s).some(s=>String(s).toLowerCase().includes(e)))}return Object.entries(t.filters).forEach(e=>{let[s,t]=e;null!=t&&""!==t&&(r=r.filter(e=>{var r;let l=null==(r=a.filters)?void 0:r.find(e=>e.id===s);if(!l)return!0;switch(l.type){case"select":return e[s]===t;case"multiselect":return!Array.isArray(t)||t.includes(e[s]);case"toggle":return!t||e[s];default:return!0}}))}),r.sort((e,s)=>{let a=e[t.sortBy],r=s[t.sortBy],l="asc"===t.sortDirection?1:-1;return a<r?-1*l:a>r?+l:0}),r},getSelectedCount:()=>s().selectedItems.size,hasActiveFilters:()=>{let e=s();return e.searchTerm.length>0||Object.values(e.filters).some(e=>null!=e&&""!==e)}}),{name:"workhub-dashboard-".concat(e),partialize:e=>({layout:e.layout,monitoring:e.monitoring,filters:e.filters,sortBy:e.sortBy,sortDirection:e.sortDirection})}),{name:"dashboard-".concat(e)}))),i.get(e)),[e])}}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,7047,6897,3860,9664,375,7876,6874,1859,5247,6453,6463,7454,3030,6233,3122,4036,4767,303,7515,8128,4411,1051,9258,8441,1684,7358],()=>s(95947)),_N_E=e.O()}]);