(()=>{var e={};e.id=8782,e.ids=[8782],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19918:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),i=t(24920),a=t(16189);t(43210);var n=t(48041),o=t(70258),c=t(54827),l=t(72273);let d=()=>{let e=(0,a.useRouter)(),{mutateAsync:r}=(0,l.Y1)(),{handleSubmit:t,isLoading:d,error:p,clearError:u,state:m,retry:x}=(0,c.k)(async t=>{let s=await r(t);return e.push("/vehicles"),s},{toast:{entityType:"vehicle",entity:{make:"",model:""},successMessage:"Vehicle added to fleet! \uD83D\uDE97"},preSubmitValidation:e=>{if(!e.make?.trim())throw Error("Make is required");if(!e.model?.trim())throw Error("Model is required");if(!e.licensePlate?.trim())throw Error("License Plate is required");return!0},transformData:e=>({...e,year:"string"==typeof e.year?parseInt(e.year,10):e.year,make:e.make?.trim()||"",model:e.model?.trim()||"",licensePlate:e.licensePlate?.trim()?.toUpperCase()||""}),retry:{maxAttempts:3,exponentialBackoff:!0,retryCondition:e=>e.message.includes("network")||e.message.includes("timeout")}});return(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,s.jsx)(n.z,{description:"Enter the details for your new vehicle.",icon:i.A,title:"Add New Vehicle"}),p&&(0,s.jsx)("div",{className:"rounded-md bg-red-50 border border-red-200 p-4",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,s.jsx)("p",{className:"text-sm text-red-700 mt-1",children:p})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:["error"===m&&(0,s.jsx)("button",{onClick:x,className:"text-red-700 hover:text-red-900 text-sm font-medium",children:"Retry"}),(0,s.jsx)("button",{onClick:u,className:"text-red-700 hover:text-red-900 text-sm",children:"✕"})]})]})}),(0,s.jsx)(o.x,{isEditing:!1,isLoading:d,onSubmit:t})]})}},19944:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\vehicles\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\new\\page.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},26902:(e,r,t)=>{Promise.resolve().then(t.bind(t,19944))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35133:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=t(65239),i=t(48088),a=t(88170),n=t.n(a),o=t(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);t.d(r,c);let l={children:["",{children:["[locale]",{children:["vehicles",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,19944)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\new\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/vehicles/new/page",pathname:"/[locale]/vehicles/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},48041:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(60687);function i({children:e,description:r,icon:t,title:i}){return(0,s.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[t&&(0,s.jsx)(t,{className:"size-8 text-primary"}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:i})]}),r&&(0,s.jsx)("p",{className:"mt-1 text-muted-foreground",children:r})]}),e&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:e})]})}t(43210)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73758:(e,r,t)=>{Promise.resolve().then(t.bind(t,19918))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,3871,7048,8390,2670,9275,6013,8739,3302,2936,4827,5348],()=>t(35133));module.exports=s})();