(()=>{var e={};e.id=1569,e.ids=[1569,5176],e.modules={241:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\delegations\\\\report\\\\list\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\report\\list\\page.tsx","default")},997:(e,s,t)=>{"use strict";t.d(s,{k:()=>p});var a=t(60687),r=t(28946),l=t(11516),i=t(20620),n=t(36644);let o=(0,t(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var d=t(43210),c=t(68752),m=t(21342),u=t(3940),x=t(22482),h=t(22364);function p({className:e,csvData:s,enableCsv:t=!1,entityId:p,fileName:g,reportContentId:f,reportType:b,tableId:y}){let[j,N]=(0,d.useState)(!1),[v,w]=(0,d.useState)(!1),{showFormSuccess:k,showFormError:C}=(0,u.t6)(),A=async()=>{N(!0);try{let e=`/api/reports/${b}${p?`/${p}`:""}`,s=document.createElement("a");s.href=e,s.download=`${g}.pdf`,s.target="_blank",document.body.append(s),s.click(),s.remove(),k({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),C(`PDF download failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{N(!1)}},S=async()=>{if(t){w(!0);try{if(s?.data&&s.headers)(0,h.og)(s.data,s.headers,`${g}.csv`);else if(y){let e=(0,h.tL)(y);(0,h.og)(e.data,e.headers,`${g}.csv`)}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");k({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),C(`CSV generation failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{w(!1)}}},R=j||v;return(0,a.jsxs)("div",{className:(0,x.cn)("flex items-center gap-2 no-print",e),children:[(0,a.jsx)(c.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,a.jsx)(r.A,{className:"size-4"})}),(0,a.jsxs)(m.rI,{children:[(0,a.jsx)(m.ty,{asChild:!0,children:(0,a.jsx)(c.r,{actionType:"secondary","aria-label":"Download report",disabled:R,size:"icon",title:"Download Report",children:R?(0,a.jsx)(l.A,{className:"size-4 animate-spin"}):(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsxs)(m.SQ,{align:"end",children:[(0,a.jsxs)(m._2,{disabled:j,onClick:A,children:[j?(0,a.jsx)(l.A,{className:"mr-2 size-4 animate-spin"}):(0,a.jsx)(n.A,{className:"mr-2 size-4"}),(0,a.jsx)("span",{children:"Download PDF"})]}),t&&(0,a.jsxs)(m._2,{disabled:v,onClick:S,children:[v?(0,a.jsx)(l.A,{className:"mr-2 size-4 animate-spin"}):(0,a.jsx)(o,{className:"mr-2 size-4"}),(0,a.jsx)("span",{children:"Download CSV"})]})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,s,t)=>{"use strict";t.d(s,{A0:()=>n,BF:()=>o,Hj:()=>d,XI:()=>i,nA:()=>m,nd:()=>c});var a=t(60687),r=t(43210),l=t(22482);let i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{className:(0,l.cn)("w-full caption-bottom text-sm",e),ref:t,...s})}));i.displayName="Table";let n=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("thead",{className:(0,l.cn)("[&_tr]:border-b",e),ref:t,...s}));n.displayName="TableHeader";let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("tbody",{className:(0,l.cn)("[&_tr:last-child]:border-0",e),ref:t,...s}));o.displayName="TableBody",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("tfoot",{className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),ref:t,...s})).displayName="TableFooter";let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("tr",{className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),ref:t,...s}));d.displayName="TableRow";let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("th",{className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),ref:t,...s}));c.displayName="TableHead";let m=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("td",{className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),ref:t,...s}));m.displayName="TableCell",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("caption",{className:(0,l.cn)("mt-4 text-sm text-muted-foreground",e),ref:t,...s})).displayName="TableCaption"},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14583:(e,s,t)=>{"use strict";t.d(s,{$o:()=>f,Eb:()=>h,Iu:()=>m,WA:()=>p,cU:()=>u,dK:()=>c,n$:()=>x});var a=t(60687),r=t(43967),l=t(74158),i=t(69795),n=t(43210),o=t(29523),d=t(22482);let c=n.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{className:(0,d.cn)("flex justify-center",e),ref:t,...s}));c.displayName="Pagination";let m=n.forwardRef(({className:e,...s},t)=>(0,a.jsx)("ul",{className:(0,d.cn)("flex flex-row items-center gap-1",e),ref:t,...s}));m.displayName="PaginationContent";let u=n.forwardRef(({className:e,...s},t)=>(0,a.jsx)("li",{className:(0,d.cn)("",e),ref:t,...s}));u.displayName="PaginationItem";let x=n.forwardRef(({className:e,isActive:s,...t},r)=>(0,a.jsx)(o.$,{"aria-current":s?"page":void 0,className:(0,d.cn)("h-9 w-9",e),ref:r,size:"icon",variant:s?"outline":"ghost",...t}));x.displayName="PaginationLink";let h=n.forwardRef(({className:e,...s},t)=>(0,a.jsxs)(o.$,{className:(0,d.cn)("h-9 w-9 gap-1",e),ref:t,size:"icon",variant:"ghost",...s,children:[(0,a.jsx)(r.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Previous page"})]}));h.displayName="PaginationPrevious";let p=n.forwardRef(({className:e,...s},t)=>(0,a.jsxs)(o.$,{className:(0,d.cn)("h-9 w-9 gap-1",e),ref:t,size:"icon",variant:"ghost",...s,children:[(0,a.jsx)(l.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Next page"})]}));p.displayName="PaginationNext";let g=n.forwardRef(({className:e,...s},t)=>(0,a.jsxs)("span",{"aria-hidden":!0,className:(0,d.cn)("flex h-9 w-9 items-center justify-center",e),ref:t,...s,children:[(0,a.jsx)(i.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"More pages"})]}));function f({className:e,currentPage:s,onPageChange:t,totalPages:r}){let l=(()=>{let e=[];e.push(1);let t=Math.max(2,s-1),a=Math.min(r-1,s+1);t>2&&e.push("ellipsis1");for(let s=t;s<=a;s++)e.push(s);return a<r-1&&e.push("ellipsis2"),r>1&&e.push(r),e})();return r<=1?null:(0,a.jsx)(c,{className:e,children:(0,a.jsxs)(m,{children:[(0,a.jsx)(u,{children:(0,a.jsx)(h,{"aria-disabled":1===s?"true":void 0,"aria-label":"Go to previous page",disabled:1===s,onClick:()=>t(s-1)})}),l.map((e,r)=>"ellipsis1"===e||"ellipsis2"===e?(0,a.jsx)(u,{children:(0,a.jsx)(g,{})},`ellipsis-${r}`):(0,a.jsx)(u,{children:(0,a.jsx)(x,{"aria-label":`Go to page ${e}`,isActive:s===e,onClick:()=>t(e),children:e})},`page-${e}`)),(0,a.jsx)(u,{children:(0,a.jsx)(p,{"aria-disabled":s===r?"true":void 0,"aria-label":"Go to next page",disabled:s===r,onClick:()=>t(s+1)})})]})})}g.displayName="PaginationEllipsis"},15079:(e,s,t)=>{"use strict";t.d(s,{bq:()=>u,eb:()=>g,gC:()=>p,l6:()=>c,yv:()=>m});var a=t(60687),r=t(22670),l=t(61662),i=t(89743),n=t(58450),o=t(43210),d=t(22482);let c=r.bL;r.YJ;let m=r.WT,u=o.forwardRef(({children:e,className:s,...t},i)=>(0,a.jsxs)(r.l9,{className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),ref:i,...t,children:[e,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"size-4 opacity-50"})})]}));u.displayName=r.l9.displayName;let x=o.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.PP,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),ref:t,...s,children:(0,a.jsx)(i.A,{className:"size-4"})}));x.displayName=r.PP.displayName;let h=o.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.wn,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),ref:t,...s,children:(0,a.jsx)(l.A,{className:"size-4"})}));h.displayName=r.wn.displayName;let p=o.forwardRef(({children:e,className:s,position:t="popper",...l},i)=>(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:t,ref:i,...l,children:[(0,a.jsx)(x,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),(0,a.jsx)(h,{})]})}));p.displayName=r.UC.displayName,o.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.JU,{className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),ref:t,...s})).displayName=r.JU.displayName;let g=o.memo(o.forwardRef(({children:e,className:s,...t},l)=>{let i=o.useCallback(e=>{"function"==typeof l?l(e):l&&(l.current=e)},[l]);return(0,a.jsxs)(r.q7,{className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),ref:i,...t,children:[(0,a.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:e})]})}));g.displayName=r.q7.displayName,o.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.wv,{className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),ref:t,...s})).displayName=r.wv.displayName},15795:(e,s,t)=>{"use strict";function a(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function r(e){if(e.fullName?.trim())return e.fullName.trim();if(e.name?.trim()){let s=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(s.toLowerCase())||s.includes("_")){let e=s.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${e} (Role)`}return s}if(e.role){let s=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${s} (Role)`}return"Unknown Employee"}function l(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function i(e){return e.replaceAll("_"," ")}t.d(s,{DV:()=>r,fZ:()=>a,s:()=>l,vq:()=>i})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20620:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},21820:e=>{"use strict";e.exports=require("os")},26398:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29368:()=>{},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43967:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},52027:(e,s,t)=>{"use strict";t.d(s,{gO:()=>u,jt:()=>g,pp:()=>x});var a=t(60687),r=t(72963),l=t(11516);t(43210);var i=t(68752),n=t(91821),o=t(85726),d=t(22482);let c={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function u({children:e,className:s,data:t,emptyComponent:r,error:l,errorComponent:i,isLoading:n,loadingComponent:o,onRetry:c}){return n?o||(0,a.jsx)(p,{...s&&{className:s},text:"Loading..."}):l?i||(0,a.jsx)(h,{...s&&{className:s},message:l,...c&&{onRetry:c}}):!t||Array.isArray(t)&&0===t.length?r||(0,a.jsx)("div",{className:(0,d.cn)("text-center py-8",s),children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,a.jsx)("div",{className:s,children:e(t)})}function x({className:e,description:s,icon:t,primaryAction:r,secondaryAction:l,title:n}){return(0,a.jsxs)("div",{className:(0,d.cn)("space-y-6 text-center py-12",e),children:[t&&(0,a.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,a.jsx)(t,{className:"h-10 w-10 text-muted-foreground"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:n}),s&&(0,a.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:s})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[r&&(0,a.jsx)(i.r,{actionType:"primary",asChild:!!r.href,icon:r.icon,onClick:r.onClick,children:r.href?(0,a.jsx)("a",{href:r.href,children:r.label}):r.label}),l&&(0,a.jsx)(i.r,{actionType:"tertiary",asChild:!!l.href,icon:l.icon,onClick:l.onClick,children:l.href?(0,a.jsx)("a",{href:l.href,children:l.label}):l.label})]})]})}function h({className:e,message:s,onRetry:t}){return(0,a.jsxs)(n.Fc,{className:(0,d.cn)("my-4",e),variant:"destructive",children:[(0,a.jsx)(r.A,{className:"size-4"}),(0,a.jsx)(n.XL,{children:"Error"}),(0,a.jsx)(n.TN,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:s}),t&&(0,a.jsx)(i.r,{actionType:"tertiary",icon:(0,a.jsx)(l.A,{className:"size-4"}),onClick:t,size:"sm",children:"Try Again"})]})})]})}function p({className:e,fullPage:s=!1,size:t="md",text:r}){return(0,a.jsx)("div",{className:(0,d.cn)("flex items-center justify-center",s&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",e),children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(l.A,{className:(0,d.cn)("animate-spin text-primary",c[t])}),r&&(0,a.jsx)("span",{className:(0,d.cn)("mt-2 text-muted-foreground",m[t]),children:r})]})})}function g({className:e,count:s=1,testId:t="loading-skeleton",variant:r="default"}){return"card"===r?(0,a.jsx)("div",{className:(0,d.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",e),"data-testid":t,children:Array(s).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,a.jsx)(o.E,{className:"aspect-[16/10] w-full"}),(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)(o.E,{className:"mb-1 h-7 w-3/4"}),(0,a.jsx)(o.E,{className:"mb-3 h-4 w-1/2"}),(0,a.jsx)(o.E,{className:"my-3 h-px w-full"}),(0,a.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.E,{className:"mr-2.5 size-5 rounded-full"}),(0,a.jsx)(o.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===r?(0,a.jsxs)("div",{className:(0,d.cn)("space-y-3",e),"data-testid":t,children:[(0,a.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,a.jsx)(o.E,{className:"h-8 flex-1"},s))}),Array(s).fill(0).map((e,s)=>(0,a.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,a.jsx)(o.E,{className:"h-6 flex-1"},s))},s))]}):"list"===r?(0,a.jsx)("div",{className:(0,d.cn)("space-y-3",e),"data-testid":t,children:Array(s).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(o.E,{className:"size-12 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)(o.E,{className:"h-4 w-1/3"}),(0,a.jsx)(o.E,{className:"h-4 w-full"})]})]},s))}):"stats"===r?(0,a.jsx)("div",{className:(0,d.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",e),"data-testid":t,children:Array(s).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(o.E,{className:"h-5 w-1/3"}),(0,a.jsx)(o.E,{className:"size-5 rounded-full"})]}),(0,a.jsx)(o.E,{className:"mt-3 h-8 w-1/2"}),(0,a.jsx)(o.E,{className:"mt-2 h-4 w-2/3"})]},s))}):(0,a.jsx)("div",{className:(0,d.cn)("space-y-2",e),"data-testid":t,children:Array(s).fill(0).map((e,s)=>(0,a.jsx)(o.E,{className:"h-5 w-full"},s))})}},52179:(e,s,t)=>{Promise.resolve().then(t.bind(t,241))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61354:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>r});var a=t(37413);let r={title:"Delegation List Report"};function l({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}},61408:(e,s,t)=>{Promise.resolve().then(t.bind(t,79493))},61425:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d={children:["",{children:["[locale]",{children:["delegations",{children:["report",{children:["list",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,241)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\report\\list\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,61354)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\report\\list\\layout.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\delegations\\report\\list\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/delegations/report/list/page",pathname:"/[locale]/delegations/report/list",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},62453:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68752:(e,s,t)=>{"use strict";t.d(s,{r:()=>d});var a=t(60687),r=t(11516),l=t(43210),i=t.n(l),n=t(29523),o=t(22482);let d=i().forwardRef(({actionType:e="primary",asChild:s=!1,children:t,className:l,disabled:i,icon:d,isLoading:c=!1,loadingText:m,...u},x)=>{let{className:h,variant:p}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[e];return(0,a.jsx)(n.$,{asChild:s,className:(0,o.cn)(h,l),disabled:c||i,ref:x,variant:p,...u,children:c?(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,a.jsx)(r.A,{className:"mr-2 size-4 animate-spin"}),m||t]}):(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",d&&(0,a.jsx)("span",{className:"mr-2",children:d}),t]})})});d.displayName="ActionButton"},69795:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},74075:e=>{"use strict";e.exports=require("zlib")},75176:(e,s,t)=>{"use strict";t.d(s,{cl:()=>a.cl,delegationApiService:()=>a.ac,employeeApiService:()=>a.aV,reliabilityApiService:()=>a.e_,taskApiService:()=>a.Hg,vehicleApiService:()=>a.oL});var a=t(3302);t(8342)},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79493:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>M});var a=t(60687),r=t(75699),l=t(58261);t(29368),t(62453);var i=t(89743),n=t(61662),o=t(41936),d=t(78726),c=t(92876),m=t(26398),u=t(48206),x=t(16189),h=t(43210),p=t(44493),g=t(22482);let f={Cancelled:{bg:"bg-gradient-to-br from-red-50 to-red-100",border:"border-red-200",text:"text-red-700"},Completed:{bg:"bg-gradient-to-br from-purple-50 to-purple-100",border:"border-purple-200",text:"text-purple-700"},Confirmed:{bg:"bg-gradient-to-br from-green-50 to-green-100",border:"border-green-200",text:"text-green-700"},In_Progress:{bg:"bg-gradient-to-br from-yellow-50 to-yellow-100",border:"border-yellow-200",text:"text-yellow-700"},No_details:{bg:"bg-gradient-to-br from-gray-50 to-gray-100",border:"border-gray-200",text:"text-gray-700"},Planned:{bg:"bg-gradient-to-br from-blue-50 to-blue-100",border:"border-blue-200",text:"text-blue-700"}},b=e=>e.replace("_"," ");function y({className:e,delegations:s}){let t=s.length,r=s.reduce((e,s)=>{let t=s.status;return e[t]=(e[t]||0)+1,e},{}),l=s.reduce((e,s)=>e+(s.delegates?.length||0),0),i=Object.entries(r).sort(([,e],[,s])=>s-e).map(([e])=>e);return(0,a.jsxs)("div",{className:(0,g.cn)("mt-6 mb-8",e),children:[(0,a.jsxs)("div",{className:"mb-6 grid grid-cols-2 gap-4 lg:grid-cols-4",children:[(0,a.jsx)(j,{className:"border-slate-200 bg-gradient-to-br from-slate-50 to-slate-100 shadow-sm transition-shadow hover:shadow-md",label:"Total Delegations",textColor:"text-slate-700",value:t,valueColor:"text-slate-800"}),(0,a.jsx)(j,{className:"border-indigo-200 bg-gradient-to-br from-indigo-50 to-indigo-100 shadow-sm transition-shadow hover:shadow-md",label:"Total Delegates",textColor:"text-indigo-700",value:l,valueColor:"text-indigo-800"}),i.slice(0,2).map(e=>{let s=f[e];return(0,a.jsx)(j,{className:(0,g.cn)(s.bg,s.border,"shadow-sm hover:shadow-md transition-shadow"),label:b(e),textColor:s.text,value:r[e],valueColor:s.text},e)})]}),i.length>2&&(0,a.jsx)("div",{className:"grid grid-cols-2 gap-3 sm:grid-cols-3 lg:grid-cols-6",children:i.slice(2).map(e=>{let s=f[e];return(0,a.jsx)(j,{className:(0,g.cn)(s.bg,s.border,"shadow-sm hover:shadow-md transition-shadow"),compact:!0,label:b(e),textColor:s.text,value:r[e],valueColor:s.text},e)})})]})}function j({className:e,compact:s=!1,label:t,textColor:r="text-gray-600",value:l,valueColor:i="text-gray-800"}){return(0,a.jsx)(p.Zp,{className:(0,g.cn)("overflow-hidden border transition-all duration-200",e),children:(0,a.jsxs)(p.Wu,{className:(0,g.cn)("text-center",s?"p-3":"p-4"),children:[(0,a.jsx)("div",{className:(0,g.cn)("font-bold",s?"text-xl mb-1":"text-3xl mb-2",i),children:l.toLocaleString()}),(0,a.jsx)("div",{className:(0,g.cn)("font-medium",s?"text-xs":"text-sm",r),children:t})]})})}var N=t(997),v=t(96834),w=t(29523),k=t(89667),C=t(52027),A=t(14583),S=t(15079),R=t(6211),E=t(63502),P=t(15795);let D=["Planned","Confirmed","In_Progress","Completed","Cancelled","No_details"],z=e=>{switch(e){case"Cancelled":return"bg-red-100 text-red-800 border-red-300";case"Completed":return"bg-purple-100 text-purple-800 border-purple-300";case"Confirmed":return"bg-green-100 text-green-800 border-green-300";case"In_Progress":return"bg-yellow-100 text-yellow-800 border-yellow-300";case"Planned":return"bg-blue-100 text-blue-800 border-blue-300";default:return"bg-gray-100 text-gray-800 border-gray-300"}},T=e=>{if(!e)return"N/A";try{return(0,r.GP)((0,l.H)(e),"MMM d, yyyy")}catch{return"Invalid Date"}};function M(){return(0,a.jsx)(h.Suspense,{fallback:(0,a.jsx)("div",{className:"py-10 text-center",children:"Loading report..."}),children:(0,a.jsx)(q,{})})}function q(){(0,x.useSearchParams)();let{data:e,error:s,isLoading:t,refetch:r}=(0,E.BD)(),l=(0,h.useMemo)(()=>e||[],[e]),[f,b]=(0,h.useState)(""),[j,M]=(0,h.useState)(""),[q,F]=(0,h.useState)("all"),[_,L]=(0,h.useState)({}),[$,I]=(0,h.useState)(1),[O]=(0,h.useState)(10),[Q,H]=(0,h.useState)("durationFrom"),[U,G]=(0,h.useState)("asc"),Z=(0,h.useCallback)((e,s,t)=>[...e].sort((e,a)=>{let r,l;switch(s){case"delegates":r=e.delegates?.length||0,l=a.delegates?.length||0;break;case"durationFrom":r=new Date(e.durationFrom).getTime(),l=new Date(a.durationFrom).getTime();break;case"eventName":r=e.eventName.toLowerCase(),l=a.eventName.toLowerCase();break;case"location":r=e.location.toLowerCase(),l=a.location.toLowerCase();break;case"status":r=e.status,l=a.status;break;default:r=e[s],l=a[s]}return null==r||null==l?0:r<l?"asc"===t?-1:1:r>l?"asc"===t?1:-1:0}),[]),V=(0,h.useMemo)(()=>{let e=[...l];if(j){let s=j.toLowerCase();e=e.filter(e=>e.eventName.toLowerCase().includes(s)||e.location.toLowerCase().includes(s)||e.delegates?.some(e=>e.name.toLowerCase().includes(s))||e.notes?.toLowerCase().includes(s)||e.status.toLowerCase().includes(s))}return"all"!==q&&(e=e.filter(e=>e.status===q)),_.from&&(e=e.filter(e=>new Date(e.durationFrom)>=_.from)),_.to&&(e=e.filter(e=>new Date(e.durationFrom)<=_.to)),Z(e,Q,U)},[l,j,q,_,Q,U,Z]),B=(0,h.useCallback)(e=>{Q===e?G("asc"===U?"desc":"asc"):(H(e),G("asc"))},[Q,U]),K=(0,h.useCallback)(e=>Q!==e?"none":"asc"===U?"ascending":"descending",[Q,U]),W=(0,h.useCallback)(()=>{b(""),M(""),F("all"),L({}),I(1)},[]),J=$*O,X=J-O,Y=(0,h.useMemo)(()=>V.slice(X,J),[V,X,J]),ee=(0,h.useMemo)(()=>Math.ceil(V.length/O),[V.length,O]),es=(0,h.useCallback)(e=>{I(e)},[]),et=(0,h.useCallback)(e=>Q!==e?null:"asc"===U?(0,a.jsx)(i.A,{className:"ml-1 inline-block size-4"}):(0,a.jsx)(n.A,{className:"ml-1 inline-block size-4"}),[Q,U]);return t?(0,a.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,a.jsx)(C.jt,{count:5,variant:"table"})}):s?(0,a.jsxs)("div",{className:"mx-auto max-w-5xl p-4 text-red-500",children:["Error loading delegations: ",s.message,(0,a.jsx)(w.$,{className:"ml-2",onClick:()=>r(),children:"Retry"})]}):(0,a.jsxs)("div",{className:"delegation-report-container",children:[(0,a.jsx)("div",{className:"no-print mb-6 text-right",children:(0,a.jsx)(N.k,{enableCsv:V.length>0,fileName:`delegations-list-report-${new Date().toISOString().split("T")[0]}`,reportContentId:"#delegations-list-report-content",reportType:"delegations",tableId:"#delegations-table"})}),(0,a.jsxs)("div",{className:"report-content",id:"delegations-list-report-content",children:[(0,a.jsxs)("header",{className:"delegation-report-header",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"delegation-report-title",children:"Delegation List Report"}),(0,a.jsx)("div",{className:"no-print mx-auto h-1 w-24 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600"})]}),(0,a.jsx)("p",{className:"delegation-report-subtitle",children:f||"all"!==q?`Filtered by: ${"all"===q?"":`Status - ${(0,P.fZ)(q)}`}${f?("all"===q?"":" | ")+`Search - "${f}"`:""}`:"All Delegations"}),(0,a.jsxs)("p",{className:"delegation-report-date",children:["Generated: ",new Date().toLocaleDateString()," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]}),(0,a.jsx)("div",{className:"no-print delegation-summary-grid",children:(0,a.jsx)(y,{delegations:V.map(e=>({...e,delegates:e.delegates||[],escortEmployeeIds:e.escorts?.map(e=>e.employeeId.toString())||[]}))})}),(0,a.jsxs)("div",{className:"print-only delegation-print-summary",children:[(0,a.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,a.jsx)("span",{className:"delegation-print-summary-label",children:"Total Delegations:"})," ",(0,a.jsx)("span",{className:"delegation-print-summary-value",children:V.length})]}),(0,a.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,a.jsx)("span",{className:"delegation-print-summary-label",children:"Total Delegates:"})," ",(0,a.jsx)("span",{className:"delegation-print-summary-value",children:V.reduce((e,s)=>e+(s.delegates?.length||0),0)})]}),D.map(e=>({count:V.filter(s=>s.status===e).length,status:e})).filter(e=>e.count>0).sort((e,s)=>s.count-e.count).slice(0,3).map(({count:e,status:s})=>(0,a.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,a.jsxs)("span",{className:"delegation-print-summary-label",children:[(0,P.fZ)(s),":"]})," ",(0,a.jsx)("span",{className:"delegation-print-summary-value",children:e})]},s)),"all"!==q&&(0,a.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,a.jsx)("span",{className:"delegation-print-summary-label",children:"Filtered by Status:"})," ",(0,a.jsx)("span",{className:"delegation-print-summary-value",children:(0,P.fZ)(q)})]}),f&&(0,a.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,a.jsx)("span",{className:"delegation-print-summary-label",children:"Search Term:"})," ",(0,a.jsxs)("span",{className:"delegation-print-summary-value",children:['"',f,'"']})]})]})]}),(0,a.jsx)("div",{className:"no-print mb-8",children:(0,a.jsx)(p.Zp,{className:"border-0 bg-gradient-to-r from-slate-50 to-gray-50 shadow-lg",children:(0,a.jsxs)(p.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"delegation-filters",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"status-filter",children:"Filter by Status"}),(0,a.jsxs)(S.l6,{"aria-label":"Filter by status",onValueChange:F,value:q,children:[(0,a.jsx)(S.bq,{className:"w-full border-gray-300 bg-white focus:border-blue-500 focus:ring-blue-500",children:(0,a.jsx)(S.yv,{placeholder:"All Statuses"})}),(0,a.jsxs)(S.gC,{children:[(0,a.jsx)(S.eb,{value:"all",children:"All Statuses"}),D.map(e=>(0,a.jsx)(S.eb,{value:e,children:(0,P.fZ)(e)},e))]})]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"search-input",children:"Search Delegations"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(k.p,{"aria-label":"Search delegations",className:"border-gray-300 bg-white px-10 focus:border-blue-500 focus:ring-blue-500",id:"search-input",onChange:e=>b(e.target.value),placeholder:"Search by event, location, or delegate...",type:"text",value:f}),(0,a.jsx)(o.A,{"aria-hidden":"true",className:"absolute left-3 top-1/2 size-5 -translate-y-1/2 text-gray-400"}),f&&(0,a.jsxs)(w.$,{"aria-label":"Clear search",className:"absolute right-1 top-1/2 size-7 -translate-y-1/2",onClick:()=>b(""),size:"icon",variant:"ghost",children:[(0,a.jsx)(d.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Clear search"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"date-range",children:"Date Range"}),(0,a.jsx)(k.p,{"aria-label":"Date range filter (coming soon)",className:"bg-gray-100 opacity-50",disabled:!0,id:"date-range",placeholder:"Date range filter coming soon",type:"text"})]})]}),(f||"all"!==q)&&(0,a.jsxs)("div",{className:"mt-6 flex items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-4",children:[(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"font-semibold text-blue-800",children:"Active Filters:"}),f&&(0,a.jsxs)("span",{className:"ml-2 rounded bg-blue-100 px-2 py-1 text-xs text-blue-800",children:['Search: "',f,'"']}),"all"!==q&&(0,a.jsxs)("span",{className:"ml-2 rounded bg-green-100 px-2 py-1 text-xs text-green-800",children:["Status:"," ",(0,P.fZ)(q)]})]}),(0,a.jsx)(w.$,{"aria-label":"Reset all filters",className:"border-blue-300 text-blue-700 hover:bg-blue-100",onClick:W,size:"sm",variant:"outline",children:"Reset Filters"})]})]})})}),0===V.length?(0,a.jsx)("div",{className:"rounded-xl border border-gray-200 bg-gradient-to-br from-gray-50 to-slate-100 py-16 text-center",children:(0,a.jsxs)("div",{className:"mx-auto max-w-md",children:[(0,a.jsx)("div",{className:"mb-4 text-gray-400",children:(0,a.jsx)(c.A,{className:"mx-auto mb-4 size-16"})}),(0,a.jsx)("h3",{className:"mb-2 text-lg font-semibold text-gray-700",children:"No delegations found"}),(0,a.jsx)("p",{className:"mb-4 text-gray-500",children:"No delegations match the current filter criteria."}),(0,a.jsx)(w.$,{"aria-label":"Reset filters to show all delegations",className:"mt-2",onClick:W,size:"lg",variant:"outline",children:"Reset Filters"})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"delegation-table-container",children:(0,a.jsxs)(R.XI,{className:"delegation-table",id:"delegations-table",children:[(0,a.jsx)(R.A0,{children:(0,a.jsxs)(R.Hj,{className:"border-b border-gray-200 bg-gradient-to-r from-slate-100 to-gray-100",children:[(0,a.jsxs)(R.nd,{"aria-label":"Sort by event name","aria-sort":K("eventName"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>B("eventName"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),B("eventName"))},role:"columnheader",style:{width:"25%"},tabIndex:0,children:["Event Name ",et("eventName")]}),(0,a.jsxs)(R.nd,{"aria-label":"Sort by location","aria-sort":K("location"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>B("location"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),B("location"))},role:"columnheader",style:{width:"20%"},tabIndex:0,children:["Location ",et("location")]}),(0,a.jsxs)(R.nd,{"aria-label":"Sort by duration","aria-sort":K("durationFrom"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>B("durationFrom"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),B("durationFrom"))},role:"columnheader",style:{width:"20%"},tabIndex:0,children:["Duration ",et("durationFrom")]}),(0,a.jsxs)(R.nd,{"aria-label":"Sort by status","aria-sort":K("status"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>B("status"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),B("status"))},role:"columnheader",style:{width:"10%"},tabIndex:0,children:["Status ",et("status")]}),(0,a.jsxs)(R.nd,{"aria-label":"Sort by number of delegates","aria-sort":K("delegates"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>B("delegates"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),B("delegates"))},role:"columnheader",style:{width:"25%"},tabIndex:0,children:["Delegates ",et("delegates")]})]})}),(0,a.jsx)(R.BF,{className:"no-print",children:Y.map((e,s)=>(0,a.jsxs)(R.Hj,{className:(0,g.cn)("page-break-inside-avoid hover:bg-slate-50 transition-colors border-b border-gray-100",s%2==0?"bg-white":"bg-slate-50/30"),children:[(0,a.jsx)(R.nA,{className:"print-text-wrap p-4 font-medium",title:e.eventName,children:(0,a.jsx)("div",{className:"font-semibold text-gray-800",children:e.eventName})}),(0,a.jsx)(R.nA,{className:"print-text-wrap print-location-col p-4",title:e.location,children:(0,a.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,a.jsx)(m.A,{className:"mr-2 size-4 text-gray-400"}),e.location]})}),(0,a.jsx)(R.nA,{className:"whitespace-nowrap p-4",children:(0,a.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,a.jsx)(c.A,{className:"mr-2 size-4 text-gray-400"}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{children:T(e.durationFrom)}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["to ",T(e.durationTo)]})]})]})}),(0,a.jsx)(R.nA,{className:"p-4",children:(0,a.jsx)(v.E,{className:(0,g.cn)("text-xs py-1 px-2 font-medium",z(e.status)),children:(0,P.fZ)(e.status)})}),(0,a.jsx)(R.nA,{className:"print-text-wrap max-w-xs p-4",title:e.delegates?.map(e=>e.name).join(", "),children:(e.delegates?.length||0)>0?(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(u.A,{className:"mr-2 mt-0.5 size-4 shrink-0 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"no-print",children:3>=(e.delegates?.length||0)?(0,a.jsx)("div",{className:"space-y-1",children:e.delegates?.map((e,s)=>(0,a.jsx)("div",{className:"text-sm text-gray-700",children:e.name},e.id||s))}):(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-1 space-y-1",children:e.delegates?.slice(0,2).map((e,s)=>(0,a.jsx)("div",{className:"text-sm text-gray-700",children:e.name},e.id||s))}),(0,a.jsxs)("span",{className:"rounded bg-gray-100 px-2 py-1 text-xs text-gray-500",children:["+",(e.delegates?.length||0)-2," ","more"]})]})}),(0,a.jsx)("span",{className:"print-only",children:(0,a.jsx)("div",{className:"space-y-1",children:e.delegates?.map((e,s)=>(0,a.jsx)("div",{className:"text-sm",children:e.name},e.id||s))})})]})]}):(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"No delegates"})})]},e.id))}),(0,a.jsx)(R.BF,{className:"print-only",children:V.map((e,s)=>(0,a.jsxs)(R.Hj,{className:(0,g.cn)("page-break-inside-avoid",s%2==0?"bg-white":"bg-slate-50/30"),children:[(0,a.jsx)(R.nA,{className:"print-text-wrap font-medium",title:e.eventName,children:(0,a.jsx)("div",{className:"font-semibold text-gray-800",children:e.eventName})}),(0,a.jsx)(R.nA,{className:"print-text-wrap print-location-col",title:e.location,children:(0,a.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,a.jsx)(m.A,{className:"mr-2 size-4 text-gray-400"}),e.location]})}),(0,a.jsx)(R.nA,{className:"",children:(0,a.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,a.jsx)(c.A,{className:"mr-2 size-4 text-gray-400"}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{children:T(e.durationFrom)}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["to ",T(e.durationTo)]})]})]})}),(0,a.jsx)(R.nA,{className:"",children:(0,a.jsx)(v.E,{className:(0,g.cn)("text-xs py-1 px-2 font-medium",z(e.status)),children:(0,P.fZ)(e.status)})}),(0,a.jsx)(R.nA,{className:"print-text-wrap",title:e.delegates?.map(e=>e.name).join(", "),children:(e.delegates?.length||0)>0?(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(u.A,{className:"mr-2 mt-0.5 size-4 shrink-0 text-gray-400"}),(0,a.jsx)("div",{children:(0,a.jsx)("div",{className:"space-y-1",children:e.delegates?.map((e,s)=>(0,a.jsx)("div",{className:"text-sm",children:e.name},e.id||s))})})]}):(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"No delegates"})})]},e.id))})]})}),V.length>O&&(0,a.jsx)("div",{className:"no-print mt-8 flex justify-center",children:(0,a.jsx)(A.$o,{currentPage:$,onPageChange:es,totalPages:ee})})]}),(0,a.jsx)("footer",{className:"delegation-report-footer",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("p",{className:"font-medium",children:["Report generated on: ",new Date().toLocaleDateString()," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]}),(0,a.jsx)("p",{className:"text-gray-400",children:"WorkHub - Delegation Management"}),(0,a.jsx)("p",{className:"print-only text-xs",children:"Confidential - For internal use only"})]})})]})]})}},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(60687),r=t(22482);function l({className:e,...s}){return(0,a.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",e),...s})}},91645:e=>{"use strict";e.exports=require("net")},92876:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},93425:(e,s,t)=>{"use strict";t.d(s,{E:()=>p});var a=t(43210),r=t(33465),l=t(5563),i=t(35536),n=t(31212);function o(e,s){let t=new Set(s);return e.filter(e=>!t.has(e))}var d=class extends i.Q{#e;#s;#t;#a;#r;#l;#i;#n;#o=[];constructor(e,s,t){super(),this.#e=e,this.#a=t,this.#t=[],this.#r=[],this.#s=[],this.setQueries(s)}onSubscribe(){1===this.listeners.size&&this.#r.forEach(e=>{e.subscribe(s=>{this.#d(e,s)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#r.forEach(e=>{e.destroy()})}setQueries(e,s){this.#t=e,this.#a=s,r.jG.batch(()=>{let e=this.#r,s=this.#c(this.#t);this.#o=s,s.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let t=s.map(e=>e.observer),a=t.map(e=>e.getCurrentResult()),r=t.some((s,t)=>s!==e[t]);(e.length!==t.length||r)&&(this.#r=t,this.#s=a,this.hasListeners()&&(o(e,t).forEach(e=>{e.destroy()}),o(t,e).forEach(e=>{e.subscribe(s=>{this.#d(e,s)})}),this.#m()))})}getCurrentResult(){return this.#s}getQueries(){return this.#r.map(e=>e.getCurrentQuery())}getObservers(){return this.#r}getOptimisticResult(e,s){let t=this.#c(e),a=t.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[a,e=>this.#u(e??a,s),()=>this.#x(a,t)]}#x(e,s){return s.map((t,a)=>{let r=e[a];return t.defaultedQueryOptions.notifyOnChangeProps?r:t.observer.trackResult(r,e=>{s.forEach(s=>{s.observer.trackProp(e)})})})}#u(e,s){return s?(this.#l&&this.#s===this.#n&&s===this.#i||(this.#i=s,this.#n=this.#s,this.#l=(0,n.BH)(this.#l,s(e))),this.#l):e}#c(e){let s=new Map(this.#r.map(e=>[e.options.queryHash,e])),t=[];return e.forEach(e=>{let a=this.#e.defaultQueryOptions(e),r=s.get(a.queryHash);r?t.push({defaultedQueryOptions:a,observer:r}):t.push({defaultedQueryOptions:a,observer:new l.$(this.#e,a)})}),t}#d(e,s){let t=this.#r.indexOf(e);-1!==t&&(this.#s=function(e,s,t){let a=e.slice(0);return a[s]=t,a}(this.#s,t,s),this.#m())}#m(){if(this.hasListeners()){let e=this.#l,s=this.#x(this.#s,this.#o);e!==this.#u(s,this.#a?.combine)&&r.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#s)})})}}},c=t(8693),m=t(24903),u=t(18228),x=t(16142),h=t(76935);function p({queries:e,...s},t){let i=(0,c.jE)(t),o=(0,m.w)(),p=(0,u.h)(),g=a.useMemo(()=>e.map(e=>{let s=i.defaultQueryOptions(e);return s._optimisticResults=o?"isRestoring":"optimistic",s}),[e,i,o]);g.forEach(e=>{(0,h.jv)(e),(0,x.LJ)(e,p)}),(0,x.wZ)(p);let[f]=a.useState(()=>new d(i,g,s)),[b,y,j]=f.getOptimisticResult(g,s.combine),N=!o&&!1!==s.subscribed;a.useSyncExternalStore(a.useCallback(e=>N?f.subscribe(r.jG.batchCalls(e)):n.lQ,[f,N]),()=>f.getCurrentResult(),()=>f.getCurrentResult()),a.useEffect(()=>{f.setQueries(g,s)},[g,s,f]);let v=b.some((e,s)=>(0,h.EU)(g[s],e))?b.flatMap((e,s)=>{let t=g[s];if(t){let s=new l.$(i,t);if((0,h.EU)(t,e))return(0,h.iL)(t,s,p);(0,h.nE)(e,o)&&(0,h.iL)(t,s,p)}return[]}):[];if(v.length>0)throw Promise.all(v);let w=b.find((e,s)=>{let t=g[s];return t&&(0,x.$1)({result:e,errorResetBoundary:p,throwOnError:t.throwOnError,query:i.getQueryCache().get(t.queryHash),suspense:t.suspense})});if(w?.error)throw w.error;return y(j())}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,3871,7048,8390,2670,8739,3302,2936,3502,2452],()=>t(61425));module.exports=a})();