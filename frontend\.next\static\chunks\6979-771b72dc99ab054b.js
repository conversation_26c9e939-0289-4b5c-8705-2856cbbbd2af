"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6979],{29360:(e,s,a)=>{a.d(s,{F:()=>S});var t=a(95155),l=a(91721),r=a(19637),i=a(8376),c=a(24371),n=a(76570),d=a(33349),m=a(50172),x=a(40207),o=a(45731),u=a(57679),j=a(37648),h=a(51920),f=a(6874),N=a.n(f),p=a(35695),g=a(12115),v=a(91394),y=a(26126),w=a(30285),_=a(66695),A=a(44838),b=a(55365),z=a(17313),R=a(40283);let E=e=>{switch(e){case"ADMIN":return"bg-purple-500 hover:bg-purple-600 text-white";case"EMPLOYEE":return"bg-green-500 hover:bg-green-600 text-white";case"MANAGER":return"bg-blue-500 hover:bg-blue-600 text-white";case"SUPER_ADMIN":return"bg-red-500 hover:bg-red-600 text-white";default:return"bg-gray-500 hover:bg-gray-600 text-white"}};function S(e){var s,a,f,S,C;let{showSignOut:U=!0,variant:D="dropdown"}=e,{signOut:B,user:Z,userRole:I}=(0,R.useAuthContext)(),M=(0,p.useRouter)(),[L,V]=(0,g.useState)(!1);if(!Z)return null;let k=async()=>{V(!0);try{await B(),M.push("/login")}catch(e){console.error("Sign out error:",e)}finally{V(!1)}},F=e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"Never",T=e=>e?new Date(e).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Never",W=e=>{if(!e)return"Never";let s=new Date,a=new Date(e),t=Math.floor((s.getTime()-a.getTime())/1e3);return t<60?"Just now":t<3600?"".concat(Math.floor(t/60),"m ago"):t<86400?"".concat(Math.floor(t/3600),"h ago"):t<2592e3?"".concat(Math.floor(t/86400),"d ago"):F(e)},P=()=>{var e,s,a;return console.log("\uD83D\uDD0D Role Debug Info:",{"user.user_metadata?.role":null==(e=Z.user_metadata)?void 0:e.role,"userRole (from context)":I,"user.user_metadata":Z.user_metadata,"user.app_metadata":Z.app_metadata,"user.email":Z.email}),(null==(s=Z.user_metadata)?void 0:s.role)&&Z.user_metadata.role!==I&&console.warn("\uD83D\uDEA8 Role mismatch detected! User needs to refresh their session.",{metadataRole:Z.user_metadata.role,contextRole:I}),(null==(a=Z.user_metadata)?void 0:a.role)||I||"USER"},q=()=>{var e;return(null==(e=Z.user_metadata)?void 0:e.is_active)!==!1?"Active":"Inactive"},O=e=>{switch(e.toUpperCase()){case"SUPER_ADMIN":case"ADMIN":return"destructive";case"MANAGER":return"default";case"USER":default:return"secondary";case"READONLY":return"outline"}},Y=e=>"Active"===e?"default":"destructive",K=Z.email?Z.email.charAt(0).toUpperCase():"U",H=Z.email||"N/A",X=null!==Z.email_confirmed_at,$=P(),G=$?$.replace("_"," "):"N/A",J=E($);return"dropdown"===D?(0,t.jsxs)(A.rI,{children:[(0,t.jsx)(A.ty,{asChild:!0,children:(0,t.jsx)(w.$,{className:"relative size-8 rounded-full",variant:"ghost",children:(0,t.jsxs)(v.eu,{className:"size-8",children:[(0,t.jsx)(v.BK,{alt:K,src:(null==(s=Z.user_metadata)?void 0:s.avatar_url)||""}),(0,t.jsx)(v.q5,{children:K})]})})}),(0,t.jsxs)(A.SQ,{align:"end",className:"w-56",forceMount:!0,children:[(0,t.jsx)(A.lp,{className:"font-normal",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium leading-none",children:H}),(0,t.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:Z.id})]})}),(0,t.jsx)(A.mB,{}),(0,t.jsxs)(A._2,{children:[(0,t.jsx)(l.A,{className:"mr-2 size-4"}),(0,t.jsx)(N(),{href:"/profile",children:"Profile"})]}),(0,t.jsxs)(A._2,{children:[(0,t.jsx)(r.A,{className:"mr-2 size-4"}),(0,t.jsx)("span",{children:X?"Email Verified":"Email Not Verified"}),X?(0,t.jsx)(i.A,{className:"ml-auto size-4 text-green-500"}):(0,t.jsx)(c.A,{className:"ml-auto size-4 text-red-500"})]}),(0,t.jsxs)(A._2,{children:[(0,t.jsx)(n.A,{className:"mr-2 size-4"}),(0,t.jsx)(y.E,{className:J,children:G})]}),(0,t.jsx)(A.mB,{}),(0,t.jsxs)(A._2,{onClick:k,children:[(0,t.jsx)(d.A,{className:"mr-2 size-4"}),(0,t.jsx)("span",{children:"Log out"})]})]})]}):"card"===D?(0,t.jsxs)(_.Zp,{className:"mx-auto w-full max-w-md",children:[(0,t.jsxs)(_.aR,{className:"flex flex-row items-center space-x-4 p-6",children:[(0,t.jsxs)(v.eu,{className:"size-16",children:[(0,t.jsx)(v.BK,{alt:K,src:(null==(a=Z.user_metadata)?void 0:a.avatar_url)||""}),(0,t.jsx)(v.q5,{className:"text-2xl",children:K})]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,t.jsx)(_.ZB,{className:"text-2xl font-bold",children:H}),(0,t.jsxs)(_.BT,{className:"text-sm text-muted-foreground",children:["User ID: ",Z.id]}),(0,t.jsx)(y.E,{className:"".concat(J," px-2 py-1 text-sm"),children:G})]})]}),(0,t.jsxs)(_.Wu,{className:"space-y-4 p-6 pt-0",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(r.A,{className:"mr-2 size-5 text-muted-foreground"}),(0,t.jsxs)("span",{className:"text-base",children:["Email: ",H]}),X?(0,t.jsx)(i.A,{className:"ml-2 size-5 text-green-500"}):(0,t.jsx)(c.A,{className:"ml-2 size-5 text-red-500"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.A,{className:"mr-2 size-5 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-base",children:"Role: "}),(0,t.jsx)(y.E,{className:"".concat(J," ml-1"),children:G})]}),U&&(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)(w.$,{onClick:k,variant:"outline",disabled:L,children:L?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"mr-2 size-4 animate-spin"}),"Signing out..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"mr-2 size-4"}),"Log out"]})})})]})]}):"detailed"===D?(0,t.jsxs)("div",{className:"w-full max-w-5xl mx-auto space-y-6",children:[(0,t.jsx)(_.Zp,{children:(0,t.jsx)(_.aR,{className:"pb-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(v.eu,{className:"size-20",children:[(0,t.jsx)(v.BK,{alt:Z.email||"",src:null==(f=Z.user_metadata)?void 0:f.avatar_url}),(0,t.jsx)(v.q5,{className:"text-xl",children:K})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:(null==(S=Z.user_metadata)?void 0:S.full_name)||"User Profile"}),(0,t.jsx)("p",{className:"text-muted-foreground text-lg",children:Z.email})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-wrap",children:[(0,t.jsx)(y.E,{className:"text-sm px-3 py-1",variant:O(P()),children:P()}),(0,t.jsxs)(y.E,{className:"text-sm px-3 py-1",variant:Y(q()),children:[(0,t.jsx)(x.A,{className:"mr-1 size-3"}),q()]}),Z.email_confirmed_at&&(0,t.jsxs)(y.E,{className:"text-sm px-3 py-1",variant:"outline",children:[(0,t.jsx)(o.A,{className:"mr-1 size-3"}),"Verified"]})]})]})]}),U&&(0,t.jsx)(w.$,{disabled:L,onClick:k,variant:"outline",size:"sm",children:L?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"mr-2 size-4 animate-spin"}),"Signing out..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"mr-2 size-4"}),"Sign out"]})})]})})}),(0,t.jsxs)(z.tU,{defaultValue:"overview",className:"w-full",children:[(0,t.jsxs)(z.j7,{className:"grid w-full grid-cols-3",children:[(0,t.jsx)(z.Xi,{value:"overview",children:"Overview"}),(0,t.jsx)(z.Xi,{value:"security",children:"Security"}),(0,t.jsx)(z.Xi,{value:"activity",children:"Activity"})]}),(0,t.jsx)(z.av,{value:"overview",className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(_.Zp,{children:[(0,t.jsx)(_.aR,{children:(0,t.jsxs)(_.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,t.jsx)(l.A,{className:"size-5"}),"Account Information"]})}),(0,t.jsx)(_.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"User ID:"}),(0,t.jsxs)("span",{className:"font-mono text-xs bg-muted px-2 py-1 rounded",children:[Z.id.slice(0,8),"..."]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Email:"}),(0,t.jsx)("span",{className:"text-sm",children:Z.email})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Role:"}),(0,t.jsx)(y.E,{variant:O(P()),children:P()})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Status:"}),(0,t.jsx)(y.E,{variant:Y(q()),children:q()})]}),(null==(C=Z.user_metadata)?void 0:C.employee_id)&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Employee ID:"}),(0,t.jsx)("span",{className:"text-sm",children:Z.user_metadata.employee_id})]})]})})]}),(0,t.jsxs)(_.Zp,{children:[(0,t.jsx)(_.aR,{children:(0,t.jsxs)(_.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,t.jsx)(r.A,{className:"size-5"}),"Contact & Verification"]})}),(0,t.jsx)(_.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Email Verified:"}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:Z.email_confirmed_at?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.A,{className:"size-4 text-green-600"}),(0,t.jsx)("span",{className:"text-sm text-green-600",children:"Verified"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.A,{className:"size-4 text-red-600"}),(0,t.jsx)("span",{className:"text-sm text-red-600",children:"Not Verified"})]})})]}),Z.email_confirmed_at&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Verified On:"}),(0,t.jsx)("span",{className:"text-sm",children:F(Z.email_confirmed_at)})]})]})})]})]})}),(0,t.jsx)(z.av,{value:"security",className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(_.Zp,{children:[(0,t.jsx)(_.aR,{children:(0,t.jsxs)(_.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,t.jsx)(o.A,{className:"size-5"}),"Security Status"]})}),(0,t.jsxs)(_.Wu,{className:"space-y-4",children:[(0,t.jsxs)(b.Fc,{children:[(0,t.jsx)(o.A,{className:"size-4"}),(0,t.jsx)(b.TN,{children:"Your account is protected by enterprise-grade security protocols. All activities are monitored and logged for security purposes."})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Account Type:"}),(0,t.jsx)("span",{className:"text-sm",children:"Standard User"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"SSO User:"}),(0,t.jsx)("span",{className:"text-sm",children:Z.is_sso_user?"Yes":"No"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Anonymous:"}),(0,t.jsx)("span",{className:"text-sm",children:Z.is_anonymous?"Yes":"No"})]})]})]})]}),(0,t.jsxs)(_.Zp,{children:[(0,t.jsx)(_.aR,{children:(0,t.jsxs)(_.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,t.jsx)(u.A,{className:"size-5"}),"Session Information"]})}),(0,t.jsx)(_.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Current Session:"}),(0,t.jsx)(y.E,{variant:"default",children:"Active"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Session ID:"}),(0,t.jsx)("span",{className:"font-mono text-xs bg-muted px-2 py-1 rounded",children:Z.id.slice(-8)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"App Metadata:"}),(0,t.jsx)("span",{className:"text-sm",children:Z.app_metadata?"Present":"None"})]})]})})]})]})}),(0,t.jsx)(z.av,{value:"activity",className:"space-y-4",children:(0,t.jsxs)(_.Zp,{children:[(0,t.jsx)(_.aR,{children:(0,t.jsxs)(_.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,t.jsx)(x.A,{className:"size-5"}),"Account Activity"]})}),(0,t.jsx)(_.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(j.A,{className:"size-5 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Last Sign In"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:T(Z.last_sign_in_at)})]})]}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:W(Z.last_sign_in_at)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(h.A,{className:"size-5 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Account Created"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:T(Z.created_at)})]})]}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:W(Z.created_at)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(l.A,{className:"size-5 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Last Updated"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:T(Z.updated_at)})]})]}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:W(Z.updated_at)})]})]})})]})})]})]}):null}},66695:(e,s,a)=>{a.d(s,{BT:()=>d,Wu:()=>m,ZB:()=>n,Zp:()=>i,aR:()=>c,wL:()=>x});var t=a(95155),l=a(12115),r=a(54036);let i=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),ref:s,...l})});i.displayName="Card";let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("flex flex-col space-y-1.5 p-6",a),ref:s,...l})});c.displayName="CardHeader";let n=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",a),ref:s,...l})});n.displayName="CardTitle";let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("text-sm text-muted-foreground",a),ref:s,...l})});d.displayName="CardDescription";let m=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("p-6 pt-0",a),ref:s,...l})});m.displayName="CardContent";let x=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)("flex items-center p-6 pt-0",a),ref:s,...l})});x.displayName="CardFooter"},91394:(e,s,a)=>{a.d(s,{BK:()=>n,eu:()=>c,q5:()=>d});var t=a(95155),l=a(54011),r=a(12115),i=a(54036);let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.bL,{className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),ref:s,...r})});c.displayName=l.bL.displayName;let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l._V,{className:(0,i.cn)("aspect-square h-full w-full",a),ref:s,...r})});n.displayName=l._V.displayName;let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.H4,{className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),ref:s,...r})});d.displayName=l.H4.displayName}}]);