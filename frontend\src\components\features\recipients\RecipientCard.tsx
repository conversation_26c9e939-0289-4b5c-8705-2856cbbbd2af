'use client';

import { format, parseISO } from 'date-fns';
import {
  Edit,
  Gift as GiftIcon,
  Mail,
  MapPin,
  MoreHorizontal,
  Phone,
  StickyNote,
  Trash2,
  User,
  Briefcase,
  Building,
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

import type { Recipient } from '@/lib/types/domain';

import { ActionButton } from '@/components/ui/action-button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface RecipientCardProps {
  recipient: Recipient;
  onEdit?: (recipient: Recipient) => void;
  onDelete?: (recipient: Recipient) => void;
  showGifts?: boolean;
  className?: string;
}

export const RecipientCard: React.FC<RecipientCardProps> = ({
  recipient,
  onEdit,
  onDelete,
  showGifts = true,
  className,
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleEdit = () => {
    setIsMenuOpen(false);
    onEdit?.(recipient);
  };

  const handleDelete = () => {
    setIsMenuOpen(false);
    onDelete?.(recipient);
  };

  const giftCount = recipient.gifts?.length || 0;

  return (
    <Card
      className={cn(
        'p-5 shadow-md hover:shadow-lg transition-shadow',
        className
      )}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <User className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold line-clamp-1">
                {recipient.name}
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                {giftCount} gift{giftCount !== 1 ? 's' : ''} received
              </CardDescription>
            </div>
          </div>

          {(onEdit || onDelete) && (
            <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <DropdownMenuTrigger asChild>
                <ActionButton
                  actionType="tertiary"
                  size="sm"
                  className="h-8 w-8 p-0"
                  aria-label="Recipient actions"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </ActionButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onEdit && (
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Recipient
                  </DropdownMenuItem>
                )}
                {onEdit && onDelete && <DropdownMenuSeparator />}
                {onDelete && (
                  <DropdownMenuItem
                    onClick={handleDelete}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Recipient
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Role and Worksite Information */}
        {(recipient.role || recipient.worksite) && (
          <div className="flex flex-wrap items-center gap-2">
            {recipient.role && (
              <Badge
                variant="secondary"
                className="bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30"
              >
                <Briefcase className="h-3 w-3 mr-1" />
                {recipient.role}
              </Badge>
            )}
            {recipient.worksite && (
              <Badge
                variant="secondary"
                className="bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800/30"
              >
                <Building className="h-3 w-3 mr-1" />
                {recipient.worksite}
              </Badge>
            )}
          </div>
        )}

        {/* Contact Information */}
        <div className="space-y-2">
          {/* Email */}
          {recipient.email && (
            <div className="flex items-center gap-2 text-sm">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <a
                href={`mailto:${recipient.email}`}
                className="text-primary hover:underline"
              >
                {recipient.email}
              </a>
            </div>
          )}

          {/* Phone */}
          {recipient.phone && (
            <div className="flex items-center gap-2 text-sm">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <a
                href={`tel:${recipient.phone}`}
                className="text-primary hover:underline"
              >
                {recipient.phone}
              </a>
            </div>
          )}

          {/* Address */}
          {recipient.address && (
            <div className="flex items-start gap-2 text-sm">
              <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
              <span className="text-muted-foreground line-clamp-2">
                {recipient.address}
              </span>
            </div>
          )}
        </div>

        {/* Gift Count Badge */}
        {showGifts && giftCount > 0 && (
          <div className="flex items-center gap-2">
            <GiftIcon className="h-4 w-4 text-muted-foreground" />
            <Badge
              variant="outline"
              className="bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20"
            >
              {giftCount} gift{giftCount !== 1 ? 's' : ''}
            </Badge>
          </div>
        )}

        {/* Recent Gifts */}
        {showGifts && recipient.gifts && recipient.gifts.length > 0 && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground">
                Recent Gifts
              </h4>
              <div className="space-y-1">
                {recipient.gifts.slice(0, 3).map(gift => (
                  <div key={gift.id} className="text-xs text-muted-foreground">
                    <Link
                      href={`/gifts/${gift.id}`}
                      className="hover:text-primary hover:underline line-clamp-1"
                    >
                      {gift.itemDescription}
                    </Link>
                    <span className="text-xs">
                      {' • '}
                      {format(parseISO(gift.dateSent), 'MMM dd, yyyy')}
                    </span>
                  </div>
                ))}
                {recipient.gifts.length > 3 && (
                  <div className="text-xs text-muted-foreground">
                    +{recipient.gifts.length - 3} more gift
                    {recipient.gifts.length - 3 !== 1 ? 's' : ''}
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {/* Notes */}
        {recipient.notes && (
          <>
            <Separator />
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <StickyNote className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">
                  Notes
                </span>
              </div>
              <p className="text-sm text-muted-foreground line-clamp-3">
                {recipient.notes}
              </p>
            </div>
          </>
        )}
      </CardContent>

      <CardFooter className="pt-4 text-xs text-muted-foreground">
        <div className="flex items-center justify-between w-full">
          <span>
            Added {format(parseISO(recipient.createdAt), 'MMM dd, yyyy')}
          </span>
          {recipient.updatedAt !== recipient.createdAt && (
            <span>
              Updated {format(parseISO(recipient.updatedAt), 'MMM dd, yyyy')}
            </span>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};
