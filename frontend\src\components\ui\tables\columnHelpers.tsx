/**
 * Column Helper Utilities for DataTable
 *
 * Provides reusable column definitions and helper functions for common
 * table column patterns, reducing duplication across different tables.
 */

import type { ColumnDef } from '@tanstack/react-table';

import { format } from 'date-fns';
import {
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  Edit,
  Eye,
  MoreHorizontal,
  Trash,
} from 'lucide-react';
import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

// ============================================================================
// INTERNATIONALIZED FACTORY FUNCTIONS
// ============================================================================

/**
 * Factory function to create fully internationalized column helpers
 * Usage: const columns = createTranslatedColumns(tHeaders, tActions, tStatus);
 */
export const createTranslatedColumns = (
  tHeaders: (key: string) => string,
  tActions: (key: string) => string,
  tStatus: (key: string) => string
) => ({
  /**
   * Creates an internationalized actions column
   */
  createActionsColumn: <T extends { id: number | string }>(options: {
    customActions?: {
      icon?: React.ComponentType<{ className?: string }>;
      labelKey: string;
      onClick: (item: T) => void;
      variant?: 'default' | 'destructive';
    }[];
    editHref?: (item: T) => string;
    onDelete?: (item: T) => void;
    onEdit?: (item: T) => void;
    onView?: (item: T) => void;
    showCopyId?: boolean;
    viewHref?: (item: T) => string;
  }): ColumnDef<T> => ({
    cell: ({ row }) => {
      const item = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button className="size-8 p-0" variant="ghost">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{tActions('actions')}</DropdownMenuLabel>

            {/* View Action */}
            {(options.onView || options.viewHref) && (
              <>
                {options.viewHref ? (
                  <DropdownMenuItem asChild>
                    <Link href={options.viewHref(item)}>
                      <Eye className="mr-2 size-4" />
                      {tActions('viewDetails')}
                    </Link>
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem onClick={() => options.onView?.(item)}>
                    <Eye className="mr-2 size-4" />
                    {tActions('viewDetails')}
                  </DropdownMenuItem>
                )}
              </>
            )}

            {/* Edit Action */}
            {(options.onEdit || options.editHref) && (
              <>
                {options.editHref ? (
                  <DropdownMenuItem asChild>
                    <Link href={options.editHref(item)}>
                      <Edit className="mr-2 size-4" />
                      {tActions('edit')}
                    </Link>
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem onClick={() => options.onEdit?.(item)}>
                    <Edit className="mr-2 size-4" />
                    {tActions('edit')}
                  </DropdownMenuItem>
                )}
              </>
            )}

            {/* Custom Actions */}
            {options.customActions?.map((action, index) => (
              <DropdownMenuItem
                className={
                  action.variant === 'destructive' ? 'text-destructive' : ''
                }
                key={index}
                onClick={() => action.onClick(item)}
              >
                {action.icon && <action.icon className="mr-2 size-4" />}
                {tActions(action.labelKey)}
              </DropdownMenuItem>
            ))}

            {/* Copy ID */}
            {options.showCopyId && (
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(String(item.id))}
              >
                {tActions('copyId')}
              </DropdownMenuItem>
            )}

            {/* Delete Action */}
            {options.onDelete && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-destructive"
                  onClick={() => options.onDelete?.(item)}
                >
                  <Trash className="mr-2 size-4" />
                  {tActions('delete')}
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
    header: tHeaders('actions'),
    id: 'actions',
  }),

  /**
   * Creates an internationalized date column
   */
  createDateColumn: <T,>(
    accessorKey: keyof T,
    headerKey: string,
    dateFormat = 'MMM dd, yyyy'
  ): ColumnDef<T> => ({
    accessorKey: accessorKey as string,
    cell: ({ getValue }) => {
      const date = getValue() as Date | string;
      if (!date) return '-';

      try {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return format(dateObj, dateFormat);
      } catch {
        return '-';
      }
    },
    header: ({ column }: { column: any }) => {
      const sortDirection = column.getIsSorted();
      return (
        <Button
          className="h-auto p-0 font-semibold hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          {tHeaders(headerKey)}
          {sortDirection === 'asc' ? (
            <ArrowUp className="ml-2 size-4" />
          ) : sortDirection === 'desc' ? (
            <ArrowDown className="ml-2 size-4" />
          ) : (
            <ArrowUpDown className="ml-2 size-4 opacity-50" />
          )}
        </Button>
      );
    },
  }),

  /**
   * Creates an internationalized sortable header
   */
  createSortableHeader: (titleKey: string) => {
    return ({ column }: { column: any }) => {
      const sortDirection = column.getIsSorted();

      return (
        <Button
          className="h-auto p-0 font-semibold hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          {tHeaders(titleKey)}
          {sortDirection === 'asc' ? (
            <ArrowUp className="ml-2 size-4" />
          ) : sortDirection === 'desc' ? (
            <ArrowDown className="ml-2 size-4" />
          ) : (
            <ArrowUpDown className="ml-2 size-4 opacity-50" />
          )}
        </Button>
      );
    };
  },

  /**
   * Creates an internationalized status column
   */
  createStatusColumn: <T,>(
    accessorKey: keyof T,
    headerKey: string,
    statusConfig?: Record<string, { label?: string; variant: string }>
  ): ColumnDef<T> => ({
    accessorKey: accessorKey as string,
    cell: ({ getValue }) => {
      const status = getValue() as string;
      if (!status) return '-';

      const config = statusConfig?.[status] || { variant: 'secondary' };
      const label = config.label || tStatus(status.toLowerCase());

      return <Badge variant={config.variant as any}>{label}</Badge>;
    },
    header: ({ column }: { column: any }) => {
      const sortDirection = column.getIsSorted();
      return (
        <Button
          className="h-auto p-0 font-semibold hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          {tHeaders(headerKey)}
          {sortDirection === 'asc' ? (
            <ArrowUp className="ml-2 size-4" />
          ) : sortDirection === 'desc' ? (
            <ArrowDown className="ml-2 size-4" />
          ) : (
            <ArrowUpDown className="ml-2 size-4 opacity-50" />
          )}
        </Button>
      );
    },
  }),

  /**
   * Creates an internationalized text column
   */
  createTextColumn: <T,>(
    accessorKey: keyof T,
    headerKey: string,
    options?: {
      className?: string;
      maxLength?: number;
    }
  ): ColumnDef<T> => ({
    accessorKey: accessorKey as string,
    cell: ({ getValue }) => {
      const value = getValue() as string;
      if (!value) return '-';

      const displayValue =
        options?.maxLength && value.length > options.maxLength
          ? `${value.slice(0, options.maxLength)}...`
          : value;

      return (
        <span className={cn('line-clamp-2', options?.className)} title={value}>
          {displayValue}
        </span>
      );
    },
    header: ({ column }: { column: any }) => {
      const sortDirection = column.getIsSorted();
      return (
        <Button
          className="h-auto p-0 font-semibold hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          {tHeaders(headerKey)}
          {sortDirection === 'asc' ? (
            <ArrowUp className="ml-2 size-4" />
          ) : sortDirection === 'desc' ? (
            <ArrowDown className="ml-2 size-4" />
          ) : (
            <ArrowUpDown className="ml-2 size-4 opacity-50" />
          )}
        </Button>
      );
    },
  }),
});

// ============================================================================
// LEGACY FUNCTIONS (For backward compatibility)
// ============================================================================

/**
 * Creates a sortable header component with proper sorting state indicators
 */
export const createSortableHeader = (title: string) => {
  return ({ column }: { column: any }) => {
    const sortDirection = column.getIsSorted();

    return (
      <Button
        className="h-auto p-0 font-semibold hover:bg-transparent"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        variant="ghost"
      >
        {title}
        {sortDirection === 'asc' ? (
          <ArrowUp className="ml-2 size-4" />
        ) : sortDirection === 'desc' ? (
          <ArrowDown className="ml-2 size-4" />
        ) : (
          <ArrowUpDown className="ml-2 size-4 opacity-50" />
        )}
      </Button>
    );
  };
};

/**
 * Creates a date column with consistent formatting
 */
export const createDateColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  dateFormat = 'MMM dd, yyyy'
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  cell: ({ getValue }) => {
    const date = getValue() as Date | string;
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return format(dateObj, dateFormat);
    } catch {
      return '-';
    }
  },
  header: createSortableHeader(header),
});

/**
 * Creates a status badge column
 */
export const createStatusColumn = <T,>(
  accessorKey: keyof T,
  header = 'Status',
  statusConfig?: Record<string, { label?: string; variant: string }>
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  cell: ({ getValue }) => {
    const status = getValue() as string;
    if (!status) return '-';

    const config = statusConfig?.[status] || { variant: 'secondary' };
    const label = config.label || status;

    return <Badge variant={config.variant as any}>{label}</Badge>;
  },
  header: createSortableHeader(header),
});

/**
 * Creates an actions column with common CRUD operations
 */
export const createActionsColumn = <
  T extends { id: number | string },
>(options: {
  customActions?: {
    icon?: React.ComponentType<{ className?: string }>;
    label: string;
    onClick: (item: T) => void;
    variant?: 'default' | 'destructive';
  }[];
  editHref?: (item: T) => string;
  onDelete?: (item: T) => void;
  onEdit?: (item: T) => void;
  onView?: (item: T) => void;
  viewHref?: (item: T) => string;
}): ColumnDef<T> => ({
  cell: ({ row }) => {
    const item = row.original;

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="size-8 p-0" variant="ghost">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {/* View Action */}
          {(options.onView || options.viewHref) && (
            <>
              {options.viewHref ? (
                <DropdownMenuItem asChild>
                  <Link href={options.viewHref(item)}>
                    <Eye className="mr-2 size-4" />
                    View
                  </Link>
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => options.onView?.(item)}>
                  <Eye className="mr-2 size-4" />
                  View
                </DropdownMenuItem>
              )}
            </>
          )}

          {/* Edit Action */}
          {(options.onEdit || options.editHref) && (
            <>
              {options.editHref ? (
                <DropdownMenuItem asChild>
                  <Link href={options.editHref(item)}>
                    <Edit className="mr-2 size-4" />
                    Edit
                  </Link>
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => options.onEdit?.(item)}>
                  <Edit className="mr-2 size-4" />
                  Edit
                </DropdownMenuItem>
              )}
            </>
          )}

          {/* Custom Actions */}
          {options.customActions?.map((action, index) => (
            <DropdownMenuItem
              className={
                action.variant === 'destructive' ? 'text-destructive' : ''
              }
              key={index}
              onClick={() => action.onClick(item)}
            >
              {action.icon && <action.icon className="mr-2 size-4" />}
              {action.label}
            </DropdownMenuItem>
          ))}

          {/* Delete Action */}
          {options.onDelete && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-destructive"
                onClick={() => options.onDelete?.(item)}
              >
                <Trash className="mr-2 size-4" />
                Delete
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  },
  header: 'Actions',
  id: 'actions',
});

/**
 * Creates a text column with optional truncation
 */
export const createTextColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  options?: {
    className?: string;
    maxLength?: number;
  }
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  cell: ({ getValue }) => {
    const value = getValue() as string;
    if (!value) return '-';

    const truncated =
      options?.maxLength && value.length > options.maxLength
        ? `${value.slice(0, Math.max(0, options.maxLength))}...`
        : value;

    return (
      <span className={options?.className} title={value}>
        {truncated}
      </span>
    );
  },
  header: createSortableHeader(header),
});

/**
 * Creates a numeric column with optional formatting
 */
export const createNumericColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  options?: {
    decimals?: number;
    format?: 'currency' | 'decimal' | 'percentage';
    prefix?: string;
    suffix?: string;
  }
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  cell: ({ getValue }) => {
    const rawValue = getValue();
    const value =
      typeof rawValue === 'string'
        ? Number.parseFloat(rawValue)
        : (rawValue as number);

    if (value === null || value === undefined || isNaN(value)) return '-';

    let formatted = value.toString();

    if (options?.format === 'currency') {
      formatted = new Intl.NumberFormat('en-US', {
        currency: 'USD',
        minimumFractionDigits: options.decimals ?? 2,
        style: 'currency',
      }).format(value);
    } else if (options?.format === 'percentage') {
      formatted = `${(value * 100).toFixed(options.decimals ?? 1)}%`;
    } else if (options?.decimals !== undefined) {
      formatted = value.toFixed(options.decimals);
    }

    if (options?.prefix) formatted = options.prefix + formatted;
    if (options?.suffix) formatted = formatted + options.suffix;

    return <span className="font-mono">{formatted}</span>;
  },
  header: createSortableHeader(header),
});

/**
 * Creates a boolean column with checkmark/cross display
 */
export const createBooleanColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  labels?: { false: string; true: string }
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  cell: ({ getValue }) => {
    const value = getValue() as boolean;

    if (labels) {
      return (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? labels.true : labels.false}
        </Badge>
      );
    }

    return (
      <span className={value ? 'text-green-600' : 'text-gray-400'}>
        {value ? '✓' : '✗'}
      </span>
    );
  },
  header: createSortableHeader(header),
});

/**
 * Creates a row selection column (checkbox)
 */
export const createSelectionColumn = <T,>(): ColumnDef<T> => ({
  cell: ({ row }) => (
    <Checkbox
      aria-label="Select row"
      checked={row.getIsSelected()}
      onCheckedChange={value => row.toggleSelected(!!value)}
    />
  ),
  enableHiding: false,
  enableSorting: false,
  header: ({ table }) => (
    <Checkbox
      aria-label="Select all"
      checked={
        table.getIsAllPageRowsSelected() ||
        (table.getIsSomePageRowsSelected() && 'indeterminate')
      }
      onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
    />
  ),
  id: 'select',
});

/**
 * Creates a complex column with title and subtitle (like DelegationTable eventName)
 */
export const createTitleSubtitleColumn = <T,>(
  titleKey: keyof T,
  subtitleKey: keyof T,
  header: string
): ColumnDef<T> => ({
  accessorKey: titleKey as string,
  cell: ({ row }) => {
    const title = row.getValue(titleKey as string) as string;
    const subtitle = (row.getValue(subtitleKey as string) || '') as string;

    return (
      <div className="space-y-1">
        <div className="font-semibold text-foreground">{title || '-'}</div>
        {subtitle && (
          <div className="line-clamp-1 text-xs text-muted-foreground">
            {subtitle}
          </div>
        )}
      </div>
    );
  },
  header: createSortableHeader(header),
});

/**
 * Creates a column with icon and text (like DelegationTable delegates count)
 */
export const createIconTextColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  icon: React.ComponentType<{ className?: string }>,
  options?: {
    className?: string;
    formatter?: (value: any) => string;
  }
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  cell: ({ row }) => {
    const value = row.getValue(accessorKey as string);
    const Icon = icon;
    const displayValue = options?.formatter ? options.formatter(value) : value;

    return (
      <div
        className={cn(
          'flex items-center justify-center gap-1 text-sm',
          options?.className
        )}
      >
        <Icon className="size-3 text-muted-foreground" />
        {String(displayValue)}
      </div>
    );
  },
  header: createSortableHeader(header),
});

/**
 * Enhanced actions column with professional styling (like DelegationTable)
 */
export const createEnhancedActionsColumn = <T extends { id: number | string }>(
  options: {
    customActions?: {
      icon?: React.ComponentType<{ className?: string }>;
      label: string;
      onClick: (item: T) => void;
      variant?: 'default' | 'destructive';
    }[];
    editHref?: (item: T) => string;
    onDelete?: (item: T) => void;
    onEdit?: (item: T) => void;
    onView?: (item: T) => void;
    showCopyId?: boolean;
    viewHref?: (item: T) => string;
  },
  translations?: {
    actions: string;
    delete: string;
    edit: string;
    viewDetails: string;
  }
): ColumnDef<T> => ({
  cell: ({ row }) => {
    const item = row.original;

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="size-8 p-0" variant="ghost">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>
            {translations?.actions || 'Actions'}
          </DropdownMenuLabel>

          {/* View Action */}
          {(options.onView || options.viewHref) && (
            <>
              {options.viewHref ? (
                <DropdownMenuItem asChild>
                  <Link href={options.viewHref(item)}>
                    <Eye className="mr-2 size-4" />
                    {translations?.viewDetails || 'View Details'}
                  </Link>
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => options.onView?.(item)}>
                  <Eye className="mr-2 size-4" />
                  {translations?.viewDetails || 'View Details'}
                </DropdownMenuItem>
              )}
            </>
          )}

          {/* Edit Action */}
          {(options.onEdit || options.editHref) && (
            <>
              {options.editHref ? (
                <DropdownMenuItem asChild>
                  <Link href={options.editHref(item)}>
                    <Edit className="mr-2 size-4" />
                    {translations?.edit || 'Edit'}
                  </Link>
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => options.onEdit?.(item)}>
                  <Edit className="mr-2 size-4" />
                  {translations?.edit || 'Edit'}
                </DropdownMenuItem>
              )}
            </>
          )}

          {/* Custom Actions */}
          {options.customActions?.map((action, index) => (
            <DropdownMenuItem
              className={
                action.variant === 'destructive' ? 'text-destructive' : ''
              }
              key={index}
              onClick={() => action.onClick(item)}
            >
              {action.icon && <action.icon className="mr-2 size-4" />}
              {action.label}
            </DropdownMenuItem>
          ))}

          {/* Copy ID */}
          {options.showCopyId && (
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(String(item.id))}
            >
              Copy ID
            </DropdownMenuItem>
          )}

          {/* Delete Action */}
          {options.onDelete && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-destructive"
                onClick={() => options.onDelete?.(item)}
              >
                <Trash className="mr-2 size-4" />
                {translations?.delete || 'Delete'}
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  },
  header: translations?.actions || 'Actions',
  id: 'actions',
});

/**
 * Factory function to get translated status configurations for common entities
 * Pass translations from your component that uses useTranslations hook
 */
export const createStatusConfigs = (tStatus: (key: string) => string) =>
  ({
    delegation: {
      Cancelled: { label: tStatus('cancelled'), variant: 'destructive' },
      Completed: { label: tStatus('completed'), variant: 'success' },
      'In Progress': { label: tStatus('inProgress'), variant: 'default' },
      Planned: { label: tStatus('planned'), variant: 'secondary' },
    },
    employee: {
      Active: { label: tStatus('active'), variant: 'success' },
      Inactive: { label: tStatus('inactive'), variant: 'secondary' },
      'On Leave': { label: tStatus('onLeave'), variant: 'warning' },
    },
    task: {
      Assigned: { label: tStatus('assigned'), variant: 'default' },
      Cancelled: { label: tStatus('cancelled'), variant: 'destructive' },
      Completed: { label: tStatus('completed'), variant: 'success' },
      In_Progress: { label: tStatus('inProgress'), variant: 'default' },
      Overdue: { label: tStatus('overdue'), variant: 'destructive' },
      Pending: { label: tStatus('pending'), variant: 'secondary' },
    },
  }) as const;
