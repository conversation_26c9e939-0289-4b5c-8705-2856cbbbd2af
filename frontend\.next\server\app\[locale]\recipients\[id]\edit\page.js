(()=>{var e={};e.id=4381,e.ids=[4381],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12662:(e,r,t)=>{"use strict";t.d(r,{AppBreadcrumb:()=>u});var s=t(60687),i=t(85814),a=t.n(i),n=t(16189),o=t(43210),c=t.n(o),d=t(70640),l=t(22482);function u({className:e,homeHref:r="/",homeLabel:t="Dashboard",showContainer:i=!0}){let o=(0,n.usePathname)(),u=o?o.split("/").filter(Boolean):[],p=e=>{if(/^\d+$/.test(e))return`ID: ${e}`;if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let r={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return r[e]?r[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},m=u.map((e,r)=>{let t="/"+u.slice(0,r+1).join("/"),i=r===u.length-1,n=p(e);return(0,s.jsxs)(c().Fragment,{children:[(0,s.jsx)(d.BreadcrumbItem,{children:i?(0,s.jsx)(d.BreadcrumbPage,{className:"font-medium text-foreground",children:n}):(0,s.jsx)(d.BreadcrumbLink,{asChild:!0,children:(0,s.jsx)(a(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:n})})}),!i&&(0,s.jsx)(d.BreadcrumbSeparator,{})]},t)}),x=(0,s.jsx)(d.Breadcrumb,{className:(0,l.cn)("text-sm",e),children:(0,s.jsxs)(d.BreadcrumbList,{className:"flex-wrap",children:[(0,s.jsx)(d.BreadcrumbItem,{children:(0,s.jsx)(d.BreadcrumbLink,{asChild:!0,children:(0,s.jsx)(a(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:r,children:t})})}),u.length>0&&(0,s.jsx)(d.BreadcrumbSeparator,{}),m]})});return i?(0,s.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,s.jsx)("div",{className:"flex items-center",children:x})}):x}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34179:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\recipients\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\recipients\\[id]\\edit\\page.tsx","default")},34631:e=>{"use strict";e.exports=require("tls")},34781:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(60687),i=t(48206),a=t(16189),n=t(77618),o=t(36849),c=t(12662),d=t(52027),l=t(48041),u=t(51812);function p(){let e=(0,a.useParams)(),r=(0,a.useRouter)(),t=(0,n.c3)("recipients"),p=(0,n.c3)("common"),m=(0,n.c3)("navigation"),x=e.id,{data:f,isLoading:h,error:b}=(0,u.$Q)(x),{mutateAsync:g,isPending:v,error:y}=(0,u.O)(),j=async e=>{try{await g({id:x,data:e}),r.push(`/recipients/${x}`)}catch(e){console.error("Error updating recipient:",e)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(c.AppBreadcrumb,{homeHref:"/",homeLabel:m("dashboard")}),(0,s.jsx)(d.gO,{data:f,error:b?.message||null,isLoading:h,loadingComponent:(0,s.jsx)(d.jt,{count:1,variant:"card"}),emptyComponent:(0,s.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,s.jsx)(i.A,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,s.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:t("recipientNotFound")}),(0,s.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:t("recipientNotFoundDescription")})]}),children:e=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.z,{description:t("editRecipientDescription"),icon:i.A,title:t("editRecipient")}),y&&(0,s.jsxs)("div",{className:"rounded-md bg-red-100 p-3 text-red-500",children:[p("error"),": ",y.message]}),(0,s.jsx)(o.g2,{initialData:e,isEditing:!0,isLoading:v,onSubmit:j})]})})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68248:(e,r,t)=>{Promise.resolve().then(t.bind(t,34179))},70640:(e,r,t)=>{"use strict";t.d(r,{Breadcrumb:()=>c,BreadcrumbItem:()=>l,BreadcrumbLink:()=>u,BreadcrumbList:()=>d,BreadcrumbPage:()=>p,BreadcrumbSeparator:()=>m});var s=t(60687),i=t(8730),a=t(74158),n=(t(69795),t(43210)),o=t(22482);let c=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("nav",{"aria-label":"breadcrumb",className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),ref:t,...r}));c.displayName="Breadcrumb";let d=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("ol",{className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),ref:t,...r}));d.displayName="BreadcrumbList";let l=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("li",{className:(0,o.cn)("inline-flex items-center gap-1.5",e),ref:t,...r}));l.displayName="BreadcrumbItem";let u=n.forwardRef(({asChild:e,className:r,...t},a)=>{let n=e?i.DX:"a";return(0,s.jsx)(n,{className:(0,o.cn)("transition-colors hover:text-foreground",r),ref:a,...t})});u.displayName="BreadcrumbLink";let p=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,o.cn)("font-normal text-foreground",e),ref:t,role:"link",...r}));p.displayName="BreadcrumbPage";let m=({children:e,className:r,...t})=>(0,s.jsx)("span",{"aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",r),role:"presentation",...t,children:e??(0,s.jsx)(a.A,{className:"size-4"})});m.displayName="BreadcrumbSeparator"},74075:e=>{"use strict";e.exports=require("zlib")},77976:(e,r,t)=>{Promise.resolve().then(t.bind(t,34781))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80057:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>d});var s=t(65239),i=t(48088),a=t(88170),n=t.n(a),o=t(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);t.d(r,c);let d={children:["",{children:["[locale]",{children:["recipients",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,34179)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\recipients\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\recipients\\[id]\\edit\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/recipients/[id]/edit/page",pathname:"/[locale]/recipients/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,3871,7048,8390,2670,9275,6013,8739,3302,2936,6866,6849],()=>t(80057));module.exports=s})();