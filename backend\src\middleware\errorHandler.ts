import type { NextFunction, Request, Response } from 'express';

import type { ApiErrorResponse } from '../types/response.types.js';

import { logAuditEvent } from '../utils/auditLogger.js';
import HttpError from '../utils/HttpError.js';
import logger, { createContextLogger } from '../utils/logger.js';

const errorHandler = (
  err: Error | HttpError,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: NextFunction, // Must be defined for Express to recognize it as an error handler
): void => {
  // Create context logger with request information
  const contextLogger = createContextLogger({
    ip: req.ip,
    method: req.method,
    requestId: req.requestId,
    service: 'error-handler',
    url: req.url,
    userAgent: req.headers['user-agent'],
    userId: req.user?.id,
  });

  const isProduction = process.env.NODE_ENV === 'production';
  let statusCode = 500;
  let message = isProduction ? 'An unexpected internal server error occurred.' : err.message;
  let code = 'INTERNAL_SERVER_ERROR';

  // Log error with full context
  if (err instanceof HttpError) {
    statusCode = err.status;
    message = err.message;
    code = err.code;
    // Log HTTP errors as warnings (client errors)
    contextLogger.warn('HTTP Error encountered', {
      code,
      message: err.message,
      stack: err.stack,
      statusCode,
    });
  } else {
    // Log unexpected errors as errors (server errors)
    contextLogger.error('Unexpected error encountered', {
      message: err.message,
      name: err.name,
      stack: err.stack,
    });

    // Add audit log for unexpected server errors
    logAuditEvent(
      {
        action: 'UNEXPECTED_SERVER_ERROR',
        details: {
          errorMessage: err.message,
          errorName: err.name,
          method: req.method,
          path: req.path,
          requestId: req.requestId,
          stack: err.stack?.substring(0, 500),
        },
        errorCode: code,
        eventType: 'SYSTEM_EVENT',
        message: `Unexpected server error: ${err.message}`,
        outcome: 'FAILURE',
        statusCode: 500,
        userId: req.user?.id,
        userRole: req.user?.role,
      },
      req,
    );
  }

  // Create standardized error response
  const errorResponse: ApiErrorResponse = {
    code,
    error: {
      message: err.message,
      ...(isProduction ? {} : { stack: err.stack }),
      ...(err instanceof HttpError && err.details ? { details: err.details } : {}),
    },
    message,
    requestId: req.requestId,
    status: 'error' as const,
    statusCode,
    timestamp: new Date().toISOString(),
  };

  res.status(statusCode).json(errorResponse);
};

export default errorHandler;
