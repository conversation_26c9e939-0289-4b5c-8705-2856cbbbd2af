"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9124],{12543:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(40157).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},17759:(e,t,s)=>{s.d(t,{C5:()=>b,MJ:()=>v,Rr:()=>x,eI:()=>p,lR:()=>f,lV:()=>o,zB:()=>u});var r=s(95155),a=s(12115),l=s(99708),i=s(62177),n=s(54036),d=s(85057);let o=i.Op,c=a.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(c.Provider,{value:{name:t.name},children:(0,r.jsx)(i.xI,{...t})})},m=()=>{let e=a.useContext(c),t=a.useContext(h),{getFieldState:s,formState:r}=(0,i.xW)(),l=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...l}},h=a.createContext({}),p=a.forwardRef((e,t)=>{let{className:s,...l}=e,i=a.useId();return(0,r.jsx)(h.Provider,{value:{id:i},children:(0,r.jsx)("div",{ref:t,className:(0,n.cn)("space-y-2",s),...l})})});p.displayName="FormItem";let f=a.forwardRef((e,t)=>{let{className:s,...a}=e,{error:l,formItemId:i}=m();return(0,r.jsx)(d.J,{ref:t,className:(0,n.cn)(l&&"text-destructive",s),htmlFor:i,...a})});f.displayName="FormLabel";let v=a.forwardRef((e,t)=>{let{...s}=e,{error:a,formItemId:i,formDescriptionId:n,formMessageId:d}=m();return(0,r.jsx)(l.DX,{ref:t,id:i,"aria-describedby":a?"".concat(n," ").concat(d):"".concat(n),"aria-invalid":!!a,...s})});v.displayName="FormControl";let x=a.forwardRef((e,t)=>{let{className:s,...a}=e,{formDescriptionId:l}=m();return(0,r.jsx)("p",{ref:t,id:l,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});x.displayName="FormDescription";let b=a.forwardRef((e,t)=>{var s;let{className:a,children:l,...i}=e,{error:d,formMessageId:o}=m(),c=d?String(null!=(s=null==d?void 0:d.message)?s:""):l;return c?(0,r.jsx)("p",{ref:t,id:o,className:(0,n.cn)("text-sm font-medium text-destructive",a),...i,children:c}):null});b.displayName="FormMessage"},31949:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(40157).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},35079:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(40157).A)("ClipboardList",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},37648:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(40157).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},59119:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(40157).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},59409:(e,t,s)=>{s.d(t,{bq:()=>m,eb:()=>v,gC:()=>f,l6:()=>c,yv:()=>u});var r=s(95155),a=s(31992),l=s(79556),i=s(77381),n=s(10518),d=s(12115),o=s(54036);let c=a.bL;a.YJ;let u=a.WT,m=d.forwardRef((e,t)=>{let{children:s,className:i,...n}=e;return(0,r.jsxs)(a.l9,{className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",i),ref:t,...n,children:[s,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"size-4 opacity-50"})})]})});m.displayName=a.l9.displayName;let h=d.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(a.PP,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),ref:t,...l,children:(0,r.jsx)(i.A,{className:"size-4"})})});h.displayName=a.PP.displayName;let p=d.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)(a.wn,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),ref:t,...i,children:(0,r.jsx)(l.A,{className:"size-4"})})});p.displayName=a.wn.displayName;let f=d.forwardRef((e,t)=>{let{children:s,className:l,position:i="popper",...n}=e;return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",l),position:i,ref:t,...n,children:[(0,r.jsx)(h,{}),(0,r.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(p,{})]})})});f.displayName=a.UC.displayName,d.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(a.JU,{className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),ref:t,...l})}).displayName=a.JU.displayName;let v=d.memo(d.forwardRef((e,t)=>{let{children:s,className:l,...i}=e,c=d.useCallback(e=>{"function"==typeof t?t(e):t&&(t.current=e)},[t]);return(0,r.jsxs)(a.q7,{className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l),ref:c,...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:s})]})}));v.displayName=a.q7.displayName,d.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(a.wv,{className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",s),ref:t,...l})}).displayName=a.wv.displayName},62523:(e,t,s)=>{s.d(t,{p:()=>i});var r=s(95155),a=s(12115),l=s(54036);let i=a.forwardRef((e,t)=>{let{className:s,type:a,...i}=e;return(0,r.jsx)("input",{className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,type:a,...i})});i.displayName="Input"},68801:(e,t,s)=>{s.d(t,{z:()=>o});var r=s(95155);s(12115);var a=s(62177),l=s(17759),i=s(62523),n=s(88539),d=s(59409);let o=e=>{let{className:t="",disabled:s=!1,label:o,name:c,placeholder:u,render:m,type:h="text",options:p=[],defaultValue:f,icon:v,...x}=e,{control:b}=(0,a.xW)();return(0,r.jsxs)(l.eI,{className:t,children:[(0,r.jsx)(l.lR,{htmlFor:c,children:o}),(0,r.jsx)(a.xI,{control:b,name:c,render:m||(e=>{var t,a;let{field:m,fieldState:{error:b}}=e;return(0,r.jsx)(l.MJ,{children:"select"===h?(0,r.jsxs)(d.l6,{onValueChange:m.onChange,value:m.value||f||"",disabled:s,children:[(0,r.jsx)(d.bq,{className:b?"border-red-500":"",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[v&&(0,r.jsx)(v,{className:"h-4 w-4 text-gray-500"}),(0,r.jsx)(d.yv,{placeholder:u||"Select ".concat(o.toLowerCase())})]})}),(0,r.jsx)(d.gC,{children:p.map(e=>(0,r.jsx)(d.eb,{value:String(e.value),disabled:e.disabled||!1,children:e.label},e.value))})]}):"textarea"===h?(0,r.jsxs)("div",{className:"relative",children:[v&&(0,r.jsx)(v,{className:"absolute left-3 top-3 h-4 w-4 text-gray-500"}),(0,r.jsx)(n.T,{...m,...x,value:null!=(t=m.value)?t:"",className:"".concat(b?"border-red-500":""," ").concat(v?"pl-10":""),disabled:s,id:c,placeholder:u})]}):(0,r.jsxs)("div",{className:"relative",children:[v&&(0,r.jsx)(v,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500"}),(0,r.jsx)(i.p,{...m,...x,value:null!=(a=m.value)?a:"",className:"".concat(b?"border-red-500":""," ").concat(v?"pl-10":""),disabled:s,id:c,placeholder:u,type:h})]})})})}),(0,r.jsx)(l.C5,{})]})}},71610:(e,t,s)=>{s.d(t,{E:()=>f});var r=s(12115),a=s(7165),l=s(76347),i=s(25910),n=s(52020);function d(e,t){let s=new Set(t);return e.filter(e=>!s.has(e))}var o=class extends i.Q{#e;#t;#s;#r;#a;#l;#i;#n;#d=[];constructor(e,t,s){super(),this.#e=e,this.#r=s,this.#s=[],this.#a=[],this.#t=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#a.forEach(e=>{e.subscribe(t=>{this.#o(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#a.forEach(e=>{e.destroy()})}setQueries(e,t){this.#s=e,this.#r=t,a.jG.batch(()=>{let e=this.#a,t=this.#c(this.#s);this.#d=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let s=t.map(e=>e.observer),r=s.map(e=>e.getCurrentResult()),a=s.some((t,s)=>t!==e[s]);(e.length!==s.length||a)&&(this.#a=s,this.#t=r,this.hasListeners()&&(d(e,s).forEach(e=>{e.destroy()}),d(s,e).forEach(e=>{e.subscribe(t=>{this.#o(e,t)})}),this.#u()))})}getCurrentResult(){return this.#t}getQueries(){return this.#a.map(e=>e.getCurrentQuery())}getObservers(){return this.#a}getOptimisticResult(e,t){let s=this.#c(e),r=s.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[r,e=>this.#m(e??r,t),()=>this.#h(r,s)]}#h(e,t){return t.map((s,r)=>{let a=e[r];return s.defaultedQueryOptions.notifyOnChangeProps?a:s.observer.trackResult(a,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#m(e,t){return t?(this.#l&&this.#t===this.#n&&t===this.#i||(this.#i=t,this.#n=this.#t,this.#l=(0,n.BH)(this.#l,t(e))),this.#l):e}#c(e){let t=new Map(this.#a.map(e=>[e.options.queryHash,e])),s=[];return e.forEach(e=>{let r=this.#e.defaultQueryOptions(e),a=t.get(r.queryHash);a?s.push({defaultedQueryOptions:r,observer:a}):s.push({defaultedQueryOptions:r,observer:new l.$(this.#e,r)})}),s}#o(e,t){let s=this.#a.indexOf(e);-1!==s&&(this.#t=function(e,t,s){let r=e.slice(0);return r[t]=s,r}(this.#t,s,t),this.#u())}#u(){if(this.hasListeners()){let e=this.#l,t=this.#h(this.#t,this.#d);e!==this.#m(t,this.#r?.combine)&&a.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#t)})})}}},c=s(26715),u=s(61581),m=s(80382),h=s(22450),p=s(4791);function f(e,t){let{queries:s,...i}=e,d=(0,c.jE)(t),f=(0,u.w)(),v=(0,m.h)(),x=r.useMemo(()=>s.map(e=>{let t=d.defaultQueryOptions(e);return t._optimisticResults=f?"isRestoring":"optimistic",t}),[s,d,f]);x.forEach(e=>{(0,p.jv)(e),(0,h.LJ)(e,v)}),(0,h.wZ)(v);let[b]=r.useState(()=>new o(d,x,i)),[y,g,j]=b.getOptimisticResult(x,i.combine),N=!f&&!1!==i.subscribed;r.useSyncExternalStore(r.useCallback(e=>N?b.subscribe(a.jG.batchCalls(e)):n.lQ,[b,N]),()=>b.getCurrentResult(),()=>b.getCurrentResult()),r.useEffect(()=>{b.setQueries(x,i)},[x,i,b]);let w=y.some((e,t)=>(0,p.EU)(x[t],e))?y.flatMap((e,t)=>{let s=x[t];if(s){let t=new l.$(d,s);if((0,p.EU)(s,e))return(0,p.iL)(s,t,v);(0,p.nE)(e,f)&&(0,p.iL)(s,t,v)}return[]}):[];if(w.length>0)throw Promise.all(w);let k=y.find((e,t)=>{let s=x[t];return s&&(0,h.$1)({result:e,errorResetBoundary:v,throwOnError:s.throwOnError,query:d.getQueryCache().get(s.queryHash),suspense:s.suspense})});if(null==k?void 0:k.error)throw k.error;return g(j())}},75668:(e,t,s)=>{s.d(t,{I:()=>n});var r=s(95155),a=s(90221),l=s(62177),i=s(17759);let n=e=>{let{children:t,defaultValues:s,onSubmit:n,schema:d,className:o="",ariaAttributes:c={}}=e,u=(0,l.mN)({...s&&{defaultValues:s},resolver:(0,a.u)(d)}),m=async e=>{await n(e)};return(0,r.jsx)(i.lV,{...u,children:(0,r.jsx)("form",{onSubmit:u.handleSubmit(m),className:o,...c,children:t})})}},76541:(e,t,s)=>{s.d(t,{A:()=>S});var r=s(95155),a=s(41784),l=s(12543),i=s(59119),n=s(35079),d=s(37648),o=s(31949),c=s(35695),u=s(12115),m=s(62177),h=s(30285),p=s(66695),f=s(75668),v=s(68801),x=s(62523),b=s(59409),y=s(53712),g=s(71153),j=s(21876);let N=g.k5(["Pending","Assigned","In_Progress","Completed","Cancelled"]),w=g.k5(["Low","Medium","High"]),k=g.Ik({completed:g.zM(),id:g.Yj().optional(),taskId:g.Yj(),title:g.Yj()}),C=g.Ik({dateTime:g.Yj().min(1,"Start date & time is required").refine(e=>(0,j.Qr)(e),{message:"Please enter a valid date and time in YYYY-MM-DD HH:MM format"}),deadline:g.Yj().refine(e=>""===e||(0,j.Qr)(e),{message:"Please enter a valid deadline date and time in YYYY-MM-DD HH:MM format"}).optional().transform(e=>""===e?void 0:e),description:g.Yj().min(1,"Task description is required"),driverEmployeeId:g.ai().int().positive("Driver Employee ID must be a positive integer").optional().nullable(),estimatedDuration:g.au.number().int().min(1,"Estimated duration must be at least 1 minute"),id:g.Yj().uuid().optional(),location:g.Yj().min(1,"Location is required"),notes:g.Yj().optional().or(g.eu("")),priority:w,requiredSkills:g.YO(g.Yj()),staffEmployeeId:g.ai({required_error:"Staff Employee is required",invalid_type_error:"Staff Employee must be a valid selection"}).int().positive("Staff Employee ID must be a positive integer"),status:N,statusChangeReason:g.Yj().optional(),subtasks:g.YO(k).optional(),vehicleId:g.ai().int().positive("Vehicle ID must be a positive integer.").nullable().optional()}).superRefine((e,t)=>{if(e.dateTime&&e.deadline){let s=new Date(e.dateTime);new Date(e.deadline)<s&&t.addIssue({code:g.eq.custom,message:"Deadline cannot be earlier than the start date & time",path:["deadline"]})}e.vehicleId&&!e.driverEmployeeId&&t.addIssue({code:g.eq.custom,message:"Vehicle cannot be assigned without a driver",path:["vehicleId"]})});var E=s(83761),R=s(80937);function S(e){let{initialData:t,isEditing:s=!1,isLoading:n=!1,onSubmit:d}=e,o=(0,c.useRouter)(),u={dateTime:(null==t?void 0:t.dateTime)?(0,j.rm)(t.dateTime,"datetime-local"):(0,a.GP)(new Date,"yyyy-MM-dd'T'HH:mm"),deadline:(null==t?void 0:t.deadline)?(0,j.rm)(t.deadline,"datetime-local"):"",description:(null==t?void 0:t.description)||"",driverEmployeeId:(null==t?void 0:t.driverEmployeeId)||null,estimatedDuration:(null==t?void 0:t.estimatedDuration)||60,location:(null==t?void 0:t.location)||"",notes:(null==t?void 0:t.notes)||"",priority:(null==t?void 0:t.priority)||"Medium",requiredSkills:(null==t?void 0:t.requiredSkills)||[],...(null==t?void 0:t.staffEmployeeId)&&{staffEmployeeId:t.staffEmployeeId},status:(null==t?void 0:t.status)?t.status.replace("_"," "):"Pending",vehicleId:(null==t?void 0:t.vehicleId)||null},m=async e=>{try{let t={...e,dateTime:(0,j.B7)(e.dateTime),deadline:e.deadline?(0,j.B7)(e.deadline):void 0,driverEmployeeId:e.driverEmployeeId?Number(e.driverEmployeeId):null,staffEmployeeId:Number(e.staffEmployeeId),vehicleId:e.vehicleId?Number(e.vehicleId):null,...e.subtasks&&{subTasks:e.subtasks}};await d(t)}catch(e){throw e}};return(0,r.jsx)(f.I,{defaultValues:u,onSubmit:m,schema:C,children:(0,r.jsxs)(p.Zp,{className:"shadow-lg",children:[(0,r.jsxs)(p.aR,{children:[(0,r.jsx)(p.ZB,{className:"text-2xl text-primary",children:s?"Edit Task":"Add New Task"}),(0,r.jsx)(p.BT,{children:"Enter the details for the task."})]}),(0,r.jsx)(p.Wu,{className:"space-y-6",children:(0,r.jsx)(I,{})}),(0,r.jsxs)(p.wL,{className:"flex justify-between gap-2 border-t pt-6",children:[(0,r.jsxs)(h.$,{onClick:()=>o.back(),type:"button",variant:"outline",children:[(0,r.jsx)(l.A,{className:"mr-2 size-4"}),"Cancel"]}),(0,r.jsxs)(h.$,{className:"bg-accent text-accent-foreground hover:bg-accent/90",type:"submit",disabled:n,children:[(0,r.jsx)(i.A,{className:"mr-2 size-4"}),s?"Save Changes":"Create Task"]})]})]})})}let I=()=>{let{watch:e}=(0,m.xW)(),{showFormError:t}=(0,y.t6)(),{data:s=[]}=(0,E.sZ)(),{data:a=[]}=(0,E.sZ)("driver"),l=s.filter(e=>"driver"!==e.role),{data:i,error:c}=(0,R.T$)(),h=(0,u.useMemo)(()=>i||[],[i]);return(0,u.useEffect)(()=>{c&&(console.error("Failed to fetch vehicles for form:",c),t(c.message||"Could not load vehicles.",{errorTitle:"Error Loading Vehicles"}))},[c,t]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-4",children:[(0,r.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,r.jsx)(n.A,{className:"mr-2 size-5 text-accent"}),"Task Details"]}),(0,r.jsx)(v.z,{label:"Description",name:"description",placeholder:"e.g., Pick up package from Warehouse A",type:"textarea"}),(0,r.jsx)(v.z,{label:"Location",name:"location",placeholder:"e.g., 123 Main St, City Center"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,r.jsx)(v.z,{label:"Start Date & Time",name:"dateTime",type:"datetime-local"}),(0,r.jsx)(v.z,{label:"Estimated Duration (minutes)",name:"estimatedDuration",placeholder:"e.g., 60",type:"number"})]})]}),(0,r.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-4",children:[(0,r.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,r.jsx)(d.A,{className:"mr-2 size-5 text-accent"}),"Scheduling & Assignment"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,r.jsx)(v.z,{label:"Priority",name:"priority",render:e=>{let{field:t}=e;return(0,r.jsxs)(b.l6,{defaultValue:t.value,onValueChange:t.onChange,children:[(0,r.jsx)(b.bq,{children:(0,r.jsx)(b.yv,{placeholder:"Select priority"})}),(0,r.jsx)(b.gC,{children:w.options.map(e=>(0,r.jsx)(b.eb,{value:e,children:e},e))})]})}}),(0,r.jsx)(v.z,{label:"Deadline (Optional)",name:"deadline",type:"datetime-local"})]}),(0,r.jsx)(v.z,{label:"Status",name:"status",render:e=>{let{field:t}=e;return(0,r.jsxs)(b.l6,{defaultValue:t.value,onValueChange:t.onChange,children:[(0,r.jsx)(b.bq,{children:(0,r.jsx)(b.yv,{placeholder:"Select status"})}),(0,r.jsx)(b.gC,{children:N.options.map(e=>(0,r.jsx)(b.eb,{value:e,children:e},e))})]})}}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,r.jsx)(v.z,{label:"Staff Employee (Required)",name:"staffEmployeeId",render:e=>{let{field:t}=e;return(0,r.jsxs)(b.l6,{onValueChange:e=>t.onChange(Number(e)),value:t.value?String(t.value):"",children:[(0,r.jsx)(b.bq,{children:(0,r.jsx)(b.yv,{placeholder:"Select staff employee"})}),(0,r.jsx)(b.gC,{children:l.map(e=>(0,r.jsxs)(b.eb,{value:String(e.id),children:[e.fullName," (",e.role,", ",e.status,")"]},e.id))})]})}}),(0,r.jsx)(v.z,{label:"Driver Employee (Optional)",name:"driverEmployeeId",render:e=>{let{field:t}=e;return(0,r.jsxs)(b.l6,{onValueChange:e=>t.onChange("none"===e?void 0:Number(e)),value:void 0===t.value?"none":String(t.value),children:[(0,r.jsx)(b.bq,{children:(0,r.jsx)(b.yv,{placeholder:"Select driver (optional)"})}),(0,r.jsxs)(b.gC,{children:[(0,r.jsx)(b.eb,{value:"none",children:"No Driver"}),a.map(e=>(0,r.jsxs)(b.eb,{value:String(e.id),children:[e.fullName," (",e.status,")",e.availability&&", ".concat(e.availability.replace("_"," ")),e.currentLocation&&", @ ".concat(e.currentLocation)]},e.id))]})]})}})]}),(0,r.jsx)(v.z,{label:"Vehicle (Optional - requires driver)",name:"vehicleId",render:t=>{let{field:s}=t;return(0,r.jsxs)(b.l6,{disabled:!e("driverEmployeeId"),onValueChange:e=>s.onChange("none"===e?void 0:Number(e)),value:void 0===s.value?"none":String(s.value),children:[(0,r.jsx)(b.bq,{children:(0,r.jsx)(b.yv,{placeholder:e("driverEmployeeId")?"Select vehicle (optional)":"Select a driver first"})}),(0,r.jsxs)(b.gC,{children:[(0,r.jsx)(b.eb,{value:"none",children:"No Vehicle"}),h.map(e=>(0,r.jsxs)(b.eb,{value:String(e.id),children:[e.make," ",e.model," (",e.year,") -"," ",e.licensePlate||"N/A"]},e.id))]})]})}})]}),(0,r.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-4",children:[(0,r.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,r.jsx)(o.A,{className:"mr-2 size-5 text-accent"}),"Additional Information"]}),(0,r.jsx)(v.z,{label:"Required Skills (Optional, comma-separated)",name:"requiredSkills",render:e=>{let{field:t}=e;return(0,r.jsx)(x.p,{onChange:e=>t.onChange(e.target.value.split(",").map(e=>e.trim()).filter(Boolean)),placeholder:"e.g., Forklift License, Customer Service",value:Array.isArray(t.value)?t.value.join(", "):""})}}),(0,r.jsx)(v.z,{label:"Notes (Optional)",name:"notes",placeholder:"e.g., Gate code is 1234, contact person: Jane Smith",type:"textarea"})]})]})}},80937:(e,t,s)=>{s.d(t,{NS:()=>p,T$:()=>c,W_:()=>u,Y1:()=>m,lR:()=>h});var r=s(26715),a=s(5041),l=s(90111),i=s(42366),n=s(99605),d=s(43840);let o={all:["vehicles"],detail:e=>["vehicles",e]},c=e=>(0,l.GK)([...o.all],async()=>(await d.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,t)=>{var s;return(0,l.GK)([...o.detail(e)],()=>d.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(null==(s=null==t?void 0:t.enabled)||s),staleTime:3e5,...t})},m=()=>{let e=(0,r.jE)(),{showError:t,showSuccess:s}=(0,i.useNotifications)();return(0,a.n)({mutationFn:e=>{let t=n.M.toCreateRequest(e);return d.vehicleApiService.create(t)},onError:e=>{t("Failed to create vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:o.all}),s('Vehicle "'.concat(t.licensePlate,'" has been created successfully!'))}})},h=()=>{let e=(0,r.jE)(),{showError:t,showSuccess:s}=(0,i.useNotifications)();return(0,a.n)({mutationFn:e=>{let{data:t,id:s}=e,r=n.M.toUpdateRequest(t);return d.vehicleApiService.update(s,r)},onError:e=>{t("Failed to update vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:o.all}),e.invalidateQueries({queryKey:o.detail(t.id)}),s('Vehicle "'.concat(t.licensePlate,'" has been updated successfully!'))}})},p=()=>{let e=(0,r.jE)(),{showError:t,showSuccess:s}=(0,i.useNotifications)();return(0,a.n)({mutationFn:e=>d.vehicleApiService.delete(e),onError:e=>{t("Failed to delete vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:(t,r)=>{e.invalidateQueries({queryKey:o.all}),e.removeQueries({queryKey:o.detail(r)}),s("Vehicle has been deleted successfully!")}})}},85057:(e,t,s)=>{s.d(t,{J:()=>o});var r=s(95155),a=s(12115),l=s(40968),i=s(74466),n=s(54036);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.b,{ref:t,className:(0,n.cn)(d(),s),...a})});o.displayName=l.b.displayName},88539:(e,t,s)=>{s.d(t,{T:()=>i});var r=s(95155),a=s(12115),l=s(54036);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...a})});i.displayName="Textarea"},95647:(e,t,s)=>{s.d(t,{z:()=>a});var r=s(95155);function a(e){let{children:t,description:s,icon:a,title:l}=e;return(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[a&&(0,r.jsx)(a,{className:"size-8 text-primary"}),(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:l})]}),s&&(0,r.jsx)("p",{className:"mt-1 text-muted-foreground",children:s})]}),t&&(0,r.jsx)("div",{className:"flex items-center gap-2",children:t})]})}s(12115)}}]);