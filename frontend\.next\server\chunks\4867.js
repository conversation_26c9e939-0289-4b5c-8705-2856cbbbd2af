"use strict";exports.id=4867,exports.ids=[4867],exports.modules={15209:(e,t,a)=>{a.d(t,{z:()=>c});var i=a(60687),r=a(75699),l=a(25841),s=a(14975),d=a(5068),o=a(54608);a(43210);var n=a(25279),u=a(15795);function c({className:e="",tasks:t,onDelete:a,onBulkDelete:c,onBulkArchive:m}){let y=e=>{try{return(0,r.GP)(new Date(e),"MMM d, yyyy h:mm a")}catch{return"Invalid date"}},p=e=>e.deadline&&(0,l.R)(new Date(e.deadline))&&"Completed"!==e.status&&"Cancelled"!==e.status,v=e=>e.staffEmployeeId?e.staffEmployee?(0,u.DV)(e.staffEmployee):`Staff ID: ${e.staffEmployeeId}`:"Unassigned",f=[(0,n.BZ)(),(0,n.K)("description","Description",{maxLength:50,className:"max-w-xs"}),(0,n.ZI)("status","Status",{Assigned:{variant:"default",label:"Assigned"},Cancelled:{variant:"secondary",label:"Cancelled"},Completed:{variant:"success",label:"Completed"},In_Progress:{variant:"default",label:"In Progress"},Pending:{variant:"warning",label:"Pending"}}),(0,n.ZI)("priority","Priority",{High:{variant:"destructive",label:"High"},Medium:{variant:"warning",label:"Medium"},Low:{variant:"secondary",label:"Low"}}),{accessorKey:"staffEmployeeId",header:"Assignee",cell:({row:e})=>{let t=e.original;return(0,i.jsx)("span",{className:t.staffEmployeeId?"":"text-muted-foreground",children:v(t)})}},{accessorKey:"dateTime",header:"Start Time",cell:({row:e})=>y(e.getValue("dateTime"))},{accessorKey:"deadline",header:"Deadline",cell:({row:e})=>{let t=e.original,a=t.deadline;if(!a)return(0,i.jsx)("span",{className:"text-muted-foreground",children:"No deadline"});let r=p(t);return(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)("span",{className:r?"text-red-600 font-medium":"",children:y(a)}),r&&(0,i.jsx)(s.A,{"aria-label":"Overdue",className:"size-4 text-red-600"})]})}},(0,n.K)("location","Location",{maxLength:30,className:"max-w-xs"}),(0,n.Wy)({viewHref:e=>`/tasks/${e.id}`,editHref:e=>`/tasks/${e.id}/edit`,...a&&{onDelete:e=>{a(e)}},showCopyId:!0})],h=[...c?[{label:"Delete Selected",icon:({className:e})=>(0,i.jsx)(d.A,{className:e}),onClick:async e=>{await c(e)},variant:"destructive"}]:[],...m?[{label:"Archive Selected",icon:({className:e})=>(0,i.jsx)(o.A,{className:e}),onClick:async e=>{await m(e)}}]:[]];return(0,i.jsx)(n.bQ,{data:t,columns:f,className:e,searchPlaceholder:"Search tasks by description or location...",searchColumn:"description",emptyMessage:"No tasks found. Create your first task to get started.",pageSize:20,enableRowSelection:!0,enableBulkActions:h.length>0,bulkActions:h,enableColumnVisibility:!0,tableClassName:"shadow-lg",headerClassName:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",rowClassName:"hover:bg-blue-50/50 dark:hover:bg-blue-900/10"})}},25841:(e,t,a)=>{a.d(t,{R:()=>r});var i=a(47138);function r(e){return+(0,i.a)(e)<Date.now()}},73227:(e,t,a)=>{a.d(t,{ZY:()=>I,AK:()=>b,b7:()=>k,xo:()=>g,si:()=>h,K:()=>E});var i=a(93425),r=a(8693),l=a(54050),s=a(43210),d=a(46349),o=a(75176),n=a(83144),u=a(57930);let c={all:["tasks"],detail:e=>["tasks",e]},m=e=>({enabled:!!e,queryFn:()=>o.taskApiService.getById(e),queryKey:c.detail(e),staleTime:3e5}),y=()=>({queryFn:()=>o.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),p=()=>({queryFn:()=>o.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),v=e=>[m(e),y(),p()];var f=a(38118);let h=e=>(0,d.GK)([...c.all],async()=>(await o.taskApiService.getAll()).data,"task",{staleTime:0,...e}),k=e=>(0,d.GK)([...c.detail(e)],async()=>await o.taskApiService.getById(e),"task",{enabled:!!e,staleTime:3e5}),g=e=>{let[t,a,r]=(0,i.E)({queries:v(e)}),l=(0,s.useMemo)(()=>{if(t?.data&&a?.data&&r?.data)try{let e=u.J.fromApi(t.data),i=Array.isArray(a.data)?a.data:[],l=Array.isArray(r.data)?r.data:[];return(0,n.R)(e,i,l)}catch(e){throw console.error("Error enriching task data:",e),e}},[t?.data,a?.data,r?.data]),d=(0,s.useCallback)(()=>{t?.refetch(),a?.refetch(),r?.refetch()},[t?.refetch,a?.refetch,r?.refetch]);return{data:l,error:t?.error||a?.error||r?.error,isError:t?.isError||a?.isError||r?.isError,isLoading:t?.isLoading||a?.isLoading||r?.isLoading,isPending:t?.isPending||a?.isPending||r?.isPending,refetch:d}},I=()=>{let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{let t=u.J.toCreateRequest(e);return await o.taskApiService.create(t)},onError:(t,a,i)=>{i?.previousTasks&&e.setQueryData(c.all,i.previousTasks),console.error("Failed to create task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:c.all});let a=e.getQueryData(c.all);return e.setQueryData(c.all,(e=[])=>{let a="optimistic-"+Date.now().toString(),i=new Date().toISOString();return[...e,{createdAt:i,dateTime:t.dateTime??null,deadline:t.deadline??null,description:t.description,driverEmployee:null,driverEmployeeId:t.driverEmployeeId??null,estimatedDuration:t.estimatedDuration??null,id:a,location:t.location??null,notes:t.notes??null,priority:t.priority,requiredSkills:t.requiredSkills??null,staffEmployee:null,staffEmployeeId:t.staffEmployeeId??null,status:t.status||"Pending",subtasks:t.subtasks?.map(e=>({completed:e.completed||!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:a,title:e.title}))||[],updatedAt:i,vehicle:null,vehicleId:t.vehicleId??null}]}),{previousTasks:a}},onSettled:()=>{e.invalidateQueries({queryKey:c.all})}})},E=()=>{let e=(0,r.jE)();return(0,l.n)({mutationFn:async({data:e,id:t})=>{let a=u.J.toUpdateRequest(e);return await o.taskApiService.update(t,a)},onError:(t,a,i)=>{i?.previousTask&&e.setQueryData(c.detail(a.id),i.previousTask),i?.previousTasksList&&e.setQueryData(c.all,i.previousTasksList),console.error("Failed to update task:",t)},onMutate:async({data:t,id:a})=>{await e.cancelQueries({queryKey:c.all}),await e.cancelQueries({queryKey:c.detail(a)});let i=e.getQueryData(c.detail(a)),r=e.getQueryData(c.all);return e.setQueryData(c.detail(a),e=>{if(!e)return e;let i=new Date().toISOString();return{...e,dateTime:void 0!==t.dateTime?t.dateTime:e.dateTime,deadline:(0,f.d$)(void 0!==t.deadline?t.deadline:e.deadline),description:t.description??e.description,driverEmployeeId:(0,f.d$)(void 0!==t.driverEmployeeId?t.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==t.estimatedDuration?t.estimatedDuration:e.estimatedDuration,location:void 0!==t.location?t.location:e.location,notes:(0,f.d$)(void 0!==t.notes?t.notes:e.notes),priority:t.priority??e.priority,requiredSkills:void 0!==t.requiredSkills?t.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==t.staffEmployeeId?t.staffEmployeeId:e.staffEmployeeId,status:t.status??e.status,subtasks:t.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:a,title:e.title}))||e.subtasks||[],updatedAt:i,vehicleId:(0,f.d$)(void 0!==t.vehicleId?t.vehicleId:e.vehicleId)}}),e.setQueryData(c.all,(e=[])=>e.map(e=>{if(e.id===a){let i=new Date().toISOString(),r=t.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:a,title:e.title}))||e.subtasks||[];return{...e,dateTime:void 0!==t.dateTime?t.dateTime:e.dateTime,deadline:(0,f.d$)(void 0!==t.deadline?t.deadline:e.deadline),description:t.description??e.description,driverEmployeeId:(0,f.d$)(void 0!==t.driverEmployeeId?t.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==t.estimatedDuration?t.estimatedDuration:e.estimatedDuration,location:void 0!==t.location?t.location:e.location,notes:(0,f.d$)(void 0!==t.notes?t.notes:e.notes),priority:t.priority??e.priority,requiredSkills:void 0!==t.requiredSkills?t.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==t.staffEmployeeId?t.staffEmployeeId:e.staffEmployeeId,status:t.status??e.status,subtasks:r,updatedAt:i,vehicleId:(0,f.d$)(void 0!==t.vehicleId?t.vehicleId:e.vehicleId)}}return e})),{previousTask:i,previousTasksList:r}},onSettled:(t,a,i)=>{e.invalidateQueries({queryKey:c.detail(i.id)}),e.invalidateQueries({queryKey:c.all})}})},b=()=>{let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>(await o.taskApiService.delete(e),e),onError:(t,a,i)=>{i?.previousTasksList&&e.setQueryData(c.all,i.previousTasksList),console.error("Failed to delete task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:c.all}),await e.cancelQueries({queryKey:c.detail(t)});let a=e.getQueryData(c.all);return e.setQueryData(c.all,(e=[])=>e.filter(e=>e.id!==t)),e.removeQueries({queryKey:c.detail(t)}),{previousTasksList:a}},onSettled:()=>{e.invalidateQueries({queryKey:c.all})}})}},83144:(e,t,a)=>{a.d(t,{R:()=>r});class i{static enrich(e,t,a){let{employeeMap:i,vehicleMap:r}=this.createLookupMaps(t,a),l=this.enrichStaffEmployee(e,i);return l=this.enrichDriverEmployee(l,i),l=this.enrichVehicle(l,r)}static createLookupMaps(e,t){let a=Array.isArray(e)?e:[],i=Array.isArray(t)?t:[];return{employeeMap:new Map(a.map(e=>[e.id,e])),vehicleMap:new Map(i.map(e=>[e.id,e]))}}static enrichDriverEmployee(e,t){if(!e.driverEmployeeId)return e;let a=e.driverEmployee??t.get(e.driverEmployeeId)??null;return{...e,driverEmployee:a}}static enrichStaffEmployee(e,t){if(!e.staffEmployeeId)return e;let a=e.staffEmployee??t.get(e.staffEmployeeId)??null;return{...e,staffEmployee:a}}static enrichVehicle(e,t){if(!e.vehicleId)return e;let a=e.vehicle??t.get(e.vehicleId)??null;return{...e,vehicle:a}}}let r=(e,t,a)=>i.enrich(e,t,a)}};