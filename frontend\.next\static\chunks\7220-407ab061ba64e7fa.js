"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7220],{45119:(e,s,r)=>{r.d(s,{HJ:()=>p,Kv:()=>u,N:()=>h,Zj:()=>o,oe:()=>m,xj:()=>x});var a=r(28755),t=r(26715),n=r(5041),l=r(90111),i=r(42366),c=r(36973);let d={all:["gifts"],lists:()=>[...d.all,"list"],list:e=>[...d.lists(),{filters:e}],details:()=>[...d.all,"detail"],detail:e=>[...d.details(),e],byRecipient:e=>[...d.all,"recipient",e],byOccasion:e=>[...d.all,"occasion",e],bySender:e=>[...d.all,"sender",e],recent:()=>[...d.all,"recent"]},o=(e,s)=>(0,l.GK)(d.list(e||{}),()=>c.Oo.getAll(e).then(e=>e.data),"gift",{staleTime:3e5,...s}),m=(e,s)=>{var r;return(0,l.GK)(d.detail(e),()=>c.Oo.getById(e),"gift",{enabled:!!e&&(null==(r=null==s?void 0:s.enabled)||r),staleTime:3e5,...s})},h=(e,s)=>(0,a.I)({queryKey:d.byRecipient(e),queryFn:()=>c.Oo.getByRecipient(e),enabled:!!e,staleTime:3e5,...s}),u=()=>{let e=(0,t.jE)(),{showError:s,showSuccess:r}=(0,i.useNotifications)();return(0,n.n)({mutationFn:e=>c.Oo.create(e),onSuccess:s=>{e.invalidateQueries({queryKey:d.all}),s.recipientId&&e.invalidateQueries({queryKey:["recipients","detail",s.recipientId]}),r("Gift created successfully")},onError:e=>{s("Failed to create gift: ".concat(e.message))}})},x=()=>{let e=(0,t.jE)(),{showError:s,showSuccess:r}=(0,i.useNotifications)();return(0,n.n)({mutationFn:e=>{let{id:s,data:r}=e;return c.Oo.update(s,r)},onSuccess:s=>{e.setQueryData(d.detail(s.id),s),e.invalidateQueries({queryKey:d.lists()}),s.recipientId&&e.invalidateQueries({queryKey:["recipients","detail",s.recipientId]}),r("Gift updated successfully")},onError:e=>{s("Failed to update gift: ".concat(e.message))}})},p=()=>{let e=(0,t.jE)(),{showError:s,showSuccess:r}=(0,i.useNotifications)();return(0,n.n)({mutationFn:e=>c.Oo.delete(e),onSuccess:(s,a)=>{e.removeQueries({queryKey:d.detail(a)}),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:["recipients"]}),r("Gift deleted successfully")},onError:e=>{s("Failed to delete gift: ".concat(e.message))}})}},56284:(e,s,r)=>{r.d(s,{IB:()=>a,Jx:()=>n,L$:()=>l});let a=["en-US","ar-IQ"],t=["ar-IQ"];function n(e){return t.includes(e)}let l={"en-US":"English","ar-IQ":"العربية"}},97154:(e,s,r)=>{r.d(s,{ce:()=>T,$$:()=>Z,ND:()=>O});var a=r(95155),t=r(2160),n=r(3561),l=r(18763),i=r(77223),c=r(51920),d=r(91721),o=r(68098),m=r(6874),h=r.n(m),u=r(12115),x=r(17652),p=r(46453),j=r(35695),g=r(56284),f=r(6560),y=r(26126),N=r(66695),v=r(44838),b=r(22346),w=r(54036);let R=e=>{if(!e)return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20";let s=e.toLowerCase();return s.includes("birthday")?"bg-pink-500/20 text-pink-700 border-pink-500/30 dark:text-pink-400 dark:bg-pink-500/10 dark:border-pink-500/20":s.includes("anniversary")?"bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20":s.includes("christmas")||s.includes("holiday")?"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20":s.includes("graduation")?"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20":s.includes("wedding")?"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20":"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20"},C=e=>{let{gift:s,onEdit:r,onDelete:m,showRecipient:C=!0,className:A}=e,[k,I]=(0,u.useState)(!1),{t:D,commonT:S,occasions:F}=function(){let e=(0,x.c3)("gifts"),s=(0,x.c3)("common"),r=(0,x.c3)("forms"),a=(0,x.c3)("gifts.occasions");return{commonT:s,formsT:r,occasionsT:a,occasions:{eidAlFitr:a("eidAlFitr"),eidAlAdha:a("eidAlAdha"),hijraNewYear:a("hijraNewYear"),eidAlMawlid:a("eidAlMawlid"),newYear:a("newYear"),independenceManifesto:a("independenceManifesto"),amazighNewYear:a("amazighNewYear"),labourDay:a("labourDay"),feastOfThrone:a("feastOfThrone"),ouedEdDahabRecovery:a("ouedEdDahabRecovery"),revolutionKingPeople:a("revolutionKingPeople"),youthDay:a("youthDay"),greenMarch:a("greenMarch"),independenceDay:a("independenceDay"),birthday:a("birthday"),anniversary:a("anniversary"),other:a("other")},t:e}}(),{formatDate:M,formatCurrency:z,isRTL:T}=function(){let e=(0,x.c3)(),s=(0,p.Ym)(),r=(0,j.useRouter)(),a=(0,j.usePathname)(),t=(0,g.Jx)(s),n=g.L$[s];return{availableLocales:g.IB.map(e=>({code:e,direction:(0,g.Jx)(e)?"rtl":"ltr",isActive:e===s,name:g.L$[e]})),direction:t?"rtl":"ltr",formatCurrency:e=>new Intl.NumberFormat(s,{currency:"ar-IQ"===s?"IQD":"USD",style:"currency"}).format(e),formatDate:(e,r)=>{let a="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat(s,{day:"numeric",month:"short",year:"numeric",...r}).format(a)},formatNumber:(e,r)=>new Intl.NumberFormat(s,r).format(e),formatRelativeTime:e=>{let r="string"==typeof e?new Date(e):e,a=Math.floor((new Date().getTime()-r.getTime())/1e3),t=new Intl.RelativeTimeFormat(s,{numeric:"auto"});if(60>Math.abs(a))return t.format(-a,"second");if(3600>Math.abs(a))return t.format(-Math.floor(a/60),"minute");if(86400>Math.abs(a))return t.format(-Math.floor(a/3600),"hour");if(2592e3>Math.abs(a))return t.format(-Math.floor(a/86400),"day");if(31536e3>Math.abs(a))return t.format(-Math.floor(a/2592e3),"month");else return t.format(-Math.floor(a/31536e3),"year")},isRTL:t,locale:s,localeName:n,switchLocale:e=>{let s=a.replace(/^\/[a-z]{2}/,"")||"/",t="/".concat(e).concat(s);r.push(t)},t:e}}();return(0,a.jsxs)(N.Zp,{className:(0,w.cn)("p-5 shadow-md hover:shadow-lg transition-shadow",A),children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-primary/10 rounded-lg",children:(0,a.jsx)(t.A,{className:"h-5 w-5 text-primary"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(N.ZB,{className:"text-lg font-semibold line-clamp-1",children:s.itemDescription}),(0,a.jsxs)(N.BT,{className:"text-sm text-muted-foreground",children:[D("form.senderName"),": ",s.senderName]})]})]}),(r||m)&&(0,a.jsxs)(v.rI,{open:k,onOpenChange:I,children:[(0,a.jsx)(v.ty,{asChild:!0,children:(0,a.jsx)(f.r,{actionType:"tertiary",size:"sm",className:"h-8 w-8 p-0","aria-label":"Gift actions",children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(v.SQ,{align:"end",children:[r&&(0,a.jsxs)(v._2,{onClick:()=>{I(!1),null==r||r(s)},children:[(0,a.jsx)(l.A,{className:"h-4 w-4 mr-2"}),D("editGift")]}),r&&m&&(0,a.jsx)(v.mB,{}),m&&(0,a.jsxs)(v._2,{onClick:()=>{I(!1),null==m||m(s)},className:"text-destructive",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-2"}),D("deleteGift")]})]})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{className:"text-muted-foreground",children:[D("form.dateSent"),":"]}),(0,a.jsx)("span",{className:"font-medium",children:M(s.dateSent)})]}),C&&s.recipient&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{className:"text-muted-foreground",children:[D("form.recipient"),":"]}),(0,a.jsx)(h(),{href:"/recipients/".concat(s.recipient.id),className:"font-medium text-primary hover:underline",children:s.recipient.name})]}),s.occasion&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)(y.E,{variant:"outline",className:R(s.occasion),children:s.occasion})]}),s.notes&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.w,{}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,a.jsx)("p",{className:"line-clamp-2",children:s.notes})})]})]}),(0,a.jsx)(N.wL,{className:"pt-4 text-xs text-muted-foreground",children:(0,a.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,a.jsxs)("span",{children:[S("created")," ",M(s.createdAt)]}),s.updatedAt!==s.createdAt&&(0,a.jsxs)("span",{children:[S("updated")," ",M(s.updatedAt)]})]})})]})};var A=r(77023),k=r(9572),I=r(25318),D=r(75074),S=r(8643),F=r(62523),M=r(85057),z=r(59409);let T=e=>{let{filters:s,onFilterChange:r,onClearFilters:t,hasActiveFilters:n,gifts:l}=e,i=(0,u.useMemo)(()=>{let e=new Map,s=new Set,r=new Set;return l.forEach(a=>{a.recipient&&e.set(a.recipient.id,a.recipient.name),a.occasion&&s.add(a.occasion),r.add(a.senderName)}),{recipients:Array.from(e.entries()).map(e=>{let[s,r]=e;return{id:s,name:r}}),occasions:Array.from(s).sort(),senders:Array.from(r).sort()}},[l]),d=e=>{r({search:e})},o=e=>{r({dateRange:{...s.dateRange,from:e}})},m=e=>{r({dateRange:{...s.dateRange,to:e}})},h=[s.search,s.recipientId,s.occasion,s.senderName,s.dateRange.from,s.dateRange.to].filter(Boolean).length;return(0,a.jsx)(N.Zp,{children:(0,a.jsxs)(S.Nt,{children:[(0,a.jsxs)(N.aR,{className:"pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.R6,{asChild:!0,children:(0,a.jsx)(f.r,{actionType:"tertiary",size:"sm",className:"p-0",children:(0,a.jsx)(k.A,{className:"h-4 w-4"})})}),(0,a.jsx)(N.ZB,{className:"text-base",children:"Filters"}),h>0&&(0,a.jsx)(y.E,{variant:"secondary",className:"ml-2",children:h})]}),n&&(0,a.jsxs)(f.r,{actionType:"tertiary",size:"sm",onClick:t,className:"text-muted-foreground hover:text-foreground",children:[(0,a.jsx)(I.A,{className:"h-4 w-4 mr-1"}),"Clear All"]})]}),(0,a.jsx)(N.BT,{children:"Filter gifts by search terms, recipients, occasions, and dates"})]}),(0,a.jsx)(S.Ke,{children:(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{htmlFor:"search",children:"Search"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(D.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(F.p,{id:"search",placeholder:"Search gifts, recipients, or senders...",value:s.search,onChange:e=>d(e.target.value),className:"pl-9"})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{htmlFor:"recipient",children:"Recipient"}),(0,a.jsxs)(z.l6,{value:s.recipientId||"all",onValueChange:e=>{r({recipientId:"all"===e?"":e})},children:[(0,a.jsx)(z.bq,{children:(0,a.jsx)(z.yv,{placeholder:"All recipients"})}),(0,a.jsxs)(z.gC,{children:[(0,a.jsx)(z.eb,{value:"all",children:"All recipients"}),i.recipients.map(e=>(0,a.jsx)(z.eb,{value:e.id,children:e.name},e.id))]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{htmlFor:"occasion",children:"Occasion"}),(0,a.jsxs)(z.l6,{value:s.occasion||"all",onValueChange:e=>{r({occasion:"all"===e?"":e})},children:[(0,a.jsx)(z.bq,{children:(0,a.jsx)(z.yv,{placeholder:"All occasions"})}),(0,a.jsxs)(z.gC,{children:[(0,a.jsx)(z.eb,{value:"all",children:"All occasions"}),i.occasions.map(e=>(0,a.jsx)(z.eb,{value:e,children:e},e))]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{htmlFor:"sender",children:"Sender"}),(0,a.jsxs)(z.l6,{value:s.senderName||"all",onValueChange:e=>{r({senderName:"all"===e?"":e})},children:[(0,a.jsx)(z.bq,{children:(0,a.jsx)(z.yv,{placeholder:"All senders"})}),(0,a.jsxs)(z.gC,{children:[(0,a.jsx)(z.eb,{value:"all",children:"All senders"}),i.senders.map(e=>(0,a.jsx)(z.eb,{value:e,children:e},e))]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{children:"Date Range"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(F.p,{type:"date",value:s.dateRange.from,onChange:e=>o(e.target.value),className:"pl-9",placeholder:"From"})]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"to"}),(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(F.p,{type:"date",value:s.dateRange.to,onChange:e=>m(e.target.value),className:"pl-9",placeholder:"To"})]})]})]})]})]})})]})})},L={search:"",recipientId:"",occasion:"",senderName:"",dateRange:{from:"",to:""}},O=e=>{let{gifts:s,isLoading:r=!1,error:t=null,onEdit:n,onDelete:l,onRetry:i,showRecipient:c=!0,className:d}=e,[o,m]=(0,u.useState)(L),h=s.filter(e=>{if(o.search){var s;let r=o.search.toLowerCase(),a=e.itemDescription.toLowerCase().includes(r),t=e.senderName.toLowerCase().includes(r),n=null==(s=e.recipient)?void 0:s.name.toLowerCase().includes(r);if(!a&&!t&&!n)return!1}if(o.recipientId&&e.recipientId!==o.recipientId||o.occasion&&e.occasion!==o.occasion)return!1;if(o.senderName){let s=o.senderName.toLowerCase();if(!e.senderName.toLowerCase().includes(s))return!1}if(o.dateRange.from||o.dateRange.to){let s=new Date(e.dateSent);if(o.dateRange.from&&s<new Date(o.dateRange.from))return!1;if(o.dateRange.to){let e=new Date(o.dateRange.to);if(e.setHours(23,59,59,999),s>e)return!1}}return!0}),x=Object.values(o).some(e=>"string"==typeof e?""!==e:"object"==typeof e&&(""!==e.from||""!==e.to));return(0,a.jsxs)("div",{className:(0,w.cn)("space-y-6",d),children:[(0,a.jsx)(T,{filters:o,onFilterChange:e=>{m(s=>({...s,...e}))},onClearFilters:()=>{m(L)},hasActiveFilters:x,gifts:s}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:x?(0,a.jsxs)(a.Fragment,{children:["Showing ",h.length," of ",s.length," gifts",h.length!==s.length&&(0,a.jsxs)("span",{className:"ml-2 text-primary",children:["(",s.length-h.length," filtered out)"]})]}):"".concat(s.length," gift").concat(1!==s.length?"s":""," total")})}),(0,a.jsx)(A.gO,{data:h,isLoading:r,error:(null==t?void 0:t.message)||null,loadingComponent:(0,a.jsx)(A.jt,{count:6,variant:"card"}),emptyComponent:(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("div",{className:"text-muted-foreground",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{className:"text-lg font-medium mb-2",children:"No gifts match your filters"}),(0,a.jsx)("p",{className:"text-sm",children:"Try adjusting your search criteria or clearing filters"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{className:"text-lg font-medium mb-2",children:"No gifts found"}),(0,a.jsx)("p",{className:"text-sm",children:"Start by adding your first gift"})]})})}),...i&&{onRetry:i},children:e=>(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:e.map(e=>(0,a.jsx)(C,{gift:e,...n&&{onEdit:n},...l&&{onDelete:l},showRecipient:c},e.id))})})]})};var q=r(90221),B=r(12543),E=r(59119),G=r(62177),J=r(71153),K=r(30285),Q=r(17759),Y=r(88539);let V=e=>J.z.object({dateSent:J.z.string().min(1,e("required")),itemDescription:J.z.string().min(1,e("required")).max(500,e("maxLength",{max:500})),notes:J.z.string().max(1e3,e("maxLength",{max:1e3})).default(""),occasion:J.z.string().max(100,e("maxLength",{max:100})).default(""),recipientId:J.z.string().min(1,e("required")),senderName:J.z.string().min(1,e("required")).max(255,e("maxLength",{max:255}))}),$=["eidAlFitr","eidAlAdha","hijraNewYear","eidAlMawlid","newYear","independenceManifesto","amazighNewYear","labourDay","feastOfThrone","ouedEdDahabRecovery","revolutionKingPeople","youthDay","greenMarch","independenceDay"],Z=e=>{var s,r,t,n,l;let{initialData:i,isEditing:c=!1,isLoading:d=!1,onSubmit:o,recipients:m=[]}=e,h=(0,j.useRouter)(),u=(0,x.c3)("forms.labels"),p=(0,x.c3)("forms.buttons"),g=(0,x.c3)("forms.placeholders"),f=(0,x.c3)("forms.titles"),y=(0,x.c3)("forms.descriptions"),v=(0,x.c3)("forms.occasions"),b=V((0,x.c3)("forms.validation")),w=(0,G.mN)({defaultValues:{dateSent:(null==i?void 0:i.dateSent)?i.dateSent.split("T")[0]:new Date().toISOString().split("T")[0],itemDescription:null!=(s=null==i?void 0:i.itemDescription)?s:"",notes:null!=(r=null==i?void 0:i.notes)?r:"",occasion:null!=(t=null==i?void 0:i.occasion)?t:"",recipientId:null!=(n=null==i?void 0:i.recipientId)?n:"",senderName:null!=(l=null==i?void 0:i.senderName)?l:""},resolver:(0,q.u)(b)}),R=async e=>{try{let s={...e,dateSent:new Date(e.dateSent).toISOString(),notes:e.notes||null,occasion:e.occasion||null};await o(s)}catch(e){console.error("Error submitting gift form:",e)}},C=()=>{h.back()};return(0,a.jsxs)(N.Zp,{className:"mx-auto max-w-2xl",children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(K.$,{className:"p-2",onClick:C,size:"sm",variant:"ghost",children:(0,a.jsx)(B.A,{className:"size-4"})}),(0,a.jsx)(N.ZB,{children:f(c?"editGift":"addGift")})]})}),(0,a.jsx)(Q.lV,{...w,children:(0,a.jsxs)("form",{onSubmit:w.handleSubmit(R),children:[(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsx)(Q.zB,{control:w.control,name:"itemDescription",render:e=>{let{field:s}=e;return(0,a.jsxs)(Q.eI,{children:[(0,a.jsxs)(Q.lR,{children:[u("itemDescription")," *"]}),(0,a.jsx)(Q.MJ,{children:(0,a.jsx)(F.p,{placeholder:g("enterDescription"),...s})}),(0,a.jsx)(Q.C5,{})]})}}),(0,a.jsx)(Q.zB,{control:w.control,name:"recipientId",render:e=>{let{field:s}=e;return(0,a.jsxs)(Q.eI,{children:[(0,a.jsxs)(Q.lR,{children:[u("recipient")," *"]}),(0,a.jsxs)(z.l6,{defaultValue:s.value,onValueChange:s.onChange,children:[(0,a.jsx)(Q.MJ,{children:(0,a.jsx)(z.bq,{children:(0,a.jsx)(z.yv,{placeholder:g("selectRecipient")})})}),(0,a.jsx)(z.gC,{children:m.map(e=>(0,a.jsx)(z.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(e.role||e.worksite)&&(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:[e.role,e.worksite].filter(Boolean).join(" • ")})]})},e.id))})]}),(0,a.jsx)(Q.Rr,{children:y("chooseRecipient")}),(0,a.jsx)(Q.C5,{})]})}}),(0,a.jsx)(Q.zB,{control:w.control,name:"dateSent",render:e=>{let{field:s}=e;return(0,a.jsxs)(Q.eI,{children:[(0,a.jsxs)(Q.lR,{children:[u("dateSent")," *"]}),(0,a.jsx)(Q.MJ,{children:(0,a.jsx)(F.p,{type:"date",...s})}),(0,a.jsx)(Q.Rr,{children:y("whenGiftSent")}),(0,a.jsx)(Q.C5,{})]})}}),(0,a.jsx)(Q.zB,{control:w.control,name:"senderName",render:e=>{let{field:s}=e;return(0,a.jsxs)(Q.eI,{children:[(0,a.jsxs)(Q.lR,{children:[u("senderName")," *"]}),(0,a.jsx)(Q.MJ,{children:(0,a.jsx)(F.p,{placeholder:g("whoSentThisGift"),...s})}),(0,a.jsx)(Q.Rr,{children:y("senderNameDesc")}),(0,a.jsx)(Q.C5,{})]})}}),(0,a.jsx)(Q.zB,{control:w.control,name:"occasion",render:e=>{let{field:s}=e;return(0,a.jsxs)(Q.eI,{children:[(0,a.jsx)(Q.lR,{children:u("occasion")}),(0,a.jsxs)(z.l6,{onValueChange:s.onChange,value:s.value,children:[(0,a.jsx)(Q.MJ,{children:(0,a.jsx)(z.bq,{children:(0,a.jsx)(z.yv,{placeholder:g("selectOccasionOptional")})})}),(0,a.jsx)(z.gC,{children:$.map(e=>(0,a.jsx)(z.eb,{value:v(e),children:v(e)},e))})]}),(0,a.jsx)(Q.Rr,{children:y("giftOccasion")}),(0,a.jsx)(Q.C5,{})]})}}),(0,a.jsx)(Q.zB,{control:w.control,name:"notes",render:e=>{let{field:s}=e;return(0,a.jsxs)(Q.eI,{children:[(0,a.jsx)(Q.lR,{children:u("notes")}),(0,a.jsx)(Q.MJ,{children:(0,a.jsx)(Y.T,{className:"min-h-[100px]",placeholder:g("enterGiftNotes"),...s})}),(0,a.jsx)(Q.Rr,{children:y("optionalNotes")}),(0,a.jsx)(Q.C5,{})]})}})]}),(0,a.jsxs)(N.wL,{className:"flex justify-end gap-4",children:[(0,a.jsx)(K.$,{disabled:d,onClick:C,type:"button",variant:"outline",children:p("cancel")}),(0,a.jsx)(K.$,{className:"min-w-[100px]",disabled:d,type:"submit",children:d?p("saving"):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"mr-2 size-4"}),p(c?"updateGift":"saveGift")]})})]})]})})]})}}}]);