(()=>{var e={};e.id=9239,e.ids=[9239],e.modules={997:(e,t,r)=>{"use strict";r.d(t,{k:()=>f});var s=r(60687),i=r(28946),n=r(11516),a=r(20620),o=r(36644);let l=(0,r(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var d=r(43210),c=r(68752),h=r(21342),u=r(3940),p=r(22482),m=r(22364);function f({className:e,csvData:t,enableCsv:r=!1,entityId:f,fileName:x,reportContentId:v,reportType:y,tableId:j}){let[g,S]=(0,d.useState)(!1),[_,b]=(0,d.useState)(!1),{showFormSuccess:k,showFormError:C}=(0,u.t6)(),w=async()=>{S(!0);try{let e=`/api/reports/${y}${f?`/${f}`:""}`,t=document.createElement("a");t.href=e,t.download=`${x}.pdf`,t.target="_blank",document.body.append(t),t.click(),t.remove(),k({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),C(`PDF download failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{S(!1)}},N=async()=>{if(r){b(!0);try{if(t?.data&&t.headers)(0,m.og)(t.data,t.headers,`${x}.csv`);else if(j){let e=(0,m.tL)(j);(0,m.og)(e.data,e.headers,`${x}.csv`)}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");k({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),C(`CSV generation failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{b(!1)}}},R=g||_;return(0,s.jsxs)("div",{className:(0,p.cn)("flex items-center gap-2 no-print",e),children:[(0,s.jsx)(c.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,s.jsx)(i.A,{className:"size-4"})}),(0,s.jsxs)(h.rI,{children:[(0,s.jsx)(h.ty,{asChild:!0,children:(0,s.jsx)(c.r,{actionType:"secondary","aria-label":"Download report",disabled:R,size:"icon",title:"Download Report",children:R?(0,s.jsx)(n.A,{className:"size-4 animate-spin"}):(0,s.jsx)(a.A,{className:"size-4"})})}),(0,s.jsxs)(h.SQ,{align:"end",children:[(0,s.jsxs)(h._2,{disabled:g,onClick:w,children:[g?(0,s.jsx)(n.A,{className:"mr-2 size-4 animate-spin"}):(0,s.jsx)(o.A,{className:"mr-2 size-4"}),(0,s.jsx)("span",{children:"Download PDF"})]}),r&&(0,s.jsxs)(h._2,{disabled:_,onClick:N,children:[_?(0,s.jsx)(n.A,{className:"mr-2 size-4 animate-spin"}):(0,s.jsx)(l,{className:"mr-2 size-4"}),(0,s.jsx)("span",{children:"Download CSV"})]})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6655:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,metadata:()=>i});var s=r(37413);let i={title:"Vehicle Report"};function n({children:e}){return(0,s.jsx)(s.Fragment,{children:e})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20620:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35421:(e,t,r)=>{Promise.resolve().then(r.bind(r,49789))},49789:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\[locale]\\\\vehicles\\\\[id]\\\\report\\\\service-history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\[id]\\report\\service-history\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},59021:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),i=r(48088),n=r(88170),a=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["vehicles",{children:["[id]",{children:["report",{children:["service-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49789)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\[id]\\report\\service-history\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6655)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\[id]\\report\\layout.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,20657)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,52714)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\[locale]\\vehicles\\[id]\\report\\service-history\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/vehicles/[id]/report/service-history/page",pathname:"/[locale]/vehicles/[id]/report/service-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},62639:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(60687),i=r(76180),n=r.n(i),a=r(44610),o=r(24920),l=r(30474),d=r(85814),c=r.n(d),h=r(16189),u=r(43210),p=r(95668),m=r(997),f=r(24847),x=r(68752),v=r(29523),y=r(44493),j=r(52027),g=r(48041),S=r(63213),_=r(72273),b=r(2775),k=r(48184);function C(){let e=(0,h.useParams)(),{loading:t,session:r,user:i}=(0,S.useAuthContext)(),d=!!i&&!!r?.access_token,C=e?.id,{data:w,error:N,isLoading:R,refetch:T}=(0,_.W_)(Number(C),{enabled:d}),{data:z=[],error:P,isLoading:A,refetch:F}=(0,b.xH)(Number(C),{enabled:d&&!!w?.id}),q=(0,u.useCallback)(()=>{T(),F()},[T,F]),D=N||P;return t||R||A?(0,s.jsx)(p.A,{children:(0,s.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(j.jt,{count:1,variant:"card"}),(0,s.jsx)(j.jt,{count:5,variant:"table"})]})})}):D||!w?(0,s.jsx)(p.A,{children:(0,s.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,s.jsx)(y.Zp,{className:"shadow-md",children:(0,s.jsxs)(y.Wu,{className:"p-6",children:[(0,s.jsx)("h2",{className:"mb-2 text-xl font-semibold text-red-600",children:"Error"}),(0,s.jsx)("p",{className:"mb-4 text-gray-700",children:(0,k.u1)(D)||"Vehicle not found"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(v.$,{onClick:q,children:"Try Again"}),(0,s.jsx)(v.$,{asChild:!0,variant:"outline",children:(0,s.jsx)(c(),{href:"/vehicles",children:"Back to Vehicles"})})]})]})})})}):(0,s.jsx)(p.A,{children:(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86 print-container mx-auto max-w-5xl space-y-6 p-4",children:[(0,s.jsx)(g.z,{description:`${w.make} ${w.model} (${w.year})`,icon:a.A,title:"Vehicle Service History",children:(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86 no-print flex gap-2",children:[(0,s.jsx)(x.r,{actionType:"tertiary",asChild:!0,icon:(0,s.jsx)(o.A,{className:"size-4"}),children:(0,s.jsx)(c(),{href:`/vehicles/${C}`,children:"View Vehicle"})}),(0,s.jsx)(m.k,{enableCsv:z.length>0,entityId:C,fileName:`vehicle-service-history-${w.make}-${w.model}`,reportContentId:"#vehicle-service-history-content",reportType:"vehicle-service-history",tableId:"#service-history-table"})]})}),(0,s.jsxs)("div",{id:"vehicle-service-history-content",className:"jsx-690e25f84e8ddf86 report-content",children:[(0,s.jsx)(y.Zp,{className:"card-print mb-6 shadow-md",children:(0,s.jsx)(y.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86 grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86 col-span-1 md:col-span-2",children:[(0,s.jsx)("h2",{className:"jsx-690e25f84e8ddf86 mb-4 text-xl font-semibold text-gray-800",children:"Vehicle Details"}),(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86 grid grid-cols-2 gap-x-4 gap-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,s.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Make:"})," ",w.make]}),(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,s.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Model:"})," ",w.model]}),(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,s.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Year:"})," ",w.year]}),w.licensePlate&&(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,s.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Plate Number:"})," ",w.licensePlate]}),w.color&&(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,s.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Color:"})," ",w.color]}),(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,s.jsx)("strong",{className:"jsx-690e25f84e8ddf86",children:"Initial Odometer:"})," ",null!==w.initialOdometer&&void 0!==w.initialOdometer?`${w.initialOdometer.toLocaleString()} miles`:"Not recorded"]}),(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,s.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Current Odometer:"})," ",z.length>0?`${Math.max(...z.map(e=>e.odometer)).toLocaleString()} miles`:null!==w.initialOdometer&&void 0!==w.initialOdometer?`${w.initialOdometer.toLocaleString()} miles`:"No odometer data available"]}),(0,s.jsxs)("div",{className:"jsx-690e25f84e8ddf86 col-span-2",children:[(0,s.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Last Updated:"})," ",z.length>0?new Date(Math.max(...z.map(e=>new Date(e.date).getTime()))).toLocaleDateString():"No service records"]})]})]}),w.imageUrl&&(0,s.jsx)("div",{className:"jsx-690e25f84e8ddf86 no-print col-span-1",children:(0,s.jsx)("div",{className:"jsx-690e25f84e8ddf86 relative aspect-[4/3] w-full overflow-hidden rounded",children:(0,s.jsx)(l.default,{alt:`${w.make} ${w.model}`,fill:!0,sizes:"(max-width: 768px) 100vw, 300px",src:w.imageUrl,style:{objectFit:"cover"}})})})]})})}),(0,s.jsxs)("header",{className:"jsx-690e25f84e8ddf86 print-only mb-8 border-b-2 border-gray-300 pb-4 text-center",children:[(0,s.jsx)("h1",{className:"jsx-690e25f84e8ddf86 text-3xl font-bold text-gray-800",children:"Vehicle Service History Report"}),(0,s.jsxs)("p",{className:"jsx-690e25f84e8ddf86 text-md text-gray-600",children:[w.make," ",w.model," (",w.year,")",w.licensePlate&&` - ${w.licensePlate}`]})]}),(0,s.jsx)(f.R,{error:null,isLoading:!1,onRetry:q,records:z,showVehicleInfo:!1,vehicleSpecific:!0}),(0,s.jsxs)("footer",{className:"jsx-690e25f84e8ddf86 mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,s.jsxs)("p",{className:"jsx-690e25f84e8ddf86",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,s.jsx)("p",{className:"jsx-690e25f84e8ddf86",children:"WorkHub - Vehicle Service Management"})]})]}),(0,s.jsx)(n(),{id:"690e25f84e8ddf86",children:".print-only{display:none}@media print{.no-print{display:none!important}.print-only{display:block}.print-container{padding:1rem}.card-print{-webkit-box-shadow:none!important;-moz-box-shadow:none!important;box-shadow:none!important;border:none!important}.print-service-col{max-width:200px;white-space:normal!important}.print-notes-col{max-width:200px;white-space:normal!important}.print-text-wrap{word-break:break-word;white-space:normal!important}}@media(max-width:640px){.overflow-x-auto{overflow-x:auto}.summary-grid{grid-template-columns:1fr 1fr!important}}"})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63037:(e,t,r)=>{Promise.resolve().then(r.bind(r,62639))},74075:e=>{"use strict";e.exports=require("zlib")},75913:(e,t,r)=>{"use strict";r(56397);var s=r(43210),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),n="undefined"!=typeof process&&process.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},o=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,i=t.optimizeForSpeed,o=void 0===i?n:i;l(a(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",l("boolean"==typeof o,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=o,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;l(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){return l(a(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},r.replaceRule=function(e,t){this._optimizeForSpeed;var r=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){n||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}return e},r.deleteRule=function(e){this._serverSheet.deleteRule(e)},r.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},r.cssRules=function(){return this._serverSheet.cssRules},r.makeStyleTag=function(e,t,r){t&&l(a(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return r?i.insertBefore(s,r):i.appendChild(s),s},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},c={};function h(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return c[s]||(c[s]="jsx-"+d(e+"-"+r)),c[s]}function u(e,t){var r=e+(t=t.replace(/\/style/gi,"\\/style"));return c[r]||(c[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,s=void 0===r?null:r,i=t.optimizeForSpeed,n=void 0!==i&&i;this._sheet=s||new o({name:"styled-jsx",optimizeForSpeed:n}),this._sheet.inject(),s&&"boolean"==typeof n&&(this._sheet.setOptimizeForSpeed(n),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var r=this.getIdAndRules(e),s=r.styleId,i=r.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var n=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=n,this._instancesCounts[s]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var s=this._fromServer&&this._fromServer[r];s?(s.parentNode.removeChild(s),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],s=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,s=e.id;if(r){var i=h(s,r);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return u(i,e)}):[u(i,t)]}}return{styleId:h(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=s.createContext(null);m.displayName="StyleSheetContext";i.default.useInsertionEffect||i.default.useLayoutEffect;var f=void 0;function x(e){var t=f||s.useContext(m);return t&&t.add(e),null}x.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=x},76180:(e,t,r)=>{"use strict";e.exports=r(75913).style},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,3871,7048,8390,2670,4897,474,8739,3302,2936,742,2452,5009,5335],()=>r(59021));module.exports=s})();