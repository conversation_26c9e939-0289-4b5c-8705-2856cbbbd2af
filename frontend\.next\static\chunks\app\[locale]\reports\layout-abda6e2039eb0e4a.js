(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8646],{3561:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},19018:(e,r,t)=>{"use strict";t.d(r,{Breadcrumb:()=>i,BreadcrumbItem:()=>u,BreadcrumbLink:()=>d,BreadcrumbList:()=>l,BreadcrumbPage:()=>f,BreadcrumbSeparator:()=>m});var a=t(95155),n=t(99708),s=t(73158),o=(t(3561),t(12115)),c=t(54036);let i=o.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("nav",{"aria-label":"breadcrumb",className:(0,c.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",t),ref:r,...n})});i.displayName="Breadcrumb";let l=o.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("ol",{className:(0,c.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",t),ref:r,...n})});l.displayName="BreadcrumbList";let u=o.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("li",{className:(0,c.cn)("inline-flex items-center gap-1.5",t),ref:r,...n})});u.displayName="BreadcrumbItem";let d=o.forwardRef((e,r)=>{let{asChild:t,className:s,...o}=e,i=t?n.DX:"a";return(0,a.jsx)(i,{className:(0,c.cn)("transition-colors hover:text-foreground",s),ref:r,...o})});d.displayName="BreadcrumbLink";let f=o.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,c.cn)("font-normal text-foreground",t),ref:r,role:"link",...n})});f.displayName="BreadcrumbPage";let m=e=>{let{children:r,className:t,...n}=e;return(0,a.jsx)("span",{"aria-hidden":"true",className:(0,c.cn)("[&>svg]:size-3.5",t),role:"presentation",...n,children:null!=r?r:(0,a.jsx)(s.A,{className:"size-4"})})};m.displayName="BreadcrumbSeparator"},35695:(e,r,t)=>{"use strict";var a=t(18999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},39249:(e,r,t)=>{"use strict";t.d(r,{C6:()=>n,Cg:()=>c,Cl:()=>s,Tt:()=>o,fX:()=>i});var a=function(e,r){return(a=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])})(e,r)};function n(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function t(){this.constructor=e}a(e,r),e.prototype=null===r?Object.create(r):(t.prototype=r.prototype,new t)}var s=function(){return(s=Object.assign||function(e){for(var r,t=1,a=arguments.length;t<a;t++)for(var n in r=arguments[t])Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n]);return e}).apply(this,arguments)};function o(e,r){var t={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>r.indexOf(a)&&(t[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)0>r.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(t[a[n]]=e[a[n]]);return t}function c(e,r,t,a){var n,s=arguments.length,o=s<3?r:null===a?a=Object.getOwnPropertyDescriptor(r,t):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,r,t,a);else for(var c=e.length-1;c>=0;c--)(n=e[c])&&(o=(s<3?n(o):s>3?n(r,t,o):n(r,t))||o);return s>3&&o&&Object.defineProperty(r,t,o),o}Object.create;function i(e,r,t){if(t||2==arguments.length)for(var a,n=0,s=r.length;n<s;n++)!a&&n in r||(a||(a=Array.prototype.slice.call(r,0,n)),a[n]=r[n]);return e.concat(a||Array.prototype.slice.call(r))}Object.create,"function"==typeof SuppressedError&&SuppressedError},39745:(e,r,t)=>{Promise.resolve().then(t.bind(t,46096)),Promise.resolve().then(t.bind(t,89440))},46096:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});var a=t(46453),n=t(95155);function s(e){let{locale:r,...t}=e;if(!r)throw Error(void 0);return(0,n.jsx)(a.Dk,{locale:r,...t})}},73158:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},89440:(e,r,t)=>{"use strict";t.d(r,{AppBreadcrumb:()=>u});var a=t(95155),n=t(6874),s=t.n(n),o=t(35695),c=t(12115),i=t(19018),l=t(54036);function u(e){let{className:r,homeHref:t="/",homeLabel:n="Dashboard",showContainer:u=!0}=e,d=(0,o.usePathname)(),f=d?d.split("/").filter(Boolean):[],m=e=>{if(/^\d+$/.test(e))return"ID: ".concat(e);if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let r={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return r[e]?r[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},p=f.map((e,r)=>{let t="/"+f.slice(0,r+1).join("/"),n=r===f.length-1,o=m(e);return(0,a.jsxs)(c.Fragment,{children:[(0,a.jsx)(i.BreadcrumbItem,{children:n?(0,a.jsx)(i.BreadcrumbPage,{className:"font-medium text-foreground",children:o}):(0,a.jsx)(i.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(s(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:o})})}),!n&&(0,a.jsx)(i.BreadcrumbSeparator,{})]},t)}),h=(0,a.jsx)(i.Breadcrumb,{className:(0,l.cn)("text-sm",r),children:(0,a.jsxs)(i.BreadcrumbList,{className:"flex-wrap",children:[(0,a.jsx)(i.BreadcrumbItem,{children:(0,a.jsx)(i.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(s(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:n})})}),f.length>0&&(0,a.jsx)(i.BreadcrumbSeparator,{}),p]})});return u?(0,a.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,a.jsx)("div",{className:"flex items-center",children:h})}):h}}},e=>{var r=r=>e(e.s=r);e.O(0,[6476,6874,6453,4036,8441,1684,7358],()=>r(39745)),_N_E=e.O()}]);