"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9139],{6560:(e,s,a)=>{a.d(s,{r:()=>n});var r=a(95155),l=a(50172),t=a(12115),d=a(30285),i=a(54036);let n=t.forwardRef((e,s)=>{let{actionType:a="primary",asChild:t=!1,children:n,className:c,disabled:m,icon:o,isLoading:x=!1,loadingText:f,...u}=e,{className:h,variant:p}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[a];return(0,r.jsx)(d.$,{asChild:t,className:(0,i.cn)(h,c),disabled:x||m,ref:s,variant:p,...u,children:x?(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,r.jsx)(l.A,{className:"mr-2 size-4 animate-spin"}),f||n]}):(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",o&&(0,r.jsx)("span",{className:"mr-2",children:o}),n]})})});n.displayName="ActionButton"},44689:(e,s,a)=>{function r(e,s){return e&&"string"==typeof e&&""!==e.trim()?e:s}function l(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:400,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:250;return"https://picsum.photos/seed/".concat(e,"/").concat(s,"/").concat(a)}function t(e,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"card",{width:t,height:d}={card:{width:400,height:250},detail:{width:600,height:375},report:{width:600,height:375}}[a];return r(e,l(s,t,d))}function d(e,s){return r(e,l(s,600,375))}a.d(s,{_x:()=>t,aI:()=>d})},55365:(e,s,a)=>{a.d(s,{Fc:()=>n,TN:()=>m,XL:()=>c});var r=a(95155),l=a(74466),t=a(12115),d=a(54036);let i=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),n=t.forwardRef((e,s)=>{let{className:a,variant:l,...t}=e;return(0,r.jsx)("div",{className:(0,d.cn)(i({variant:l}),a),ref:s,role:"alert",...t})});n.displayName="Alert";let c=t.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("h5",{className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",a),ref:s,...l})});c.displayName="AlertTitle";let m=t.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{className:(0,d.cn)("text-sm [&_p]:leading-relaxed",a),ref:s,...l})});m.displayName="AlertDescription"},62523:(e,s,a)=>{a.d(s,{p:()=>d});var r=a(95155),l=a(12115),t=a(54036);let d=l.forwardRef((e,s)=>{let{className:a,type:l,...d}=e;return(0,r.jsx)("input",{className:(0,t.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,type:l,...d})});d.displayName="Input"},66695:(e,s,a)=>{a.d(s,{BT:()=>c,Wu:()=>m,ZB:()=>n,Zp:()=>d,aR:()=>i,wL:()=>o});var r=a(95155),l=a(12115),t=a(54036);let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{className:(0,t.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),ref:s,...l})});d.displayName="Card";let i=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{className:(0,t.cn)("flex flex-col space-y-1.5 p-6",a),ref:s,...l})});i.displayName="CardHeader";let n=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{className:(0,t.cn)("text-2xl font-semibold leading-none tracking-tight",a),ref:s,...l})});n.displayName="CardTitle";let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{className:(0,t.cn)("text-sm text-muted-foreground",a),ref:s,...l})});c.displayName="CardDescription";let m=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{className:(0,t.cn)("p-6 pt-0",a),ref:s,...l})});m.displayName="CardContent";let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{className:(0,t.cn)("flex items-center p-6 pt-0",a),ref:s,...l})});o.displayName="CardFooter"},68856:(e,s,a)=>{a.d(s,{E:()=>t});var r=a(95155),l=a(54036);function t(e){let{className:s,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",s),...a})}},77023:(e,s,a)=>{a.d(s,{gO:()=>x,jt:()=>p,pp:()=>f});var r=a(95155),l=a(11133),t=a(50172);a(12115);var d=a(6560),i=a(55365),n=a(68856),c=a(54036);let m={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},o={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function x(e){let{children:s,className:a,data:l,emptyComponent:t,error:d,errorComponent:i,isLoading:n,loadingComponent:m,onRetry:o}=e;return n?m||(0,r.jsx)(h,{...a&&{className:a},text:"Loading..."}):d?i||(0,r.jsx)(u,{...a&&{className:a},message:d,...o&&{onRetry:o}}):!l||Array.isArray(l)&&0===l.length?t||(0,r.jsx)("div",{className:(0,c.cn)("text-center py-8",a),children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,r.jsx)("div",{className:a,children:s(l)})}function f(e){let{className:s,description:a,icon:l,primaryAction:t,secondaryAction:i,title:n}=e;return(0,r.jsxs)("div",{className:(0,c.cn)("space-y-6 text-center py-12",s),children:[l&&(0,r.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,r.jsx)(l,{className:"h-10 w-10 text-muted-foreground"})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:n}),a&&(0,r.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:a})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[t&&(0,r.jsx)(d.r,{actionType:"primary",asChild:!!t.href,icon:t.icon,onClick:t.onClick,children:t.href?(0,r.jsx)("a",{href:t.href,children:t.label}):t.label}),i&&(0,r.jsx)(d.r,{actionType:"tertiary",asChild:!!i.href,icon:i.icon,onClick:i.onClick,children:i.href?(0,r.jsx)("a",{href:i.href,children:i.label}):i.label})]})]})}function u(e){let{className:s,message:a,onRetry:n}=e;return(0,r.jsxs)(i.Fc,{className:(0,c.cn)("my-4",s),variant:"destructive",children:[(0,r.jsx)(l.A,{className:"size-4"}),(0,r.jsx)(i.XL,{children:"Error"}),(0,r.jsx)(i.TN,{children:(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:a}),n&&(0,r.jsx)(d.r,{actionType:"tertiary",icon:(0,r.jsx)(t.A,{className:"size-4"}),onClick:n,size:"sm",children:"Try Again"})]})})]})}function h(e){let{className:s,fullPage:a=!1,size:l="md",text:d}=e;return(0,r.jsx)("div",{className:(0,c.cn)("flex items-center justify-center",a&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(t.A,{className:(0,c.cn)("animate-spin text-primary",m[l])}),d&&(0,r.jsx)("span",{className:(0,c.cn)("mt-2 text-muted-foreground",o[l]),children:d})]})})}function p(e){let{className:s,count:a=1,testId:l="loading-skeleton",variant:t="default"}=e;return"card"===t?(0,r.jsx)("div",{className:(0,c.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",s),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,r.jsx)(n.E,{className:"aspect-[16/10] w-full"}),(0,r.jsxs)("div",{className:"p-5",children:[(0,r.jsx)(n.E,{className:"mb-1 h-7 w-3/4"}),(0,r.jsx)(n.E,{className:"mb-3 h-4 w-1/2"}),(0,r.jsx)(n.E,{className:"my-3 h-px w-full"}),(0,r.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.E,{className:"mr-2.5 size-5 rounded-full"}),(0,r.jsx)(n.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===t?(0,r.jsxs)("div",{className:(0,c.cn)("space-y-3",s),"data-testid":l,children:[(0,r.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,r.jsx)(n.E,{className:"h-8 flex-1"},s))}),Array(a).fill(0).map((e,s)=>(0,r.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,r.jsx)(n.E,{className:"h-6 flex-1"},s))},s))]}):"list"===t?(0,r.jsx)("div",{className:(0,c.cn)("space-y-3",s),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(n.E,{className:"size-12 rounded-full"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)(n.E,{className:"h-4 w-1/3"}),(0,r.jsx)(n.E,{className:"h-4 w-full"})]})]},s))}):"stats"===t?(0,r.jsx)("div",{className:(0,c.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",s),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,r.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(n.E,{className:"h-5 w-1/3"}),(0,r.jsx)(n.E,{className:"size-5 rounded-full"})]}),(0,r.jsx)(n.E,{className:"mt-3 h-8 w-1/2"}),(0,r.jsx)(n.E,{className:"mt-2 h-4 w-2/3"})]},s))}):(0,r.jsx)("div",{className:(0,c.cn)("space-y-2",s),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,r.jsx)(n.E,{className:"h-5 w-full"},s))})}}}]);